/**
 * 语素库规模统计脚本
 * 统计V5引擎中语素库的实际规模
 */

// 从V5引擎中提取的语素库结构
const elementLibrary = {
  subjects: {
    古代人物: ['贫僧', '道士', '书生', '侠客', '状元', '举人', '秀才', '太守', '县令', '师爷'],
    现代职业: ['程序员', '设计师', '产品经理', '运营', '策划', '文案', '测试', '架构师', '前端', '后端'],
    网络身份: ['UP主', '主播', '网红', '博主', '自媒体', '数字游民', '斜杠青年', 'KOL', '网络达人', '内容创作者'],
    动物世界: ['猫', '狗', '猪', '鸟', '鱼', '蜗牛', '大象', '蚂蚁', '老鼠', '兔子', '熊猫', '企鹅', '海豚', '狮子'],
    天体宇宙: ['月亮', '星星', '太阳', '云朵', '彩虹', '闪电', '银河', '宇宙', '星球', '流星', '黑洞', '星云'],
    抽象概念: ['快乐', '悲伤', '愤怒', '温柔', '智慧', '勇敢', '梦想', '希望', '爱情', '友情', '孤独', '自由'],
    食物美食: ['芝士', '咖啡', '茶', '巧克力', '蛋糕', '面包', '牛奶', '果汁', '冰淇淋', '薯片', '火锅', '烧烤'],
    技术概念: ['WiFi', '404', '500', '数据库', '服务器', '算法', 'AI', '云端', '区块链', '元宇宙', 'VR', 'AR'],
    情绪状态: ['间歇性努力', '积极废人', '外向孤独症', '理性但感性', '想独立又想被照顾', '社交恐惧但渴望理解', '想要自由又怕孤独', '低调张扬', '懒惰但有梦想', '成熟幼稚', '高敏感低社交', '理想主义现实派', '温和愤青', '精神内耗专业户', '低落但坚韧', '焦虑型乐观主义者', '消极但自愈', '受挫但坚持', '社交电池耗尽', '选择恐惧症晚期', 'FOMO综合症', '拖延症晚期', '信息过载焦虑', '周一恐惧症', '深夜emo专业户', '早睡失败专业户', '减肥失败但快乐', '存钱失败但开心', '学习失败但努力', '社交废物但可爱', '运动失败但健康', '计划失败但乐观', '熬夜冠军但精神', '拖延症但有爱', '迷糊但温暖'],
    食物关联: ['奶茶星人', '火锅爱好者', '烧烤达人', '小龙虾专家', '螺蛳粉忠粉', '咖啡成瘾者', '甜品控', '炸鸡爱好者', '麻辣烫专家', '珍珠奶茶党', '冰淇淋狂热者', '寿司达人', '泡面专家', '汉堡爱好者', '薯条控', '披萨党', '酸辣粉忠粉', '烤肉专家', '蛋糕控', '粥品爱好者', '热汤治愈师', '妈妈菜专家', '温暖粥品师', '甜品治愈师', '零食安慰师', '温牛奶守护者', '蜂蜜茶调配师', '暖胃面条师', '红糖水专家', '温暖饺子师', '热巧克力调配师', '养生汤品师', '温暖包子师', '治愈烘焙师', '温心小食师', '饿货', '吃货', '美食家', '减肥中', '夜宵党', '外卖达人', '厨房杀手', '下厨新手', '零食囤积者', '口味挑剔者', '健康饮食者', '暴食症候群', '食物摄影师', '深夜觅食者', '节食失败者']
  },
  actions: {
    日常行为: ['吃', '睡', '玩', '工作', '学习', '休息', '思考', '发呆', '散步', '购物', '聊天', '刷手机'],
    特殊动作: ['飞', '游泳', '追', '跑', '爬', '跳', '舞蹈', '唱歌', '画画', '写作', '演讲', '表演'],
    抽象动作: ['贩卖', '收集', '制造', '修理', '设计', '创造', '破坏', '保护', '传播', '分享', '探索', '发现'],
    网络行为: ['直播', '带货', '刷视频', '点赞', '转发', '评论', '关注', '充电', '打赏', '连麦', '开播', '下播'],
    现代生活: ['洗头', '化妆', '健身', '减肥', '加班', '熬夜', '摸鱼', '划水', '开会', '汇报', '出差', '居家']
  },
  modifiers: {
    权威级别: ['首席', '高级', '资深', '专业', '认证', '官方', '特级', '顶级', '大师级', '传奇', '史诗', '钻石'],
    空间范围: ['全球', '国际', '世界级', '宇宙级', '银河系', '太阳系', '跨国', '跨界', '跨次元', '本地', '区域'],
    程度强化: ['超级', '极度', '非常', '特别', '完全', '绝对', '史诗', '终极', '至尊', '满级', '深度', '极致'],
    时间频率: ['永远', '从不', '偶尔', '经常', '总是', '间歇性', '定期', '不定期', '随时', '24小时', '全天候'],
    状态描述: ['在线', '离线', '忙碌', '空闲', '活跃', '潜水', '冒泡', '隐身', '免打扰', '勿扰', '正常', '异常']
  },
  connectors: {
    对比转折: ['但', '却', '然而', '不过', '虽然', '尽管', '反而', '相反', '倒是', '偏偏', '恰恰', '竟然'],
    并列关系: ['和', '与', '及', '以及', '还有', '同时', '一边', '一面', '既', '又', '也', '亦'],
    递进强化: ['更', '还', '甚至', '竟然', '居然', '简直', '完全', '彻底', '深度', '极致', '进一步', '更加'],
    因果关系: ['因为', '所以', '由于', '导致', '造成', '引起', '产生', '带来', '形成', '促使', '致使', '使得']
  }
}

function countElements() {
  console.log('🔍 V5引擎语素库规模统计\n')
  
  let totalElements = 0
  const categoryStats = {}
  
  // 统计各大类别
  Object.keys(elementLibrary).forEach(category => {
    let categoryTotal = 0
    const subcategoryStats = {}
    
    Object.keys(elementLibrary[category]).forEach(subcategory => {
      const count = elementLibrary[category][subcategory].length
      subcategoryStats[subcategory] = count
      categoryTotal += count
      totalElements += count
    })
    
    categoryStats[category] = {
      total: categoryTotal,
      subcategories: subcategoryStats
    }
  })
  
  // 输出详细统计
  console.log('📊 详细统计结果:')
  console.log('=' .repeat(50))
  
  Object.keys(categoryStats).forEach(category => {
    const stats = categoryStats[category]
    console.log(`\n${category.toUpperCase()} (${stats.total}个元素):`)
    
    Object.keys(stats.subcategories).forEach(subcategory => {
      const count = stats.subcategories[subcategory]
      console.log(`  ${subcategory}: ${count}个`)
    })
  })
  
  console.log('\n' + '=' .repeat(50))
  console.log(`📈 总计: ${totalElements}个语素元素`)
  
  // 分析与文档描述的差异
  console.log('\n🔍 与文档描述对比:')
  if (totalElements === 343) {
    console.log('✅ 语素库规模与文档描述一致 (343个元素)')
  } else if (totalElements > 343) {
    console.log(`📈 语素库已扩展: ${totalElements}个元素 (比文档多${totalElements - 343}个)`)
    console.log('⚠️  需要更新文档中的"343个元素"描述')
  } else {
    console.log(`📉 语素库规模小于文档描述: ${totalElements}个元素 (比文档少${343 - totalElements}个)`)
  }
  
  // 检查是否接近3000个元素
  console.log('\n🎯 扩展目标检查:')
  if (totalElements >= 3000) {
    console.log('🎉 已达到3000个元素的扩展目标!')
  } else {
    console.log(`📊 当前${totalElements}个元素，距离3000个目标还需${3000 - totalElements}个`)
    console.log(`📈 完成度: ${(totalElements / 3000 * 100).toFixed(1)}%`)
  }
  
  return {
    totalElements,
    categoryStats,
    needsDocUpdate: totalElements !== 343
  }
}

// 执行统计
if (require.main === module) {
  const result = countElements()
  
  // 生成统计报告
  const report = {
    timestamp: new Date().toISOString(),
    totalElements: result.totalElements,
    categoryBreakdown: result.categoryStats,
    documentationStatus: result.needsDocUpdate ? 'NEEDS_UPDATE' : 'UP_TO_DATE',
    expansionProgress: {
      current: result.totalElements,
      target: 3000,
      percentage: (result.totalElements / 3000 * 100).toFixed(1)
    }
  }
  
  require('fs').writeFileSync('element-library-stats.json', JSON.stringify(report, null, 2))
  console.log('\n📄 详细统计报告已保存到: element-library-stats.json')
}

module.exports = { countElements, elementLibrary }
