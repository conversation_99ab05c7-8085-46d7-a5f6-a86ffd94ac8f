{"timestamp": "2025-06-19T12:42:30.974Z", "integration_summary": {"total_integrated": 213, "categories_updated": 4}, "classified_morphemes": {"subjects_extensions": {"现代职业扩展": ["医师", "工程师", "律师", "会计师", "建筑师", "艺术家", "音乐家", "作家", "厨师", "消防员", "药师", "数据分析师", "用户体验师", "测试工程师", "运维工程师", "安全工程师", "心理咨询师", "营养师", "插画师", "动画师", "游戏设计师", "摄影师", "剪辑师", "AI训练师", "区块链工程师"], "创意职业": ["艺术家", "游戏设计师", "文案策划", "元宇宙设计师", "形象设计师"], "新兴职业": ["AI训练师", "区块链工程师", "云计算专家", "网络安全专家"], "服务职业": ["咨询顾问", "投资顾问", "理财顾问", "心理咨询师"], "文人雅士": ["词圣", "词人", "诗人", "文人", "如诗", "风雅", "典雅", "高雅", "文雅", "儒雅", "古雅", "尔雅", "雅人", "贤士", "名士", "逸士", "隐士", "居士", "奇士", "童生", "监生"], "古典意象": ["秋月", "夏雨", "冬雪", "梅兰", "竹菊", "烟雨", "花茶", "风雅", "兰香", "日月", "岁月", "菊花", "杏花", "梨花", "樱花"]}, "traits_extensions": {"深层情感": ["深爱", "真诚", "真心", "纯真", "专注", "专心"], "文艺情感": ["诗韵", "诗心", "雅致", "雅意", "优雅", "清雅", "小清新"], "现代情感": ["佛系", "元气"], "性格特征": ["开朗", "稳重", "幽默", "机智"], "品质特征": ["诚信", "可靠"], "传统美德": ["智者", "仁心", "仁者", "义气", "义人", "大义", "礼数", "有礼", "知礼", "智识", "明智", "信用", "守信", "忠心", "忠正", "孝顺", "孝心", "行孝", "尽孝"], "文化概念": ["墨客", "琴棋", "书画", "花茶", "瑶琴", "茶香", "笔墨", "翰墨", "琴师", "棋师", "画师", "茶师"]}, "modifiers_extensions": {"能力特征": ["精通", "创新", "高效"], "程度描述": ["高效", "独特"], "现代修饰": ["萌萌", "呆萌", "软萌", "奶萌", "甜萌", "酷萌", "帅萌", "超酷", "很酷", "贼酷", "死酷", "时尚范"], "网络流行": ["靠谱", "厉害"]}, "new_categories": {"massiveEmotions": ["细润", "浓郁", "柔美", "静雅", "静美", "静心", "祥和"], "massiveProfessions": ["内容策划师", "社群运营师", "社群专家", "品牌策划师", "品牌专家", "市场分析师", "市场专家", "数据专家", "人工智能专家", "区块链专家", "云架构师", "安全顾问", "全栈工程师", "产品设计师", "交互设计师", "体验设计师", "游戏美术师", "音频工程师", "特效师", "直播策划师", "电商运营师", "电商专家", "新媒体分析师", "课程设计师", "学习顾问", "职业规划师", "健康管理师", "宠物医生", "烘焙师", "收纳师", "会展策划师", "庆典策划师", "派对策划师", "旅游策划师", "旅游顾问", "领队", "民宿管家", "酒店管家", "管家服务师", "礼仪师", "自媒体人"], "massiveCharacteristics": ["卓越", "专业化", "资深级", "顶尖", "顶呱呱", "一流", "一流水准", "一等一", "完美无缺", "理想", "理想化", "创新型", "独特性", "原创性", "新颖性", "潮流范", "前卫性", "先进性", "领先性", "高能性"]}, "expanded_extensions": {}}, "backup_files": {"elementLibrary": "/home/<USER>/develop/workspace/namer/config/element-library-config.ts.backup", "expandedLibrary": "/home/<USER>/develop/workspace/namer/config/expanded-element-library-config.ts.backup"}}