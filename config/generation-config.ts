/**
 * 真实语素生成系统 - 统一配置管理
 * 消除硬编码，实现配置化管理
 */

// 生成风格配置
export const GENERATION_STYLES = {
  modern: {
    id: 'modern',
    label: '现代',
    description: '时尚前卫的表达方式',
    weight: 1.0,
    patterns: ['temporal_displacement', 'identity_elevation', 'tech_expression', 'emotion_state']
  },
  classic: {
    id: 'classic',
    label: '经典',
    description: '传统优雅的命名风格',
    weight: 0.9,
    patterns: ['temporal_displacement', 'service_personification', 'contradiction_unity']
  },
  creative: {
    id: 'creative',
    label: '创意',
    description: '富有想象力的组合',
    weight: 1.1,
    patterns: ['service_personification', 'contradiction_unity', 'tech_expression']
  },
  professional: {
    id: 'professional',
    label: '专业',
    description: '正式商务的表达',
    weight: 0.8,
    patterns: ['identity_elevation', 'service_personification']
  },
  casual: {
    id: 'casual',
    label: '随性',
    description: '轻松自然的风格',
    weight: 1.0,
    patterns: ['food_association', 'emotion_state', 'contradiction_unity']
  },
  artistic: {
    id: 'artistic',
    label: '文艺',
    description: '富有艺术气息',
    weight: 0.9,
    patterns: ['temporal_displacement', 'service_personification', 'emotion_state']
  }
} as const

// 主题标签配置
export const THEME_TAGS = {
  tech: {
    id: '科技',
    label: '科技',
    icon: '💻',
    weight: 1.0,
    patterns: ['tech_expression', 'temporal_displacement'],
    description: '科技感十足的表达'
  },
  work: {
    id: '职场',
    label: '职场',
    icon: '💼',
    weight: 1.0,
    patterns: ['identity_elevation', 'contradiction_unity'],
    description: '职场相关的表达'
  },
  humor: {
    id: '幽默',
    label: '幽默',
    icon: '😄',
    weight: 1.2,
    patterns: ['contradiction_unity', 'food_association'],
    description: '幽默风趣的表达'
  },
  creative: {
    id: '创意',
    label: '创意',
    icon: '🎨',
    weight: 1.1,
    patterns: ['service_personification', 'temporal_displacement'],
    description: '富有创意的表达'
  },
  culture: {
    id: '文化',
    label: '文化',
    icon: '📚',
    weight: 0.9,
    patterns: ['temporal_displacement', 'service_personification'],
    description: '文化内涵丰富的表达'
  },
  emotion: {
    id: '情感',
    label: '情感',
    icon: '💭',
    weight: 1.0,
    patterns: ['emotion_state', 'contradiction_unity'],
    description: '情感表达类型'
  },
  food: {
    id: '美食',
    label: '美食',
    icon: '🍜',
    weight: 1.0,
    patterns: ['food_association', 'service_personification'],
    description: '美食相关的表达'
  },
  life: {
    id: '生活',
    label: '生活',
    icon: '🌟',
    weight: 1.0,
    patterns: ['emotion_state', 'food_association', 'contradiction_unity'],
    description: '生活化的表达'
  }
} as const

// 生成模式配置
export const GENERATION_PATTERNS = {
  identity_elevation: {
    id: 'identity_elevation',
    name: '身份升维包装',
    description: '将日常行为包装为权威职位',
    weight: 0.96,
    type: 'elevation',
    complexity_range: [1, 5],
    formula: '[权威修饰] + [日常行为] + [职位后缀]',
    example: '资深摸鱼专家'
  },
  contradiction_unity: {
    id: 'contradiction_unity',
    name: '矛盾统一',
    description: '将对立特质巧妙融合',
    weight: 0.94,
    type: 'contradiction',
    complexity_range: [2, 5],
    formula: '[正面特质] + [转折连词] + [负面特质]',
    example: '温柔却强硬'
  },
  temporal_displacement: {
    id: 'temporal_displacement',
    name: '时空错位重组',
    description: '将不同时空的元素创意组合',
    weight: 0.95,
    type: 'misplacement',
    complexity_range: [2, 4],
    formula: '[古代元素] + [现代行为/物品]',
    example: '贫僧直播'
  },
  service_personification: {
    id: 'service_personification',
    name: '服务拟人化',
    description: '将抽象概念具象化为服务角色',
    weight: 0.92,
    type: 'personification',
    complexity_range: [1, 4],
    formula: '[抽象概念] + [服务角色]',
    example: '快乐邮递员'
  },
  tech_expression: {
    id: 'tech_expression',
    name: '技术化表达',
    description: '用技术术语表达生活状态',
    weight: 0.91,
    type: 'tech',
    complexity_range: [2, 5],
    formula: '[生活概念] + [技术术语]',
    example: '人生404未找到'
  },
  emotion_state: {
    id: 'emotion_state',
    name: '情绪状态模式',
    description: '表达复杂的情感状态',
    weight: 0.88,
    type: 'emotion',
    complexity_range: [1, 5],
    formula: '[情绪状态词汇] + [身份后缀]',
    example: '间歇性努力专家'
  },
  food_association: {
    id: 'food_association',
    name: '食物关联模式',
    description: '与美食文化相关的表达',
    weight: 0.85,
    type: 'food',
    complexity_range: [1, 3],
    formula: '[食物关联词汇] + [身份后缀]',
    example: '奶茶星人'
  }
} as const

// 质量评估权重配置
export const QUALITY_ASSESSMENT_WEIGHTS = {
  novelty: 0.3,        // 新颖性权重
  relevance: 0.25,     // 相关性权重
  comprehensibility: 0.25,  // 可理解性权重
  memorability: 0.2    // 记忆性权重
} as const

// 质量评估基础分数配置
export const QUALITY_BASE_SCORES = {
  novelty: {
    base: 0.8,
    misplacement_bonus: 0.15,
    contradiction_bonus: 0.12
  },
  relevance: {
    base: 0.75,
    elevation_bonus: 0.15,
    tech_bonus: 0.12,
    personification_bonus: 0.1
  },
  comprehensibility: {
    base: 0.7,
    short_name_bonus: 0.15,  // <=6字符
    very_short_bonus: 0.1,   // <=4字符
    announcement_bonus: 0.1
  },
  memorability: {
    base: 0.65,
    contradiction_bonus: 0.15,
    absurd_bonus: 0.12
  }
} as const

// 生成参数限制配置
export const GENERATION_LIMITS = {
  count: {
    min: 1,
    max: 3,
    default: 1
  },
  complexity: {
    min: 1,
    max: 5,
    default: 3
  },
  username_length: {
    min: 2,
    max: 12,
    optimal: 6
  }
} as const

// 缓存配置
export const CACHE_CONFIG = {
  generation_history: {
    max_size: 100,
    ttl_ms: 10 * 60 * 1000  // 10分钟
  },
  element_usage: {
    max_size: 1000,
    reset_interval_ms: 24 * 60 * 60 * 1000  // 24小时
  },
  quality_cache: {
    max_size: 500,
    ttl_ms: 60 * 60 * 1000  // 1小时
  }
} as const

// 语言配置
export const LANGUAGE_CONFIG = {
  supported: ['zh'] as const,
  default: 'zh' as const,
  fallback: 'zh' as const
} as const

// 导出类型定义
export type GenerationStyle = keyof typeof GENERATION_STYLES
export type ThemeTag = keyof typeof THEME_TAGS
export type GenerationPattern = keyof typeof GENERATION_PATTERNS
export type SupportedLanguage = typeof LANGUAGE_CONFIG.supported[number]

// 工具函数
export function getStyleConfig(style: string) {
  return GENERATION_STYLES[style as GenerationStyle] || GENERATION_STYLES.modern
}

export function getThemeConfig(theme: string) {
  return THEME_TAGS[Object.keys(THEME_TAGS).find(key => 
    THEME_TAGS[key as ThemeTag].id === theme
  ) as ThemeTag]
}

export function getPatternConfig(pattern: string) {
  return GENERATION_PATTERNS[pattern as GenerationPattern]
}

export function getAllStyleOptions() {
  return Object.values(GENERATION_STYLES)
}

export function getAllThemeOptions() {
  return Object.values(THEME_TAGS)
}

export function getAllPatternOptions() {
  return Object.values(GENERATION_PATTERNS)
}

export function validateGenerationParams(params: {
  style?: string
  themes?: string[]
  complexity?: number
  count?: number
  pattern?: string
}) {
  const errors: string[] = []
  
  if (params.style && !GENERATION_STYLES[params.style as GenerationStyle]) {
    errors.push(`Invalid style: ${params.style}`)
  }
  
  if (params.themes) {
    const validThemeIds: string[] = Object.values(THEME_TAGS).map(t => t.id)
    const invalidThemes = params.themes.filter((t: string) => !validThemeIds.includes(t))
    if (invalidThemes.length > 0) {
      errors.push(`Invalid themes: ${invalidThemes.join(', ')}`)
    }
  }
  
  if (params.complexity !== undefined) {
    if (params.complexity < GENERATION_LIMITS.complexity.min || 
        params.complexity > GENERATION_LIMITS.complexity.max) {
      errors.push(`Complexity must be between ${GENERATION_LIMITS.complexity.min} and ${GENERATION_LIMITS.complexity.max}`)
    }
  }
  
  if (params.count !== undefined) {
    if (params.count < GENERATION_LIMITS.count.min || 
        params.count > GENERATION_LIMITS.count.max) {
      errors.push(`Count must be between ${GENERATION_LIMITS.count.min} and ${GENERATION_LIMITS.count.max}`)
    }
  }
  
  if (params.pattern && !GENERATION_PATTERNS[params.pattern as GenerationPattern]) {
    errors.push(`Invalid pattern: ${params.pattern}`)
  }
  
  return {
    valid: errors.length === 0,
    errors
  }
}
