/**
 * V5引擎扩展语素库配置
 * 基于词汇扩展引擎集成的丰富词汇
 * 
 * @generated 自动生成于 2025-06-19T08:07:07.349Z
 * @source vocabulary-expansion-engine
 */

// ============ 扩展主体词汇配置 ============
export const EXPANDED_SUBJECTS_CONFIG = {
  // 原有配置保持不变...
  
  // 新增职业类别
  传统职业扩展: {
    category: '传统职业扩展',
    description: '传统行业的专业职业',
    elements: [
        "医师",
        "教师",
        "工程师",
        "律师",
        "会计师",
        "建筑师",
        "艺术家",
        "音乐家",
        "作家",
        "记者",
        "编辑",
        "翻译"
    ],
    usage_patterns: ['identity_elevation', 'temporal_displacement'],
    cultural_weight: 1.2
  },
  现代职业扩展: {
    category: '现代职业扩展',
    description: '现代社会的专业职业',
    elements: [
        "产品经理",
        "项目经理",
        "运营经理",
        "数据分析师",
        "用户体验师",
        "前端工程师",
        "后端工程师",
        "算法工程师",
        "测试工程师"
    ],
    usage_patterns: ['identity_elevation', 'temporal_displacement'],
    cultural_weight: 1
  },
  创意职业: {
    category: '创意职业',
    description: '创意产业的职业角色',
    elements: [
        "插画师",
        "动画师",
        "游戏设计师",
        "影视制作人",
        "摄影师",
        "文案策划",
        "创意总监",
        "内容创作者",
        "独立开发者",
        "自由职业者"
    ],
    usage_patterns: ['identity_elevation', 'temporal_displacement'],
    cultural_weight: 1.1
  },
  新兴职业: {
    category: '新兴职业',
    description: '新兴行业的职业身份',
    elements: [
        "AI训练师",
        "数据科学家",
        "区块链工程师",
        "云计算专家",
        "用户增长专家",
        "社群运营",
        "元宇宙设计师",
        "数字艺术家"
    ],
    usage_patterns: ['identity_elevation', 'temporal_displacement'],
    cultural_weight: 1
  },
  文人雅士: {
    category: '文人雅士',
    description: '传统文化中的文人学者',
    elements: [
        "诗仙",
        "词圣",
        "诗人",
        "文人",
        "雅士",
        "才子",
        "佳人",
        "墨客",
        "骚人",
        "书生"
    ],
    usage_patterns: ['temporal_displacement', 'service_personification'],
    cultural_weight: 1.2
  },
} as const

// ============ 扩展特质词汇配置 ============
export const EXPANDED_TRAITS_CONFIG = {
  // 原有配置保持不变...
  
  // 新增情感特质
  基础情感: {
    category: '基础情感',
    description: '基础的情感表达词汇',
    elements: [
        "温暖",
        "温柔",
        "温馨",
        "温和",
        "温情",
        "热情",
        "热忱",
        "热心",
        "热烈",
        "热诚",
        "冷静",
        "冷淡",
        "平静",
        "平和",
        "平稳"
    ],
    usage_patterns: ['contradiction_unity', 'emotion_state'],
    cultural_weight: 1,
    polarity: 'neutral'
  },
  积极情感: {
    category: '积极情感',
    description: '积极正面的情感词汇',
    elements: [
        "欢乐",
        "欢喜",
        "欢快",
        "愉快",
        "愉悦",
        "快乐",
        "快意",
        "喜悦",
        "喜乐",
        "兴奋",
        "兴致",
        "兴趣"
    ],
    usage_patterns: ['contradiction_unity', 'emotion_state'],
    cultural_weight: 1,
    polarity: 'positive'
  },
  深层情感: {
    category: '深层情感',
    description: '深层次的情感体验',
    elements: [
        "深情",
        "深爱",
        "深切",
        "深沉",
        "真诚",
        "真挚",
        "真心",
        "纯真",
        "纯洁",
        "纯净",
        "专注",
        "专一"
    ],
    usage_patterns: ['contradiction_unity', 'emotion_state'],
    cultural_weight: 1,
    polarity: 'neutral'
  },
  文艺情感: {
    category: '文艺情感',
    description: '具有文艺气息的情感',
    elements: [
        "诗意",
        "诗情",
        "雅致",
        "雅韵",
        "优雅",
        "优美",
        "清雅",
        "清新",
        "清纯",
        "清澈"
    ],
    usage_patterns: ['contradiction_unity', 'emotion_state'],
    cultural_weight: 1,
    polarity: 'positive'
  },
  现代情感: {
    category: '现代情感',
    description: '现代社会的情感表达',
    elements: [
        "治愈",
        "暖心",
        "贴心",
        "用心",
        "走心",
        "佛系",
        "淡然",
        "随性",
        "自在",
        "洒脱",
        "元气",
        "活力",
        "朝气"
    ],
    usage_patterns: ['contradiction_unity', 'emotion_state'],
    cultural_weight: 1,
    polarity: 'positive'
  },
  传统概念: {
    category: '传统概念',
    description: '传统文化概念词汇',
    elements: [
        "书香",
        "墨香",
        "茶香",
        "文房",
        "四宝",
        "笔墨",
        "丹青",
        "琴棋",
        "书画",
        "诗酒"
    ],
    usage_patterns: ['contradiction_unity'],
    cultural_weight: 1.2,
    polarity: 'positive'
  },
  传统美德: {
    category: '传统美德',
    description: '传统道德品德词汇',
    elements: [
        "仁爱",
        "仁慈",
        "义气",
        "义理",
        "礼貌",
        "礼仪",
        "智慧",
        "信义",
        "忠诚",
        "孝顺"
    ],
    usage_patterns: ['contradiction_unity'],
    cultural_weight: 1.2,
    polarity: 'positive'
  },
  日常生活: {
    category: '日常生活',
    description: '日常生活中的流行表达',
    elements: [
        "温馨",
        "舒适",
        "惬意",
        "悠闲",
        "轻松",
        "自在",
        "简单",
        "简约",
        "自然",
        "天然"
    ],
    usage_patterns: ['emotion_state', 'contradiction_unity'],
    cultural_weight: 1.1,
    polarity: 'positive'
  },
  网络流行: {
    category: '网络流行',
    description: '网络流行文化词汇',
    elements: [
        "给力",
        "靠谱",
        "厉害",
        "霸气",
        "萌萌",
        "可爱",
        "甜美",
        "酷炫",
        "治愈系",
        "元气满满"
    ],
    usage_patterns: ['emotion_state', 'contradiction_unity'],
    cultural_weight: 1.1,
    polarity: 'positive'
  },
  二次元文化: {
    category: '二次元文化',
    description: '二次元动漫文化词汇',
    elements: [
        "二次元",
        "萌系",
        "宅男",
        "宅女",
        "中二",
        "颜值",
        "傲娇",
        "腹黑",
        "治愈系",
        "元气系"
    ],
    usage_patterns: ['emotion_state', 'tech_expression'],
    cultural_weight: 1.0,
    polarity: 'neutral'
  },
  网络亚文化: {
    category: '网络亚文化',
    description: '网络亚文化群体词汇',
    elements: [
        "破圈",
        "出圈",
        "内卷",
        "躺平",
        "摆烂",
        "佛系",
        "社畜",
        "打工人",
        "凡尔赛",
        "茶艺"
    ],
    usage_patterns: ['emotion_state', 'tech_expression'],
    cultural_weight: 1.0,
    polarity: 'neutral'
  },
  Z世代文化: {
    category: 'Z世代文化',
    description: 'Z世代文化特色词汇',
    elements: [
        "emo",
        "精神内耗",
        "社恐",
        "社牛",
        "社死",
        "yyds",
        "绝绝子",
        "芭比Q",
        "破防",
        "上头"
    ],
    usage_patterns: ['emotion_state', 'tech_expression'],
    cultural_weight: 1.0,
    polarity: 'neutral'
  },
} as const

// ============ 扩展修饰词配置 ============
export const EXPANDED_MODIFIERS_CONFIG = {
  // 原有配置保持不变...
  
  // 新增特征修饰词
  能力特征: {
    category: '能力特征',
    description: '能力和技能相关的特征',
    elements: [
        "专业",
        "精通",
        "熟练",
        "精湛",
        "创新",
        "创意",
        "高效",
        "迅速",
        "敏捷",
        "严谨",
        "精确",
        "全面"
    ],
    usage_patterns: ['identity_elevation', 'contradiction_unity'],
    authority_weight: 1
  },
  品质特征: {
    category: '品质特征',
    description: '品质和品格相关的特征',
    elements: [
        "诚信",
        "诚实",
        "真诚",
        "可靠",
        "稳定",
        "踏实",
        "负责",
        "认真",
        "积极",
        "主动",
        "坚持",
        "坚定"
    ],
    usage_patterns: ['identity_elevation', 'contradiction_unity'],
    authority_weight: 0.9
  },
  性格特征: {
    category: '性格特征',
    description: '个性和性格相关的特征',
    elements: [
        "开朗",
        "活泼",
        "外向",
        "内向",
        "稳重",
        "成熟",
        "幽默",
        "机智",
        "聪明",
        "智慧",
        "善良",
        "友善"
    ],
    usage_patterns: ['identity_elevation', 'contradiction_unity'],
    authority_weight: 0.8
  },
} as const

// ============ 统计信息 ============
export const EXPANDED_LIBRARY_STATS = {
  total_categories_added: 20,
  total_elements_added: 217,
  categories_breakdown: {
    subjects: 5,
    traits: 12,
    modifiers: 3
  }
}

// ============ 工具函数 ============
export function getAllExpandedElements() {
  return {
    ...EXPANDED_SUBJECTS_CONFIG,
    ...EXPANDED_TRAITS_CONFIG,
    ...EXPANDED_MODIFIERS_CONFIG
  }
}

export function getExpandedElementCount() {
  const allElements = getAllExpandedElements()
  return Object.values(allElements).reduce((total, config) => total + config.elements.length, 0)
}

// 新增语素类别 (词汇扩展引擎集成)
export const MASSIVEEMOTIONS_CONFIG = {
  massiveEmotions: ['细润', '浓郁', '柔美', '静雅', '静美', '静心', '祥和']
}

export const MASSIVEPROFESSIONS_CONFIG = {
  massiveProfessions: ['内容策划师', '社群运营师', '社群专家', '品牌策划师', '品牌专家', '市场分析师', '市场专家', '数据专家', '人工智能专家', '区块链专家', '云架构师', '安全顾问', '全栈工程师', '产品设计师', '交互设计师', '体验设计师', '游戏美术师', '音频工程师', '特效师', '直播策划师', '电商运营师', '电商专家', '新媒体分析师', '课程设计师', '学习顾问', '职业规划师', '健康管理师', '宠物医生', '烘焙师', '收纳师', '会展策划师', '庆典策划师', '派对策划师', '旅游策划师', '旅游顾问', '领队', '民宿管家', '酒店管家', '管家服务师', '礼仪师', '自媒体人']
}

export const MASSIVECHARACTERISTICS_CONFIG = {
  massiveCharacteristics: ['卓越', '专业化', '资深级', '顶尖', '顶呱呱', '一流', '一流水准', '一等一', '完美无缺', '理想', '理想化', '创新型', '独特性', '原创性', '新颖性', '潮流范', '前卫性', '先进性', '领先性', '高能性']
}


// 词汇扩展引擎完全集成 (2025-06-19T13:02:25.893Z)
// 总集成语素: 1389个
// 分布: subjects(224), traits(1100), modifiers(65)

export const VOCABULARY_EXPANSION_INTEGRATION_INFO = {
  timestamp: '2025-06-19T13:02:25.893Z',
  totalIntegrated: 1389,
  distribution: {
    subjects: 224,
    traits: 1100,
    modifiers: 65
  }
}
