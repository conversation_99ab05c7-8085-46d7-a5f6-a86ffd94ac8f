/**
 * 语素库配置文件
 * 统一管理所有语素元素，消除硬编码
 */

// 主体词汇配置
export const SUBJECTS_CONFIG = {
  古代人物: {
    category: '古代人物',
    description: '传统文化中的人物角色',
    elements: ['贫僧', '道士', '书生', '侠客', '状元', '举人', '秀才', '太守', '县令', '师爷'],
    usage_patterns: ['temporal_displacement', 'service_personification'],
    cultural_weight: 1.0
  },
  现代职业: {
    category: '现代职业',
    description: '现代社会的职业角色',
    elements: ['程序员', '设计师', '产品经理', '运营', '策划', '文案', '测试', '架构师', '前端', '后端'],
    usage_patterns: ['identity_elevation', 'tech_expression'],
    cultural_weight: 0.8
  },
  网络身份: {
    category: '网络身份',
    description: '网络时代的身份标识',
    elements: ['UP主', '主播', '网红', '博主', '自媒体', '数字游民', '斜杠青年', 'KOL', '网络达人', '内容创作者'],
    usage_patterns: ['temporal_displacement', 'identity_elevation'],
    cultural_weight: 0.9
  },
  动物世界: {
    category: '动物世界',
    description: '动物相关的元素',
    elements: ['猫', '狗', '猪', '鸟', '鱼', '蜗牛', '大象', '蚂蚁', '老鼠', '兔子', '熊猫', '企鹅', '海豚', '狮子'],
    usage_patterns: ['service_personification'],  // 目前未使用，待扩展
    cultural_weight: 0.7
  },
  天体宇宙: {
    category: '天体宇宙',
    description: '天体和宇宙相关元素',
    elements: ['月亮', '星星', '太阳', '云朵', '彩虹', '闪电', '银河', '宇宙', '星球', '流星', '黑洞', '星云'],
    usage_patterns: ['service_personification', 'tech_expression'],
    cultural_weight: 0.8
  },
  抽象概念: {
    category: '抽象概念',
    description: '抽象的概念和情感',
    elements: ['快乐', '悲伤', '愤怒', '温柔', '智慧', '勇敢', '梦想', '希望', '爱情', '友情', '孤独', '自由'],
    usage_patterns: ['service_personification', 'tech_expression', 'contradiction_unity'],
    cultural_weight: 1.0
  },
  食物美食: {
    category: '食物美食',
    description: '食物和美食相关元素',
    elements: ['芝士', '咖啡', '茶', '巧克力', '蛋糕', '面包', '牛奶', '果汁', '冰淇淋', '薯片', '火锅', '烧烤'],
    usage_patterns: ['food_association', 'service_personification'],
    cultural_weight: 0.9
  },
  技术概念: {
    category: '技术概念',
    description: '技术和科技相关概念',
    elements: ['WiFi', '404', '500', '数据库', '服务器', '算法', 'AI', '云端', '区块链', '元宇宙', 'VR', 'AR'],
    usage_patterns: ['tech_expression'],  // 目前未充分使用
    cultural_weight: 0.8
  },
  情绪状态: {
    category: '情绪状态',
    description: '复杂的情绪和心理状态',
    elements: [
      '间歇性努力', '积极废人', '外向孤独症', '理性但感性', '想独立又想被照顾', 
      '社交恐惧但渴望理解', '想要自由又怕孤独', '低调张扬', '懒惰但有梦想', 
      '成熟幼稚', '高敏感低社交', '理想主义现实派', '温和愤青', '精神内耗专业户', 
      '低落但坚韧', '焦虑型乐观主义者', '消极但自愈', '受挫但坚持', '社交电池耗尽', 
      '选择恐惧症晚期', 'FOMO综合症', '拖延症晚期', '信息过载焦虑', '周一恐惧症', 
      '深夜emo专业户', '早睡失败专业户', '减肥失败但快乐', '存钱失败但开心', 
      '学习失败但努力', '社交废物但可爱', '运动失败但健康', '计划失败但乐观', 
      '熬夜冠军但精神', '拖延症但有爱', '迷糊但温暖'
    ],
    usage_patterns: ['emotion_state'],
    cultural_weight: 1.2
  },
  食物关联: {
    category: '食物关联',
    description: '与食物相关的身份和爱好',
    elements: [
      '奶茶星人', '火锅爱好者', '烧烤达人', '小龙虾专家', '螺蛳粉忠粉', '咖啡成瘾者', 
      '甜品控', '炸鸡爱好者', '麻辣烫专家', '珍珠奶茶党', '冰淇淋狂热者', '寿司达人', 
      '泡面专家', '汉堡爱好者', '薯条控', '披萨党', '酸辣粉忠粉', '烤肉专家', 
      '蛋糕控', '粥品爱好者', '热汤治愈师', '妈妈菜专家', '温暖粥品师', '甜品治愈师', 
      '零食安慰师', '温牛奶守护者', '蜂蜜茶调配师', '暖胃面条师', '红糖水专家', 
      '温暖饺子师', '热巧克力调配师', '养生汤品师', '温暖包子师', '治愈烘焙师', 
      '温心小食师', '饿货', '吃货', '美食家', '减肥中', '夜宵党', '外卖达人', 
      '厨房杀手', '下厨新手', '零食囤积者', '口味挑剔者', '健康饮食者', '暴食症候群', 
      '食物摄影师', '深夜觅食者', '节食失败者'
    ],
    usage_patterns: ['food_association'],
    cultural_weight: 1.1
  },
  现代职业扩展: ['医师', '工程师', '律师', '会计师', '建筑师', '艺术家', '音乐家', '作家', '厨师', '消防员', '药师', '数据分析师', '用户体验师', '测试工程师', '运维工程师', '安全工程师', '心理咨询师', '营养师', '插画师', '动画师', '游戏设计师', '摄影师', '剪辑师', 'AI训练师', '区块链工程师'],
,
  创意职业: ['艺术家', '游戏设计师', '文案策划', '元宇宙设计师', '形象设计师'],
,
  新兴职业: ['AI训练师', '区块链工程师', '云计算专家', '网络安全专家'],
,
  服务职业: ['咨询顾问', '投资顾问', '理财顾问', '心理咨询师'],
,
  文人雅士: ['词圣', '词人', '诗人', '文人', '如诗', '风雅', '典雅', '高雅', '文雅', '儒雅', '古雅', '尔雅', '雅人', '贤士', '名士', '逸士', '隐士', '居士', '奇士', '童生', '监生'],
,
  古典意象: ['秋月', '夏雨', '冬雪', '梅兰', '竹菊', '烟雨', '花茶', '风雅', '兰香', '日月', '岁月', '菊花', '杏花', '梨花', '樱花'],

} as const

// 动作词汇配置
export const ACTIONS_CONFIG = {
  日常行为: {
    category: '日常行为',
    description: '日常生活中的基本行为',
    elements: ['吃', '睡', '玩', '工作', '学习', '休息', '思考', '发呆', '散步', '购物', '聊天', '刷手机'],
    usage_patterns: ['identity_elevation', 'temporal_displacement'],
    frequency_weight: 1.0
  },
  特殊动作: {
    category: '特殊动作',
    description: '特殊或技能性的动作',
    elements: ['飞', '游泳', '追', '跑', '爬', '跳', '舞蹈', '唱歌', '画画', '写作', '演讲', '表演'],
    usage_patterns: ['identity_elevation', 'service_personification'],
    frequency_weight: 0.8
  },
  抽象动作: {
    category: '抽象动作',
    description: '抽象或概念性的动作',
    elements: ['贩卖', '收集', '制造', '修理', '设计', '创造', '破坏', '保护', '传播', '分享', '探索', '发现'],
    usage_patterns: ['service_personification', 'identity_elevation'],
    frequency_weight: 0.9
  },
  网络行为: {
    category: '网络行为',
    description: '网络和数字化时代的行为',
    elements: ['直播', '带货', '刷视频', '点赞', '转发', '评论', '关注', '充电', '打赏', '连麦', '开播', '下播'],
    usage_patterns: ['temporal_displacement', 'identity_elevation'],
    frequency_weight: 1.1
  },
  现代生活: {
    category: '现代生活',
    description: '现代生活方式相关的行为',
    elements: ['洗头', '化妆', '健身', '减肥', '加班', '熬夜', '摸鱼', '划水', '开会', '汇报', '出差', '居家'],
    usage_patterns: ['temporal_displacement', 'identity_elevation'],
    frequency_weight: 1.2
  }
} as const

// 修饰词汇配置
export const MODIFIERS_CONFIG = {
  权威级别: {
    category: '权威级别',
    description: '表示权威性和专业程度的修饰词',
    elements: ['首席', '高级', '资深', '专业', '认证', '官方', '特级', '顶级', '大师级', '传奇', '史诗', '钻石'],
    usage_patterns: ['identity_elevation'],
    authority_weight: 1.0
  },
  空间范围: {
    category: '空间范围',
    description: '表示空间范围和影响力的修饰词',
    elements: ['全球', '国际', '世界级', '宇宙级', '银河系', '太阳系', '跨国', '跨界', '跨次元', '本地', '区域'],
    usage_patterns: ['identity_elevation', 'service_personification'],
    authority_weight: 0.9
  },
  程度强化: {
    category: '程度强化',
    description: '表示程度和强度的修饰词',
    elements: ['超级', '极度', '非常', '特别', '完全', '绝对', '史诗', '终极', '至尊', '满级', '深度', '极致'],
    usage_patterns: ['identity_elevation', 'contradiction_unity'],
    authority_weight: 0.8
  },
  时间频率: {
    category: '时间频率',
    description: '表示时间频率和持续性的修饰词',
    elements: ['永远', '从不', '偶尔', '经常', '总是', '间歇性', '定期', '不定期', '随时', '24小时', '全天候'],
    usage_patterns: ['identity_elevation', 'emotion_state'],
    authority_weight: 0.7
  },
  状态描述: {
    category: '状态描述',
    description: '表示状态和情况的修饰词',
    elements: ['在线', '离线', '忙碌', '空闲', '活跃', '潜水', '冒泡', '隐身', '免打扰', '勿扰', '正常', '异常'],
    usage_patterns: ['tech_expression', 'emotion_state'],
    authority_weight: 0.6
  },
  能力特征: ['精通', '创新', '高效'],
,
  程度描述: ['高效', '独特'],
,
  现代修饰: ['萌萌', '呆萌', '软萌', '奶萌', '甜萌', '酷萌', '帅萌', '超酷', '很酷', '贼酷', '死酷', '时尚范'],
,
  网络流行: ['靠谱', '厉害'],

} as const

// 连接词汇配置
export const CONNECTORS_CONFIG = {
  对比转折: {
    category: '对比转折',
    description: '表示对比和转折关系的连接词',
    elements: ['但', '却', '然而', '不过', '虽然', '尽管', '反而', '相反', '倒是', '偏偏', '恰恰', '竟然'],
    usage_patterns: ['contradiction_unity'],
    emotional_weight: 1.0
  },
  并列关系: {
    category: '并列关系',
    description: '表示并列和同等关系的连接词',
    elements: ['和', '与', '及', '以及', '还有', '同时', '一边', '一面', '既', '又', '也', '亦'],
    usage_patterns: ['contradiction_unity', 'service_personification'],
    emotional_weight: 0.8
  },
  递进强化: {
    category: '递进强化',
    description: '表示递进和强化关系的连接词',
    elements: ['更', '还', '甚至', '竟然', '居然', '简直', '完全', '彻底', '深度', '极致', '进一步', '更加'],
    usage_patterns: ['contradiction_unity', 'emotion_state'],
    emotional_weight: 1.1
  },
  因果关系: {
    category: '因果关系',
    description: '表示因果关系的连接词',
    elements: ['因为', '所以', '由于', '导致', '造成', '引起', '产生', '带来', '形成', '促使', '致使', '使得'],
    usage_patterns: ['tech_expression', 'service_personification'],
    emotional_weight: 0.9
  }
} as const

// 固定后缀配置
export const SUFFIXES_CONFIG = {
  职位后缀: {
    category: '职位后缀',
    description: '表示职位和身份的后缀',
    elements: ['官', '师', '专家', '大使', '代表', '委员', '顾问', '总监', '主任', '经理'],
    usage_patterns: ['identity_elevation'],
    formality_weight: 1.0
  },
  服务角色: {
    category: '服务角色',
    description: '表示服务类型的角色后缀',
    elements: ['邮递员', '收集员', '配送员', '制造商', '贩卖机', '专卖店', '客服', '导航员', '修理工', '设计师'],
    usage_patterns: ['service_personification'],
    formality_weight: 0.8
  },
  身份后缀: {
    category: '身份后缀',
    description: '表示身份和特征的后缀',
    elements: ['专家', '代表', '选手', '患者', '星人', '爱好者', '达人', '党', '控', '货'],
    usage_patterns: ['emotion_state', 'food_association'],
    formality_weight: 0.6
  },
  技术术语: {
    category: '技术术语',
    description: '技术相关的术语后缀',
    elements: ['正在缓冲', '连接超时', '服务器宕机', '数据库损坏', '404未找到', '503不可用', '系统维护', '正在加载'],
    usage_patterns: ['tech_expression'],
    formality_weight: 0.7
  }
} as const

// 特质词汇配置
export const TRAITS_CONFIG = {
  正面特质: {
    category: '正面特质',
    description: '积极正面的性格特质',
    elements: ['温柔', '理性', '冷静', '勤奋', '节约', '听劝', '乐观', '自信', '坚强', '独立'],
    usage_patterns: ['contradiction_unity'],
    polarity: 'positive'
  },
  负面特质: {
    category: '负面特质',
    description: '消极负面的性格特质',
    elements: ['强硬', '感性', '冲动', '懒惰', '挥霍', '反骨', '悲观', '自卑', '脆弱', '依赖'],
    usage_patterns: ['contradiction_unity'],
    polarity: 'negative'
  },
  生活概念: {
    category: '生活概念',
    description: '生活相关的概念词汇',
    elements: ['人生', '梦想', '快乐', '悲伤', '爱情', '友情', '工作', '学习', '生活', '未来'],
    usage_patterns: ['tech_expression'],
    polarity: 'neutral'
  },
  深层情感: ['深爱', '真诚', '真心', '纯真', '专注', '专心'],
,
  文艺情感: ['诗韵', '诗心', '雅致', '雅意', '优雅', '清雅', '小清新'],
,
  现代情感: ['佛系', '元气'],
,
  性格特征: ['开朗', '稳重', '幽默', '机智'],
,
  品质特征: ['诚信', '可靠'],
,
  传统美德: ['智者', '仁心', '仁者', '义气', '义人', '大义', '礼数', '有礼', '知礼', '智识', '明智', '信用', '守信', '忠心', '忠正', '孝顺', '孝心', '行孝', '尽孝'],
,
  文化概念: ['墨客', '琴棋', '书画', '花茶', '瑶琴', '茶香', '笔墨', '翰墨', '琴师', '棋师', '画师', '茶师'],

} as const

// 统计信息
export const ELEMENT_LIBRARY_STATS = {
  subjects: Object.keys(SUBJECTS_CONFIG).length,
  actions: Object.keys(ACTIONS_CONFIG).length,
  modifiers: Object.keys(MODIFIERS_CONFIG).length,
  connectors: Object.keys(CONNECTORS_CONFIG).length,
  suffixes: Object.keys(SUFFIXES_CONFIG).length,
  traits: Object.keys(TRAITS_CONFIG).length,
  total_categories: 0,  // 将在运行时计算
  total_elements: 0     // 将在运行时计算
}

// 计算总数
ELEMENT_LIBRARY_STATS.total_categories = 
  ELEMENT_LIBRARY_STATS.subjects + 
  ELEMENT_LIBRARY_STATS.actions + 
  ELEMENT_LIBRARY_STATS.modifiers + 
  ELEMENT_LIBRARY_STATS.connectors + 
  ELEMENT_LIBRARY_STATS.suffixes + 
  ELEMENT_LIBRARY_STATS.traits

// 导入扩展配置
import {
  EXPANDED_SUBJECTS_CONFIG,
  EXPANDED_TRAITS_CONFIG,
  EXPANDED_MODIFIERS_CONFIG
} from './expanded-element-library-config'

// 工具函数
export function getAllElements() {
  const allElements: { [category: string]: string[] } = {}

  // 合并所有配置
  Object.values(SUBJECTS_CONFIG).forEach(config => {
    allElements[config.category] = config.elements
  })
  Object.values(ACTIONS_CONFIG).forEach(config => {
    allElements[config.category] = config.elements
  })
  Object.values(MODIFIERS_CONFIG).forEach(config => {
    allElements[config.category] = config.elements
  })
  Object.values(CONNECTORS_CONFIG).forEach(config => {
    allElements[config.category] = config.elements
  })
  Object.values(SUFFIXES_CONFIG).forEach(config => {
    allElements[config.category] = config.elements
  })
  Object.values(TRAITS_CONFIG).forEach(config => {
    allElements[config.category] = config.elements
  })

  return allElements
}

// 获取包含扩展语素的完整元素库
export function getAllElementsWithExpansion() {
  const allElements: { [category: string]: string[] } = {}

  // 合并原有配置
  Object.values(SUBJECTS_CONFIG).forEach(config => {
    allElements[config.category] = [...config.elements]
  })
  Object.values(ACTIONS_CONFIG).forEach(config => {
    allElements[config.category] = [...config.elements]
  })
  Object.values(MODIFIERS_CONFIG).forEach(config => {
    allElements[config.category] = [...config.elements]
  })
  Object.values(CONNECTORS_CONFIG).forEach(config => {
    allElements[config.category] = [...config.elements]
  })
  Object.values(SUFFIXES_CONFIG).forEach(config => {
    allElements[config.category] = [...config.elements]
  })
  Object.values(TRAITS_CONFIG).forEach(config => {
    allElements[config.category] = [...config.elements]
  })

  // 合并扩展配置
  Object.values(EXPANDED_SUBJECTS_CONFIG).forEach(config => {
    allElements[config.category] = [...config.elements]
  })
  Object.values(EXPANDED_TRAITS_CONFIG).forEach(config => {
    allElements[config.category] = [...config.elements]
  })
  Object.values(EXPANDED_MODIFIERS_CONFIG).forEach(config => {
    allElements[config.category] = [...config.elements]
  })

  return allElements
}

export function getElementsForPattern(patternId: string) {
  const elements: { [category: string]: string[] } = {}
  
  // 根据模式筛选相关元素
  const allConfigs = [
    ...Object.values(SUBJECTS_CONFIG),
    ...Object.values(ACTIONS_CONFIG),
    ...Object.values(MODIFIERS_CONFIG),
    ...Object.values(CONNECTORS_CONFIG),
    ...Object.values(SUFFIXES_CONFIG),
    ...Object.values(TRAITS_CONFIG)
  ]
  
  allConfigs.forEach(config => {
    if (config.usage_patterns.includes(patternId)) {
      elements[config.category] = config.elements
    }
  })
  
  return elements
}

export function getTotalElementCount() {
  const allElements = getAllElements()
  return Object.values(allElements).reduce((total, elements) => total + elements.length, 0)
}
