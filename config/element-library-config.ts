/**
 * 语素库配置文件
 * 统一管理所有语素元素，消除硬编码
 */

// 主体词汇配置
export const SUBJECTS_CONFIG = {
  古代人物: {
    category: '古代人物',
    description: '传统文化中的人物角色',
    elements: ['贫僧', '道士', '书生', '侠客', '状元', '举人', '秀才', '太守', '县令', '师爷'],
    usage_patterns: ['temporal_displacement', 'service_personification'],
    cultural_weight: 1.0
  },
  现代职业: {
    category: '现代职业',
    description: '现代社会的职业角色',
    elements: ['程序员', '设计师', '产品经理', '运营', '策划', '文案', '测试', '架构师', '前端', '后端'],
    usage_patterns: ['identity_elevation', 'tech_expression'],
    cultural_weight: 0.8
  },
  网络身份: {
    category: '网络身份',
    description: '网络时代的身份标识',
    elements: ['UP主', '主播', '网红', '博主', '自媒体', '数字游民', '斜杠青年', 'KOL', '网络达人', '内容创作者'],
    usage_patterns: ['temporal_displacement', 'identity_elevation'],
    cultural_weight: 0.9
  },
  动物世界: {
    category: '动物世界',
    description: '动物相关的元素',
    elements: ['猫', '狗', '猪', '鸟', '鱼', '蜗牛', '大象', '蚂蚁', '老鼠', '兔子', '熊猫', '企鹅', '海豚', '狮子'],
    usage_patterns: ['service_personification'],  // 目前未使用，待扩展
    cultural_weight: 0.7
  },
  天体宇宙: {
    category: '天体宇宙',
    description: '天体和宇宙相关元素',
    elements: ['月亮', '星星', '太阳', '云朵', '彩虹', '闪电', '银河', '宇宙', '星球', '流星', '黑洞', '星云'],
    usage_patterns: ['service_personification', 'tech_expression'],
    cultural_weight: 0.8
  },
  抽象概念: {
    category: '抽象概念',
    description: '抽象的概念和情感',
    elements: ['快乐', '悲伤', '愤怒', '温柔', '智慧', '勇敢', '梦想', '希望', '爱情', '友情', '孤独', '自由'],
    usage_patterns: ['service_personification', 'tech_expression', 'contradiction_unity'],
    cultural_weight: 1.0
  },
  食物美食: {
    category: '食物美食',
    description: '食物和美食相关元素',
    elements: ['芝士', '咖啡', '茶', '巧克力', '蛋糕', '面包', '牛奶', '果汁', '冰淇淋', '薯片', '火锅', '烧烤'],
    usage_patterns: ['food_association', 'service_personification'],
    cultural_weight: 0.9
  },
  技术概念: {
    category: '技术概念',
    description: '技术和科技相关概念',
    elements: ['WiFi', '404', '500', '数据库', '服务器', '算法', 'AI', '云端', '区块链', '元宇宙', 'VR', 'AR'],
    usage_patterns: ['tech_expression'],  // 目前未充分使用
    cultural_weight: 0.8
  },
  情绪状态: {
    category: '情绪状态',
    description: '复杂的情绪和心理状态',
    elements: [
      '间歇性努力', '积极废人', '外向孤独症', '理性但感性', '想独立又想被照顾', 
      '社交恐惧但渴望理解', '想要自由又怕孤独', '低调张扬', '懒惰但有梦想', 
      '成熟幼稚', '高敏感低社交', '理想主义现实派', '温和愤青', '精神内耗专业户', 
      '低落但坚韧', '焦虑型乐观主义者', '消极但自愈', '受挫但坚持', '社交电池耗尽', 
      '选择恐惧症晚期', 'FOMO综合症', '拖延症晚期', '信息过载焦虑', '周一恐惧症', 
      '深夜emo专业户', '早睡失败专业户', '减肥失败但快乐', '存钱失败但开心', 
      '学习失败但努力', '社交废物但可爱', '运动失败但健康', '计划失败但乐观', 
      '熬夜冠军但精神', '拖延症但有爱', '迷糊但温暖'
    ],
    usage_patterns: ['emotion_state'],
    cultural_weight: 1.2
  },
  食物关联: {
    category: '食物关联',
    description: '与食物相关的身份和爱好',
    elements: [
      '奶茶星人', '火锅爱好者', '烧烤达人', '小龙虾专家', '螺蛳粉忠粉', '咖啡成瘾者', 
      '甜品控', '炸鸡爱好者', '麻辣烫专家', '珍珠奶茶党', '冰淇淋狂热者', '寿司达人', 
      '泡面专家', '汉堡爱好者', '薯条控', '披萨党', '酸辣粉忠粉', '烤肉专家', 
      '蛋糕控', '粥品爱好者', '热汤治愈师', '妈妈菜专家', '温暖粥品师', '甜品治愈师', 
      '零食安慰师', '温牛奶守护者', '蜂蜜茶调配师', '暖胃面条师', '红糖水专家', 
      '温暖饺子师', '热巧克力调配师', '养生汤品师', '温暖包子师', '治愈烘焙师', 
      '温心小食师', '饿货', '吃货', '美食家', '减肥中', '夜宵党', '外卖达人', 
      '厨房杀手', '下厨新手', '零食囤积者', '口味挑剔者', '健康饮食者', '暴食症候群', 
      '食物摄影师', '深夜觅食者', '节食失败者'
    ],
    usage_patterns: ['food_association'],
    cultural_weight: 1.1
  },
  现代职业扩展: ['医师', '工程师', '律师', '会计师', '建筑师', '艺术家', '音乐家', '作家', '厨师', '消防员', '药师', '数据分析师', '用户体验师', '测试工程师', '运维工程师', '安全工程师', '心理咨询师', '营养师', '插画师', '动画师', '游戏设计师', '摄影师', '剪辑师', 'AI训练师', '区块链工程师'],
,
  创意职业: ['艺术家', '游戏设计师', '文案策划', '元宇宙设计师', '形象设计师'],
,
  新兴职业: ['AI训练师', '区块链工程师', '云计算专家', '网络安全专家'],
,
  服务职业: ['咨询顾问', '投资顾问', '理财顾问', '心理咨询师'],
,
  文人雅士: ['词圣', '词人', '诗人', '文人', '如诗', '风雅', '典雅', '高雅', '文雅', '儒雅', '古雅', '尔雅', '雅人', '贤士', '名士', '逸士', '隐士', '居士', '奇士', '童生', '监生'],
,
  古典意象: ['秋月', '夏雨', '冬雪', '梅兰', '竹菊', '烟雨', '花茶', '风雅', '兰香', '日月', '岁月', '菊花', '杏花', '梨花', '樱花'],
,
  词汇扩展引擎集成_职业: ['医师', '教师', '工程师', '律师', '会计师', '建筑师', '设计师', '艺术家', '音乐家', '作家', '记者', '厨师', '消防员', '药师', '数据分析师', '用户体验师', '界面设计师', '前端工程师', '后端工程师', '算法工程师', '测试工程师', '运维工程师', '安全工程师', '架构师', '心理咨询师', '营养师', '插画师', '动画师', '游戏设计师', '影视制作人', '摄影师', '音效师', '剪辑师', '内容创作者', '独立开发者', '自由职业者', '创业者', '投资人', 'AI训练师', '数据科学家', '区块链工程师', '云计算专家', '网络安全专家', '用户增长专家', '元宇宙设计师', '虚拟偶像制作人', '数字艺术家', 'NFT创作者', '加密货币分析师', '可持续发展专家', '环保工程师', '新能源专家', '智能制造工程师', '瑜伽老师', '舞蹈老师', '音乐老师', '美术老师', '宠物美容师', '宠物训练师', '花艺师', '茶艺师', '咖啡师', '调酒师', '糕点师', '面包师', '营养配餐师', '私人定制师', '整理师', '搭配师', '形象设计师', '婚礼策划师', '活动策划师', '墨客', '骚人', '词人', '诗人', '文人', '佳人', '素手', '贤者', '智者', '仁者', '义人', '文客', '书生', '书客', '诗客', '雅人', '雅客', '学人', '学者', '才人', '贤人', '智人', '名人', '名家', '高人', '逸人', '隐者', '山人', '野人', '散人', '闲人', '达人', '先生', '老师', '导师', '恩师', '良师', '圣人', '能人', '奇人', '异人', '侠客', '剑客', '刀客', '琴师', '棋师', '画师', '茶师', '道人', '真人', '仙人', '大家', '宗师', '大师', '圣手', '举人', '童生', '监生', '潮人', '独家', '个人', '魅人', '数字策展人', '数字营销师', '数字运营师', '内容策划师', '内容运营师', '内容审核师', '内容分析师', '社群运营师', '社群管理员', '社群策划师', '社群分析师', '社群专家', '用户研究员', '用户增长师', '用户运营师', '用户分析师', '品牌策划师', '品牌设计师', '品牌运营师', '品牌管理师', '品牌专家', '市场分析师', '市场研究员', '市场策划师', '市场运营师', '市场专家', '数据工程师', '数据专家', '机器学习工程师', '深度学习专家', '人工智能专家', '区块链专家', '智能合约开发者', 'DeFi专家', '云计算工程师', '云架构师', '云安全专家', '云运维工程师', '信息安全工程师', '安全架构师', '安全分析师', '移动开发工程师', '全栈工程师', '质量保证工程师', 'DevOps工程师', 'SRE工程师', '产品设计师', '交互设计师', '视觉设计师', '体验设计师', '游戏策划师', '游戏美术师', '游戏程序员', '游戏制作人', '视频剪辑师', '音频工程师', '特效师', '直播策划师', '直播运营师', '直播技术员', '直播主持人', '直播分析师', '电商运营师', '电商策划师', '电商分析师', '电商专家', '新媒体分析师', '新媒体专家', '知识付费讲师', '在线教育师', '课程设计师', '教学设计师', '职业规划师', '健康管理师', '宠物医生', '宠物护理师', '宠物行为师', '烘焙师', '收纳师', '会展策划师', '庆典策划师', '派对策划师', '旅游策划师', '旅行定制师', '民宿管家', '酒店管家', '管家服务师', '礼仪师', '自媒体人', '原生'],
} as const

// 动作词汇配置
export const ACTIONS_CONFIG = {
  日常行为: {
    category: '日常行为',
    description: '日常生活中的基本行为',
    elements: ['吃', '睡', '玩', '工作', '学习', '休息', '思考', '发呆', '散步', '购物', '聊天', '刷手机'],
    usage_patterns: ['identity_elevation', 'temporal_displacement'],
    frequency_weight: 1.0
  },
  特殊动作: {
    category: '特殊动作',
    description: '特殊或技能性的动作',
    elements: ['飞', '游泳', '追', '跑', '爬', '跳', '舞蹈', '唱歌', '画画', '写作', '演讲', '表演'],
    usage_patterns: ['identity_elevation', 'service_personification'],
    frequency_weight: 0.8
  },
  抽象动作: {
    category: '抽象动作',
    description: '抽象或概念性的动作',
    elements: ['贩卖', '收集', '制造', '修理', '设计', '创造', '破坏', '保护', '传播', '分享', '探索', '发现'],
    usage_patterns: ['service_personification', 'identity_elevation'],
    frequency_weight: 0.9
  },
  网络行为: {
    category: '网络行为',
    description: '网络和数字化时代的行为',
    elements: ['直播', '带货', '刷视频', '点赞', '转发', '评论', '关注', '充电', '打赏', '连麦', '开播', '下播'],
    usage_patterns: ['temporal_displacement', 'identity_elevation'],
    frequency_weight: 1.1
  },
  现代生活: {
    category: '现代生活',
    description: '现代生活方式相关的行为',
    elements: ['洗头', '化妆', '健身', '减肥', '加班', '熬夜', '摸鱼', '划水', '开会', '汇报', '出差', '居家'],
    usage_patterns: ['temporal_displacement', 'identity_elevation'],
    frequency_weight: 1.2
  }
} as const

// 修饰词汇配置
export const MODIFIERS_CONFIG = {
  权威级别: {
    category: '权威级别',
    description: '表示权威性和专业程度的修饰词',
    elements: ['首席', '高级', '资深', '专业', '认证', '官方', '特级', '顶级', '大师级', '传奇', '史诗', '钻石'],
    usage_patterns: ['identity_elevation'],
    authority_weight: 1.0
  },
  空间范围: {
    category: '空间范围',
    description: '表示空间范围和影响力的修饰词',
    elements: ['全球', '国际', '世界级', '宇宙级', '银河系', '太阳系', '跨国', '跨界', '跨次元', '本地', '区域'],
    usage_patterns: ['identity_elevation', 'service_personification'],
    authority_weight: 0.9
  },
  程度强化: {
    category: '程度强化',
    description: '表示程度和强度的修饰词',
    elements: ['超级', '极度', '非常', '特别', '完全', '绝对', '史诗', '终极', '至尊', '满级', '深度', '极致'],
    usage_patterns: ['identity_elevation', 'contradiction_unity'],
    authority_weight: 0.8
  },
  时间频率: {
    category: '时间频率',
    description: '表示时间频率和持续性的修饰词',
    elements: ['永远', '从不', '偶尔', '经常', '总是', '间歇性', '定期', '不定期', '随时', '24小时', '全天候'],
    usage_patterns: ['identity_elevation', 'emotion_state'],
    authority_weight: 0.7
  },
  状态描述: {
    category: '状态描述',
    description: '表示状态和情况的修饰词',
    elements: ['在线', '离线', '忙碌', '空闲', '活跃', '潜水', '冒泡', '隐身', '免打扰', '勿扰', '正常', '异常'],
    usage_patterns: ['tech_expression', 'emotion_state'],
    authority_weight: 0.6
  },
  能力特征: ['精通', '创新', '高效'],
,
  程度描述: ['高效', '独特'],
,
  现代修饰: ['萌萌', '呆萌', '软萌', '奶萌', '甜萌', '酷萌', '帅萌', '超酷', '很酷', '贼酷', '死酷', '时尚范'],
,
  网络流行: ['靠谱', '厉害'],
,
  词汇扩展引擎集成_修饰: ['兴高', '高效', '积极', '独特', '高雅', '太极', '高山', '秋高', '高士', '高贤', '高才', '高兴', '超酷', '极酷', '超帅', '极帅', '超美', '极美', '超治愈', '超脱', '高冷', '特色', '特点', '特征', '特质', '特别', '特殊', '特有', '风度', '气度', '积极向上', '积极进取', '深度', '资深级', '高级', '高端', '高档', '高质', '高品', '高标', '顶级', '顶尖', '顶端', '顶峰', '顶流', '顶配', '顶格', '顶呱呱', '一级', '超级', '超强', '超棒', '超赞', '超牛', '超萌', '极致', '极品', '极佳', '极好', '极棒', '极赞', '极萌', '高能', '高产', '高速'],
} as const

// 连接词汇配置
export const CONNECTORS_CONFIG = {
  对比转折: {
    category: '对比转折',
    description: '表示对比和转折关系的连接词',
    elements: ['但', '却', '然而', '不过', '虽然', '尽管', '反而', '相反', '倒是', '偏偏', '恰恰', '竟然'],
    usage_patterns: ['contradiction_unity'],
    emotional_weight: 1.0
  },
  并列关系: {
    category: '并列关系',
    description: '表示并列和同等关系的连接词',
    elements: ['和', '与', '及', '以及', '还有', '同时', '一边', '一面', '既', '又', '也', '亦'],
    usage_patterns: ['contradiction_unity', 'service_personification'],
    emotional_weight: 0.8
  },
  递进强化: {
    category: '递进强化',
    description: '表示递进和强化关系的连接词',
    elements: ['更', '还', '甚至', '竟然', '居然', '简直', '完全', '彻底', '深度', '极致', '进一步', '更加'],
    usage_patterns: ['contradiction_unity', 'emotion_state'],
    emotional_weight: 1.1
  },
  因果关系: {
    category: '因果关系',
    description: '表示因果关系的连接词',
    elements: ['因为', '所以', '由于', '导致', '造成', '引起', '产生', '带来', '形成', '促使', '致使', '使得'],
    usage_patterns: ['tech_expression', 'service_personification'],
    emotional_weight: 0.9
  }
} as const

// 固定后缀配置
export const SUFFIXES_CONFIG = {
  职位后缀: {
    category: '职位后缀',
    description: '表示职位和身份的后缀',
    elements: ['官', '师', '专家', '大使', '代表', '委员', '顾问', '总监', '主任', '经理'],
    usage_patterns: ['identity_elevation'],
    formality_weight: 1.0
  },
  服务角色: {
    category: '服务角色',
    description: '表示服务类型的角色后缀',
    elements: ['邮递员', '收集员', '配送员', '制造商', '贩卖机', '专卖店', '客服', '导航员', '修理工', '设计师'],
    usage_patterns: ['service_personification'],
    formality_weight: 0.8
  },
  身份后缀: {
    category: '身份后缀',
    description: '表示身份和特征的后缀',
    elements: ['专家', '代表', '选手', '患者', '星人', '爱好者', '达人', '党', '控', '货'],
    usage_patterns: ['emotion_state', 'food_association'],
    formality_weight: 0.6
  },
  技术术语: {
    category: '技术术语',
    description: '技术相关的术语后缀',
    elements: ['正在缓冲', '连接超时', '服务器宕机', '数据库损坏', '404未找到', '503不可用', '系统维护', '正在加载'],
    usage_patterns: ['tech_expression'],
    formality_weight: 0.7
  }
} as const

// 特质词汇配置
export const TRAITS_CONFIG = {
  正面特质: {
    category: '正面特质',
    description: '积极正面的性格特质',
    elements: ['温柔', '理性', '冷静', '勤奋', '节约', '听劝', '乐观', '自信', '坚强', '独立'],
    usage_patterns: ['contradiction_unity'],
    polarity: 'positive'
  },
  负面特质: {
    category: '负面特质',
    description: '消极负面的性格特质',
    elements: ['强硬', '感性', '冲动', '懒惰', '挥霍', '反骨', '悲观', '自卑', '脆弱', '依赖'],
    usage_patterns: ['contradiction_unity'],
    polarity: 'negative'
  },
  生活概念: {
    category: '生活概念',
    description: '生活相关的概念词汇',
    elements: ['人生', '梦想', '快乐', '悲伤', '爱情', '友情', '工作', '学习', '生活', '未来'],
    usage_patterns: ['tech_expression'],
    polarity: 'neutral'
  },
  深层情感: ['深爱', '真诚', '真心', '纯真', '专注', '专心'],
,
  文艺情感: ['诗韵', '诗心', '雅致', '雅意', '优雅', '清雅', '小清新'],
,
  现代情感: ['佛系', '元气'],
,
  性格特征: ['开朗', '稳重', '幽默', '机智'],
,
  品质特征: ['诚信', '可靠'],
,
  传统美德: ['智者', '仁心', '仁者', '义气', '义人', '大义', '礼数', '有礼', '知礼', '智识', '明智', '信用', '守信', '忠心', '忠正', '孝顺', '孝心', '行孝', '尽孝'],
,
  文化概念: ['墨客', '琴棋', '书画', '花茶', '瑶琴', '茶香', '笔墨', '翰墨', '琴师', '棋师', '画师', '茶师'],
,
  词汇扩展引擎集成_特质: ['温暖', '温柔', '温馨', '温和', '温情', '热情', '热忱', '热心', '热烈', '热诚', '冷静', '冷淡', '冷酷', '冷漠', '冷峻', '平静', '平和', '平淡', '平稳', '平缓', '欢乐', '欢喜', '欢快', '欢悦', '欢腾', '愉快', '愉悦', '愉心', '愉情', '愉神', '快乐', '快意', '快慰', '快活', '快感', '喜悦', '喜乐', '喜庆', '喜气', '喜盈', '兴奋', '兴致', '兴趣', '兴旺', '深情', '深爱', '深切', '深沉', '深邃', '真诚', '真挚', '真心', '真情', '真意', '纯真', '纯洁', '纯净', '纯朴', '纯粹', '专注', '专一', '专心', '专情', '专诚', '诗意', '诗情', '诗韵', '诗心', '诗魂', '雅致', '雅韵', '雅趣', '雅兴', '雅意', '优雅', '优美', '优秀', '优质', '优良', '清雅', '清新', '清纯', '清澈', '清香', '治愈', '暖心', '贴心', '用心', '走心', '佛系', '淡然', '随性', '自在', '洒脱', '元气', '活力', '朝气', '生机', '青春', '文艺', '小清新', '慢生活', '正能量', '满满', '编辑', '翻译', '导游', '司机', '警察', '护士', '产品经理', '项目经理', '运营经理', '市场经理', '品牌经理', '咨询顾问', '投资顾问', '理财顾问', '文案策划', '创意总监', '美术指导', '主播', '博主', '网红', 'UP主', '斜杠青年', '社群运营', '直播运营', '电商运营', '新媒体运营', '碳中和顾问', '健身教练', '开朗', '活泼', '外向', '内向', '稳重', '成熟', '幽默', '风趣', '机智', '聪明', '智慧', '睿智', '博学', '渊博', '深刻', '细心', '耐心', '善良', '友善', '和善', '慈善', '仁善', '专业', '精通', '熟练', '娴熟', '精湛', '创新', '创意', '创造', '独创', '原创', '迅速', '敏捷', '灵活', '机敏', '严谨', '精确', '准确', '精准', '细致', '全面', '综合', '多元', '多样', '丰富', '诚信', '诚实', '坦诚', '忠诚', '可靠', '稳定', '踏实', '务实', '实在', '负责', '认真', '严肃', '正经', '端正', '主动', '进取', '上进', '奋进', '坚持', '坚定', '坚强', '坚韧', '顽强', '简约', '简洁', '简单', '朴素', '朴实', '精致', '精美', '精细', '精巧', '时尚', '潮流', '前卫', '先锋', '新潮', '经典', '传统', '复古', '怀旧', '古典', '现代', '当代', '新式', '新颖', '新鲜', '自信', '自然', '自由', '自主', '独立', '独有', '独到', '平衡', '和谐', '协调', '统一', '完整', '充实', '丰满', '饱满', '满足', '满意', '清晰', '明确', '明朗', '明亮', '光明', '诗仙', '词圣', '诗圣', '诗佛', '诗鬼', '诗豪', '诗杰', '诗魔', '雅士', '才子', '春花', '秋月', '夏雨', '冬雪', '晨露', '夕阳', '明月', '清风', '梅兰', '竹菊', '松柏', '荷莲', '桃李', '杨柳', '芙蓉', '牡丹', '江南', '塞北', '关山', '烟雨', '楼台', '亭榭', '园林', '山水', '琴棋', '书画', '诗酒', '花茶', '香炉', '古琴', '瑶琴', '玉笛', '青衫', '白衣', '红袖', '绿裙', '纤指', '明眸', '皓齿', '温润', '如玉', '似水', '如花', '若梦', '如烟', '似雾', '如诗', '淡泊', '宁静', '致远', '明志', '修身', '养性', '怡情', '悦性', '风雅', '典雅', '文雅', '儒雅', '古雅', '画意', '禅意', '古意', '情意', '心意', '意境', '韵味', '韵致', '韵律', '神韵', '风韵', '气韵', '音韵', '书香', '墨香', '茶香', '花香', '檀香', '兰香', '桂香', '梅香', '文房', '四宝', '笔墨', '纸砚', '丹青', '翰墨', '金石', '香道', '茶道', '花道', '剑道', '八卦', '阴阳', '五行', '天地', '乾坤', '日月', '星辰', '春秋', '冬夏', '寒暑', '朝暮', '晨昏', '日夜', '光阴', '岁月', '江山', '河川', '湖海', '溪涧', '泉瀑', '云雾', '烟霞', '风雨', '松竹', '梅花', '兰草', '竹叶', '菊花', '荷花', '芍药', '海棠', '桃花', '杏花', '梨花', '樱花', '桂花', '君子', '淑女', '诗书', '礼乐', '仁义', '道德', '忠孝', '礼仪', '廉耻', '信义', '温文', '尔雅', '知书', '达理', '才华', '横溢', '学富', '五车', '博古', '通今', '文武', '双全', '德才', '兼备', '品学', '兼优', '流水', '琴瑟', '和鸣', '相得', '益彰', '珠联', '璧合', '天作', '之合', '静好', '时光', '荏苒', '似箭', '白驹', '过隙', '流年', '春暖', '花开', '气爽', '四季', '如春', '万物', '复苏', '心如', '止水', '厚德', '载物', '自强', '不息', '温故', '知新', '学而', '时习', '有教', '无类', '因材', '施教', '循循', '善诱', '仁爱', '仁慈', '仁德', '仁心', '仁厚', '仁和', '义气', '义理', '义士', '义举', '义行', '正义', '大义', '礼貌', '礼节', '礼数', '礼让', '礼敬', '有礼', '知礼', '智识', '智谋', '智勇', '明智', '信用', '信誉', '信实', '信守', '信赖', '守信', '忠义', '忠心', '忠实', '忠贞', '忠厚', '忠良', '忠正', '孝顺', '孝敬', '孝心', '孝道', '孝义', '孝行', '行孝', '尽孝', '友爱', '友好', '友谊', '和睦', '和气', '和蔼', '谦逊', '谦和', '谦恭', '谦让', '谦虚', '谦卑', '低调', '内敛', '勤奋', '勤勉', '勤劳', '勤俭', '勤学', '勤政', '勤恳', '勤谨', '节俭', '简朴', '俭朴', '清廉', '廉洁', '正直', '刚正', '诚恳', '诚挚', '赤诚', '至诚', '精诚', '文士', '文友', '书友', '书童', '墨士', '诗友', '诗童', '雅友', '学士', '学童', '才士', '才女', '贤士', '贤良', '智士', '智叟', '名士', '名流', '逸士', '隐士', '居士', '逸民', '夫子', '师父', '师傅', '贤君', '明君', '游侠', '道士', '奇士', '怪才', '文豪', '泰斗', '巨匠', '翰林', '进士', '秀才', '舒适', '惬意', '悠闲', '轻松', '随意', '简易', '简明', '干净', '整洁', '天然', '纯然', '怡然', '悠然', '泰然', '坦然', '温婉', '温雅', '清爽', '清甜', '清淡', '清秀', '舒心', '舒服', '舒畅', '舒缓', '舒展', '舒爽', '放松', '开心', '欣喜', '开怀', '开阔', '开放', '开明', '豁达', '乐观', '畅快', '痛快', '爽快', '利索', '麻利', '干脆', '美好', '美妙', '美丽', '美观', '美味', '好看', '漂亮', '精良', '精品', '精选', '精心', '优异', '出色', '杰出', '完美', '完善', '完全', '完备', '齐全', '周全', '妥当', '和顺', '和平', '和美', '平安', '平顺', '顺利', '顺心', '安静', '安详', '安宁', '安逸', '安心', '安全', '安稳', '安康', '宁和', '宁谧', '静谧', '幽静', '恬静', '娴静', '文静', '淡定', '淡雅', '从容', '镇定', '沉着', '健康', '精神', '年轻', '给力', '靠谱', '厉害', '牛逼', '强悍', '霸气', '威武', '彪悍', '萌萌', '可爱', '呆萌', '软萌', '奶萌', '甜萌', '酷萌', '帅萌', '甜美', '甜心', '甜蜜', '甜腻', '香甜', '蜜糖', '糖果', '酷炫', '酷帅', '很酷', '巨酷', '贼酷', '死酷', '帅气', '帅呆', '很帅', '巨帅', '贼帅', '死帅', '美腻', '美炸', '美哭', '很美', '巨美', '贼美', '仙气', '仙女', '小仙女', '仙儿', '仙子', '神仙', '天仙', '地仙', '治愈系', '小治愈', '很治愈', '软糯', '软妹', '软乎', '软绵', '柔软', '轻柔', '神采', '光彩', '阳光', '灿烂', '明媚', '小确幸', '小美好', '小温暖', '小文艺', '小浪漫', '随缘', '看开', '放下', '释然', '宝藏', '绝绝子', '爱了', '慕了', '酸了', '柠檬', '真香', '社死', '尴尬', '无语', '服了', '绝了', '醉了', '疯了', '傻了', '沙雕', '逗比', '搞笑', '有趣', '好玩', '逗乐', '傲娇', '腹黑', '毒舌', '单纯', '暖男', '奶狗', '小狼狗', '小奶猫', '小可爱', '小天使', '小恶魔', '大佬', '大神', '巨佬', '老铁', '兄弟', '姐妹', '宝贝', '亲爱', '时髦', '时兴', '时新', '时代', '时潮', '时尚感', '时尚范', '潮男', '潮女', '潮范', '潮感', '潮味', '潮系', '前沿', '前瞻', '前进', '前行', '前锋', '前导', '前驱', '新奇', '新意', '新风', '新派', '创作', '创建', '创立', '创始', '创举', '独自', '独占', '个性', '个体', '个别', '个案', '个例', '个样', '个色', '特性', '风格', '风范', '风采', '风姿', '风味', '风情', '品味', '品质', '品格', '品性', '品德', '品行', '品相', '品位', '格调', '格局', '格式', '格外', '格致', '格物', '格言', '格律', '气质', '气息', '气场', '气势', '气派', '气象', '魅力', '魅惑', '魅影', '魅音', '魅色', '魅态', '魅眼', '吸引', '吸引力', '吸睛', '吸粉', '吸金', '吸流', '吸热', '出众', '出彩', '出挑', '出格', '出新', '出奇', '出类', '突出', '突破', '突进', '突飞', '突变', '突然', '突起', '突显', '亮眼', '亮点', '亮色', '亮丽', '亮相', '亮出', '亮起', '亮晶', '闪亮', '闪耀', '闪光', '闪烁', '闪现', '闪动', '闪电', '闪闪', '精彩', '精妙', '精密', '感动', '感激', '感谢', '感恩', '感慨', '感叹', '感受', '感知', '深远', '深入', '真实', '真正', '真切', '真纯', '纯正', '纯美', '纯善', '专精', '专门', '坚毅', '坚固', '坚实', '坚硬', '勇敢', '勇气', '勇士', '勇猛', '勇武', '勇毅', '勇进', '勇往', '自立', '自我', '积累', '积聚', '积极性', '乐天', '乐意', '乐于', '乐此', '乐在', '乐享', '乐活', '开通', '开达', '热爱', '热衷', '热切', '活跃', '活动', '活络', '活现', '活灵', '活水', '生动', '生气', '生命', '生活', '生趣', '生辉', '生色', '朝阳', '朝霞', '朝露', '朝花', '朝日', '朝晖', '朝光', '青年', '青涩', '青翠', '青绿', '青蓝', '青天', '青山', '阳春', '阳台', '阳面', '阳性', '阳刚', '阳气', '阳和', '深层', '细腻', '细密', '细微', '细节', '细润', '丰盈', '丰厚', '丰硕', '丰沛', '丰饶', '丰茂', '浓郁', '浓烈', '浓厚', '浓重', '浓密', '浓缩', '浓情', '浓香', '淡静', '淡远', '淡薄', '淡漠', '清明', '清朗', '清亮', '清透', '柔和', '柔美', '柔情', '柔顺', '柔润', '柔嫩', '柔弱', '刚毅', '刚强', '刚直', '刚硬', '刚烈', '刚健', '刚劲', '灵动', '灵敏', '灵巧', '灵气', '灵性', '灵魂', '灵感', '静雅', '静美', '静心', '静神', '静默', '静寂', '祥和', '祥瑞', '祥光', '祥云', '祥气', '祥兆', '祥符', '祥麟', '优越', '优先', '完好', '完成', '完毕', '数字产品经理', '数据产品经理', '云产品经理', '安全顾问', '电商客服', '新媒体编辑', '新媒体策划', '学习顾问', '生涯教练', '色彩顾问', '旅游顾问', '领队', '司仪', '数字游民', '卓越', '专业化', '资深', '一流', '一等', '一线', '一品', '一流水准', '一等一', '完美无缺', '理想', '理想化', '理想型', '理想状态', '理想主义', '理想派', '创新型', '创意型', '独特性', '原始', '原本', '原汁', '原味', '原创性', '原创型', '新型', '新颖性', '新潮流', '潮流感', '潮流范', '前卫性', '前沿性', '先进', '先导', '先驱', '先行', '先进性', '先锋性', '先导性', '领先', '领导', '领军', '领头', '领跑', '领先性', '领导性', '领军性', '高效性', '高能性', '高产性'],
} as const

// 统计信息
export const ELEMENT_LIBRARY_STATS = {
  subjects: Object.keys(SUBJECTS_CONFIG).length,
  actions: Object.keys(ACTIONS_CONFIG).length,
  modifiers: Object.keys(MODIFIERS_CONFIG).length,
  connectors: Object.keys(CONNECTORS_CONFIG).length,
  suffixes: Object.keys(SUFFIXES_CONFIG).length,
  traits: Object.keys(TRAITS_CONFIG).length,
  total_categories: 0,  // 将在运行时计算
  total_elements: 0     // 将在运行时计算
}

// 计算总数
ELEMENT_LIBRARY_STATS.total_categories = 
  ELEMENT_LIBRARY_STATS.subjects + 
  ELEMENT_LIBRARY_STATS.actions + 
  ELEMENT_LIBRARY_STATS.modifiers + 
  ELEMENT_LIBRARY_STATS.connectors + 
  ELEMENT_LIBRARY_STATS.suffixes + 
  ELEMENT_LIBRARY_STATS.traits

// 导入扩展配置
import {
  EXPANDED_SUBJECTS_CONFIG,
  EXPANDED_TRAITS_CONFIG,
  EXPANDED_MODIFIERS_CONFIG
} from './expanded-element-library-config'

// 工具函数
export function getAllElements() {
  const allElements: { [category: string]: string[] } = {}

  // 合并所有配置
  Object.values(SUBJECTS_CONFIG).forEach(config => {
    allElements[config.category] = config.elements
  })
  Object.values(ACTIONS_CONFIG).forEach(config => {
    allElements[config.category] = config.elements
  })
  Object.values(MODIFIERS_CONFIG).forEach(config => {
    allElements[config.category] = config.elements
  })
  Object.values(CONNECTORS_CONFIG).forEach(config => {
    allElements[config.category] = config.elements
  })
  Object.values(SUFFIXES_CONFIG).forEach(config => {
    allElements[config.category] = config.elements
  })
  Object.values(TRAITS_CONFIG).forEach(config => {
    allElements[config.category] = config.elements
  })

  return allElements
}

// 获取包含扩展语素的完整元素库
export function getAllElementsWithExpansion() {
  const allElements: { [category: string]: string[] } = {}

  // 合并原有配置
  Object.values(SUBJECTS_CONFIG).forEach(config => {
    allElements[config.category] = [...config.elements]
  })
  Object.values(ACTIONS_CONFIG).forEach(config => {
    allElements[config.category] = [...config.elements]
  })
  Object.values(MODIFIERS_CONFIG).forEach(config => {
    allElements[config.category] = [...config.elements]
  })
  Object.values(CONNECTORS_CONFIG).forEach(config => {
    allElements[config.category] = [...config.elements]
  })
  Object.values(SUFFIXES_CONFIG).forEach(config => {
    allElements[config.category] = [...config.elements]
  })
  Object.values(TRAITS_CONFIG).forEach(config => {
    allElements[config.category] = [...config.elements]
  })

  // 合并扩展配置
  Object.values(EXPANDED_SUBJECTS_CONFIG).forEach(config => {
    allElements[config.category] = [...config.elements]
  })
  Object.values(EXPANDED_TRAITS_CONFIG).forEach(config => {
    allElements[config.category] = [...config.elements]
  })
  Object.values(EXPANDED_MODIFIERS_CONFIG).forEach(config => {
    allElements[config.category] = [...config.elements]
  })

  return allElements
}

export function getElementsForPattern(patternId: string) {
  const elements: { [category: string]: string[] } = {}
  
  // 根据模式筛选相关元素
  const allConfigs = [
    ...Object.values(SUBJECTS_CONFIG),
    ...Object.values(ACTIONS_CONFIG),
    ...Object.values(MODIFIERS_CONFIG),
    ...Object.values(CONNECTORS_CONFIG),
    ...Object.values(SUFFIXES_CONFIG),
    ...Object.values(TRAITS_CONFIG)
  ]
  
  allConfigs.forEach(config => {
    if (config.usage_patterns.includes(patternId)) {
      elements[config.category] = config.elements
    }
  })
  
  return elements
}

export function getTotalElementCount() {
  const allElements = getAllElements()
  return Object.values(allElements).reduce((total, elements) => total + elements.length, 0)
}
