{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"clean": "rm -rf node_modules && rm -rf pnpm-lock.yaml", "CB": "npx pnpm run clean && npx pnpm install && npx nuxt build", "BD": "npx pnpm build && npx pnpm dev", "build": "npx nuxt build", "dev": "npx nuxt dev", "preview": "npx nuxt preview", "test": "vitest run"}, "dependencies": {"nuxt": "^3.17.5", "pinia": "^3.0.3", "vue": "^3.5.16", "vue-i18n": "^11.1.5", "vue-router": "^4.5.1"}, "devDependencies": {"@iconify-json/heroicons": "^1.2.2", "@nuxt/eslint": "^1.4.1", "@nuxt/image": "^1.10.0", "@nuxt/schema": "^3.17.5", "@nuxt/scripts": "^0.11.8", "@nuxt/test-utils": "^3.19.1", "@nuxtjs/color-mode": "^3.5.2", "@nuxtjs/i18n": "^9.5.5", "@unocss/nuxt": "^66.1.4", "eslint": "^9.28.0", "typescript": "^5.8.3", "vitest": "^1.4.0", "vue-tsc": "^2.2.10"}}