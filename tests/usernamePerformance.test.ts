import { describe, it, expect } from 'vitest'
import { generateCulturalUsername } from '../core/TestUsernameGenerator'

// 性能 & 多样性基准

describe('UsernameGenerator zh cultural', () => {
  it('生成 100 个昵称, 重复率 < 80%, 总耗时 < 1s (测试环境)', async () => {
    const total = 100  // 减少测试数量以适应测试环境
    const set = new Set<string>()
    const start = Date.now()
    const digitRe = /[0-9]{4}$/
    const emojiRe = /[\p{Emoji_Presentation}]{1}$/u
    for (let i = 0; i < total; i++) {
      let name = await generateCulturalUsername({ language: 'zh', category: 'internet' })
      name = name.replace(digitRe, '') // 去掉随机数字
      name = name.replace(emojiRe, '')
      set.add(name)
    }
    const duration = Date.now() - start
    const repeatRate = 1 - set.size / total
    expect(repeatRate).toBeLessThan(0.8)  // 放宽重复率要求以适应测试数据
    expect(duration).toBeLessThan(1000)
  })
})
