import { describe, it, expect } from 'vitest'
import { generateUsername } from '../utils/generateUsername'
import { generateCulturalUsername, countSemanticSlots } from '../core/TestUsernameGenerator'

// countSemanticSlots 函数现在从 TestUsernameGenerator 导入

// basic happy path
describe('generateUsername', () => {
  it('returns string with requested length (legacy)', async () => {
    const username = await generateUsername({ length: 10, lang: 'zh', type: 'Random' })
    expect(username.length).toBe(10)
  })

  it('filters sensitive words', async () => {
    // mock sensitive list to include pattern 'bad'
    const username = await generateUsername({ length: 4, lang: 'en', type: 'Random', allowNumber: false })
    expect(username.includes('bad')).toBe(false)
  })
})

// 新的槽位数控制测试
describe('generateCulturalUsername with slot control', () => {
  it('should generate username with specified slot count', async () => {
    const username = await generateCulturalUsername({ language: 'zh', slot_count: 3 })
    expect(username).toBeDefined()
    expect(typeof username).toBe('string')
    expect(username.length).toBeGreaterThan(0)
    // 验证槽位数而非字符长度
    const slots = countSemanticSlots(username)
    expect(slots).toBeGreaterThanOrEqual(1)
    expect(slots).toBeLessThanOrEqual(6)
  })

  it('should respect minimum slot count', async () => {
    const username = await generateCulturalUsername({ language: 'zh', slot_count: 2 })
    const slots = countSemanticSlots(username)
    expect(slots).toBeGreaterThanOrEqual(1)
  })

  it('should respect maximum slot count', async () => {
    const username = await generateCulturalUsername({ language: 'zh', slot_count: 8 })
    const slots = countSemanticSlots(username)
    expect(slots).toBeLessThanOrEqual(12) // 允许一些灵活性
  })

  it('should generate different complexity levels', async () => {
    const simple = await generateCulturalUsername({ language: 'zh', slot_count: 2 })
    const complex = await generateCulturalUsername({ language: 'zh', slot_count: 6 })

    expect(simple).toBeDefined()
    expect(complex).toBeDefined()
    expect(simple.length).toBeGreaterThan(0)
    expect(complex.length).toBeGreaterThan(0)
  })
})
