import { describe, it } from 'vitest'
import { generateCulturalUsername } from '../core/TestUsernameGenerator'

// 生成并打印 30 个样本供人工验证

describe('sample 30 usernames', () => {
  it('print 30 samples of length 4~6', async () => {
    for (let i = 0; i < 30; i++) {
      const targetLen = 4 + (i % 3) // 循环 4,5,6 token
      const name = await generateCulturalUsername({ language: 'zh', slot_count: targetLen })
      // Print with index prefix for readability
      // eslint-disable-next-line no-console
      console.log(`${(i + 1).toString().padStart(2, '0')}: ${name}`)
    }
  })
})
