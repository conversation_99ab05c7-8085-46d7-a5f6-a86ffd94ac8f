/**
 * 优化闭环系统演示测试
 * 展示完整的 测试->验证->分析->优化->测试 流程
 */

import { describe, it, expect } from 'vitest'
import { InterestAnalysisSystem } from '../core/InterestAnalysisSystem'
import { OptimizationLoop } from '../core/OptimizationLoop'
import { generateCulturalUsername } from '../core/TestUsernameGenerator'

describe('用户名有趣性优化闭环系统', () => {
  
  it('应该能够分析单个用户名的有趣性', async () => {
    const analyzer = new InterestAnalysisSystem('zh')
    
    const testUsernames = [
      '超级大神王',
      '究极玩家yyds', 
      '神级剑客绝子',
      '霸道法师',
      '传说勇者'
    ]
    
    console.log('\n🔍 单个用户名有趣性分析:')
    console.log('=' .repeat(50))
    
    for (const username of testUsernames) {
      const result = await analyzer.analyzeUsername(username)
      
      console.log(`\n📝 用户名: ${username}`)
      console.log(`📊 总体有趣性: ${result.metrics.overall_interest.toFixed(3)}`)
      console.log(`🎵 语言学评分: ${Object.values(result.metrics.linguistic).map(v => v.toFixed(2)).join(', ')}`)
      console.log(`💭 语义评分: ${Object.values(result.metrics.semantic).map(v => v.toFixed(2)).join(', ')}`)
      console.log(`🧠 心理学评分: ${Object.values(result.metrics.psychological).map(v => v.toFixed(2)).join(', ')}`)
      console.log(`⚙️ 实用性评分: ${Object.values(result.metrics.practical).map(v => v.toFixed(2)).join(', ')}`)
      
      expect(result.metrics.overall_interest).toBeGreaterThan(0)
      expect(result.metrics.overall_interest).toBeLessThanOrEqual(1)
      expect(result.metrics.confidence).toBeGreaterThan(0.5)
    }
  })
  
  it('应该能够进行批量分析并生成洞察报告', async () => {
    const analyzer = new InterestAnalysisSystem('zh')
    
    // 生成测试样本
    const testSamples: string[] = []
    for (let i = 0; i < 20; i++) {
      const username = await generateCulturalUsername({ 
        language: 'zh', 
        category: 'internet' 
      })
      testSamples.push(username)
    }
    
    console.log('\n📊 批量分析演示:')
    console.log('=' .repeat(50))
    console.log(`🎯 分析样本数量: ${testSamples.length}`)
    console.log(`📝 样本示例: ${testSamples.slice(0, 5).join(', ')}...`)
    
    const batchReport = await analyzer.analyzeBatch(testSamples)
    
    console.log(`\n📈 分析结果:`)
    console.log(`📊 样本大小: ${batchReport.sample_size}`)
    console.log(`🌍 语言: ${batchReport.language}`)
    console.log(`⏰ 分析时间: ${batchReport.timestamp}`)
    
    // 验证报告结构
    expect(batchReport.sample_size).toBe(testSamples.length)
    expect(batchReport.language).toBe('zh')
    expect(batchReport.distribution_analysis).toBeDefined()
    expect(batchReport.pattern_insights).toBeDefined()
    expect(batchReport.optimization_recommendations).toBeDefined()
    expect(batchReport.cultural_insights).toBeDefined()
    
    console.log(`✅ 批量分析报告生成成功`)
  })
  
  it('应该能够运行完整的优化闭环', async () => {
    const config = {
      language: 'zh',
      sample_size: 15,  // 小样本以加快测试
      target_interest_threshold: 0.7,
      max_iterations: 3,  // 限制迭代次数
      convergence_threshold: 0.01,
      ai_provider: 'local' as const
    }
    
    console.log('\n🚀 优化闭环演示:')
    console.log('=' .repeat(50))
    console.log(`🎯 目标: 将用户名有趣性提升至 ${config.target_interest_threshold}`)
    console.log(`📊 每轮样本: ${config.sample_size}`)
    console.log(`🔄 最大迭代: ${config.max_iterations}`)
    
    const optimizer = new OptimizationLoop(config)
    const summary = await optimizer.runOptimizationLoop()
    
    console.log('\n📋 优化总结:')
    console.log(`🔄 总迭代次数: ${summary.total_iterations}`)
    console.log(`📈 最终性能: ${summary.final_performance.toFixed(3)}`)
    console.log(`📊 改进幅度: ${summary.improvement_achieved.toFixed(3)}`)
    console.log(`🔍 关键发现: ${summary.key_discoveries.join(', ')}`)
    
    // 验证优化效果
    expect(summary.total_iterations).toBeGreaterThan(0)
    expect(summary.total_iterations).toBeLessThanOrEqual(config.max_iterations)
    expect(summary.final_performance).toBeGreaterThan(0)
    
    // 获取详细历史
    const history = optimizer.getOptimizationHistory()
    console.log(`\n📚 迭代历史详情:`)
    
    history.forEach((iteration, index) => {
      console.log(`\n🔄 第 ${iteration.iteration} 轮:`)
      console.log(`  📊 平均有趣性: ${iteration.performance_metrics.avg_interest_score.toFixed(3)}`)
      console.log(`  📈 改进幅度: ${iteration.performance_metrics.score_improvement.toFixed(3)}`)
      console.log(`  🎯 样本示例: ${iteration.test_samples.slice(0, 3).join(', ')}...`)
      console.log(`  🔧 优化动作: ${Object.keys(iteration.optimizations_applied.weight_changes).length} 权重调整`)
    })
    
    expect(history.length).toBe(summary.total_iterations)
    
    console.log(`\n✅ 优化闭环演示完成`)
  })
  
  it('应该能够识别和分析用户名模式', async () => {
    const analyzer = new InterestAnalysisSystem('zh')
    
    // 构造具有明显模式的测试数据
    const patternSamples = [
      // 模式1: 前缀+核心+后缀
      '超级大神王', '究极高手君', '无敌玩家者',
      // 模式2: 核心+流行后缀
      '大神yyds', '高手yyds', '玩家yyds',
      // 模式3: 简短核心
      '剑客', '法师', '战士',
      // 模式4: 复合词汇
      '神级剑客', '霸道法师', '传说勇者'
    ]
    
    console.log('\n🔍 模式识别演示:')
    console.log('=' .repeat(50))
    console.log(`📝 测试样本: ${patternSamples.join(', ')}`)
    
    const report = await analyzer.analyzeBatch(patternSamples)
    
    console.log(`\n📊 模式分析结果:`)
    console.log(`🎯 成功模式数量: ${report.pattern_insights.successful_patterns.length}`)
    console.log(`⚠️ 问题模式数量: ${report.pattern_insights.problematic_patterns.length}`)
    
    // 显示识别到的模式
    report.pattern_insights.successful_patterns.forEach((pattern, index) => {
      console.log(`  ✅ 模式 ${index + 1}: ${pattern.pattern}`)
      console.log(`     频率: ${pattern.frequency}, 平均有趣性: ${pattern.avg_interest.toFixed(3)}`)
      console.log(`     示例: ${pattern.examples.join(', ')}`)
    })
    
    expect(report.pattern_insights).toBeDefined()
    console.log(`✅ 模式识别完成`)
  })
  
  it('应该能够生成跨语言优化建议', async () => {
    console.log('\n🌍 跨语言优化建议演示:')
    console.log('=' .repeat(50))
    
    // 模拟多语言分析
    const languages = ['zh', 'en']
    const crossLanguageInsights: Record<string, any> = {}
    
    for (const lang of languages) {
      const analyzer = new InterestAnalysisSystem(lang)
      
      // 生成该语言的样本
      const samples: string[] = []
      for (let i = 0; i < 10; i++) {
        const username = await generateCulturalUsername({ 
          language: lang, 
          category: 'internet' 
        })
        samples.push(username)
      }
      
      const report = await analyzer.analyzeBatch(samples)
      crossLanguageInsights[lang] = {
        avg_scores: report.distribution_analysis.dimension_averages,
        cultural_elements: report.cultural_insights.effective_cultural_elements,
        sample_examples: samples.slice(0, 3)
      }
      
      console.log(`\n🌐 ${lang.toUpperCase()} 语言分析:`)
      console.log(`  📝 样本: ${samples.slice(0, 3).join(', ')}...`)
      console.log(`  📊 平均表现: ${JSON.stringify(report.distribution_analysis.dimension_averages, null, 2)}`)
    }
    
    // 生成跨语言对比洞察
    console.log(`\n🔄 跨语言对比洞察:`)
    console.log(`📊 中文样本特点: 更注重音韵和谐与文化共鸣`)
    console.log(`📊 英文样本特点: 更注重简洁性与国际化表达`)
    console.log(`🎯 通用优化原则: 平衡创意性与实用性`)
    
    expect(crossLanguageInsights).toBeDefined()
    expect(Object.keys(crossLanguageInsights)).toContain('zh')
    expect(Object.keys(crossLanguageInsights)).toContain('en')
    
    console.log(`✅ 跨语言分析完成`)
  })
})

// 辅助函数：生成演示报告
export function generateDemoReport() {
  console.log('\n📋 用户名有趣性优化系统演示报告')
  console.log('=' .repeat(60))
  console.log('🎯 系统目标: 通过AI驱动的闭环优化提升用户名有趣性')
  console.log('🔄 核心流程: 测试 -> 验证 -> 分析 -> 优化 -> 测试')
  console.log('📊 评估维度: 语言学、语义、心理学、实用性')
  console.log('🌍 扩展能力: 支持多语言、跨文化适配')
  console.log('🚀 应用价值: 为新语种快速建立有趣用户名生成能力')
  console.log('=' .repeat(60))
}
