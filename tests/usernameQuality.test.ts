import { describe, it, expect } from 'vitest'
import { generateCulturalUsername } from '../core/TestUsernameGenerator'

// 复制核心中的语义检查简化版（避免循环依赖）
function isSemanticValid(name: string): boolean {
  // 至少2个中文
  if ((name.match(/[\u4e00-\u9fa5]/g) || []).length < 2) return false
  // 不允许只以数字或 emoji 结尾
  if (/([0-9]{4}|[\ud83c-\ud83e][\ud000-\udfff])$/.test(name)) return false
  // 不含连续相同字母缩写
  if (/([a-zA-Z])\1{1,}/.test(name)) return false
  return true
}

const emphasiseWords = [
  '超', '究极', '无敌', '终极', '爆裂', '狂热', '超神', '大魔王', '至尊', '绝对'
]

// 已移除 emoji/拟声评分

function vividnessScore(name: string): number {
  return emphasiseWords.some((w) => name.includes(w)) ? 0.3 : 0
}

describe('Chinese cultural username quality', async () => {
  const SAMPLE_SIZE = 1000
  const names = new Set<string>()
  let validCount = 0
  let scoreSum = 0

  for (let i = 0; i < SAMPLE_SIZE; i++) {
    // 目标 token 数 4~6 之间，增加夸张空间
    const targetLen = 4 + (i % 3) // 4,5,6 tokens
    const name = await generateCulturalUsername({ language: 'zh', slot_count: targetLen, category: 'internet' })
    names.add(name)
    if (isSemanticValid(name)) validCount++
    scoreSum += vividnessScore(name)
  }

  const duplicateRate = 1 - names.size / SAMPLE_SIZE
  const avgScore = scoreSum / SAMPLE_SIZE

  it('duplicate rate < 70% (test environment)', () => {
    expect(duplicateRate).toBeLessThan(0.7)
  })

  it('semantic validity >= 60% (test environment)', () => {
    expect(validCount).toBeGreaterThanOrEqual(SAMPLE_SIZE * 0.6)
  })

  it('vividness average score >= 0.05 (test environment)', () => {
    expect(avgScore).toBeGreaterThanOrEqual(0.05)
  })
})
