/**
 * 任务4: 高重复率问题诊断与解决
 * 深入分析重复率问题的根本原因并提供解决方案
 */

const fs = require('fs');

console.log('📋 任务4: 高重复率问题诊断与解决');
console.log('='.repeat(80));

// V5引擎模拟类 (用于重复率测试)
class V5DiagnosticEngine {
  constructor() {
    this.elementLibrary = this.buildElementLibrary();
    this.generationPatterns = this.buildGenerationPatterns();
    this.generationHistory = []; // 记录生成历史
    this.elementUsageCount = {}; // 记录元素使用频率
  }
  
  buildElementLibrary() {
    return {
      subjects: {
        古代人物: ['贫僧', '道士', '书生', '侠客', '状元', '秀才', '举人', '进士', '太监', '皇帝', '将军', '丞相'],
        抽象概念: ['快乐', '悲伤', '愤怒', '温柔', '智慧', '勇气', '希望', '梦想', '孤独', '焦虑', '兴奋', '平静'],
        天体宇宙: ['月亮', '太阳', '星星', '银河', '宇宙', '黑洞', '彗星', '流星', '行星', '恒星', '星云', '陨石']
      },
      actions: {
        日常行为: ['吃', '睡', '玩', '工作', '学习', '跑步', '游泳', '阅读', '写作', '思考', '发呆', '散步'],
        网络行为: ['直播', '带货', '刷视频', '点赞', '转发', '评论', '分享', '关注', '取关', '拉黑', '举报', '私信'],
        现代生活: ['洗头', '化妆', '健身', '减肥', '加班', '购物', '旅游', '聚餐', '约会', '相亲', '搬家', '装修']
      },
      modifiers: {
        权威级别: ['首席', '高级', '资深', '专业', '认证', '特级', '顶级', '超级', '终极', '至尊', '王牌', '精英']
      },
      connectors: {
        对比转折: ['但', '却', '然而', '不过', '虽然', '尽管', '即使', '哪怕', '纵然', '纵使', '就算', '便是']
      }
    };
  }
  
  buildGenerationPatterns() {
    return [
      { id: 'identity_elevation', name: '身份升维包装', weight: 0.96 },
      { id: 'contradiction_unity', name: '矛盾统一', weight: 0.94 },
      { id: 'temporal_displacement', name: '时空错位重组', weight: 0.95 },
      { id: 'service_personification', name: '服务拟人化', weight: 0.92 },
      { id: 'tech_expression', name: '技术化表达', weight: 0.91 },
      { id: 'homophone_creative', name: '创意谐音', weight: 0.95 }
    ];
  }
  
  // 带偏向性的随机选择 (模拟可能的问题)
  biasedRandomSelect(array, bias = 0.3) {
    // 模拟算法偏向性：前几个元素被选中的概率更高
    const biasedIndex = Math.random() < bias ? 
      Math.floor(Math.random() * Math.min(3, array.length)) : 
      Math.floor(Math.random() * array.length);
    
    const selected = array[biasedIndex];
    
    // 记录元素使用频率
    this.elementUsageCount[selected] = (this.elementUsageCount[selected] || 0) + 1;
    
    return selected;
  }
  
  // 正常随机选择
  normalRandomSelect(array) {
    const selected = array[Math.floor(Math.random() * array.length)];
    this.elementUsageCount[selected] = (this.elementUsageCount[selected] || 0) + 1;
    return selected;
  }
  
  generateByPattern(patternId, useBiasedSelection = false) {
    const pattern = this.generationPatterns.find(p => p.id === patternId);
    if (!pattern) return null;
    
    const selectMethod = useBiasedSelection ? this.biasedRandomSelect.bind(this) : this.normalRandomSelect.bind(this);
    
    let username = '';
    let elementsUsed = [];
    
    try {
      switch (patternId) {
        case 'identity_elevation':
          const authority = selectMethod(this.elementLibrary.modifiers.权威级别);
          const behavior = selectMethod(this.elementLibrary.actions.日常行为);
          const suffix = selectMethod(['官', '师', '专家', '大使', '代表', '委员', '顾问', '总监', '主任', '经理', '主管', '负责人']);
          username = `${authority}${behavior}${suffix}`;
          elementsUsed = [authority, behavior, suffix];
          break;
          
        case 'contradiction_unity':
          const positive = selectMethod(['温柔', '理性', '冷静', '勤奋', '节约', '听劝', '乐观', '自信', '坚强', '独立', '谦虚', '耐心']);
          const connector = selectMethod(this.elementLibrary.connectors.对比转折);
          const negative = selectMethod(['强硬', '感性', '冲动', '懒惰', '挥霍', '反骨', '悲观', '自卑', '脆弱', '依赖', '骄傲', '急躁']);
          username = `${positive}${connector}${negative}`;
          elementsUsed = [positive, connector, negative];
          break;
          
        case 'temporal_displacement':
          const ancient = selectMethod(this.elementLibrary.subjects.古代人物);
          const modern = selectMethod([
            ...this.elementLibrary.actions.网络行为,
            ...this.elementLibrary.actions.现代生活
          ]);
          username = `${ancient}${modern}`;
          elementsUsed = [ancient, modern];
          break;
          
        case 'service_personification':
          const concept = selectMethod([
            ...this.elementLibrary.subjects.抽象概念,
            ...this.elementLibrary.subjects.天体宇宙
          ]);
          const service = selectMethod(['邮递员', '收集员', '配送员', '制造商', '贩卖机', '专卖店', '客服', '导航员', '修理工', '设计师', '顾问', '助手']);
          username = `${concept}${service}`;
          elementsUsed = [concept, service];
          break;
          
        case 'tech_expression':
          const lifeConcept = selectMethod(['人生', '梦想', '快乐', '悲伤', '爱情', '友情', '工作', '学习', '生活', '未来', '过去', '现在']);
          const techTerm = selectMethod(['正在缓冲', '连接超时', '服务器宕机', '数据库损坏', '404未找到', '503不可用', '系统维护', '正在加载', '网络异常', '权限不足', '访问拒绝', '请求超时']);
          username = `${lifeConcept}${techTerm}`;
          elementsUsed = [lifeConcept, techTerm];
          break;
          
        case 'homophone_creative':
          const homophones = [
            { original: '知识就是力量', replacement: '芝士就是力量' },
            { original: '没心没肺', replacement: '莓心没肺' },
            { original: '无恶不作', replacement: '无饿不作' },
            { original: '有压力很大', replacement: '有鸭梨很大' },
            { original: '一见钟情', replacement: '一见粽情' },
            { original: '心想事成', replacement: '薪想事成' },
            { original: '马到成功', replacement: '码到成功' },
            { original: '天马行空', replacement: '天码行空' },
            { original: '年年有余', replacement: '年年有鱼' },
            { original: '步步高升', replacement: '布布高升' },
            { original: '一帆风顺', replacement: '一番风顺' },
            { original: '财源广进', replacement: '菜源广进' }
          ];
          const selected = selectMethod(homophones);
          username = selected.replacement;
          elementsUsed = [selected.original, '→', selected.replacement];
          break;
          
        default:
          return null;
      }
      
      // 记录生成历史
      this.generationHistory.push({
        username,
        pattern: pattern.name,
        elementsUsed,
        timestamp: Date.now()
      });
      
      return {
        username,
        pattern: pattern.name,
        elements_used: elementsUsed,
        generation_time: Date.now()
      };
      
    } catch (error) {
      return null;
    }
  }
  
  // 检查重复率
  checkDuplicationRate() {
    const usernames = this.generationHistory.map(h => h.username);
    const uniqueUsernames = [...new Set(usernames)];
    const duplicateCount = usernames.length - uniqueUsernames.length;
    const duplicationRate = usernames.length > 0 ? (duplicateCount / usernames.length * 100).toFixed(2) : 0;
    
    return {
      total: usernames.length,
      unique: uniqueUsernames.length,
      duplicates: duplicateCount,
      duplicationRate: parseFloat(duplicationRate)
    };
  }
  
  // 分析元素使用偏向性
  analyzeElementBias() {
    const sortedElements = Object.entries(this.elementUsageCount)
      .sort(([,a], [,b]) => b - a);
    
    const totalUsage = Object.values(this.elementUsageCount).reduce((sum, count) => sum + count, 0);
    const top10Elements = sortedElements.slice(0, 10);
    const top10Usage = top10Elements.reduce((sum, [, count]) => sum + count, 0);
    const top10Percentage = totalUsage > 0 ? (top10Usage / totalUsage * 100).toFixed(1) : 0;
    
    return {
      totalElements: sortedElements.length,
      totalUsage,
      top10Elements,
      top10Usage,
      top10Percentage: parseFloat(top10Percentage),
      bias: parseFloat(top10Percentage) > 50 // 如果前10个元素占用超过50%，认为存在偏向性
    };
  }
}

// 读取示例文件中的用户名
function loadExampleUsernames() {
  try {
    const content = fs.readFileSync('docs/name_example', 'utf8');
    const usernames = new Set();
    
    const lines = content.split('\n');
    lines.forEach(line => {
      line = line.trim();
      
      if (!line || line.startsWith('#') || line.startsWith('*') || 
          line.startsWith('-') || line.startsWith('=') || 
          line.includes('有趣之处') || line.includes('适合') ||
          line.includes('类') || line.length < 2) {
        return;
      }
      
      const numberedMatch = line.match(/^\d+\.\s*\*?\*?([^*()（）]+)\*?\*?/);
      if (numberedMatch) {
        const username = numberedMatch[1].trim();
        if (username && !username.includes('**') && !username.includes('有趣')) {
          usernames.add(username);
        }
        return;
      }
      
      const starMatch = line.match(/\*\*([^*]+)\*\*/);
      if (starMatch) {
        const username = starMatch[1].trim();
        if (username && !username.includes('有趣') && !username.includes('适合')) {
          usernames.add(username);
        }
        return;
      }
      
      if (line.length >= 2 && line.length <= 20 && 
          !line.includes('：') && !line.includes(':') && 
          !line.includes('。') && !line.includes('，') &&
          !line.includes('(') && !line.includes('（') &&
          !line.includes('这类') && !line.includes('适合') &&
          !line.includes('形容') && !line.includes('描述')) {
        usernames.add(line);
      }
    });
    
    return Array.from(usernames);
  } catch (error) {
    console.error('读取示例文件失败:', error.message);
    return [];
  }
}

// 执行重复率诊断测试
async function runDuplicationDiagnosis() {
  console.log('🔍 开始高重复率问题诊断\n');
  
  // 1. 读取示例文件
  console.log('📖 读取示例文件...');
  const exampleUsernames = loadExampleUsernames();
  console.log(`   示例用户名数量: ${exampleUsernames.length}个`);
  
  // 2. 模拟实际使用场景测试
  console.log('\n🧪 模拟实际使用场景测试...');
  
  const testScenarios = [
    {
      name: '正常随机算法',
      description: '使用完全随机的元素选择',
      useBiasedSelection: false,
      testCount: 100
    },
    {
      name: '偏向性算法',
      description: '模拟存在选择偏向性的算法',
      useBiasedSelection: true,
      testCount: 100
    },
    {
      name: '高频使用场景',
      description: '模拟用户高频使用相同参数',
      useBiasedSelection: false,
      testCount: 200,
      sameParams: true
    }
  ];
  
  const testResults = [];
  
  for (const scenario of testScenarios) {
    console.log(`\n📋 测试场景: ${scenario.name}`);
    console.log(`   ${scenario.description}`);
    
    const engine = new V5DiagnosticEngine();
    const patterns = ['identity_elevation', 'contradiction_unity', 'temporal_displacement', 'service_personification', 'tech_expression', 'homophone_creative'];
    
    // 执行生成测试
    for (let i = 0; i < scenario.testCount; i++) {
      const pattern = scenario.sameParams ? 'identity_elevation' : patterns[Math.floor(Math.random() * patterns.length)];
      engine.generateByPattern(pattern, scenario.useBiasedSelection);
    }
    
    // 分析结果
    const duplicationStats = engine.checkDuplicationRate();
    const biasAnalysis = engine.analyzeElementBias();
    
    // 检查与示例文件的重复
    const generatedUsernames = engine.generationHistory.map(h => h.username);
    const exampleSet = new Set(exampleUsernames);
    const exampleDuplicates = generatedUsernames.filter(username => exampleSet.has(username));
    const exampleDuplicationRate = (exampleDuplicates.length / generatedUsernames.length * 100).toFixed(2);
    
    const result = {
      scenario: scenario.name,
      description: scenario.description,
      testCount: scenario.testCount,
      duplicationStats,
      biasAnalysis,
      exampleDuplicates: exampleDuplicates.length,
      exampleDuplicationRate: parseFloat(exampleDuplicationRate),
      generatedSamples: generatedUsernames.slice(0, 10) // 前10个样本
    };
    
    testResults.push(result);
    
    console.log(`   生成总数: ${duplicationStats.total}个`);
    console.log(`   唯一数量: ${duplicationStats.unique}个`);
    console.log(`   内部重复率: ${duplicationStats.duplicationRate}%`);
    console.log(`   与示例重复: ${exampleDuplicates.length}个 (${exampleDuplicationRate}%)`);
    console.log(`   元素偏向性: ${biasAnalysis.bias ? '存在' : '不存在'} (前10元素占${biasAnalysis.top10Percentage}%)`);
  }
  
  // 3. 根本原因分析
  console.log('\n🔍 根本原因分析');
  console.log('='.repeat(60));
  
  const rootCauses = analyzeRootCauses(testResults, exampleUsernames);
  
  rootCauses.forEach((cause, index) => {
    const severityIcon = cause.severity === 'high' ? '🔴' : cause.severity === 'medium' ? '🟡' : '🟢';
    console.log(`\n   ${index + 1}. ${severityIcon} ${cause.title} (${cause.severity}严重性)`);
    console.log(`      问题: ${cause.problem}`);
    console.log(`      证据: ${cause.evidence}`);
    console.log(`      影响: ${cause.impact}`);
  });
  
  // 4. 解决方案
  console.log('\n💡 解决方案');
  console.log('='.repeat(60));
  
  const solutions = generateSolutions(rootCauses, testResults);
  
  ['immediate', 'shortTerm', 'longTerm'].forEach(timeframe => {
    const timeframeName = {
      'immediate': '立即实施',
      'shortTerm': '短期改进',
      'longTerm': '长期优化'
    }[timeframe];
    
    console.log(`\n📅 ${timeframeName}:`);
    
    if (solutions[timeframe].length > 0) {
      solutions[timeframe].forEach((solution, index) => {
        const priorityIcon = solution.priority === 'high' ? '🔴' : 
                           solution.priority === 'medium' ? '🟡' : '🟢';
        console.log(`\n   ${index + 1}. ${priorityIcon} ${solution.title}`);
        console.log(`      方案: ${solution.description}`);
        console.log(`      实现: ${solution.implementation}`);
        console.log(`      预期效果: ${solution.expectedImpact}`);
      });
    } else {
      console.log('   ✅ 暂无需要实施的方案');
    }
  });
  
  // 5. 保存诊断结果
  const diagnosisResult = {
    timestamp: new Date().toISOString(),
    exampleUsernamesCount: exampleUsernames.length,
    testResults,
    rootCauses,
    solutions,
    summary: {
      worstDuplicationRate: Math.max(...testResults.map(r => r.duplicationStats.duplicationRate)),
      worstExampleDuplicationRate: Math.max(...testResults.map(r => r.exampleDuplicationRate)),
      biasDetected: testResults.some(r => r.biasAnalysis.bias),
      criticalIssues: rootCauses.filter(c => c.severity === 'high').length,
      totalSolutions: solutions.immediate.length + solutions.shortTerm.length + solutions.longTerm.length
    }
  };
  
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const filename = `task4-duplication-diagnosis-${timestamp}.json`;
  
  fs.writeFileSync(filename, JSON.stringify(diagnosisResult, null, 2));
  console.log(`\n💾 诊断结果已保存到: ${filename}`);
  
  return diagnosisResult;
}

// 分析根本原因
function analyzeRootCauses(testResults, exampleUsernames) {
  const causes = [];
  
  // 检查词库规模问题
  const maxDuplicationRate = Math.max(...testResults.map(r => r.duplicationStats.duplicationRate));
  if (maxDuplicationRate > 15) {
    causes.push({
      title: '词库规模不足',
      severity: 'high',
      problem: '词库中的元素数量不足以支持大量不重复的组合生成',
      evidence: `最高内部重复率达到${maxDuplicationRate}%，超过15%的可接受阈值`,
      impact: '用户在短时间内多次生成时容易看到重复的用户名，影响体验',
      category: 'vocabulary_size'
    });
  }
  
  // 检查算法偏向性问题
  const biasedTest = testResults.find(r => r.scenario === '偏向性算法');
  if (biasedTest && biasedTest.biasAnalysis.bias) {
    causes.push({
      title: '随机算法存在偏向性',
      severity: 'high',
      problem: '随机选择算法倾向于选择特定的元素，导致某些组合出现频率过高',
      evidence: `偏向性测试中前10个元素占用${biasedTest.biasAnalysis.top10Percentage}%的使用率`,
      impact: '生成结果缺乏多样性，用户容易看到相似的用户名模式',
      category: 'algorithm_bias'
    });
  }
  
  // 检查示例文件重复问题
  const maxExampleDuplication = Math.max(...testResults.map(r => r.exampleDuplicationRate));
  if (maxExampleDuplication > 5) {
    causes.push({
      title: '与示例文件内容重复',
      severity: 'medium',
      problem: '生成的用户名与示例文件中的内容存在重复',
      evidence: `最高示例重复率达到${maxExampleDuplication}%`,
      impact: '用户可能认为系统只是在重复已有的示例，缺乏创新性',
      category: 'example_duplication'
    });
  }
  
  // 检查相同参数重复问题
  const sameParamsTest = testResults.find(r => r.scenario === '高频使用场景');
  if (sameParamsTest && sameParamsTest.duplicationStats.duplicationRate > 20) {
    causes.push({
      title: '相同参数组合重复率过高',
      severity: 'medium',
      problem: '用户使用相同参数多次生成时，重复率显著增加',
      evidence: `相同参数测试重复率达到${sameParamsTest.duplicationStats.duplicationRate}%`,
      impact: '用户偏好特定风格时，体验会快速下降',
      category: 'same_params_duplication'
    });
  }
  
  return causes;
}

// 生成解决方案
function generateSolutions(rootCauses, testResults) {
  const solutions = {
    immediate: [],
    shortTerm: [],
    longTerm: []
  };
  
  rootCauses.forEach(cause => {
    switch (cause.category) {
      case 'vocabulary_size':
        solutions.immediate.push({
          priority: 'high',
          title: '扩展词库规模',
          description: '立即扩展各类词库的元素数量',
          implementation: '将每个词库的元素数量增加2-3倍，特别是高频使用的权威级别、行为动词、职位后缀等',
          expectedImpact: '预期将重复率降低到10%以下'
        });
        break;
        
      case 'algorithm_bias':
        solutions.immediate.push({
          priority: 'high',
          title: '修复随机算法偏向性',
          description: '实现真正的均匀随机选择算法',
          implementation: '使用加权随机或洗牌算法，确保每个元素被选中的概率相等',
          expectedImpact: '消除元素选择偏向性，提高生成多样性'
        });
        break;
        
      case 'example_duplication':
        solutions.shortTerm.push({
          priority: 'medium',
          title: '实现示例去重检查',
          description: '在生成过程中检查并避免与示例文件重复',
          implementation: '建立示例用户名黑名单，生成时进行检查和重新生成',
          expectedImpact: '完全消除与示例文件的重复'
        });
        break;
        
      case 'same_params_duplication':
        solutions.shortTerm.push({
          priority: 'medium',
          title: '实现智能去重机制',
          description: '记录最近生成的用户名，避免短期内重复',
          implementation: '维护最近100个生成结果的缓存，新生成时检查重复',
          expectedImpact: '显著降低短期内的重复率'
        });
        break;
    }
  });
  
  // 通用长期解决方案
  solutions.longTerm.push({
    priority: 'medium',
    title: '实现动态词库扩展',
    description: '基于用户反馈和使用数据动态扩展词库',
    implementation: '收集用户喜好数据，定期添加新的元素和组合模式',
    expectedImpact: '持续提升生成质量和多样性'
  });
  
  solutions.longTerm.push({
    priority: 'low',
    title: '实现个性化生成',
    description: '基于用户历史偏好提供个性化的生成结果',
    implementation: '学习用户的选择模式，提供定制化的元素组合',
    expectedImpact: '提升用户满意度和使用粘性'
  });
  
  return solutions;
}

// 运行诊断
runDuplicationDiagnosis().then(result => {
  console.log('\n🏁 任务4: 高重复率问题诊断与解决完成');
  console.log(`最高重复率: ${result.summary.worstDuplicationRate}%`);
  console.log(`关键问题: ${result.summary.criticalIssues}个`);
  console.log(`解决方案: ${result.summary.totalSolutions}个`);
}).catch(error => {
  console.error('任务4执行失败:', error);
});
