{"timestamp": "2025-06-14T15:59:59.459Z", "exampleUsernamesCount": 168, "testResults": [{"scenario": "正常随机算法", "description": "使用完全随机的元素选择", "testCount": 100, "duplicationStats": {"total": 100, "unique": 90, "duplicates": 10, "duplicationRate": 10}, "biasAnalysis": {"totalElements": 116, "totalUsage": 212, "top10Elements": [["[object Object]", 21], ["侠客", 5], ["导航员", 4], ["高级", 4], ["工作", 4], ["专家", 4], ["玩", 4], ["顾问", 3], ["快乐", 3], ["委员", 3]], "top10Usage": 55, "top10Percentage": 25.9, "bias": false}, "exampleDuplicates": 3, "exampleDuplicationRate": 3, "generatedSamples": ["有鸭梨很大", "超级发呆顾问", "乐观却依赖", "焦虑导航员", "星云邮递员", "快乐导航员", "理性尽管懒惰", "过去服务器宕机", "高级写作委员", "秀才减肥"]}, {"scenario": "偏向性算法", "description": "模拟存在选择偏向性的算法", "testCount": 100, "duplicationStats": {"total": 100, "unique": 92, "duplicates": 8, "duplicationRate": 8}, "biasAnalysis": {"totalElements": 106, "totalUsage": 212, "top10Elements": [["[object Object]", 19], ["理性", 7], ["邮递员", 6], ["梦想", 5], ["然而", 4], ["感性", 4], ["快乐", 4], ["刷视频", 4], ["书生", 4], ["睡", 4]], "top10Usage": 61, "top10Percentage": 28.8, "bias": false}, "exampleDuplicates": 7, "exampleDuplicationRate": 7, "generatedSamples": ["布布高升", "理性然而感性", "快乐收集员", "冷静然而冲动", "悲伤邮递员", "理性却强硬", "布布高升", "现在正在缓冲", "梦想数据库损坏", "流星邮递员"]}, {"scenario": "高频使用场景", "description": "模拟用户高频使用相同参数", "testCount": 200, "duplicationStats": {"total": 200, "unique": 187, "duplicates": 13, "duplicationRate": 6.5}, "biasAnalysis": {"totalElements": 36, "totalUsage": 600, "top10Elements": [["总监", 25], ["工作", 25], ["终极", 23], ["负责人", 23], ["高级", 22], ["师", 22], ["吃", 20], ["专业", 20], ["顶级", 20], ["学习", 19]], "top10Usage": 219, "top10Percentage": 36.5, "bias": false}, "exampleDuplicates": 0, "exampleDuplicationRate": 0, "generatedSamples": ["特级学习主任", "资深跑步主管", "王牌睡总监", "首席散步主任", "特级工作委员", "高级思考代表", "高级阅读总监", "王牌吃委员", "首席思考总监", "资深发呆主任"]}], "rootCauses": [{"title": "与示例文件内容重复", "severity": "medium", "problem": "生成的用户名与示例文件中的内容存在重复", "evidence": "最高示例重复率达到7%", "impact": "用户可能认为系统只是在重复已有的示例，缺乏创新性", "category": "example_duplication"}], "solutions": {"immediate": [], "shortTerm": [{"priority": "medium", "title": "实现示例去重检查", "description": "在生成过程中检查并避免与示例文件重复", "implementation": "建立示例用户名黑名单，生成时进行检查和重新生成", "expectedImpact": "完全消除与示例文件的重复"}], "longTerm": [{"priority": "medium", "title": "实现动态词库扩展", "description": "基于用户反馈和使用数据动态扩展词库", "implementation": "收集用户喜好数据，定期添加新的元素和组合模式", "expectedImpact": "持续提升生成质量和多样性"}, {"priority": "low", "title": "实现个性化生成", "description": "基于用户历史偏好提供个性化的生成结果", "implementation": "学习用户的选择模式，提供定制化的元素组合", "expectedImpact": "提升用户满意度和使用粘性"}]}, "summary": {"worstDuplicationRate": 10, "worstExampleDuplicationRate": 7, "biasDetected": false, "criticalIssues": 0, "totalSolutions": 3}}