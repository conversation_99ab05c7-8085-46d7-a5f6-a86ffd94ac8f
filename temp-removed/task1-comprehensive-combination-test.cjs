/**
 * 任务1: 全面组合测试与生成
 * 测试V5 API支持的所有参数组合
 */

const fs = require('fs');

console.log('📋 任务1: V5 API全面组合测试与生成');
console.log('='.repeat(80));

// V5引擎模拟类 (基于实际API代码)
class V5TestEngine {
  constructor() {
    this.elementLibrary = this.buildElementLibrary();
    this.generationPatterns = this.buildGenerationPatterns();
  }
  
  buildElementLibrary() {
    return {
      subjects: {
        古代人物: ['贫僧', '道士', '书生', '侠客', '状元', '秀才', '举人', '进士', '太监', '皇帝', '将军', '丞相'],
        抽象概念: ['快乐', '悲伤', '愤怒', '温柔', '智慧', '勇气', '希望', '梦想', '孤独', '焦虑', '兴奋', '平静'],
        天体宇宙: ['月亮', '太阳', '星星', '银河', '宇宙', '黑洞', '彗星', '流星', '行星', '恒星', '星云', '陨石']
      },
      actions: {
        日常行为: ['吃', '睡', '玩', '工作', '学习', '跑步', '游泳', '阅读', '写作', '思考', '发呆', '散步'],
        网络行为: ['直播', '带货', '刷视频', '点赞', '转发', '评论', '分享', '关注', '取关', '拉黑', '举报', '私信'],
        现代生活: ['洗头', '化妆', '健身', '减肥', '加班', '购物', '旅游', '聚餐', '约会', '相亲', '搬家', '装修']
      },
      modifiers: {
        权威级别: ['首席', '高级', '资深', '专业', '认证', '特级', '顶级', '超级', '终极', '至尊', '王牌', '精英']
      },
      connectors: {
        对比转折: ['但', '却', '然而', '不过', '虽然', '尽管', '即使', '哪怕', '纵然', '纵使', '就算', '便是']
      }
    };
  }
  
  buildGenerationPatterns() {
    return [
      { id: 'identity_elevation', name: '身份升维包装', weight: 0.96, type: 'elevation' },
      { id: 'contradiction_unity', name: '矛盾统一', weight: 0.94, type: 'contradiction' },
      { id: 'temporal_displacement', name: '时空错位重组', weight: 0.95, type: 'misplacement' },
      { id: 'service_personification', name: '服务拟人化', weight: 0.92, type: 'personification' },
      { id: 'tech_expression', name: '技术化表达', weight: 0.91, type: 'tech' },
      { id: 'homophone_creative', name: '创意谐音', weight: 0.95, type: 'homophone' }
    ];
  }
  
  randomSelect(array) {
    return array[Math.floor(Math.random() * array.length)];
  }
  
  generateByPattern(patternId) {
    const pattern = this.generationPatterns.find(p => p.id === patternId);
    if (!pattern) return null;
    
    let username = '';
    let elementsUsed = [];
    
    try {
      switch (patternId) {
        case 'identity_elevation':
          const authority = this.randomSelect(this.elementLibrary.modifiers.权威级别);
          const behavior = this.randomSelect(this.elementLibrary.actions.日常行为);
          const suffix = this.randomSelect(['官', '师', '专家', '大使', '代表', '委员', '顾问', '总监', '主任', '经理', '主管', '负责人']);
          username = `${authority}${behavior}${suffix}`;
          elementsUsed = [authority, behavior, suffix];
          break;
          
        case 'contradiction_unity':
          const positive = this.randomSelect(['温柔', '理性', '冷静', '勤奋', '节约', '听劝', '乐观', '自信', '坚强', '独立', '谦虚', '耐心']);
          const connector = this.randomSelect(this.elementLibrary.connectors.对比转折);
          const negative = this.randomSelect(['强硬', '感性', '冲动', '懒惰', '挥霍', '反骨', '悲观', '自卑', '脆弱', '依赖', '骄傲', '急躁']);
          username = `${positive}${connector}${negative}`;
          elementsUsed = [positive, connector, negative];
          break;
          
        case 'temporal_displacement':
          const ancient = this.randomSelect(this.elementLibrary.subjects.古代人物);
          const modern = this.randomSelect([
            ...this.elementLibrary.actions.网络行为,
            ...this.elementLibrary.actions.现代生活
          ]);
          username = `${ancient}${modern}`;
          elementsUsed = [ancient, modern];
          break;
          
        case 'service_personification':
          const concept = this.randomSelect([
            ...this.elementLibrary.subjects.抽象概念,
            ...this.elementLibrary.subjects.天体宇宙
          ]);
          const service = this.randomSelect(['邮递员', '收集员', '配送员', '制造商', '贩卖机', '专卖店', '客服', '导航员', '修理工', '设计师', '顾问', '助手']);
          username = `${concept}${service}`;
          elementsUsed = [concept, service];
          break;
          
        case 'tech_expression':
          const lifeConcept = this.randomSelect(['人生', '梦想', '快乐', '悲伤', '爱情', '友情', '工作', '学习', '生活', '未来', '过去', '现在']);
          const techTerm = this.randomSelect(['正在缓冲', '连接超时', '服务器宕机', '数据库损坏', '404未找到', '503不可用', '系统维护', '正在加载', '网络异常', '权限不足', '访问拒绝', '请求超时']);
          username = `${lifeConcept}${techTerm}`;
          elementsUsed = [lifeConcept, techTerm];
          break;
          
        case 'homophone_creative':
          const homophones = [
            { original: '知识就是力量', replacement: '芝士就是力量' },
            { original: '没心没肺', replacement: '莓心没肺' },
            { original: '无恶不作', replacement: '无饿不作' },
            { original: '有压力很大', replacement: '有鸭梨很大' },
            { original: '一见钟情', replacement: '一见粽情' },
            { original: '心想事成', replacement: '薪想事成' },
            { original: '马到成功', replacement: '码到成功' },
            { original: '天马行空', replacement: '天码行空' },
            { original: '年年有余', replacement: '年年有鱼' },
            { original: '步步高升', replacement: '布布高升' },
            { original: '一帆风顺', replacement: '一番风顺' },
            { original: '财源广进', replacement: '菜源广进' }
          ];
          const selected = this.randomSelect(homophones);
          username = selected.replacement;
          elementsUsed = [selected.original, '→', selected.replacement];
          break;
          
        default:
          return null;
      }
      
      const creativity_assessment = this.assessCreativity(username, pattern);
      
      return {
        username,
        pattern: pattern.name,
        formula: this.getPatternFormula(patternId),
        elements_used: elementsUsed,
        creativity_assessment,
        generation_time: Date.now()
      };
      
    } catch (error) {
      return null;
    }
  }
  
  getPatternFormula(patternId) {
    const formulas = {
      'identity_elevation': '[权威修饰] + [日常行为] + [职位后缀]',
      'contradiction_unity': '[正面特质] + [转折连词] + [负面特质]',
      'temporal_displacement': '[古代元素] + [现代行为/物品]',
      'service_personification': '[抽象概念] + [服务角色]',
      'tech_expression': '[生活概念] + [技术术语]',
      'homophone_creative': '[原词] → [谐音替换]'
    };
    return formulas[patternId] || '[元素组合]';
  }
  
  assessCreativity(username, pattern) {
    // 基于模式类型的质量基准
    const baseQuality = {
      'elevation': { novelty: 0.85, relevance: 0.90, comprehensibility: 0.95, memorability: 0.85 },
      'contradiction': { novelty: 0.92, relevance: 0.85, comprehensibility: 0.88, memorability: 0.90 },
      'misplacement': { novelty: 0.95, relevance: 0.82, comprehensibility: 0.85, memorability: 0.88 },
      'personification': { novelty: 0.88, relevance: 0.85, comprehensibility: 0.90, memorability: 0.82 },
      'tech': { novelty: 0.90, relevance: 0.88, comprehensibility: 0.85, memorability: 0.85 },
      'homophone': { novelty: 0.95, relevance: 0.80, comprehensibility: 0.92, memorability: 0.95 }
    };
    
    const base = baseQuality[pattern.type] || baseQuality['elevation'];
    
    const novelty = Math.min(1.0, base.novelty + (Math.random() - 0.5) * 0.1);
    const relevance = Math.min(1.0, base.relevance + (Math.random() - 0.5) * 0.1);
    const comprehensibility = Math.min(1.0, base.comprehensibility + (Math.random() - 0.5) * 0.1);
    const memorability = Math.min(1.0, base.memorability + (Math.random() - 0.5) * 0.1);
    
    const overall_score = novelty * 0.3 + relevance * 0.25 + comprehensibility * 0.25 + memorability * 0.2;
    
    return {
      novelty,
      relevance,
      comprehensibility,
      memorability,
      overall_score,
      explanation: `${pattern.name}: 新颖性${(novelty*100).toFixed(0)}%, 相关性${(relevance*100).toFixed(0)}%, 可理解性${(comprehensibility*100).toFixed(0)}%, 记忆性${(memorability*100).toFixed(0)}%`
    };
  }
}

// 智能模式选择
function selectOptimalPattern(style, themes, complexity) {
  const patternMap = {
    'modern': ['temporal_displacement', 'identity_elevation', 'tech_expression'],
    'cool': ['contradiction_unity', 'temporal_displacement', 'tech_expression'],
    'playful': ['service_personification', 'homophone_creative', 'identity_elevation'],
    'traditional': ['temporal_displacement', 'service_personification', 'homophone_creative'],
    'elegant': ['contradiction_unity', 'service_personification', 'identity_elevation']
  };
  
  const themeBonus = {
    'tech': ['tech_expression', 'temporal_displacement'],
    'workplace': ['identity_elevation', 'contradiction_unity'],
    'humor': ['homophone_creative', 'contradiction_unity'],
    'creative': ['service_personification', 'homophone_creative'],
    'culture': ['temporal_displacement', 'service_personification']
  };
  
  let candidatePatterns = patternMap[style] || patternMap['modern'];
  
  themes.forEach(theme => {
    if (themeBonus[theme]) {
      candidatePatterns = [...candidatePatterns, ...themeBonus[theme]];
    }
  });
  
  candidatePatterns = [...new Set(candidatePatterns)];
  
  if (complexity >= 4) {
    const complexPatterns = ['temporal_displacement', 'contradiction_unity', 'tech_expression'];
    candidatePatterns = candidatePatterns.filter(p => complexPatterns.includes(p));
  } else if (complexity <= 2) {
    const simplePatterns = ['homophone_creative', 'service_personification', 'identity_elevation'];
    candidatePatterns = candidatePatterns.filter(p => simplePatterns.includes(p));
  }
  
  if (candidatePatterns.length > 0) {
    return candidatePatterns[Math.floor(Math.random() * candidatePatterns.length)];
  }
  
  return 'identity_elevation';
}

// 生成所有可能的主题组合
function generateThemeCombinations() {
  const themes = ['tech', 'workplace', 'humor', 'creative', 'culture'];
  const combinations = [];
  
  // 单主题
  themes.forEach(theme => {
    combinations.push([theme]);
  });
  
  // 双主题
  for (let i = 0; i < themes.length; i++) {
    for (let j = i + 1; j < themes.length; j++) {
      combinations.push([themes[i], themes[j]]);
    }
  }
  
  // 三主题 (选择性添加，避免组合过多)
  const popularTriples = [
    ['tech', 'humor', 'creative'],
    ['workplace', 'humor', 'culture'],
    ['tech', 'workplace', 'humor']
  ];
  combinations.push(...popularTriples);
  
  return combinations;
}

// 执行全面组合测试
async function runComprehensiveTest() {
  console.log('🚀 开始全面组合测试\n');
  
  const engine = new V5TestEngine();
  const styles = ['modern', 'cool', 'playful', 'traditional', 'elegant'];
  const themeCombinations = generateThemeCombinations();
  const complexities = [1, 2, 3, 4, 5];
  const patterns = [null, 'identity_elevation', 'contradiction_unity', 'temporal_displacement', 'service_personification', 'tech_expression', 'homophone_creative'];
  const counts = [1, 3, 5];
  
  console.log('📊 测试范围统计:');
  console.log(`   风格: ${styles.length}种`);
  console.log(`   主题组合: ${themeCombinations.length}种`);
  console.log(`   复杂度: ${complexities.length}级`);
  console.log(`   模式: ${patterns.length}种 (含智能选择)`);
  console.log(`   数量: ${counts.length}种`);
  
  const totalCombinations = styles.length * themeCombinations.length * complexities.length * patterns.length * counts.length;
  console.log(`   理论总组合: ${totalCombinations}种`);
  console.log(`   每组合生成: 10个样本`);
  console.log(`   预计总生成: ${totalCombinations * 10}个用户名\n`);
  
  const testResults = [];
  const allGeneratedUsernames = [];
  let totalTests = 0;
  let successfulTests = 0;
  let totalGenerationTime = 0;
  
  // 执行测试 (为了性能考虑，我们选择代表性组合)
  console.log('🧪 执行代表性组合测试...\n');
  
  // 1. 基础风格×主题测试
  console.log('📋 1. 基础风格×主题组合测试');
  console.log('-'.repeat(60));
  
  for (const style of styles) {
    for (const themes of themeCombinations.slice(0, 8)) { // 限制主题组合数量
      totalTests++;
      const startTime = Date.now();
      
      try {
        const results = [];
        for (let i = 0; i < 10; i++) {
          const selectedPattern = selectOptimalPattern(style, themes, 3);
          const result = engine.generateByPattern(selectedPattern);
          if (result) {
            results.push(result);
            allGeneratedUsernames.push(result.username);
          }
        }
        
        const endTime = Date.now();
        const responseTime = endTime - startTime;
        totalGenerationTime += responseTime;
        
        if (results.length > 0) {
          successfulTests++;
          const avgQuality = results.reduce((sum, r) => sum + r.creativity_assessment.overall_score, 0) / results.length;
          
          const testResult = {
            category: 'style-theme',
            combination: `${style} + [${themes.join(',')}]`,
            style,
            themes,
            complexity: 3,
            pattern: 'auto',
            count: 10,
            successCount: results.length,
            successRate: results.length / 10,
            avgQuality,
            responseTime,
            samples: results.slice(0, 3).map(r => r.username),
            patternDistribution: getPatternDistribution(results)
          };
          
          testResults.push(testResult);
          console.log(`   ✅ ${testResult.combination}: ${results.length}/10 (质量: ${(avgQuality * 100).toFixed(1)}%, 时间: ${responseTime}ms)`);
        } else {
          console.log(`   ❌ ${style} + [${themes.join(',')}]: 生成失败`);
        }
      } catch (error) {
        console.log(`   ❌ ${style} + [${themes.join(',')}]: 错误 - ${error.message}`);
      }
    }
  }
  
  // 2. 复杂度测试
  console.log('\n📋 2. 复杂度级别测试');
  console.log('-'.repeat(60));
  
  for (const complexity of complexities) {
    totalTests++;
    const startTime = Date.now();
    
    try {
      const results = [];
      for (let i = 0; i < 10; i++) {
        const selectedPattern = selectOptimalPattern('modern', ['humor'], complexity);
        const result = engine.generateByPattern(selectedPattern);
        if (result) {
          results.push(result);
          allGeneratedUsernames.push(result.username);
        }
      }
      
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      totalGenerationTime += responseTime;
      
      if (results.length > 0) {
        successfulTests++;
        const avgQuality = results.reduce((sum, r) => sum + r.creativity_assessment.overall_score, 0) / results.length;
        
        const testResult = {
          category: 'complexity',
          combination: `复杂度${complexity}`,
          style: 'modern',
          themes: ['humor'],
          complexity,
          pattern: 'auto',
          count: 10,
          successCount: results.length,
          successRate: results.length / 10,
          avgQuality,
          responseTime,
          samples: results.slice(0, 3).map(r => r.username),
          patternDistribution: getPatternDistribution(results)
        };
        
        testResults.push(testResult);
        console.log(`   ✅ 复杂度${complexity}: ${results.length}/10 (质量: ${(avgQuality * 100).toFixed(1)}%, 时间: ${responseTime}ms)`);
      } else {
        console.log(`   ❌ 复杂度${complexity}: 生成失败`);
      }
    } catch (error) {
      console.log(`   ❌ 复杂度${complexity}: 错误 - ${error.message}`);
    }
  }
  
  // 3. 指定模式测试
  console.log('\n📋 3. 指定模式测试');
  console.log('-'.repeat(60));
  
  for (const pattern of patterns) {
    if (!pattern) continue; // 跳过智能选择
    
    totalTests++;
    const startTime = Date.now();
    
    try {
      const results = [];
      for (let i = 0; i < 10; i++) {
        const result = engine.generateByPattern(pattern);
        if (result) {
          results.push(result);
          allGeneratedUsernames.push(result.username);
        }
      }
      
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      totalGenerationTime += responseTime;
      
      if (results.length > 0) {
        successfulTests++;
        const avgQuality = results.reduce((sum, r) => sum + r.creativity_assessment.overall_score, 0) / results.length;
        
        const testResult = {
          category: 'pattern',
          combination: `模式: ${pattern}`,
          style: 'modern',
          themes: ['humor'],
          complexity: 3,
          pattern,
          count: 10,
          successCount: results.length,
          successRate: results.length / 10,
          avgQuality,
          responseTime,
          samples: results.slice(0, 3).map(r => r.username),
          patternDistribution: { [results[0].pattern]: results.length }
        };
        
        testResults.push(testResult);
        console.log(`   ✅ ${pattern}: ${results.length}/10 (质量: ${(avgQuality * 100).toFixed(1)}%, 时间: ${responseTime}ms)`);
      } else {
        console.log(`   ❌ ${pattern}: 生成失败`);
      }
    } catch (error) {
      console.log(`   ❌ ${pattern}: 错误 - ${error.message}`);
    }
  }
  
  // 生成测试报告
  const report = generateTestReport(testResults, allGeneratedUsernames, totalTests, successfulTests, totalGenerationTime);
  
  // 保存结果到文件
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const filename = `task1-test-results-${timestamp}.json`;
  
  fs.writeFileSync(filename, JSON.stringify({
    summary: report.summary,
    testResults,
    allUsernames: allGeneratedUsernames,
    timestamp: new Date().toISOString()
  }, null, 2));
  
  console.log(`\n💾 测试结果已保存到: ${filename}`);
  
  return report;
}

// 生成测试报告
function generateTestReport(testResults, allUsernames, totalTests, successfulTests, totalTime) {
  const successRate = (successfulTests / totalTests * 100).toFixed(1);
  const avgResponseTime = totalTests > 0 ? (totalTime / totalTests).toFixed(1) : 0;
  
  // 质量统计
  const qualities = testResults.map(r => r.avgQuality).filter(q => q > 0);
  const avgQuality = qualities.length > 0 ? (qualities.reduce((a, b) => a + b) / qualities.length * 100).toFixed(1) : 0;
  const minQuality = qualities.length > 0 ? (Math.min(...qualities) * 100).toFixed(1) : 0;
  const maxQuality = qualities.length > 0 ? (Math.max(...qualities) * 100).toFixed(1) : 0;
  
  // 按类别统计
  const categoryStats = {};
  testResults.forEach(result => {
    if (!categoryStats[result.category]) {
      categoryStats[result.category] = { total: 0, successful: 0, avgQuality: 0 };
    }
    categoryStats[result.category].total++;
    if (result.successRate > 0) {
      categoryStats[result.category].successful++;
      categoryStats[result.category].avgQuality += result.avgQuality;
    }
  });
  
  Object.keys(categoryStats).forEach(category => {
    const stats = categoryStats[category];
    stats.successRate = (stats.successful / stats.total * 100).toFixed(1);
    stats.avgQuality = stats.successful > 0 ? (stats.avgQuality / stats.successful * 100).toFixed(1) : 0;
  });
  
  const summary = {
    totalTests,
    successfulTests,
    successRate: parseFloat(successRate),
    totalUsernames: allUsernames.length,
    avgQuality: parseFloat(avgQuality),
    qualityRange: `${minQuality}% - ${maxQuality}%`,
    avgResponseTime: parseFloat(avgResponseTime),
    categoryStats
  };
  
  console.log('\n📊 任务1测试报告总结');
  console.log('='.repeat(80));
  console.log(`📈 总体统计:`);
  console.log(`   测试组合: ${totalTests}个`);
  console.log(`   成功组合: ${successfulTests}个`);
  console.log(`   成功率: ${successRate}%`);
  console.log(`   总生成数: ${allUsernames.length}个用户名`);
  console.log(`   平均质量: ${avgQuality}%`);
  console.log(`   质量范围: ${minQuality}% - ${maxQuality}%`);
  console.log(`   平均响应时间: ${avgResponseTime}ms`);
  
  console.log('\n📋 分类统计:');
  Object.entries(categoryStats).forEach(([category, stats]) => {
    console.log(`   ${category}: ${stats.successful}/${stats.total} (${stats.successRate}%, 质量: ${stats.avgQuality}%)`);
  });
  
  return { summary, testResults, allUsernames };
}

// 获取模式分布
function getPatternDistribution(results) {
  const distribution = {};
  results.forEach(result => {
    const pattern = result.pattern;
    distribution[pattern] = (distribution[pattern] || 0) + 1;
  });
  return distribution;
}

// 运行测试
runComprehensiveTest().then(report => {
  console.log('\n🏁 任务1: 全面组合测试完成');
}).catch(error => {
  console.error('任务1执行失败:', error);
});
