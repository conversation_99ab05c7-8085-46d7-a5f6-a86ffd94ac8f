/**
 * 用户模拟测试系统
 * 模拟真实用户使用场景，生成大量结果并评估质量
 */

console.log('🧪 用户模拟测试系统');
console.log('='.repeat(60));

// 模拟V2生成器接口
function simulateV2Generator(options) {
  // 模拟现有系统的生成逻辑
  const mockResults = {
    // 复杂度1-2的结果
    simple: [
      { username: '星月', quality: 0.45, pattern: 'simple', explanation: '传统组合' },
      { username: '雅静', quality: 0.42, pattern: 'simple', explanation: '传统组合' },
      { username: '清风', quality: 0.48, pattern: 'simple', explanation: '传统组合' }
    ],
    // 复杂度3的结果
    medium: [
      { username: '摸鱼星', quality: 0.75, pattern: 'v2_enhanced', explanation: '网络流行语+传统' },
      { username: '社畜月', quality: 0.72, pattern: 'v2_enhanced', explanation: '职场梗+传统' },
      { username: '代码诗人', quality: 0.68, pattern: 'v2_enhanced', explanation: '职业+文艺' },
      // 直接输出样例的问题
      { username: '首席干饭官', quality: 0.85, pattern: 'direct_copy', explanation: '直接复制样例' },
      { username: '专业退堂鼓选手', quality: 0.82, pattern: 'direct_copy', explanation: '直接复制样例' }
    ],
    // 复杂度4-5的结果
    complex: [
      { username: '间歇性想努力', quality: 0.78, pattern: 'v3_pattern', explanation: '状态描述模式' },
      { username: '温柔且强硬', quality: 0.76, pattern: 'v3_pattern', explanation: '矛盾状态模式' },
      { username: '月亮邮递员', quality: 0.74, pattern: 'v3_pattern', explanation: '拟人化服务模式' }
    ]
  };
  
  let resultPool = [];
  if (options.complexity <= 2) {
    resultPool = mockResults.simple;
  } else if (options.complexity === 3) {
    resultPool = mockResults.medium;
  } else {
    resultPool = mockResults.complex;
  }
  
  // 随机选择结果，模拟生成过程
  const selected = resultPool[Math.floor(Math.random() * resultPool.length)];
  
  return {
    ...selected,
    components: [
      { word: selected.username.slice(0, Math.ceil(selected.username.length / 2)) },
      { word: selected.username.slice(Math.ceil(selected.username.length / 2)) }
    ],
    interest_analysis: {
      overall_interest_score: selected.quality,
      surprise_factor: selected.quality * 0.8 + Math.random() * 0.2,
      cleverness_factor: selected.quality * 0.9 + Math.random() * 0.1,
      relatability_factor: selected.quality * 1.1 - Math.random() * 0.1,
      memorability_factor: selected.quality * 0.95 + Math.random() * 0.05,
      shareability_factor: selected.quality * 0.85 + Math.random() * 0.15
    }
  };
}

// 用户场景模拟
class UserSimulator {
  constructor() {
    this.userProfiles = [
      {
        name: '程序员小王',
        preferences: { style: 'modern', themes: ['tech', 'humor'], complexity: 3 },
        expectations: { min_quality: 0.7, prefer_originality: true }
      },
      {
        name: '设计师小李',
        preferences: { style: 'cool', themes: ['creative'], complexity: 4 },
        expectations: { min_quality: 0.75, prefer_creativity: true }
      },
      {
        name: '学生小张',
        preferences: { style: 'playful', themes: ['humor', 'cute'], complexity: 2 },
        expectations: { min_quality: 0.6, prefer_fun: true }
      },
      {
        name: '职场新人小陈',
        preferences: { style: 'modern', themes: ['workplace'], complexity: 3 },
        expectations: { min_quality: 0.7, prefer_relatability: true }
      },
      {
        name: '文艺青年小刘',
        preferences: { style: 'elegant', themes: ['culture'], complexity: 5 },
        expectations: { min_quality: 0.8, prefer_depth: true }
      }
    ];
    
    this.testResults = [];
    this.knownExamples = [
      '首席干饭官', '专业退堂鼓选手', '国家一级抬杠运动员', '拖延症全球推广大使',
      '听劝但反骨', '温柔且强硬', '社恐但话多', '间歇性想努力', '月亮邮递员',
      '暂停营业', '禁止访问', '芝士就是力量', '贫僧洗头用飘柔'
    ];
  }
  
  // 模拟单个用户的使用过程
  simulateUserSession(userProfile, sessionCount = 10) {
    console.log(`\n👤 模拟用户: ${userProfile.name}`);
    console.log(`   偏好: 风格=${userProfile.preferences.style}, 主题=${userProfile.preferences.themes.join(',')}, 复杂度=${userProfile.preferences.complexity}`);
    
    const sessionResults = [];
    
    for (let i = 0; i < sessionCount; i++) {
      const result = simulateV2Generator(userProfile.preferences);
      
      // 评估结果
      const evaluation = this.evaluateResult(result, userProfile);
      
      sessionResults.push({
        attempt: i + 1,
        result: result,
        evaluation: evaluation,
        user_satisfaction: this.calculateUserSatisfaction(result, userProfile, evaluation)
      });
    }
    
    return sessionResults;
  }
  
  // 评估生成结果
  evaluateResult(result, userProfile) {
    const evaluation = {
      quality_score: result.quality,
      meets_expectations: result.quality >= userProfile.expectations.min_quality,
      is_original: !this.knownExamples.includes(result.username),
      is_direct_copy: this.knownExamples.includes(result.username),
      pattern_diversity: this.assessPatternDiversity(result.pattern),
      cultural_appropriateness: this.assessCulturalAppropriateness(result, userProfile),
      issues: []
    };
    
    // 检测问题
    if (evaluation.is_direct_copy) {
      evaluation.issues.push('直接复制样例');
    }
    
    if (result.quality < 0.5) {
      evaluation.issues.push('质量过低');
    }
    
    if (result.username.length < 2) {
      evaluation.issues.push('长度过短');
    }
    
    if (result.username.length > 8) {
      evaluation.issues.push('长度过长');
    }
    
    return evaluation;
  }
  
  // 计算用户满意度
  calculateUserSatisfaction(result, userProfile, evaluation) {
    let satisfaction = 0.5; // 基础满意度
    
    // 质量满意度
    if (evaluation.meets_expectations) satisfaction += 0.3;
    
    // 原创性满意度
    if (evaluation.is_original) satisfaction += 0.2;
    else satisfaction -= 0.4; // 直接复制严重影响满意度
    
    // 风格匹配满意度
    if (evaluation.cultural_appropriateness > 0.7) satisfaction += 0.2;
    
    // 个人偏好满意度
    if (userProfile.expectations.prefer_originality && evaluation.is_original) satisfaction += 0.1;
    if (userProfile.expectations.prefer_creativity && result.interest_analysis.cleverness_factor > 0.8) satisfaction += 0.1;
    if (userProfile.expectations.prefer_fun && result.interest_analysis.shareability_factor > 0.8) satisfaction += 0.1;
    
    return Math.max(0, Math.min(1, satisfaction));
  }
  
  // 评估模式多样性
  assessPatternDiversity(pattern) {
    const diversityMap = {
      'simple': 0.3,
      'v2_enhanced': 0.6,
      'v3_pattern': 0.8,
      'direct_copy': 0.1 // 直接复制多样性最低
    };
    return diversityMap[pattern] || 0.5;
  }
  
  // 评估文化适配性
  assessCulturalAppropriateness(result, userProfile) {
    // 简化评估：基于风格匹配
    const styleMatch = {
      'modern': ['tech', 'humor', 'workplace'],
      'cool': ['creative', 'tech'],
      'playful': ['humor', 'cute'],
      'elegant': ['culture', 'traditional']
    };
    
    const expectedThemes = styleMatch[userProfile.preferences.style] || [];
    const hasMatchingTheme = userProfile.preferences.themes.some(theme => 
      expectedThemes.includes(theme)
    );
    
    return hasMatchingTheme ? 0.8 : 0.5;
  }
  
  // 运行完整的用户模拟测试
  runFullSimulation() {
    console.log('\n🎯 开始完整用户模拟测试');
    console.log('-'.repeat(40));
    
    const allResults = [];
    
    this.userProfiles.forEach(profile => {
      const sessionResults = this.simulateUserSession(profile, 10);
      allResults.push({
        user: profile,
        sessions: sessionResults
      });
      
      // 显示用户会话总结
      this.printUserSessionSummary(profile, sessionResults);
    });
    
    // 生成总体分析报告
    this.generateOverallReport(allResults);
    
    return allResults;
  }
  
  // 打印用户会话总结
  printUserSessionSummary(userProfile, sessionResults) {
    const avgQuality = sessionResults.reduce((sum, s) => sum + s.result.quality, 0) / sessionResults.length;
    const avgSatisfaction = sessionResults.reduce((sum, s) => sum + s.user_satisfaction, 0) / sessionResults.length;
    const originalityRate = sessionResults.filter(s => s.evaluation.is_original).length / sessionResults.length;
    const directCopyCount = sessionResults.filter(s => s.evaluation.is_direct_copy).length;
    
    console.log(`   平均质量: ${(avgQuality * 100).toFixed(1)}%`);
    console.log(`   用户满意度: ${(avgSatisfaction * 100).toFixed(1)}%`);
    console.log(`   原创率: ${(originalityRate * 100).toFixed(1)}%`);
    console.log(`   直接复制次数: ${directCopyCount}/10`);
    
    if (directCopyCount > 0) {
      console.log(`   ⚠️  发现直接复制样例问题！`);
    }
  }
  
  // 生成总体分析报告
  generateOverallReport(allResults) {
    console.log('\n📊 总体分析报告');
    console.log('='.repeat(60));
    
    // 计算总体指标
    const allSessions = allResults.flatMap(r => r.sessions);
    const totalAttempts = allSessions.length;
    
    const avgQuality = allSessions.reduce((sum, s) => sum + s.result.quality, 0) / totalAttempts;
    const avgSatisfaction = allSessions.reduce((sum, s) => sum + s.user_satisfaction, 0) / totalAttempts;
    const originalityRate = allSessions.filter(s => s.evaluation.is_original).length / totalAttempts;
    const directCopyRate = allSessions.filter(s => s.evaluation.is_direct_copy).length / totalAttempts;
    const qualityPassRate = allSessions.filter(s => s.evaluation.meets_expectations).length / totalAttempts;
    
    console.log(`\n📈 核心指标:`);
    console.log(`   总测试次数: ${totalAttempts}`);
    console.log(`   平均质量: ${(avgQuality * 100).toFixed(1)}%`);
    console.log(`   平均用户满意度: ${(avgSatisfaction * 100).toFixed(1)}%`);
    console.log(`   原创率: ${(originalityRate * 100).toFixed(1)}%`);
    console.log(`   直接复制率: ${(directCopyRate * 100).toFixed(1)}%`);
    console.log(`   质量达标率: ${(qualityPassRate * 100).toFixed(1)}%`);
    
    // 问题分析
    console.log(`\n⚠️  发现的问题:`);
    if (directCopyRate > 0.1) {
      console.log(`   1. 直接复制样例问题严重 (${(directCopyRate * 100).toFixed(1)}%)`);
    }
    if (avgQuality < 0.7) {
      console.log(`   2. 平均质量偏低 (${(avgQuality * 100).toFixed(1)}%)`);
    }
    if (avgSatisfaction < 0.6) {
      console.log(`   3. 用户满意度不足 (${(avgSatisfaction * 100).toFixed(1)}%)`);
    }
    
    // 复杂度分析
    console.log(`\n📊 复杂度分析:`);
    const complexityGroups = {
      low: allResults.filter(r => r.user.preferences.complexity <= 2),
      medium: allResults.filter(r => r.user.preferences.complexity === 3),
      high: allResults.filter(r => r.user.preferences.complexity >= 4)
    };
    
    Object.entries(complexityGroups).forEach(([level, users]) => {
      if (users.length > 0) {
        const sessions = users.flatMap(u => u.sessions);
        const avgQual = sessions.reduce((sum, s) => sum + s.result.quality, 0) / sessions.length;
        const avgSat = sessions.reduce((sum, s) => sum + s.user_satisfaction, 0) / sessions.length;
        const copyRate = sessions.filter(s => s.evaluation.is_direct_copy).length / sessions.length;
        
        console.log(`   ${level}复杂度: 质量${(avgQual * 100).toFixed(1)}%, 满意度${(avgSat * 100).toFixed(1)}%, 复制率${(copyRate * 100).toFixed(1)}%`);
      }
    });
    
    // 改进建议
    console.log(`\n💡 改进建议:`);
    console.log(`   1. 增加词库多样性，减少直接复制样例`);
    console.log(`   2. 优化复杂度3的生成算法，提升原创性`);
    console.log(`   3. 加强质量控制，确保生成结果达到最低标准`);
    console.log(`   4. 实现更智能的用户偏好匹配`);
    console.log(`   5. 建立实时的质量监控和反馈机制`);
  }
}

// 主测试函数
function runUserSimulationTest() {
  const simulator = new UserSimulator();
  const results = simulator.runFullSimulation();
  
  console.log('\n🎯 测试结论');
  console.log('='.repeat(60));
  console.log('✅ 用户模拟测试系统成功运行');
  console.log('✅ 发现了直接复制样例的关键问题');
  console.log('✅ 识别了质量和满意度的改进空间');
  console.log('✅ 提供了具体的优化建议');
  
  console.log('\n🚀 下一步行动:');
  console.log('1. 立即修复直接复制样例的问题');
  console.log('2. 扩展词库，增加生成多样性');
  console.log('3. 优化复杂度3的生成逻辑');
  console.log('4. 建立持续的质量监控机制');
  
  return results;
}

// 运行测试
runUserSimulationTest();
