/**
 * 测试真实的V5 API接口
 * 验证实际的API调用是否正常工作
 */

const http = require('http');

console.log('🧪 真实V5 API接口测试');
console.log('='.repeat(80));

// 测试用例配置
const testCases = [
  {
    name: '基础现代风格+技术主题',
    params: {
      language: 'zh',
      style: 'modern',
      themes: ['tech'],
      complexity: 3,
      count: 1
    }
  },
  {
    name: '酷炫风格+多主题',
    params: {
      language: 'zh',
      style: 'cool',
      themes: ['tech', 'humor'],
      complexity: 4,
      count: 1
    }
  },
  {
    name: '活泼风格+创意主题',
    params: {
      language: 'zh',
      style: 'playful',
      themes: ['creative', 'humor'],
      complexity: 2,
      count: 1
    }
  },
  {
    name: '指定身份升维模式',
    params: {
      language: 'zh',
      style: 'modern',
      themes: ['workplace'],
      complexity: 3,
      count: 1,
      pattern: 'identity_elevation'
    }
  },
  {
    name: '指定矛盾统一模式',
    params: {
      language: 'zh',
      style: 'cool',
      themes: ['humor'],
      complexity: 4,
      count: 1,
      pattern: 'contradiction_unity'
    }
  },
  {
    name: '生成多个用户名',
    params: {
      language: 'zh',
      style: 'modern',
      themes: ['tech', 'humor'],
      complexity: 3,
      count: 3
    }
  },
  {
    name: '边界测试-空主题',
    params: {
      language: 'zh',
      style: 'modern',
      themes: [],
      complexity: 3,
      count: 1
    }
  },
  {
    name: '边界测试-最高复杂度',
    params: {
      language: 'zh',
      style: 'elegant',
      themes: ['culture'],
      complexity: 5,
      count: 1
    }
  }
];

// 发送HTTP请求的函数
function makeRequest(path, data) {
  return new Promise((resolve, reject) => {
    const postData = JSON.stringify(data);
    
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: path,
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      }
    };
    
    const req = http.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        try {
          const parsedData = JSON.parse(responseData);
          resolve({
            statusCode: res.statusCode,
            data: parsedData
          });
        } catch (error) {
          resolve({
            statusCode: res.statusCode,
            data: responseData,
            parseError: error.message
          });
        }
      });
    });
    
    req.on('error', (error) => {
      reject(error);
    });
    
    req.write(postData);
    req.end();
  });
}

// 测试单个API调用
async function testSingleAPI(testCase) {
  console.log(`\n📋 测试: ${testCase.name}`);
  console.log(`   参数: ${JSON.stringify(testCase.params, null, 2)}`);
  
  try {
    const startTime = Date.now();
    const response = await makeRequest('/api/v5-generate', testCase.params);
    const endTime = Date.now();
    const responseTime = endTime - startTime;
    
    console.log(`   响应时间: ${responseTime}ms`);
    console.log(`   状态码: ${response.statusCode}`);
    
    if (response.statusCode === 200) {
      if (response.data && response.data.success) {
        console.log(`   ✅ 成功: ${response.data.engine} v${response.data.version}`);
        console.log(`   📊 生成数量: ${response.data.total}个`);
        console.log(`   📈 平均质量: ${(response.data.average_quality * 100).toFixed(1)}%`);
        
        if (response.data.results && response.data.results.length > 0) {
          response.data.results.forEach((result, index) => {
            console.log(`      ${index + 1}. ${result.username} (${result.pattern})`);
            console.log(`         质量: ${(result.creativity_assessment.overall_score * 100).toFixed(1)}%`);
            console.log(`         公式: ${result.formula}`);
          });
        }
        
        return {
          success: true,
          responseTime,
          resultCount: response.data.total,
          averageQuality: response.data.average_quality,
          results: response.data.results
        };
      } else {
        console.log(`   ❌ API返回失败: ${response.data.error || '未知错误'}`);
        return {
          success: false,
          error: response.data.error || '未知错误',
          responseTime
        };
      }
    } else {
      console.log(`   ❌ HTTP错误: ${response.statusCode}`);
      console.log(`   响应内容: ${JSON.stringify(response.data)}`);
      return {
        success: false,
        error: `HTTP ${response.statusCode}`,
        responseTime
      };
    }
  } catch (error) {
    console.log(`   ❌ 请求失败: ${error.message}`);
    return {
      success: false,
      error: error.message,
      responseTime: 0
    };
  }
}

// 检查服务器是否运行
async function checkServerStatus() {
  console.log('🔍 检查服务器状态...');
  
  try {
    const response = await makeRequest('/', {});
    if (response.statusCode === 200 || response.statusCode === 404) {
      console.log('✅ 服务器正在运行');
      return true;
    } else {
      console.log(`❌ 服务器响应异常: ${response.statusCode}`);
      return false;
    }
  } catch (error) {
    console.log(`❌ 无法连接到服务器: ${error.message}`);
    console.log('💡 请确保运行了 npm run dev 启动开发服务器');
    return false;
  }
}

// 运行所有测试
async function runAllAPITests() {
  console.log('🚀 开始真实V5 API测试\n');
  
  // 检查服务器状态
  const serverRunning = await checkServerStatus();
  if (!serverRunning) {
    console.log('\n❌ 服务器未运行，无法进行API测试');
    console.log('请先运行: npm run dev');
    return;
  }
  
  const results = [];
  let totalTests = 0;
  let successfulTests = 0;
  let totalResponseTime = 0;
  let totalQuality = 0;
  let qualityCount = 0;
  
  // 执行所有测试用例
  for (const testCase of testCases) {
    totalTests++;
    const result = await testSingleAPI(testCase);
    results.push({ testCase, result });
    
    if (result.success) {
      successfulTests++;
      totalResponseTime += result.responseTime;
      
      if (result.averageQuality) {
        totalQuality += result.averageQuality;
        qualityCount++;
      }
    }
    
    // 测试间隔，避免过快请求
    await new Promise(resolve => setTimeout(resolve, 100));
  }
  
  // 生成测试报告
  console.log('\n📋 真实API测试报告');
  console.log('='.repeat(80));
  
  const successRate = (successfulTests / totalTests * 100).toFixed(1);
  const avgResponseTime = successfulTests > 0 ? (totalResponseTime / successfulTests).toFixed(1) : 0;
  const avgQuality = qualityCount > 0 ? (totalQuality / qualityCount * 100).toFixed(1) : 0;
  
  console.log('\n📊 总体统计:');
  console.log(`   总测试数: ${totalTests}个`);
  console.log(`   成功测试: ${successfulTests}个`);
  console.log(`   失败测试: ${totalTests - successfulTests}个`);
  console.log(`   成功率: ${successRate}%`);
  console.log(`   平均响应时间: ${avgResponseTime}ms`);
  console.log(`   平均生成质量: ${avgQuality}%`);
  
  console.log('\n📋 详细结果:');
  results.forEach((item, index) => {
    const { testCase, result } = item;
    const status = result.success ? '✅' : '❌';
    console.log(`   ${index + 1}. ${status} ${testCase.name}`);
    if (result.success) {
      console.log(`      响应时间: ${result.responseTime}ms`);
      console.log(`      生成数量: ${result.resultCount}个`);
      if (result.averageQuality) {
        console.log(`      平均质量: ${(result.averageQuality * 100).toFixed(1)}%`);
      }
    } else {
      console.log(`      错误: ${result.error}`);
    }
  });
  
  // 失败测试分析
  const failedTests = results.filter(item => !item.result.success);
  if (failedTests.length > 0) {
    console.log('\n❌ 失败测试分析:');
    failedTests.forEach((item, index) => {
      console.log(`   ${index + 1}. ${item.testCase.name}: ${item.result.error}`);
    });
  }
  
  // 性能分析
  console.log('\n⚡ 性能分析:');
  if (avgResponseTime <= 50) {
    console.log(`   🟢 响应时间优秀 (${avgResponseTime}ms ≤ 50ms)`);
  } else if (avgResponseTime <= 100) {
    console.log(`   🟡 响应时间良好 (${avgResponseTime}ms ≤ 100ms)`);
  } else {
    console.log(`   🔴 响应时间需要优化 (${avgResponseTime}ms > 100ms)`);
  }
  
  // 质量分析
  console.log('\n🎨 质量分析:');
  if (avgQuality >= 90) {
    console.log(`   🟢 生成质量优秀 (${avgQuality}% ≥ 90%)`);
  } else if (avgQuality >= 80) {
    console.log(`   🟡 生成质量良好 (${avgQuality}% ≥ 80%)`);
  } else {
    console.log(`   🔴 生成质量需要提升 (${avgQuality}% < 80%)`);
  }
  
  // 总结建议
  console.log('\n💡 测试总结:');
  if (successRate >= 95) {
    console.log('   🎉 V5 API工作完美！所有功能正常');
    console.log('   ✅ 可以放心使用V5引擎');
    console.log('   ✅ 性能和质量都达到预期');
  } else if (successRate >= 80) {
    console.log('   🟡 V5 API基本正常，有少量问题');
    console.log('   🔧 建议检查失败的测试用例');
    console.log('   🔧 优化错误处理机制');
  } else {
    console.log('   🔴 V5 API存在较多问题');
    console.log('   🚨 需要重点修复API功能');
    console.log('   🔧 建议检查服务器配置和代码');
  }
  
  return {
    totalTests,
    successfulTests,
    successRate: parseFloat(successRate),
    avgResponseTime: parseFloat(avgResponseTime),
    avgQuality: parseFloat(avgQuality),
    status: successRate >= 95 ? 'excellent' : successRate >= 80 ? 'good' : 'needs_improvement'
  };
}

// 运行测试
runAllAPITests().then(result => {
  console.log('\n🏁 真实API测试完成');
  if (result) {
    console.log(`最终状态: ${result.status}`);
  }
}).catch(error => {
  console.error('测试执行失败:', error);
});
