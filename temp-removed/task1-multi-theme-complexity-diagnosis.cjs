/**
 * 任务1: 多主题复杂度生成效果诊断
 * 模拟V5UsernameGenerator.vue页面的实际操作测试
 */

const fs = require('fs');

console.log('📋 任务1: 多主题复杂度生成效果诊断');
console.log('='.repeat(80));

// V5引擎完整模拟类
class V5DiagnosticEngine {
  constructor() {
    this.elementLibrary = this.buildCompleteElementLibrary();
    this.generationPatterns = this.buildGenerationPatterns();
    this.patternMap = this.buildPatternMap();
    this.themeBonus = this.buildThemeBonus();
  }

  buildCompleteElementLibrary() {
    return {
      subjects: {
        古代人物: ['贫僧', '道士', '书生', '侠客', '状元', '秀才', '举人', '进士', '太监', '皇帝', '将军', '丞相'],
        抽象概念: ['快乐', '悲伤', '愤怒', '温柔', '智慧', '勇气', '希望', '梦想', '孤独', '焦虑', '兴奋', '平静'],
        天体宇宙: ['月亮', '太阳', '星星', '银河', '宇宙', '黑洞', '彗星', '流星', '行星', '恒星', '星云', '陨石']
      },
      actions: {
        日常行为: ['吃', '睡', '玩', '工作', '学习', '跑步', '游泳', '阅读', '写作', '思考', '发呆', '散步'],
        网络行为: ['直播', '带货', '刷视频', '点赞', '转发', '评论', '分享', '关注', '取关', '拉黑', '举报', '私信'],
        现代生活: ['洗头', '化妆', '健身', '减肥', '加班', '购物', '旅游', '聚餐', '约会', '相亲', '搬家', '装修']
      },
      modifiers: {
        权威级别: ['首席', '高级', '资深', '专业', '认证', '特级', '顶级', '超级', '终极', '至尊', '王牌', '精英']
      },
      connectors: {
        对比转折: ['但', '却', '然而', '不过', '虽然', '尽管', '即使', '哪怕', '纵然', '纵使', '就算', '便是']
      },
      suffixes: {
        职位后缀: ['官', '师', '专家', '大使', '代表', '委员', '顾问', '总监', '主任', '经理', '主管', '负责人']
      }
    };
  }

  buildGenerationPatterns() {
    return [
      { id: 'identity_elevation', name: '身份升维包装', weight: 0.96, complexity_range: [1, 5] },
      { id: 'contradiction_unity', name: '矛盾统一', weight: 0.94, complexity_range: [2, 5] },
      { id: 'temporal_displacement', name: '时空错位重组', weight: 0.95, complexity_range: [2, 5] },
      { id: 'service_personification', name: '服务拟人化', weight: 0.92, complexity_range: [1, 4] },
      { id: 'tech_expression', name: '技术化表达', weight: 0.91, complexity_range: [2, 5] },
      { id: 'homophone_creative', name: '创意谐音', weight: 0.95, complexity_range: [1, 3] }
    ];
  }

  buildPatternMap() {
    return {
      'modern': ['temporal_displacement', 'identity_elevation', 'tech_expression'],
      'cool': ['contradiction_unity', 'temporal_displacement', 'tech_expression'],
      'playful': ['service_personification', 'homophone_creative', 'identity_elevation'],
      'traditional': ['temporal_displacement', 'service_personification', 'homophone_creative'],
      'elegant': ['contradiction_unity', 'service_personification', 'identity_elevation']
    };
  }

  buildThemeBonus() {
    return {
      'tech': ['tech_expression', 'temporal_displacement'],
      'workplace': ['identity_elevation', 'contradiction_unity'],
      'humor': ['homophone_creative', 'contradiction_unity'],
      'creative': ['service_personification', 'homophone_creative'],
      'culture': ['temporal_displacement', 'service_personification']
    };
  }

  // 智能模式选择 (模拟V5引擎逻辑)
  selectOptimalPattern(style, themes, complexity) {
    let candidatePatterns = this.patternMap[style] || this.patternMap['modern'];

    // 主题扩展
    themes.forEach(theme => {
      if (this.themeBonus[theme]) {
        candidatePatterns = [...candidatePatterns, ...this.themeBonus[theme]];
      }
    });

    // 去重
    candidatePatterns = [...new Set(candidatePatterns)];

    // 复杂度过滤
    if (complexity >= 4) {
      const complexPatterns = ['temporal_displacement', 'contradiction_unity', 'tech_expression'];
      candidatePatterns = candidatePatterns.filter(p => complexPatterns.includes(p));
    } else if (complexity <= 2) {
      const simplePatterns = ['homophone_creative', 'service_personification', 'identity_elevation'];
      candidatePatterns = candidatePatterns.filter(p => simplePatterns.includes(p));
    }

    // 如果过滤后为空，使用默认模式
    if (candidatePatterns.length === 0) {
      candidatePatterns = ['identity_elevation'];
    }

    return candidatePatterns[Math.floor(Math.random() * candidatePatterns.length)];
  }

  // 随机选择元素
  randomSelect(array) {
    return array[Math.floor(Math.random() * array.length)];
  }

  // 按复杂度生成用户名
  generateByComplexity(patternId, complexity) {
    const pattern = this.generationPatterns.find(p => p.id === patternId);
    if (!pattern) return null;

    let username = '';
    let elementsUsed = [];
    let structureComplexity = '';
    let semanticLayers = 0;

    try {
      switch (patternId) {
        case 'identity_elevation':
          if (complexity <= 2) {
            // 简单结构: 修饰词 + 行为 + 后缀
            const authority = this.randomSelect(this.elementLibrary.modifiers.权威级别);
            const behavior = this.randomSelect(this.elementLibrary.actions.日常行为);
            const suffix = this.randomSelect(this.elementLibrary.suffixes.职位后缀);
            username = `${authority}${behavior}${suffix}`;
            elementsUsed = [authority, behavior, suffix];
            structureComplexity = '简单三元组合';
            semanticLayers = 1;
          } else if (complexity === 3) {
            // 中等结构: 增加描述性元素
            const authority = this.randomSelect(this.elementLibrary.modifiers.权威级别);
            const behavior = this.randomSelect([...this.elementLibrary.actions.日常行为, ...this.elementLibrary.actions.现代生活]);
            const suffix = this.randomSelect(this.elementLibrary.suffixes.职位后缀);
            username = `${authority}${behavior}${suffix}`;
            elementsUsed = [authority, behavior, suffix];
            structureComplexity = '中等三元组合';
            semanticLayers = 2;
          } else {
            // 复杂结构: 多重修饰
            const authority1 = this.randomSelect(this.elementLibrary.modifiers.权威级别);
            const authority2 = this.randomSelect(this.elementLibrary.modifiers.权威级别);
            const behavior = this.randomSelect(this.elementLibrary.actions.网络行为);
            const suffix = this.randomSelect(this.elementLibrary.suffixes.职位后缀);
            username = `${authority1}${authority2}${behavior}${suffix}`;
            elementsUsed = [authority1, authority2, behavior, suffix];
            structureComplexity = '复杂多重修饰';
            semanticLayers = 3;
          }
          break;

        case 'contradiction_unity':
          if (complexity <= 2) {
            // 简单对比
            const positive = this.randomSelect(['温柔', '理性', '冷静', '勤奋']);
            const connector = this.randomSelect(['但', '却']);
            const negative = this.randomSelect(['强硬', '感性', '冲动', '懒惰']);
            username = `${positive}${connector}${negative}`;
            elementsUsed = [positive, connector, negative];
            structureComplexity = '简单对比结构';
            semanticLayers = 2;
          } else if (complexity === 3) {
            // 中等对比
            const positive = this.randomSelect(['温柔', '理性', '冷静', '勤奋', '节约', '听劝']);
            const connector = this.randomSelect(this.elementLibrary.connectors.对比转折);
            const negative = this.randomSelect(['强硬', '感性', '冲动', '懒惰', '挥霍', '反骨']);
            username = `${positive}${connector}${negative}`;
            elementsUsed = [positive, connector, negative];
            structureComplexity = '中等对比结构';
            semanticLayers = 2;
          } else {
            // 复杂多重对比
            const positive1 = this.randomSelect(['温柔', '理性', '冷静']);
            const connector1 = this.randomSelect(['却', '但']);
            const negative1 = this.randomSelect(['强硬', '感性', '冲动']);
            const connector2 = this.randomSelect(['的', '']);
            const role = this.randomSelect(['程序员', '设计师', '产品经理']);
            username = `${positive1}${connector1}${negative1}${connector2}${role}`;
            elementsUsed = [positive1, connector1, negative1, connector2, role];
            structureComplexity = '复杂多重对比';
            semanticLayers = 3;
          }
          break;

        case 'temporal_displacement':
          if (complexity <= 2) {
            // 简单时空错位
            const ancient = this.randomSelect(this.elementLibrary.subjects.古代人物);
            const modern = this.randomSelect(this.elementLibrary.actions.现代生活);
            username = `${ancient}${modern}`;
            elementsUsed = [ancient, modern];
            structureComplexity = '简单时空对比';
            semanticLayers = 2;
          } else if (complexity === 3) {
            // 中等时空错位
            const ancient = this.randomSelect(this.elementLibrary.subjects.古代人物);
            const modern = this.randomSelect([...this.elementLibrary.actions.网络行为, ...this.elementLibrary.actions.现代生活]);
            username = `${ancient}${modern}`;
            elementsUsed = [ancient, modern];
            structureComplexity = '中等时空对比';
            semanticLayers = 2;
          } else {
            // 复杂时空错位
            const ancient = this.randomSelect(this.elementLibrary.subjects.古代人物);
            const modernAction = this.randomSelect(this.elementLibrary.actions.网络行为);
            const modernTool = this.randomSelect(['用iPhone', '开特斯拉', '玩VR', '做直播']);
            username = `${ancient}${modernAction}${modernTool}`;
            elementsUsed = [ancient, modernAction, modernTool];
            structureComplexity = '复杂时空融合';
            semanticLayers = 3;
          }
          break;

        case 'service_personification':
          if (complexity <= 2) {
            // 简单拟人化
            const concept = this.randomSelect(this.elementLibrary.subjects.抽象概念);
            const service = this.randomSelect(['邮递员', '收集员', '配送员']);
            username = `${concept}${service}`;
            elementsUsed = [concept, service];
            structureComplexity = '简单拟人化';
            semanticLayers = 1;
          } else if (complexity === 3) {
            // 中等拟人化
            const concept = this.randomSelect([...this.elementLibrary.subjects.抽象概念, ...this.elementLibrary.subjects.天体宇宙]);
            const service = this.randomSelect(['邮递员', '收集员', '配送员', '制造商', '专卖店', '客服']);
            username = `${concept}${service}`;
            elementsUsed = [concept, service];
            structureComplexity = '中等拟人化';
            semanticLayers = 2;
          } else {
            // 复杂拟人化
            const modifier = this.randomSelect(['专业', '资深', '认证']);
            const concept = this.randomSelect(this.elementLibrary.subjects.天体宇宙);
            const service = this.randomSelect(['导航员', '修理工', '设计师', '顾问']);
            username = `${modifier}${concept}${service}`;
            elementsUsed = [modifier, concept, service];
            structureComplexity = '复杂拟人化';
            semanticLayers = 3;
          }
          break;

        case 'tech_expression':
          if (complexity <= 2) {
            // 简单技术化
            const lifeConcept = this.randomSelect(['人生', '爱情', '友情']);
            const techTerm = this.randomSelect(['404', '503', '正在加载']);
            username = `${lifeConcept}${techTerm}`;
            elementsUsed = [lifeConcept, techTerm];
            structureComplexity = '简单技术化';
            semanticLayers = 2;
          } else if (complexity === 3) {
            // 中等技术化
            const lifeConcept = this.randomSelect(['人生', '梦想', '快乐', '悲伤', '爱情', '友情']);
            const techTerm = this.randomSelect(['404未找到', '正在缓冲', '连接超时', '系统维护']);
            username = `${lifeConcept}${techTerm}`;
            elementsUsed = [lifeConcept, techTerm];
            structureComplexity = '中等技术化';
            semanticLayers = 2;
          } else {
            // 复杂技术化
            const lifeConcept = this.randomSelect(['人生', '梦想', '爱情']);
            const techProcess = this.randomSelect(['正在编译', '正在部署', '正在重构']);
            const techResult = this.randomSelect(['请稍候', '遇到异常', '需要重启']);
            username = `${lifeConcept}${techProcess}${techResult}`;
            elementsUsed = [lifeConcept, techProcess, techResult];
            structureComplexity = '复杂技术化';
            semanticLayers = 3;
          }
          break;

        case 'homophone_creative':
          // 谐音相对简单，复杂度主要体现在选择的谐音难度
          const homophones = complexity <= 2 ?
            [
              { original: '没心没肺', replacement: '莓心没肺' },
              { original: '年年有余', replacement: '年年有鱼' }
            ] : complexity === 3 ?
            [
              { original: '知识就是力量', replacement: '芝士就是力量' },
              { original: '心想事成', replacement: '薪想事成' }
            ] :
            [
              { original: '马到成功', replacement: '码到成功' },
              { original: '天马行空', replacement: '天码行空' }
            ];

          const selected = this.randomSelect(homophones);
          username = selected.replacement;
          elementsUsed = [selected.original, '→', selected.replacement];
          structureComplexity = complexity <= 2 ? '简单谐音' : complexity === 3 ? '中等谐音' : '复杂谐音';
          semanticLayers = 1;
          break;

        default:
          return null;
      }

      // 生成质量评估
      const creativity_assessment = this.assessCreativityByComplexity(username, pattern, complexity);

      return {
        username,
        pattern: pattern.name,
        patternId,
        complexity,
        elements_used: elementsUsed,
        structure_complexity: structureComplexity,
        semantic_layers: semanticLayers,
        character_length: username.length,
        creativity_assessment,
        generation_time: Date.now()
      };

    } catch (error) {
      console.error(`生成错误 (${patternId}, 复杂度${complexity}):`, error);
      return null;
    }
  }

  // 基于复杂度的质量评估
  assessCreativityByComplexity(username, pattern, complexity) {
    // 基础质量根据复杂度调整
    const complexityFactors = {
      1: { novelty: 0.7, relevance: 0.95, comprehensibility: 0.98, memorability: 0.8 },
      2: { novelty: 0.75, relevance: 0.92, comprehensibility: 0.95, memorability: 0.82 },
      3: { novelty: 0.85, relevance: 0.88, comprehensibility: 0.90, memorability: 0.88 },
      4: { novelty: 0.92, relevance: 0.82, comprehensibility: 0.80, memorability: 0.85 },
      5: { novelty: 0.96, relevance: 0.78, comprehensibility: 0.70, memorability: 0.90 }
    };

    const base = complexityFactors[complexity];

    // 添加随机波动
    const novelty = Math.min(1.0, base.novelty + (Math.random() - 0.5) * 0.1);
    const relevance = Math.min(1.0, base.relevance + (Math.random() - 0.5) * 0.1);
    const comprehensibility = Math.min(1.0, base.comprehensibility + (Math.random() - 0.5) * 0.1);
    const memorability = Math.min(1.0, base.memorability + (Math.random() - 0.5) * 0.1);

    const overall_score = novelty * 0.3 + relevance * 0.25 + comprehensibility * 0.25 + memorability * 0.2;

    return {
      novelty,
      relevance,
      comprehensibility,
      memorability,
      overall_score,
      explanation: `复杂度${complexity}级-${pattern.name}: 新颖性${(novelty*100).toFixed(0)}%, 相关性${(relevance*100).toFixed(0)}%, 可理解性${(comprehensibility*100).toFixed(0)}%, 记忆性${(memorability*100).toFixed(0)}%`
    };
  }
}

// 执行多主题复杂度诊断测试
async function runMultiThemeComplexityDiagnosis() {
  console.log('🚀 开始多主题复杂度生成效果诊断\n');

  const engine = new V5DiagnosticEngine();

  // 测试主题组合
  const testCombinations = [
    { themes: ['tech', 'humor'], name: 'tech+humor', description: '技术+幽默' },
    { themes: ['workplace', 'culture'], name: 'workplace+culture', description: '职场+文化' },
    { themes: ['creative', 'humor'], name: 'creative+humor', description: '创意+幽默' },
    { themes: ['tech', 'workplace'], name: 'tech+workplace', description: '技术+职场' }
  ];

  const allResults = [];
  const diagnosisReport = {
    testCombinations: testCombinations.length,
    complexityLevels: 5,
    samplesPerTest: 10,
    totalSamples: 0,
    results: [],
    analysis: {},
    problems: [],
    solutions: []
  };

  console.log('📊 测试配置:');
  console.log(`   主题组合: ${testCombinations.length}种`);
  console.log(`   复杂度级别: 1-5级`);
  console.log(`   每组样本: 10个`);
  console.log(`   总预期样本: ${testCombinations.length * 5 * 10}个\n`);

  // 对每个主题组合进行测试
  for (const combo of testCombinations) {
    console.log(`🎯 测试主题组合: ${combo.description} [${combo.themes.join('+')}]`);
    console.log('-'.repeat(60));

    const comboResults = {
      combination: combo,
      complexityResults: [],
      summary: {}
    };

    // 对每个复杂度级别进行测试
    for (let complexity = 1; complexity <= 5; complexity++) {
      console.log(`\n   复杂度${complexity}级测试:`);

      const complexityResults = {
        complexity,
        samples: [],
        patterns: {},
        structures: {},
        semanticLayers: {},
        characterLengths: [],
        qualityScores: []
      };

      // 生成10个样本
      for (let i = 0; i < 10; i++) {
        const selectedPattern = engine.selectOptimalPattern('modern', combo.themes, complexity);
        const result = engine.generateByComplexity(selectedPattern, complexity);

        if (result) {
          complexityResults.samples.push(result);
          allResults.push(result);
          diagnosisReport.totalSamples++;

          // 统计模式使用
          complexityResults.patterns[result.patternId] = (complexityResults.patterns[result.patternId] || 0) + 1;

          // 统计结构复杂度
          complexityResults.structures[result.structure_complexity] = (complexityResults.structures[result.structure_complexity] || 0) + 1;

          // 统计语义层次
          complexityResults.semanticLayers[result.semantic_layers] = (complexityResults.semanticLayers[result.semantic_layers] || 0) + 1;

          // 记录字符长度
          complexityResults.characterLengths.push(result.character_length);

          // 记录质量分数
          complexityResults.qualityScores.push(result.creativity_assessment.overall_score);
        }
      }

      // 计算统计数据
      const avgLength = complexityResults.characterLengths.reduce((a, b) => a + b, 0) / complexityResults.characterLengths.length;
      const avgQuality = complexityResults.qualityScores.reduce((a, b) => a + b, 0) / complexityResults.qualityScores.length;
      const maxLength = Math.max(...complexityResults.characterLengths);
      const minLength = Math.min(...complexityResults.characterLengths);

      complexityResults.statistics = {
        avgLength: avgLength.toFixed(1),
        lengthRange: `${minLength}-${maxLength}`,
        avgQuality: (avgQuality * 100).toFixed(1),
        patternCount: Object.keys(complexityResults.patterns).length,
        structureCount: Object.keys(complexityResults.structures).length,
        semanticLayerRange: `${Math.min(...Object.keys(complexityResults.semanticLayers))}-${Math.max(...Object.keys(complexityResults.semanticLayers))}`
      };

      // 显示结果
      console.log(`      生成样本: ${complexityResults.samples.length}/10个`);
      console.log(`      使用模式: ${Object.keys(complexityResults.patterns).join(', ')}`);
      console.log(`      平均长度: ${avgLength.toFixed(1)}字符 (${minLength}-${maxLength})`);
      console.log(`      平均质量: ${(avgQuality * 100).toFixed(1)}%`);
      console.log(`      结构类型: ${Object.keys(complexityResults.structures).length}种`);
      console.log(`      语义层次: ${Math.min(...Object.keys(complexityResults.semanticLayers))}-${Math.max(...Object.keys(complexityResults.semanticLayers))}层`);

      // 显示样本示例
      console.log(`      样本示例: ${complexityResults.samples.slice(0, 3).map(s => `"${s.username}"`).join(', ')}`);

      comboResults.complexityResults.push(complexityResults);
    }

    // 分析该组合的整体趋势
    comboResults.summary = analyzeComplexityTrends(comboResults.complexityResults);
    diagnosisReport.results.push(comboResults);

    console.log(`\n   ${combo.description}组合总结:`);
    console.log(`      复杂度差异明显度: ${comboResults.summary.differenceClarity}`);
    console.log(`      长度增长趋势: ${comboResults.summary.lengthTrend}`);
    console.log(`      质量变化趋势: ${comboResults.summary.qualityTrend}`);
    console.log(`      结构复杂度趋势: ${comboResults.summary.structureTrend}\n`);
  }

  // 生成诊断分析
  diagnosisReport.analysis = performDiagnosisAnalysis(diagnosisReport.results);
  diagnosisReport.problems = identifyProblems(diagnosisReport.analysis);
  diagnosisReport.solutions = generateSolutions(diagnosisReport.problems);

  // 输出诊断报告
  console.log('📋 多主题复杂度诊断报告');
  console.log('='.repeat(80));

  console.log('\n📈 总体统计:');
  console.log(`   测试组合: ${diagnosisReport.testCombinations}种`);
  console.log(`   生成样本: ${diagnosisReport.totalSamples}个`);
  console.log(`   成功率: ${(diagnosisReport.totalSamples / (testCombinations.length * 5 * 10) * 100).toFixed(1)}%`);

  console.log('\n🔍 关键发现:');
  Object.entries(diagnosisReport.analysis).forEach(([key, value]) => {
    console.log(`   ${key}: ${value}`);
  });

  console.log('\n❌ 识别问题:');
  diagnosisReport.problems.forEach((problem, index) => {
    console.log(`   ${index + 1}. ${problem.title}`);
    console.log(`      严重程度: ${problem.severity}`);
    console.log(`      问题描述: ${problem.description}`);
    console.log(`      影响范围: ${problem.impact}`);
  });

  console.log('\n💡 解决方案:');
  diagnosisReport.solutions.forEach((solution, index) => {
    console.log(`   ${index + 1}. ${solution.title}`);
    console.log(`      实施优先级: ${solution.priority}`);
    console.log(`      具体方案: ${solution.description}`);
    console.log(`      预期效果: ${solution.expectedImpact}`);
  });

  // 保存诊断结果
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const filename = `task1-complexity-diagnosis-${timestamp}.json`;

  fs.writeFileSync(filename, JSON.stringify({
    diagnosisReport,
    allResults,
    timestamp: new Date().toISOString()
  }, null, 2));

  console.log(`\n💾 诊断结果已保存到: ${filename}`);

  return diagnosisReport;
}

// 分析复杂度趋势
function analyzeComplexityTrends(complexityResults) {
  const lengths = complexityResults.map(r => parseFloat(r.statistics.avgLength));
  const qualities = complexityResults.map(r => parseFloat(r.statistics.avgQuality));
  const structureCounts = complexityResults.map(r => r.statistics.structureCount);

  return {
    differenceClarity: calculateDifferenceClarity(lengths, qualities, structureCounts),
    lengthTrend: analyzeTrend(lengths, '长度'),
    qualityTrend: analyzeTrend(qualities, '质量'),
    structureTrend: analyzeTrend(structureCounts, '结构复杂度')
  };
}

// 计算差异明显度
function calculateDifferenceClarity(lengths, qualities, structures) {
  const lengthVariance = calculateVariance(lengths);
  const qualityVariance = calculateVariance(qualities);
  const structureVariance = calculateVariance(structures);

  const avgVariance = (lengthVariance + qualityVariance + structureVariance) / 3;

  if (avgVariance > 2) return '明显';
  if (avgVariance > 1) return '中等';
  return '不明显';
}

// 分析趋势
function analyzeTrend(values, name) {
  const first = values[0];
  const last = values[values.length - 1];
  const change = ((last - first) / first * 100).toFixed(1);

  if (Math.abs(change) < 5) return `${name}基本稳定 (${change}%)`;
  if (change > 0) return `${name}递增趋势 (+${change}%)`;
  return `${name}递减趋势 (${change}%)`;
}

// 计算方差
function calculateVariance(values) {
  const mean = values.reduce((a, b) => a + b) / values.length;
  const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length;
  return variance;
}

// 执行诊断分析
function performDiagnosisAnalysis(results) {
  const analysis = {};

  // 分析复杂度差异明显度
  const clarityLevels = results.map(r => r.summary.differenceClarity);
  const obviousDiff = clarityLevels.filter(c => c === '明显').length;
  analysis['复杂度差异明显度'] = `${obviousDiff}/${results.length}个组合差异明显`;

  // 分析长度变化趋势
  const lengthTrends = results.map(r => r.summary.lengthTrend);
  const increasingLength = lengthTrends.filter(t => t.includes('递增')).length;
  analysis['长度变化趋势'] = `${increasingLength}/${results.length}个组合长度递增`;

  // 分析质量变化趋势
  const qualityTrends = results.map(r => r.summary.qualityTrend);
  const stableQuality = qualityTrends.filter(t => t.includes('稳定')).length;
  analysis['质量变化趋势'] = `${stableQuality}/${results.length}个组合质量稳定`;

  return analysis;
}

// 识别问题
function identifyProblems(analysis) {
  const problems = [];

  // 检查复杂度差异问题
  if (analysis['复杂度差异明显度'].includes('0/') || analysis['复杂度差异明显度'].includes('1/')) {
    problems.push({
      title: '复杂度差异不明显',
      severity: '高',
      description: '不同复杂度级别生成的用户名在结构和质量上差异不够明显',
      impact: '用户无法感受到复杂度调节的效果，影响功能价值',
      rootCause: '词库元素不足或生成逻辑过于简化'
    });
  }

  // 检查长度变化问题
  if (!analysis['长度变化趋势'].includes('递增')) {
    problems.push({
      title: '长度变化趋势不符合预期',
      severity: '中',
      description: '复杂度提升时用户名长度没有相应增加',
      impact: '复杂度的视觉表现不够直观',
      rootCause: '复杂度实现逻辑需要优化'
    });
  }

  // 检查质量稳定性问题
  if (analysis['质量变化趋势'].includes('0/')) {
    problems.push({
      title: '质量评估不稳定',
      severity: '中',
      description: '不同复杂度下的质量评估波动过大',
      impact: '质量评估的可信度降低',
      rootCause: '质量评估算法需要调优'
    });
  }

  return problems;
}

// 生成解决方案
function generateSolutions(problems) {
  const solutions = [];

  problems.forEach(problem => {
    switch (problem.title) {
      case '复杂度差异不明显':
        solutions.push({
          title: '增强复杂度差异化实现',
          priority: '高',
          description: '1. 扩展词库规模，为不同复杂度提供更多元素选择；2. 优化生成逻辑，确保高复杂度使用更复杂的语法结构；3. 增加复杂度特有的生成模式',
          expectedImpact: '用户能明显感受到复杂度调节效果，提升功能价值',
          codeChanges: [
            '扩展elementLibrary中各类词库的规模',
            '在generateByComplexity方法中增强复杂度差异化逻辑',
            '为高复杂度添加专属的语法结构模板'
          ]
        });
        break;

      case '长度变化趋势不符合预期':
        solutions.push({
          title: '优化复杂度长度控制逻辑',
          priority: '中',
          description: '调整生成逻辑，确保高复杂度生成更长的用户名，通过增加修饰词、连接词等方式',
          expectedImpact: '复杂度的视觉表现更加直观',
          codeChanges: [
            '修改generateByComplexity方法中的元素组合逻辑',
            '为高复杂度添加更多修饰元素',
            '增加复杂句式结构'
          ]
        });
        break;

      case '质量评估不稳定':
        solutions.push({
          title: '优化质量评估算法',
          priority: '中',
          description: '调整assessCreativityByComplexity方法，减少随机波动，增加基于实际特征的评估',
          expectedImpact: '质量评估更加稳定和可信',
          codeChanges: [
            '减少质量评估中的随机因子',
            '增加基于用户名实际特征的评估维度',
            '优化四维评估的权重分配'
          ]
        });
        break;
    }
  });

  return solutions;
}

// 运行诊断
runMultiThemeComplexityDiagnosis().then(report => {
  console.log('\n🏁 任务1: 多主题复杂度生成效果诊断完成');
  console.log(`诊断结果: ${report.problems.length}个问题，${report.solutions.length}个解决方案`);
}).catch(error => {
  console.error('任务1执行失败:', error);
});