/**
 * 任务2: 词库语料丰富度评估与扩充方案
 * 全面分析当前V5引擎词库规模和质量，制定详细扩充方案
 */

const fs = require('fs');

console.log('📋 任务2: 词库语料丰富度评估与扩充方案');
console.log('='.repeat(80));

// V5引擎词库分析器
class V5VocabularyAnalyzer {
  constructor() {
    this.currentVocabulary = this.extractCurrentVocabulary();
    this.expansionPlan = this.createExpansionPlan();
  }
  
  // 提取当前词库
  extractCurrentVocabulary() {
    return {
      权威修饰词: {
        current: ['首席', '高级', '资深', '专业', '认证', '特级', '顶级', '超级', '终极', '至尊', '王牌', '精英'],
        count: 12,
        category: '修饰类',
        usage_frequency: 'high',
        quality_score: 8.5
      },
      日常行为动词: {
        current: ['吃', '睡', '玩', '工作', '学习', '跑步', '游泳', '阅读', '写作', '思考', '发呆', '散步'],
        count: 12,
        category: '动作类',
        usage_frequency: 'high',
        quality_score: 8.0
      },
      网络行为动词: {
        current: ['直播', '带货', '刷视频', '点赞', '转发', '评论', '分享', '关注', '取关', '拉黑', '举报', '私信'],
        count: 12,
        category: '动作类',
        usage_frequency: 'high',
        quality_score: 9.0
      },
      现代生活动词: {
        current: ['洗头', '化妆', '健身', '减肥', '加班', '购物', '旅游', '聚餐', '约会', '相亲', '搬家', '装修'],
        count: 12,
        category: '动作类',
        usage_frequency: 'medium',
        quality_score: 8.5
      },
      职位后缀词: {
        current: ['官', '师', '专家', '大使', '代表', '委员', '顾问', '总监', '主任', '经理', '主管', '负责人'],
        count: 12,
        category: '后缀类',
        usage_frequency: 'high',
        quality_score: 8.0
      },
      古代人物词: {
        current: ['贫僧', '道士', '书生', '侠客', '状元', '秀才', '举人', '进士', '太监', '皇帝', '将军', '丞相'],
        count: 12,
        category: '主体类',
        usage_frequency: 'medium',
        quality_score: 9.0
      },
      抽象概念词: {
        current: ['快乐', '悲伤', '愤怒', '温柔', '智慧', '勇气', '希望', '梦想', '孤独', '焦虑', '兴奋', '平静'],
        count: 12,
        category: '概念类',
        usage_frequency: 'medium',
        quality_score: 8.5
      },
      天体宇宙词: {
        current: ['月亮', '太阳', '星星', '银河', '宇宙', '黑洞', '彗星', '流星', '行星', '恒星', '星云', '陨石'],
        count: 12,
        category: '概念类',
        usage_frequency: 'low',
        quality_score: 8.0
      },
      对比转折词: {
        current: ['但', '却', '然而', '不过', '虽然', '尽管', '即使', '哪怕', '纵然', '纵使', '就算', '便是'],
        count: 12,
        category: '连接类',
        usage_frequency: 'medium',
        quality_score: 7.5
      },
      服务角色词: {
        current: ['邮递员', '收集员', '配送员', '制造商', '贩卖机', '专卖店', '客服', '导航员', '修理工', '设计师', '顾问', '助手'],
        count: 12,
        category: '角色类',
        usage_frequency: 'medium',
        quality_score: 8.0
      },
      生活概念词: {
        current: ['人生', '梦想', '快乐', '悲伤', '爱情', '友情', '工作', '学习', '生活', '未来', '过去', '现在'],
        count: 12,
        category: '概念类',
        usage_frequency: 'high',
        quality_score: 9.0
      },
      技术术语词: {
        current: ['正在缓冲', '连接超时', '服务器宕机', '数据库损坏', '404未找到', '503不可用', '系统维护', '正在加载', '网络异常', '权限不足', '访问拒绝', '请求超时'],
        count: 12,
        category: '技术类',
        usage_frequency: 'medium',
        quality_score: 9.5
      },
      谐音词库: {
        current: [
          '知识就是力量→芝士就是力量', '没心没肺→莓心没肺', '无恶不作→无饿不作',
          '有压力很大→有鸭梨很大', '一见钟情→一见粽情', '心想事成→薪想事成',
          '马到成功→码到成功', '天马行空→天码行空', '年年有余→年年有鱼',
          '步步高升→布布高升', '一帆风顺→一番风顺', '财源广进→菜源广进'
        ],
        count: 12,
        category: '谐音类',
        usage_frequency: 'medium',
        quality_score: 9.0
      }
    };
  }
  
  // 创建扩充方案
  createExpansionPlan() {
    return {
      权威修饰词: {
        target_count: 50,
        priority: 'high',
        timeline: '1个月',
        expansion_sources: ['商业术语', '学术称谓', '军事等级', '技术认证'],
        new_words: [
          // 商业术语 (15个)
          '执行', '战略', '核心', '领先', '顶尖', '卓越', '精品', '旗舰', '标杆', '典范', '权威', '官方', '正版', '原创', '独家',
          // 学术称谓 (10个)
          '博士', '教授', '院士', '学者', '研究员', '专员', '讲师', '导师', '学霸', '大神',
          // 军事等级 (8个)
          '元帅', '将军', '上校', '少校', '中尉', '上士', '班长', '队长',
          // 技术认证 (5个)
          '认证', '授权', '许可', '注册', '验证'
        ],
        quality_control: '确保词汇具有权威感和正式性，避免过于夸张或不合适的表达'
      },
      日常行为动词: {
        target_count: 80,
        priority: 'high',
        timeline: '2个月',
        expansion_sources: ['生活起居', '娱乐休闲', '社交活动', '学习工作'],
        new_words: [
          // 生活起居 (20个)
          '起床', '洗漱', '刷牙', '洗脸', '梳头', '穿衣', '做饭', '吃饭', '喝水', '休息',
          '午睡', '晚睡', '早起', '熬夜', '打扫', '整理', '收拾', '洗衣', '晾衣', '叠衣',
          // 娱乐休闲 (15个)
          '看剧', '听歌', '唱歌', '跳舞', '画画', '写字', '拍照', '录像', '玩游戏', '看书',
          '看报', '看新闻', '聊天', '发呆', '冥想',
          // 社交活动 (15个)
          '聚会', '聚餐', '约会', '相亲', '交友', '串门', '拜访', '做客', '请客', '送礼',
          '祝贺', '慰问', '探望', '陪伴', '照顾',
          // 学习工作 (18个)
          '上班', '下班', '加班', '请假', '出差', '开会', '汇报', '总结', '计划', '安排',
          '执行', '检查', '审核', '批准', '签字', '盖章', '存档', '备份'
        ],
        quality_control: '选择常见且容易理解的动词，避免过于专业或生僻的词汇'
      },
      网络行为动词: {
        target_count: 60,
        priority: 'high',
        timeline: '1个月',
        expansion_sources: ['社交媒体', '内容创作', '电商购物', '在线娱乐'],
        new_words: [
          // 社交媒体 (15个)
          '发朋友圈', '发微博', '发动态', '刷抖音', '刷微博', '刷朋友圈', '点赞', '评论', '转发', '收藏',
          '关注', '取关', '拉黑', '举报', '私信',
          // 内容创作 (12个)
          '写文章', '发视频', '做直播', '录音频', '做PPT', '写代码', '做设计', '修图', '剪视频', '配音',
          '写脚本', '做策划',
          // 电商购物 (12个)
          '网购', '下单', '付款', '收货', '退货', '换货', '评价', '晒单', '比价', '砍价',
          '拼团', '秒杀',
          // 在线娱乐 (9个)
          '看直播', '打游戏', '听音乐', '看电影', '追剧', '刷短视频', '看小说', '听播客', '上网课'
        ],
        quality_control: '紧跟网络流行趋势，选择年轻人熟悉的网络行为'
      },
      职位后缀词: {
        target_count: 60,
        priority: 'medium',
        timeline: '1个月',
        expansion_sources: ['传统职位', '新兴职业', '网络称谓', '技能标签'],
        new_words: [
          // 传统职位 (15个)
          '董事长', '总经理', '副总', '部长', '科长', '组长', '主管', '主任', '经理', '助理',
          '秘书', '文员', '会计', '出纳', '司机',
          // 新兴职业 (15个)
          '产品经理', '运营专员', '数据分析师', 'UI设计师', 'UX设计师', '前端工程师', '后端工程师',
          '测试工程师', '运维工程师', '算法工程师', '人工智能工程师', '区块链工程师', '网红', '博主', 'UP主',
          // 网络称谓 (10个)
          '大神', '大佬', '大咖', '达人', '高手', '专家', '老师', '导师', '偶像', '网红',
          // 技能标签 (8个)
          '选手', '玩家', '爱好者', '收藏家', '发烧友', '粉丝', '追星族', '技术宅'
        ],
        quality_control: '平衡传统与现代，确保称谓的时代感和亲和力'
      },
      古代人物词: {
        target_count: 40,
        priority: 'medium',
        timeline: '2个月',
        expansion_sources: ['历史人物', '文学角色', '神话传说', '古代职业'],
        new_words: [
          // 历史人物 (10个)
          '孔子', '老子', '庄子', '孟子', '诸葛亮', '曹操', '刘备', '关羽', '张飞', '赵云',
          // 文学角色 (8个)
          '唐僧', '孙悟空', '猪八戒', '沙僧', '林黛玉', '贾宝玉', '武松', '林冲',
          // 神话传说 (6个)
          '玉帝', '王母', '观音', '如来', '太上老君', '嫦娥',
          // 古代职业 (4个)
          '捕快', '镖师', '账房', '掌柜'
        ],
        quality_control: '选择知名度高、文化内涵丰富的古代元素'
      },
      谐音词库: {
        target_count: 200,
        priority: 'high',
        timeline: '3个月',
        expansion_sources: ['成语谐音', '网络流行语', '生活俗语', '职场用语', '技术术语'],
        new_categories: {
          成语谐音: {
            target: 80,
            examples: [
              '一鸣惊人→一名惊人', '画龙点睛→画龙点晴', '守株待兔→守猪待兔',
              '亡羊补牢→亡羊补劳', '杯水车薪→杯水车新', '刻舟求剑→刻舟求键'
            ]
          },
          网络流行语: {
            target: 60,
            examples: [
              '社会我虎哥→社会我胡哥', '确认过眼神→确认过颜值', '我太南了→我太难了',
              '雨女无瓜→与你无关', '好嗨哟→好high哟', '盘他→盘它'
            ]
          },
          生活俗语: {
            target: 40,
            examples: [
              '吃得苦中苦→吃得库中库', '早起的鸟儿有虫吃→早起的鸟儿有虫次',
              '一分耕耘一分收获→一分耕耘一分收货'
            ]
          },
          职场用语: {
            target: 20,
            examples: [
              '升职加薪→升值加心', '工作汇报→工作会抱', '团队合作→团队和作'
            ]
          }
        },
        quality_control: '确保谐音自然流畅，保持原意的同时增加趣味性'
      }
    };
  }
  
  // 执行词库评估
  performVocabularyAssessment() {
    console.log('🔍 当前词库规模和质量评估\n');
    
    let totalWords = 0;
    let totalCategories = 0;
    let avgQuality = 0;
    
    console.log('📊 各类词库详细统计:');
    console.log('-'.repeat(80));
    
    Object.entries(this.currentVocabulary).forEach(([category, data]) => {
      totalWords += data.count;
      totalCategories++;
      avgQuality += data.quality_score;
      
      console.log(`\n📚 ${category}:`);
      console.log(`   当前规模: ${data.count}个`);
      console.log(`   使用频率: ${data.usage_frequency}`);
      console.log(`   质量评分: ${data.quality_score}/10`);
      console.log(`   词汇示例: ${data.current.slice(0, 6).join(', ')}...`);
      
      // 分析词库密度
      const density = this.calculateVocabularyDensity(data.count, data.usage_frequency);
      console.log(`   词库密度: ${density.level} (${density.description})`);
    });
    
    avgQuality = avgQuality / totalCategories;
    
    console.log('\n📈 词库总体评估:');
    console.log(`   总词汇量: ${totalWords}个`);
    console.log(`   词库类别: ${totalCategories}个`);
    console.log(`   平均质量: ${avgQuality.toFixed(1)}/10`);
    console.log(`   整体评级: ${this.getOverallRating(totalWords, avgQuality)}`);
    
    return {
      totalWords,
      totalCategories,
      avgQuality,
      overallRating: this.getOverallRating(totalWords, avgQuality)
    };
  }
  
  // 计算词库密度
  calculateVocabularyDensity(count, frequency) {
    const densityScore = count * this.getFrequencyWeight(frequency);
    
    if (densityScore >= 100) return { level: '高密度', description: '词汇丰富，选择多样' };
    if (densityScore >= 60) return { level: '中密度', description: '词汇适中，基本够用' };
    return { level: '低密度', description: '词汇不足，需要扩充' };
  }
  
  // 获取频率权重
  getFrequencyWeight(frequency) {
    const weights = { 'high': 10, 'medium': 6, 'low': 3 };
    return weights[frequency] || 5;
  }
  
  // 获取整体评级
  getOverallRating(totalWords, avgQuality) {
    const score = (totalWords / 20) + avgQuality;
    
    if (score >= 15) return '优秀';
    if (score >= 12) return '良好';
    if (score >= 9) return '一般';
    return '需改进';
  }
  
  // 生成扩充方案
  generateExpansionPlan() {
    console.log('\n📋 详细词库扩充方案');
    console.log('='.repeat(80));
    
    const implementationPlan = {
      phases: [],
      totalTargetWords: 0,
      totalCurrentWords: 0,
      expansionRatio: 0,
      timeline: '6个月',
      budget_estimate: '中等',
      resource_requirements: []
    };
    
    // 按优先级排序
    const sortedCategories = Object.entries(this.expansionPlan)
      .sort(([,a], [,b]) => {
        const priorityOrder = { 'high': 3, 'medium': 2, 'low': 1 };
        return priorityOrder[b.priority] - priorityOrder[a.priority];
      });
    
    console.log('\n🎯 扩充优先级排序:');
    
    sortedCategories.forEach(([category, plan], index) => {
      const currentCount = this.currentVocabulary[category]?.count || 0;
      const expansion = plan.target_count - currentCount;
      const expansionRatio = ((expansion / currentCount) * 100).toFixed(1);
      
      implementationPlan.totalTargetWords += plan.target_count;
      implementationPlan.totalCurrentWords += currentCount;
      
      console.log(`\n   ${index + 1}. ${category} (${plan.priority}优先级):`);
      console.log(`      当前规模: ${currentCount}个`);
      console.log(`      目标规模: ${plan.target_count}个`);
      console.log(`      需要扩充: ${expansion}个 (+${expansionRatio}%)`);
      console.log(`      完成时间: ${plan.timeline}`);
      console.log(`      扩充来源: ${plan.expansion_sources.join(', ')}`);
      
      if (plan.new_words) {
        console.log(`      新词示例: ${plan.new_words.slice(0, 8).join(', ')}...`);
      }
      
      if (plan.new_categories) {
        console.log(`      新增类别:`);
        Object.entries(plan.new_categories).forEach(([subcat, subplan]) => {
          console.log(`        ${subcat}: ${subplan.target}个 (${subplan.examples.slice(0, 3).join(', ')}...)`);
        });
      }
      
      console.log(`      质量控制: ${plan.quality_control}`);
      
      // 添加到实施计划
      implementationPlan.phases.push({
        phase: Math.ceil((index + 1) / 3),
        category,
        priority: plan.priority,
        timeline: plan.timeline,
        currentCount,
        targetCount: plan.target_count,
        expansion,
        expansionRatio: parseFloat(expansionRatio)
      });
    });
    
    implementationPlan.expansionRatio = ((implementationPlan.totalTargetWords - implementationPlan.totalCurrentWords) / implementationPlan.totalCurrentWords * 100).toFixed(1);
    
    return implementationPlan;
  }
  
  // 生成实施步骤
  generateImplementationSteps(plan) {
    console.log('\n🚀 可执行的实施步骤');
    console.log('='.repeat(80));
    
    const steps = [
      {
        step: 1,
        title: '词库收集阶段',
        duration: '2周',
        description: '收集和整理各类词汇资源',
        tasks: [
          '建立词汇收集表格模板',
          '从网络资源收集热门词汇',
          '整理传统文化相关词汇',
          '收集行业专业术语',
          '建立词汇分类标准'
        ]
      },
      {
        step: 2,
        title: '质量筛选阶段',
        duration: '1周',
        description: '对收集的词汇进行质量评估和筛选',
        tasks: [
          '制定词汇质量评估标准',
          '进行词汇适用性测试',
          '去除重复和不合适词汇',
          '按使用频率分级',
          '建立词汇质量档案'
        ]
      },
      {
        step: 3,
        title: '分类组织阶段',
        duration: '1周',
        description: '将筛选后的词汇按类别组织',
        tasks: [
          '建立词汇分类体系',
          '为每个词汇添加标签',
          '建立词汇关联关系',
          '制作词汇使用指南',
          '建立词汇更新机制'
        ]
      },
      {
        step: 4,
        title: '系统集成阶段',
        duration: '1周',
        description: '将新词库集成到V5引擎中',
        tasks: [
          '更新词库数据文件',
          '修改生成算法逻辑',
          '进行系统兼容性测试',
          '优化词汇选择权重',
          '更新API文档'
        ]
      },
      {
        step: 5,
        title: '测试验证阶段',
        duration: '1周',
        description: '全面测试扩充后的词库效果',
        tasks: [
          '进行生成效果测试',
          '评估词汇使用分布',
          '检查重复率变化',
          '测试用户接受度',
          '收集反馈意见'
        ]
      },
      {
        step: 6,
        title: '优化调整阶段',
        duration: '1周',
        description: '根据测试结果进行优化调整',
        tasks: [
          '调整词汇权重分配',
          '优化生成算法参数',
          '修复发现的问题',
          '完善词库文档',
          '制定维护计划'
        ]
      }
    ];
    
    console.log(`\n📅 总体时间安排: ${steps.length}个阶段，预计${steps.reduce((sum, step) => sum + parseInt(step.duration), 0)}周完成\n`);
    
    steps.forEach(step => {
      console.log(`阶段${step.step}: ${step.title} (${step.duration})`);
      console.log(`   目标: ${step.description}`);
      console.log(`   任务清单:`);
      step.tasks.forEach((task, index) => {
        console.log(`     ${index + 1}. ${task}`);
      });
      console.log('');
    });
    
    return steps;
  }

  // 生成质量控制标准
  generateQualityControlStandards() {
    return {
      词汇筛选标准: {
        基本要求: [
          '字符长度2-6个字符',
          '含义明确，无歧义',
          '适合目标用户群体',
          '符合社会主流价值观',
          '避免敏感词汇'
        ],
        质量评估维度: [
          '常用性 (1-10分): 词汇在日常生活中的使用频率',
          '趣味性 (1-10分): 词汇的幽默感和创意度',
          '时代性 (1-10分): 词汇的时代感和流行度',
          '文化性 (1-10分): 词汇的文化内涵和底蕴',
          '适配性 (1-10分): 词汇与生成模式的匹配度'
        ],
        淘汰标准: [
          '总分低于30分的词汇',
          '任一维度低于4分的词汇',
          '与现有词汇高度重复的词汇',
          '可能引起争议的词汇',
          '过于生僻难懂的词汇'
        ]
      },
      分类组织标准: {
        主分类原则: [
          '按词性分类 (名词、动词、形容词等)',
          '按主题分类 (技术、文化、生活等)',
          '按使用场景分类 (正式、非正式、网络等)',
          '按时代特征分类 (传统、现代、未来等)'
        ],
        标签体系: [
          '使用频率标签: high/medium/low',
          '适用年龄标签: young/adult/all',
          '文化背景标签: traditional/modern/international',
          '情感色彩标签: positive/neutral/negative',
          '专业程度标签: general/professional/expert'
        ]
      },
      更新维护标准: {
        定期评估: [
          '每季度评估词汇使用频率',
          '每半年更新流行词汇',
          '每年进行全面质量审核',
          '根据用户反馈及时调整'
        ],
        动态优化: [
          '监控生成结果的用户满意度',
          '分析词汇使用的分布情况',
          '识别和补充缺失的词汇类型',
          '淘汰过时或不合适的词汇'
        ]
      }
    };
  }
}

// 执行词库评估与扩充方案制定
async function runVocabularyAssessmentAndExpansion() {
  console.log('🚀 开始词库语料丰富度评估与扩充方案制定\n');

  const analyzer = new V5VocabularyAnalyzer();

  // 1. 执行当前词库评估
  const assessment = analyzer.performVocabularyAssessment();

  // 2. 生成扩充方案
  const expansionPlan = analyzer.generateExpansionPlan();

  // 3. 生成实施步骤
  const implementationSteps = analyzer.generateImplementationSteps(expansionPlan);

  // 4. 生成质量控制标准
  const qualityStandards = analyzer.generateQualityControlStandards();

  // 5. 生成综合报告
  console.log('\n📋 词库评估与扩充综合报告');
  console.log('='.repeat(80));

  console.log('\n📊 当前状况总结:');
  console.log(`   词库总量: ${assessment.totalWords}个词汇`);
  console.log(`   词库类别: ${assessment.totalCategories}个类别`);
  console.log(`   平均质量: ${assessment.avgQuality.toFixed(1)}/10分`);
  console.log(`   整体评级: ${assessment.overallRating}`);

  console.log('\n🎯 扩充目标:');
  console.log(`   目标总量: ${expansionPlan.totalTargetWords}个词汇`);
  console.log(`   扩充数量: ${expansionPlan.totalTargetWords - expansionPlan.totalCurrentWords}个`);
  console.log(`   扩充比例: +${expansionPlan.expansionRatio}%`);
  console.log(`   完成时间: ${expansionPlan.timeline}`);
  console.log(`   预算评估: ${expansionPlan.budget_estimate}`);

  console.log('\n📈 预期效果:');
  const expectedImprovements = calculateExpectedImprovements(assessment, expansionPlan);
  Object.entries(expectedImprovements).forEach(([metric, improvement]) => {
    console.log(`   ${metric}: ${improvement}`);
  });

  console.log('\n⚠️ 风险评估:');
  const risks = assessImplementationRisks(expansionPlan);
  risks.forEach((risk, index) => {
    console.log(`   ${index + 1}. ${risk.title} (${risk.level}风险)`);
    console.log(`      影响: ${risk.impact}`);
    console.log(`      缓解措施: ${risk.mitigation}`);
  });

  console.log('\n💡 关键建议:');
  const recommendations = generateKeyRecommendations(assessment, expansionPlan);
  recommendations.forEach((rec, index) => {
    console.log(`   ${index + 1}. ${rec.title}`);
    console.log(`      重要性: ${rec.importance}`);
    console.log(`      具体建议: ${rec.description}`);
  });

  // 6. 保存完整报告
  const comprehensiveReport = {
    timestamp: new Date().toISOString(),
    currentAssessment: assessment,
    expansionPlan,
    implementationSteps,
    qualityStandards,
    expectedImprovements,
    risks,
    recommendations,
    summary: {
      currentWords: assessment.totalWords,
      targetWords: expansionPlan.totalTargetWords,
      expansionRatio: parseFloat(expansionPlan.expansionRatio),
      timeline: expansionPlan.timeline,
      overallRating: assessment.overallRating,
      implementationComplexity: 'medium'
    }
  };

  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const filename = `task2-vocabulary-expansion-plan-${timestamp}.json`;

  fs.writeFileSync(filename, JSON.stringify(comprehensiveReport, null, 2));
  console.log(`\n💾 完整报告已保存到: ${filename}`);

  return comprehensiveReport;
}

// 计算预期改进效果
function calculateExpectedImprovements(assessment, plan) {
  const currentWords = assessment.totalWords;
  const targetWords = plan.totalTargetWords;
  const expansionRatio = parseFloat(plan.expansionRatio);

  return {
    '词汇丰富度': `提升${expansionRatio.toFixed(1)}%，从${currentWords}个增加到${targetWords}个`,
    '生成多样性': `预计提升${Math.min(expansionRatio * 0.8, 200).toFixed(1)}%，大幅减少重复率`,
    '用户满意度': `预计提升${Math.min(expansionRatio * 0.3, 50).toFixed(1)}%，更符合用户期望`,
    '系统竞争力': `词库规模达到行业领先水平，提升${Math.min(expansionRatio * 0.5, 100).toFixed(1)}%`,
    '文化内涵': `增加传统文化和现代元素，提升文化底蕴${Math.min(expansionRatio * 0.4, 80).toFixed(1)}%`
  };
}

// 评估实施风险
function assessImplementationRisks(plan) {
  return [
    {
      title: '词汇质量控制风险',
      level: '中等',
      impact: '可能引入不合适或低质量的词汇，影响生成效果',
      mitigation: '建立严格的质量评估标准，多轮筛选和测试'
    },
    {
      title: '系统性能影响风险',
      level: '低',
      impact: '词库扩大可能影响生成速度和系统响应',
      mitigation: '优化数据结构和算法，进行性能测试'
    },
    {
      title: '文化敏感性风险',
      level: '中等',
      impact: '部分词汇可能存在文化敏感性或争议性',
      mitigation: '建立文化敏感性审核机制，多方面征求意见'
    },
    {
      title: '维护成本增加风险',
      level: '低',
      impact: '词库规模增大会增加后续维护和更新的工作量',
      mitigation: '建立自动化维护工具和标准化流程'
    }
  ];
}

// 生成关键建议
function generateKeyRecommendations(assessment, plan) {
  return [
    {
      title: '优先扩充高频使用词库',
      importance: '高',
      description: '重点扩充权威修饰词、网络行为动词等高频使用的词库，能够快速提升用户体验'
    },
    {
      title: '建立动态更新机制',
      importance: '高',
      description: '建立词库的动态更新和维护机制，及时跟进网络流行语和时代变化'
    },
    {
      title: '注重文化平衡',
      importance: '中',
      description: '在扩充过程中平衡传统文化和现代元素，满足不同用户群体的需求'
    },
    {
      title: '实施分阶段扩充',
      importance: '中',
      description: '按优先级分阶段实施扩充计划，每个阶段都进行效果评估和调整'
    },
    {
      title: '建立用户反馈机制',
      importance: '中',
      description: '建立用户反馈收集和分析机制，根据实际使用效果调整词库内容'
    }
  ];
}

// 运行评估
runVocabularyAssessmentAndExpansion().then(report => {
  console.log('\n🏁 任务2: 词库语料丰富度评估与扩充方案完成');
  console.log(`扩充目标: ${report.summary.currentWords} → ${report.summary.targetWords}个词汇 (+${report.summary.expansionRatio}%)`);
  console.log(`实施复杂度: ${report.summary.implementationComplexity}`);
  console.log(`预期完成时间: ${report.summary.timeline}`);
}).catch(error => {
  console.error('任务2执行失败:', error);
});
