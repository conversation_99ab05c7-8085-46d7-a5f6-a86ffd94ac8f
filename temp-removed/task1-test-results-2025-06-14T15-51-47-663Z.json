{"summary": {"totalTests": 51, "successfulTests": 51, "successRate": 100, "totalUsernames": 510, "avgQuality": 88.2, "qualityRange": "86.4% - 89.9%", "avgResponseTime": 0.2, "categoryStats": {"pattern": {"total": 6, "successful": 6, "avgQuality": "88.2", "successRate": "100.0"}}}, "testResults": [{"category": "pattern", "combination": "模式: identity_elevation", "style": "modern", "themes": ["humor"], "complexity": 3, "pattern": "identity_elevation", "count": 10, "successCount": 10, "successRate": 1, "avgQuality": 0.890514999588538, "responseTime": 0, "samples": ["至尊学习负责人", "至尊吃大使", "首席睡负责人"], "patternDistribution": {"身份升维包装": 10}}, {"category": "pattern", "combination": "模式: contradiction_unity", "style": "modern", "themes": ["humor"], "complexity": 3, "pattern": "contradiction_unity", "count": 10, "successCount": 10, "successRate": 1, "avgQuality": 0.8896419296029443, "responseTime": 0, "samples": ["乐观尽管自卑", "温柔纵使急躁", "勤奋纵使悲观"], "patternDistribution": {"矛盾统一": 10}}, {"category": "pattern", "combination": "模式: temporal_displacement", "style": "modern", "themes": ["humor"], "complexity": 3, "pattern": "temporal_displacement", "count": 10, "successCount": 10, "successRate": 1, "avgQuality": 0.8779647898399574, "responseTime": 0, "samples": ["进士约会", "皇帝拉黑", "道士洗头"], "patternDistribution": {"时空错位重组": 10}}, {"category": "pattern", "combination": "模式: service_personification", "style": "modern", "themes": ["humor"], "complexity": 3, "pattern": "service_personification", "count": 10, "successCount": 10, "successRate": 1, "avgQuality": 0.8640436696260313, "responseTime": 0, "samples": ["黑洞客服", "陨石贩卖机", "彗星制造商"], "patternDistribution": {"服务拟人化": 10}}, {"category": "pattern", "combination": "模式: tech_expression", "style": "modern", "themes": ["humor"], "complexity": 3, "pattern": "tech_expression", "count": 10, "successCount": 10, "successRate": 1, "avgQuality": 0.8708181215821129, "responseTime": 0, "samples": ["工作正在缓冲", "友情系统维护", "梦想访问拒绝"], "patternDistribution": {"技术化表达": 10}}, {"category": "pattern", "combination": "模式: homophone_creative", "style": "modern", "themes": ["humor"], "complexity": 3, "pattern": "homophone_creative", "count": 10, "successCount": 10, "successRate": 1, "avgQuality": 0.8993394707324833, "responseTime": 0, "samples": ["菜源广进", "无饿不作", "布布高升"], "patternDistribution": {"创意谐音": 10}}], "allUsernames": ["友情404未找到", "过去请求超时", "贫僧相亲", "高级工作代表", "快乐网络异常", "特级发呆主任", "未来服务器宕机", "书生洗头", "太监聚餐", "丞相分享", "将军带货", "友情404未找到", "终极阅读代表", "节约纵然悲观", "至尊学习顾问", "过去正在缓冲", "坚强不过挥霍", "认证写作负责人", "温柔不过懒惰", "快乐请求超时", "菜源广进", "无饿不作", "芝士就是力量", "爱情连接超时", "人生数据库损坏", "终极散步负责人", "侠客约会", "无饿不作", "节约就算懒惰", "节约不过挥霍", "认证睡专家", "焦虑配送员", "超级玩负责人", "未来503不可用", "愤怒客服", "布布高升", "太阳邮递员", "专业玩师", "至尊阅读负责人", "首席玩经理", "太阳助手", "爱情服务器宕机", "工作正在加载", "勇气专卖店", "未来服务器宕机", "平静邮递员", "梦想系统维护", "工作连接超时", "工作数据库损坏", "工作数据库损坏", "独立不过悲观", "过去访问拒绝", "未来数据库损坏", "道士减肥", "独立便是脆弱", "梦想连接超时", "精英思考主管", "勤奋尽管急躁", "人生访问拒绝", "学习数据库损坏", "认证工作总监", "生活服务器宕机", "薪想事成", "耐心便是脆弱", "一见粽情", "菜源广进", "乐观便是自卑", "专业游泳委员", "皇帝私信", "高级睡顾问", "皇帝举报", "温柔设计师", "贫僧洗头", "高级工作总监", "过去数据库损坏", "皇帝直播", "丞相洗头", "愤怒助手", "精英吃经理", "年年有鱼", "乐观然而脆弱", "皇帝旅游", "勤奋却急躁", "节约哪怕骄傲", "举人举报", "未来404未找到", "状元化妆", "耐心然而自卑", "过去请求超时", "悲伤系统维护", "谦虚但反骨", "资深工作专家", "工作正在缓冲", "秀才直播", "乐观纵使脆弱", "理性纵使急躁", "快乐404未找到", "理性却挥霍", "耐心但依赖", "人生503不可用", "无饿不作", "布布高升", "过去503不可用", "年年有鱼", "乐观然而悲观", "理性但脆弱", "耐心尽管反骨", "书生拉黑", "学习访问拒绝", "无饿不作", "现在系统维护", "恒星收集员", "莓心没肺", "年年有鱼", "布布高升", "工作系统维护", "悲伤网络异常", "星星客服", "太监加班", "勤奋然而懒惰", "状元刷视频", "彗星邮递员", "希望制造商", "冷静哪怕懒惰", "丞相评论", "听劝然而脆弱", "兴奋贩卖机", "生活404未找到", "梦想404未找到", "梦想网络异常", "听劝虽然依赖", "进士装修", "进士化妆", "丞相关注", "勤奋哪怕自卑", "书生减肥", "学习503不可用", "自信纵然急躁", "耐心纵使依赖", "贫僧私信", "爱情请求超时", "生活权限不足", "一见粽情", "耐心不过骄傲", "书生装修", "谦虚即使急躁", "一见粽情", "自信纵使骄傲", "学习请求超时", "薪想事成", "道士约会", "太阳收集员", "状元评论", "耐心纵使懒惰", "节约尽管冲动", "秀才健身", "月亮设计师", "天码行空", "彗星修理工", "布布高升", "梦想顾问", "工作权限不足", "皇帝举报", "状元健身", "薪想事成", "年年有鱼", "贫僧点赞", "精英发呆负责人", "皇帝约会", "侠客分享", "勤奋纵使自卑", "顶级吃负责人", "谦虚然而反骨", "月亮配送员", "一见粽情", "勇气客服", "温柔收集员", "薪想事成", "听劝就算懒惰", "菜源广进", "温柔即使感性", "薪想事成", "行星邮递员", "一见粽情", "特级睡经理", "一见粽情", "首席散步专家", "高级阅读顾问", "平静制造商", "勤奋虽然反骨", "愤怒导航员", "一番风顺", "特级阅读师", "愤怒制造商", "菜源广进", "终极学习经理", "至尊思考代表", "有鸭梨很大", "芝士就是力量", "温柔贩卖机", "愤怒制造商", "莓心没肺", "陨石专卖店", "丞相私信", "举人化妆", "状元取关", "菜源广进", "莓心没肺", "芝士就是力量", "认证学习顾问", "特级写作专家", "耐心不过挥霍", "无饿不作", "愤怒修理工", "将军装修", "一番风顺", "星星导航员", "贫僧点赞", "理性虽然冲动", "进士私信", "悲伤连接超时", "星星修理工", "愤怒客服", "平静专卖店", "王牌跑步主任", "一见粽情", "未来访问拒绝", "皇帝聚餐", "布布高升", "生活连接超时", "薪想事成", "将军加班", "爱情连接超时", "现在连接超时", "芝士就是力量", "银河邮递员", "码到成功", "布布高升", "精英阅读主管", "学习正在缓冲", "年年有鱼", "行星客服", "无饿不作", "皇帝洗头", "书生搬家", "工作网络异常", "生活访问拒绝", "快乐404未找到", "丞相健身", "码到成功", "勤奋却反骨", "温柔虽然悲观", "书生约会", "秀才装修", "菜源广进", "码到成功", "终极游泳总监", "谦虚虽然脆弱", "彗星助手", "认证阅读负责人", "陨石客服", "莓心没肺", "天码行空", "陨石客服", "将军评论", "勤奋不过挥霍", "书生洗头", "平静设计师", "进士直播", "丞相取关", "行星邮递员", "菜源广进", "侠客带货", "道士私信", "薪想事成", "菜源广进", "有鸭梨很大", "皇帝洗头", "平静专卖店", "平静设计师", "将军举报", "进士转发", "举人私信", "码到成功", "星云导航员", "彗星设计师", "太监举报", "举人健身", "年年有鱼", "行星设计师", "状元旅游", "丞相搬家", "焦虑收集员", "有鸭梨很大", "自信但强硬", "谦虚纵使脆弱", "梦想正在加载", "梦想服务器宕机", "菜源广进", "节约纵使自卑", "现在连接超时", "自信纵然悲观", "侠客洗头", "快乐网络异常", "一番风顺", "进士取关", "流星助手", "独立就算懒惰", "菜源广进", "孤独制造商", "无饿不作", "道士约会", "丞相聚餐", "银河收集员", "梦想503不可用", "悲伤连接超时", "皇帝拉黑", "年年有鱼", "勇气助手", "皇帝旅游", "行星收集员", "听劝不过强硬", "爱情数据库损坏", "悲伤正在加载", "未来服务器宕机", "焦虑导航员", "乐观却自卑", "贫僧减肥", "特级学习委员", "悲伤404未找到", "专业发呆大使", "智慧配送员", "坚强尽管自卑", "希望贩卖机", "专业写作专家", "耐心却懒惰", "至尊发呆大使", "快乐助手", "希望收集员", "耐心不过反骨", "乐观尽管挥霍", "温柔贩卖机", "芝士就是力量", "坚强即使感性", "年年有鱼", "高级吃经理", "兴奋制造商", "特级写作主任", "码到成功", "无饿不作", "勤奋即使依赖", "认证游泳委员", "一番风顺", "无饿不作", "勇气修理工", "高级阅读主管", "无饿不作", "精英跑步顾问", "勤奋却反骨", "莓心没肺", "独立却脆弱", "耐心然而自卑", "举人洗头", "秀才购物", "资深发呆大使", "温柔就算感性", "温柔便是冲动", "谦虚然而反骨", "终极思考代表", "资深阅读经理", "友情正在加载", "节约就算冲动", "梦想系统维护", "节约纵使冲动", "工作数据库损坏", "学习404未找到", "陨石修理工", "秀才拉黑", "快乐连接超时", "梦想404未找到", "丞相洗头", "未来正在缓冲", "人生权限不足", "友情正在缓冲", "码到成功", "温柔即使冲动", "顶级跑步专家", "温柔然而依赖", "菜源广进", "莓心没肺", "愤怒设计师", "生活请求超时", "温柔虽然骄傲", "耐心尽管依赖", "布布高升", "天码行空", "超级吃代表", "星云制造商", "一番风顺", "终极学习专家", "高级睡总监", "资深跑步经理", "一见粽情", "精英发呆顾问", "菜源广进", "精英睡委员", "认证游泳经理", "终极游泳顾问", "认证学习代表", "超级跑步官", "高级阅读经理", "年年有鱼", "终极学习师", "精英散步师", "年年有鱼", "布布高升", "终极工作官", "王牌睡总监", "无饿不作", "至尊跑步师", "温柔然而悲观", "自信纵然强硬", "听劝哪怕反骨", "年年有鱼", "贫僧转发", "乐观尽管强硬", "无饿不作", "一见粽情", "高级写作大使", "天码行空", "友情数据库损坏", "自信但悲观", "梦想网络异常", "书生相亲", "状元拉黑", "理性虽然感性", "坚强纵使感性", "温柔虽然依赖", "听劝不过急躁", "坚强纵然急躁", "梦想连接超时", "耐心不过急躁", "贫僧减肥", "生活503不可用", "举人直播", "勤奋尽管冲动", "将军评论", "梦想权限不足", "友情访问拒绝", "皇帝相亲", "至尊学习负责人", "至尊吃大使", "首席睡负责人", "至尊思考专家", "超级游泳主管", "资深写作官", "资深吃主管", "认证吃专家", "超级玩师", "专业阅读主任", "乐观尽管自卑", "温柔纵使急躁", "勤奋纵使悲观", "勤奋即使急躁", "坚强虽然脆弱", "自信哪怕反骨", "理性虽然急躁", "勤奋却懒惰", "耐心虽然依赖", "谦虚哪怕懒惰", "进士约会", "皇帝拉黑", "道士洗头", "书生直播", "进士评论", "皇帝搬家", "进士旅游", "道士搬家", "道士聚餐", "举人购物", "黑洞客服", "陨石贩卖机", "彗星制造商", "勇气顾问", "陨石导航员", "愤怒贩卖机", "星云助手", "快乐顾问", "希望导航员", "希望客服", "工作正在缓冲", "友情系统维护", "梦想访问拒绝", "学习权限不足", "友情请求超时", "悲伤请求超时", "现在503不可用", "未来连接超时", "过去网络异常", "快乐正在加载", "菜源广进", "无饿不作", "布布高升", "一番风顺", "芝士就是力量", "菜源广进", "一番风顺", "码到成功", "码到成功", "码到成功"], "timestamp": "2025-06-14T15:51:47.663Z"}