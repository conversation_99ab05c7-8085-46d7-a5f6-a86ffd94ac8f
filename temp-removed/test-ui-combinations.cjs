/**
 * 全面测试UI上所有生成组合
 * 检查各种参数组合是否正常工作
 */

console.log('🧪 UI生成组合全面测试');
console.log('='.repeat(80));

// 模拟V5引擎类
class TestV5Engine {
  constructor() {
    this.elementLibrary = this.buildElementLibrary();
    this.generationPatterns = this.buildGenerationPatterns();
  }
  
  buildElementLibrary() {
    return {
      subjects: {
        古代人物: ['贫僧', '道士', '书生', '侠客', '状元'],
        现代职业: ['程序员', '设计师', '产品经理', '运营', '策划'],
        抽象概念: ['快乐', '悲伤', '愤怒', '温柔', '智慧']
      },
      actions: {
        日常行为: ['吃', '睡', '玩', '工作', '学习'],
        网络行为: ['直播', '带货', '刷视频', '点赞', '转发'],
        现代生活: ['洗头', '化妆', '健身', '减肥', '加班']
      },
      modifiers: {
        权威级别: ['首席', '高级', '资深', '专业', '认证'],
        程度强化: ['超级', '极度', '非常', '特别', '完全']
      },
      connectors: {
        对比转折: ['但', '却', '然而', '不过', '虽然']
      }
    };
  }
  
  buildGenerationPatterns() {
    return [
      { id: 'identity_elevation', name: '身份升维包装', weight: 0.96 },
      { id: 'contradiction_unity', name: '矛盾统一', weight: 0.94 },
      { id: 'temporal_displacement', name: '时空错位重组', weight: 0.95 },
      { id: 'service_personification', name: '服务拟人化', weight: 0.92 },
      { id: 'tech_expression', name: '技术化表达', weight: 0.91 },
      { id: 'homophone_creative', name: '创意谐音', weight: 0.95 }
    ];
  }
  
  randomSelect(array) {
    return array[Math.floor(Math.random() * array.length)];
  }
  
  generateByPattern(patternId) {
    const pattern = this.generationPatterns.find(p => p.id === patternId);
    if (!pattern) return null;
    
    let username = '';
    let elementsUsed = [];
    
    switch (patternId) {
      case 'identity_elevation':
        const authority = this.randomSelect(this.elementLibrary.modifiers.权威级别);
        const behavior = this.randomSelect(this.elementLibrary.actions.日常行为);
        const suffix = this.randomSelect(['官', '师', '专家', '大使', '代表']);
        username = `${authority}${behavior}${suffix}`;
        elementsUsed = [authority, behavior, suffix];
        break;
        
      case 'contradiction_unity':
        const positive = this.randomSelect(['温柔', '理性', '冷静', '勤奋', '节约']);
        const connector = this.randomSelect(this.elementLibrary.connectors.对比转折);
        const negative = this.randomSelect(['强硬', '感性', '冲动', '懒惰', '挥霍']);
        username = `${positive}${connector}${negative}`;
        elementsUsed = [positive, connector, negative];
        break;
        
      case 'temporal_displacement':
        const ancient = this.randomSelect(this.elementLibrary.subjects.古代人物);
        const modern = this.randomSelect([...this.elementLibrary.actions.网络行为, ...this.elementLibrary.actions.现代生活]);
        username = `${ancient}${modern}`;
        elementsUsed = [ancient, modern];
        break;
        
      case 'service_personification':
        const concept = this.randomSelect(this.elementLibrary.subjects.抽象概念);
        const service = this.randomSelect(['邮递员', '收集员', '配送员', '制造商', '贩卖机']);
        username = `${concept}${service}`;
        elementsUsed = [concept, service];
        break;
        
      case 'tech_expression':
        const lifeConcept = this.randomSelect(['人生', '梦想', '快乐', '悲伤', '爱情']);
        const techTerm = this.randomSelect(['正在缓冲', '连接超时', '服务器宕机', '404未找到']);
        username = `${lifeConcept}${techTerm}`;
        elementsUsed = [lifeConcept, techTerm];
        break;
        
      case 'homophone_creative':
        const homophones = [
          { original: '知识就是力量', replacement: '芝士就是力量' },
          { original: '心想事成', replacement: '薪想事成' },
          { original: '马到成功', replacement: '码到成功' }
        ];
        const selected = this.randomSelect(homophones);
        username = selected.replacement;
        elementsUsed = [selected.original, '→', selected.replacement];
        break;
        
      default:
        username = `创意用户名${Math.floor(Math.random() * 1000)}`;
        elementsUsed = ['默认', '生成'];
    }
    
    return {
      username,
      pattern: pattern.name,
      elements_used: elementsUsed,
      creativity_assessment: {
        novelty: 0.8 + Math.random() * 0.2,
        relevance: 0.8 + Math.random() * 0.2,
        comprehensibility: 0.8 + Math.random() * 0.2,
        memorability: 0.8 + Math.random() * 0.2,
        overall_score: 0.8 + Math.random() * 0.2,
        explanation: `${pattern.name}策略生成`
      }
    };
  }
  
  selectOptimalPattern(style, themes, complexity) {
    const patternMap = {
      'modern': ['temporal_displacement', 'identity_elevation', 'tech_expression'],
      'cool': ['contradiction_unity', 'service_personification'],
      'playful': ['service_personification', 'homophone_creative'],
      'traditional': ['temporal_displacement', 'service_personification'],
      'elegant': ['contradiction_unity', 'identity_elevation']
    };
    
    const themeBonus = {
      'tech': ['tech_expression', 'temporal_displacement'],
      'workplace': ['identity_elevation', 'contradiction_unity'],
      'humor': ['homophone_creative'],
      'creative': ['service_personification'],
      'culture': ['temporal_displacement']
    };
    
    let candidatePatterns = patternMap[style] || patternMap['modern'];
    
    themes.forEach(theme => {
      if (themeBonus[theme]) {
        candidatePatterns = [...candidatePatterns, ...themeBonus[theme]];
      }
    });
    
    candidatePatterns = [...new Set(candidatePatterns)];
    
    if (complexity >= 4) {
      const complexPatterns = ['temporal_displacement', 'contradiction_unity'];
      candidatePatterns = candidatePatterns.filter(p => complexPatterns.includes(p));
    } else if (complexity <= 2) {
      const simplePatterns = ['homophone_creative', 'service_personification'];
      candidatePatterns = candidatePatterns.filter(p => simplePatterns.includes(p));
    }
    
    if (candidatePatterns.length > 0) {
      return candidatePatterns[Math.floor(Math.random() * candidatePatterns.length)];
    }
    
    return 'identity_elevation';
  }
}

// 测试所有风格组合
function testAllStyleCombinations() {
  console.log('\n🎨 测试所有风格组合');
  console.log('-'.repeat(60));
  
  const engine = new TestV5Engine();
  const styles = ['modern', 'cool', 'playful', 'traditional', 'elegant'];
  const themes = ['tech', 'workplace', 'humor', 'creative', 'culture'];
  const complexities = [1, 2, 3, 4, 5];
  
  let totalTests = 0;
  let successfulTests = 0;
  let failedTests = [];
  
  styles.forEach(style => {
    console.log(`\n📋 测试风格: ${style.toUpperCase()}`);
    
    // 测试单主题
    themes.forEach(theme => {
      complexities.forEach(complexity => {
        totalTests++;
        
        try {
          const selectedPattern = engine.selectOptimalPattern(style, [theme], complexity);
          const result = engine.generateByPattern(selectedPattern);
          
          if (result && result.username) {
            successfulTests++;
            console.log(`   ✅ ${style}+${theme}+${complexity}: ${result.username} (${selectedPattern})`);
          } else {
            failedTests.push({ style, themes: [theme], complexity, error: 'No result generated' });
            console.log(`   ❌ ${style}+${theme}+${complexity}: 生成失败`);
          }
        } catch (error) {
          failedTests.push({ style, themes: [theme], complexity, error: error.message });
          console.log(`   ❌ ${style}+${theme}+${complexity}: ${error.message}`);
        }
      });
    });
  });
  
  return { totalTests, successfulTests, failedTests };
}

// 测试多主题组合
function testMultiThemeCombinations() {
  console.log('\n🏷️ 测试多主题组合');
  console.log('-'.repeat(60));
  
  const engine = new TestV5Engine();
  const multiThemeCombinations = [
    ['tech', 'humor'],
    ['workplace', 'humor'],
    ['creative', 'culture'],
    ['tech', 'workplace'],
    ['humor', 'creative'],
    ['tech', 'humor', 'creative'],
    ['workplace', 'culture', 'humor']
  ];
  
  let totalTests = 0;
  let successfulTests = 0;
  let failedTests = [];
  
  multiThemeCombinations.forEach(themeCombo => {
    console.log(`\n📋 测试主题组合: [${themeCombo.join(', ')}]`);
    
    ['modern', 'cool', 'playful'].forEach(style => {
      [2, 3, 4].forEach(complexity => {
        totalTests++;
        
        try {
          const selectedPattern = engine.selectOptimalPattern(style, themeCombo, complexity);
          const result = engine.generateByPattern(selectedPattern);
          
          if (result && result.username) {
            successfulTests++;
            console.log(`   ✅ ${style}+[${themeCombo.join(',')}]+${complexity}: ${result.username}`);
          } else {
            failedTests.push({ style, themes: themeCombo, complexity, error: 'No result generated' });
            console.log(`   ❌ ${style}+[${themeCombo.join(',')}]+${complexity}: 生成失败`);
          }
        } catch (error) {
          failedTests.push({ style, themes: themeCombo, complexity, error: error.message });
          console.log(`   ❌ ${style}+[${themeCombo.join(',')}]+${complexity}: ${error.message}`);
        }
      });
    });
  });
  
  return { totalTests, successfulTests, failedTests };
}

// 测试指定模式生成
function testSpecificPatterns() {
  console.log('\n🎭 测试指定模式生成');
  console.log('-'.repeat(60));
  
  const engine = new TestV5Engine();
  const patterns = engine.generationPatterns;
  
  let totalTests = 0;
  let successfulTests = 0;
  let failedTests = [];
  
  patterns.forEach(pattern => {
    console.log(`\n📋 测试模式: ${pattern.name} (${pattern.id})`);
    
    for (let i = 0; i < 5; i++) {
      totalTests++;
      
      try {
        const result = engine.generateByPattern(pattern.id);
        
        if (result && result.username) {
          successfulTests++;
          console.log(`   ${i + 1}. ✅ ${result.username} (质量: ${(result.creativity_assessment.overall_score * 100).toFixed(1)}%)`);
        } else {
          failedTests.push({ pattern: pattern.id, attempt: i + 1, error: 'No result generated' });
          console.log(`   ${i + 1}. ❌ 生成失败`);
        }
      } catch (error) {
        failedTests.push({ pattern: pattern.id, attempt: i + 1, error: error.message });
        console.log(`   ${i + 1}. ❌ ${error.message}`);
      }
    }
  });
  
  return { totalTests, successfulTests, failedTests };
}

// 测试边界条件
function testEdgeCases() {
  console.log('\n⚠️ 测试边界条件');
  console.log('-'.repeat(60));
  
  const engine = new TestV5Engine();
  const edgeCases = [
    { name: '空主题数组', params: { style: 'modern', themes: [], complexity: 3 } },
    { name: '无效风格', params: { style: 'invalid', themes: ['tech'], complexity: 3 } },
    { name: '无效主题', params: { style: 'modern', themes: ['invalid'], complexity: 3 } },
    { name: '最低复杂度', params: { style: 'modern', themes: ['tech'], complexity: 1 } },
    { name: '最高复杂度', params: { style: 'modern', themes: ['tech'], complexity: 5 } },
    { name: '超出范围复杂度', params: { style: 'modern', themes: ['tech'], complexity: 10 } },
    { name: '大量主题', params: { style: 'modern', themes: ['tech', 'workplace', 'humor', 'creative', 'culture'], complexity: 3 } }
  ];
  
  let totalTests = 0;
  let successfulTests = 0;
  let failedTests = [];
  
  edgeCases.forEach(testCase => {
    totalTests++;
    console.log(`\n📋 测试: ${testCase.name}`);
    console.log(`   参数: ${JSON.stringify(testCase.params)}`);
    
    try {
      const selectedPattern = engine.selectOptimalPattern(
        testCase.params.style,
        testCase.params.themes,
        testCase.params.complexity
      );
      
      const result = engine.generateByPattern(selectedPattern);
      
      if (result && result.username) {
        successfulTests++;
        console.log(`   ✅ 成功: ${result.username} (模式: ${selectedPattern})`);
      } else {
        failedTests.push({ ...testCase, error: 'No result generated' });
        console.log(`   ❌ 生成失败`);
      }
    } catch (error) {
      failedTests.push({ ...testCase, error: error.message });
      console.log(`   ❌ 错误: ${error.message}`);
    }
  });
  
  return { totalTests, successfulTests, failedTests };
}

// 测试生成数量
function testGenerationCounts() {
  console.log('\n🔢 测试生成数量');
  console.log('-'.repeat(60));
  
  const engine = new TestV5Engine();
  const counts = [1, 3, 5, 10];
  
  let totalTests = 0;
  let successfulTests = 0;
  let failedTests = [];
  
  counts.forEach(count => {
    totalTests++;
    console.log(`\n📋 测试生成数量: ${count}`);
    
    try {
      const results = [];
      
      for (let i = 0; i < count; i++) {
        const selectedPattern = engine.selectOptimalPattern('modern', ['tech'], 3);
        const result = engine.generateByPattern(selectedPattern);
        if (result) {
          results.push(result);
        }
      }
      
      if (results.length === count) {
        successfulTests++;
        console.log(`   ✅ 成功生成${count}个用户名:`);
        results.forEach((result, index) => {
          console.log(`      ${index + 1}. ${result.username}`);
        });
      } else {
        failedTests.push({ count, generated: results.length, error: 'Incomplete generation' });
        console.log(`   ❌ 只生成了${results.length}个，期望${count}个`);
      }
    } catch (error) {
      failedTests.push({ count, error: error.message });
      console.log(`   ❌ 错误: ${error.message}`);
    }
  });
  
  return { totalTests, successfulTests, failedTests };
}

// 主测试函数
async function runAllUITests() {
  console.log('🚀 开始UI生成组合全面测试\n');
  
  const results = {
    styleTests: testAllStyleCombinations(),
    multiThemeTests: testMultiThemeCombinations(),
    patternTests: testSpecificPatterns(),
    edgeCaseTests: testEdgeCases(),
    countTests: testGenerationCounts()
  };
  
  // 生成测试报告
  console.log('\n📋 测试报告总结');
  console.log('='.repeat(80));
  
  let totalAllTests = 0;
  let totalSuccessful = 0;
  let allFailedTests = [];
  
  Object.entries(results).forEach(([testType, result]) => {
    totalAllTests += result.totalTests;
    totalSuccessful += result.successfulTests;
    allFailedTests = [...allFailedTests, ...result.failedTests];
    
    const successRate = (result.successfulTests / result.totalTests * 100).toFixed(1);
    console.log(`\n${testType}:`);
    console.log(`   总测试: ${result.totalTests}个`);
    console.log(`   成功: ${result.successfulTests}个`);
    console.log(`   失败: ${result.failedTests.length}个`);
    console.log(`   成功率: ${successRate}%`);
  });
  
  const overallSuccessRate = (totalSuccessful / totalAllTests * 100).toFixed(1);
  
  console.log('\n🎯 总体测试结果:');
  console.log(`   总测试数: ${totalAllTests}个`);
  console.log(`   成功测试: ${totalSuccessful}个`);
  console.log(`   失败测试: ${allFailedTests.length}个`);
  console.log(`   总成功率: ${overallSuccessRate}%`);
  
  if (allFailedTests.length > 0) {
    console.log('\n❌ 失败测试详情:');
    allFailedTests.forEach((test, index) => {
      console.log(`   ${index + 1}. ${JSON.stringify(test)}`);
    });
  }
  
  console.log('\n💡 问题分析和建议:');
  
  if (overallSuccessRate >= 95) {
    console.log('   🎉 测试结果优秀！UI生成组合工作正常');
    console.log('   ✅ 所有主要功能都能正常工作');
    console.log('   ✅ 边界条件处理良好');
  } else if (overallSuccessRate >= 85) {
    console.log('   🟡 测试结果良好，有少量问题需要关注');
    console.log('   🔧 建议检查失败的测试用例');
    console.log('   🔧 优化边界条件处理');
  } else {
    console.log('   🔴 测试结果需要改进');
    console.log('   🚨 存在较多问题，需要重点修复');
    console.log('   🔧 建议优先修复核心功能');
  }
  
  console.log('\n🔧 具体建议:');
  console.log('   1. 检查空主题数组的处理逻辑');
  console.log('   2. 验证无效参数的错误处理');
  console.log('   3. 确保所有模式都能正常生成');
  console.log('   4. 优化复杂度边界值处理');
  console.log('   5. 测试多主题组合的兼容性');
  
  return {
    totalTests: totalAllTests,
    successfulTests: totalSuccessful,
    failedTests: allFailedTests,
    successRate: overallSuccessRate,
    status: overallSuccessRate >= 95 ? 'excellent' : overallSuccessRate >= 85 ? 'good' : 'needs_improvement'
  };
}

// 运行测试
runAllUITests().then(result => {
  console.log('\n🏁 UI组合测试完成');
  console.log(`最终状态: ${result.status}`);
}).catch(error => {
  console.error('测试执行失败:', error);
});
