/**
 * 任务2: 重复性检查与去重分析
 * 检查生成的用户名与示例文件的重复情况
 */

const fs = require('fs');

console.log('📋 任务2: 重复性检查与去重分析');
console.log('='.repeat(80));

// 读取示例文件中的用户名
function extractUsernamesFromExample() {
  try {
    const content = fs.readFileSync('docs/name_example', 'utf8');
    const usernames = new Set();
    
    // 提取各种格式的用户名
    const lines = content.split('\n');
    
    lines.forEach(line => {
      line = line.trim();
      
      // 跳过空行、标题行、说明行
      if (!line || line.startsWith('#') || line.startsWith('*') || 
          line.startsWith('-') || line.startsWith('=') || 
          line.includes('有趣之处') || line.includes('适合') ||
          line.includes('类') || line.length < 2) {
        return;
      }
      
      // 提取带编号的用户名 (如: "1. 芝士就是力量")
      const numberedMatch = line.match(/^\d+\.\s*\*?\*?([^*()（）]+)\*?\*?/);
      if (numberedMatch) {
        const username = numberedMatch[1].trim();
        if (username && !username.includes('**') && !username.includes('有趣')) {
          usernames.add(username);
        }
        return;
      }
      
      // 提取带星号的用户名 (如: "**芝士就是力量**")
      const starMatch = line.match(/\*\*([^*]+)\*\*/);
      if (starMatch) {
        const username = starMatch[1].trim();
        if (username && !username.includes('有趣') && !username.includes('适合')) {
          usernames.add(username);
        }
        return;
      }
      
      // 提取简单的用户名行
      if (line.length >= 2 && line.length <= 20 && 
          !line.includes('：') && !line.includes(':') && 
          !line.includes('。') && !line.includes('，') &&
          !line.includes('(') && !line.includes('（') &&
          !line.includes('这类') && !line.includes('适合') &&
          !line.includes('形容') && !line.includes('描述')) {
        usernames.add(line);
      }
    });
    
    return Array.from(usernames);
  } catch (error) {
    console.error('读取示例文件失败:', error.message);
    return [];
  }
}

// 读取任务1的生成结果
function loadTask1Results() {
  try {
    const files = fs.readdirSync('.').filter(f => f.startsWith('task1-test-results-') && f.endsWith('.json'));
    if (files.length === 0) {
      console.log('⚠️ 未找到任务1的结果文件，将生成模拟数据');
      return generateMockTask1Results();
    }
    
    // 使用最新的结果文件
    const latestFile = files.sort().pop();
    console.log(`📁 读取任务1结果文件: ${latestFile}`);
    
    const content = fs.readFileSync(latestFile, 'utf8');
    const data = JSON.parse(content);
    
    return {
      allUsernames: data.allUsernames || [],
      testResults: data.testResults || [],
      summary: data.summary || {}
    };
  } catch (error) {
    console.error('读取任务1结果失败:', error.message);
    return generateMockTask1Results();
  }
}

// 生成模拟的任务1结果 (如果文件不存在)
function generateMockTask1Results() {
  console.log('🔄 生成模拟的任务1结果数据');
  
  const mockUsernames = [
    // 一些可能重复的用户名
    '芝士就是力量', '专业退堂鼓选手', '首席干饭官', '贫僧洗头用飘柔',
    '快乐申请出战', '间歇性想努力', '平平无奇小天才', '发财有限公司',
    '晚风有信', '贩卖人间黄昏', '月亮邮递员', '云朵收藏家',
    
    // 一些新生成的用户名
    '首席睡觉专家', '高级发呆顾问', '资深摸鱼代表', '认证躺平大使',
    '温柔却强硬', '理性但感性', '冷静然而冲动', '勤奋不过懒惰',
    '贫僧直播', '状元带货', '书生刷视频', '侠客购物',
    '快乐邮递员', '悲伤收集员', '愤怒配送员', '温柔制造商',
    '人生404未找到', '爱情正在缓冲', '梦想连接超时', '友情服务器宕机',
    '码到成功', '天码行空', '薪想事成', '年年有鱼'
  ];
  
  return {
    allUsernames: mockUsernames,
    testResults: [],
    summary: {
      totalUsernames: mockUsernames.length,
      totalTests: 10,
      successfulTests: 10
    }
  };
}

// 执行重复性分析
function performDuplicationAnalysis() {
  console.log('🔍 开始重复性检查与分析\n');
  
  // 1. 读取示例文件中的用户名
  console.log('📖 提取示例文件中的用户名...');
  const exampleUsernames = extractUsernamesFromExample();
  console.log(`   提取到 ${exampleUsernames.length} 个示例用户名`);
  
  // 显示部分示例用户名
  console.log('   示例用户名预览:');
  exampleUsernames.slice(0, 10).forEach((name, index) => {
    console.log(`     ${index + 1}. ${name}`);
  });
  if (exampleUsernames.length > 10) {
    console.log(`     ... 还有 ${exampleUsernames.length - 10} 个`);
  }
  
  // 2. 读取任务1的生成结果
  console.log('\n📊 读取任务1生成结果...');
  const task1Results = loadTask1Results();
  const generatedUsernames = task1Results.allUsernames;
  console.log(`   生成的用户名总数: ${generatedUsernames.length}`);
  
  // 3. 检查重复情况
  console.log('\n🔍 检查重复情况...');
  
  const exampleSet = new Set(exampleUsernames);
  const generatedSet = new Set(generatedUsernames);
  
  // 找出重复的用户名
  const duplicates = [];
  const duplicateDetails = [];
  
  generatedUsernames.forEach((username, index) => {
    if (exampleSet.has(username)) {
      duplicates.push(username);
      duplicateDetails.push({
        username,
        generatedIndex: index,
        source: 'example_file'
      });
    }
  });
  
  // 找出生成结果内部的重复
  const internalDuplicates = [];
  const usernameCount = {};
  
  generatedUsernames.forEach((username, index) => {
    if (usernameCount[username]) {
      usernameCount[username].count++;
      usernameCount[username].indices.push(index);
    } else {
      usernameCount[username] = { count: 1, indices: [index] };
    }
  });
  
  Object.entries(usernameCount).forEach(([username, data]) => {
    if (data.count > 1) {
      internalDuplicates.push({
        username,
        count: data.count,
        indices: data.indices
      });
    }
  });
  
  // 4. 计算重复率
  const externalDuplicateRate = (duplicates.length / generatedUsernames.length * 100).toFixed(2);
  const internalDuplicateCount = internalDuplicates.reduce((sum, item) => sum + item.count - 1, 0);
  const internalDuplicateRate = (internalDuplicateCount / generatedUsernames.length * 100).toFixed(2);
  const totalDuplicateRate = ((duplicates.length + internalDuplicateCount) / generatedUsernames.length * 100).toFixed(2);
  
  // 5. 分析重复模式
  console.log('\n📊 重复性分析结果');
  console.log('='.repeat(60));
  
  console.log('\n📈 重复率统计:');
  console.log(`   与示例文件重复: ${duplicates.length}个 (${externalDuplicateRate}%)`);
  console.log(`   内部重复: ${internalDuplicateCount}个 (${internalDuplicateRate}%)`);
  console.log(`   总重复率: ${totalDuplicateRate}%`);
  console.log(`   唯一用户名: ${generatedSet.size}个`);
  console.log(`   唯一率: ${(generatedSet.size / generatedUsernames.length * 100).toFixed(2)}%`);
  
  // 6. 显示具体重复用户名
  if (duplicates.length > 0) {
    console.log('\n❌ 与示例文件重复的用户名:');
    duplicates.forEach((username, index) => {
      console.log(`   ${index + 1}. ${username}`);
    });
  } else {
    console.log('\n✅ 未发现与示例文件重复的用户名');
  }
  
  if (internalDuplicates.length > 0) {
    console.log('\n🔄 内部重复的用户名:');
    internalDuplicates.slice(0, 10).forEach((item, index) => {
      console.log(`   ${index + 1}. "${item.username}" - 出现${item.count}次`);
    });
    if (internalDuplicates.length > 10) {
      console.log(`   ... 还有 ${internalDuplicates.length - 10} 个重复项`);
    }
  } else {
    console.log('\n✅ 未发现内部重复的用户名');
  }
  
  // 7. 分析重复模式
  console.log('\n🎭 重复模式分析:');
  
  // 按模式分析重复情况 (如果有测试结果数据)
  if (task1Results.testResults && task1Results.testResults.length > 0) {
    const patternDuplicates = {};
    const styleDuplicates = {};
    const themeDuplicates = {};
    
    // 这里需要更详细的数据来分析，暂时提供概要分析
    console.log('   📋 需要更详细的生成数据来进行模式分析');
    console.log('   💡 建议在生成时记录每个用户名的生成参数');
  }
  
  // 8. 质量评估
  console.log('\n🎯 质量评估:');
  if (parseFloat(totalDuplicateRate) <= 5) {
    console.log('   🟢 重复率优秀 (≤5%)');
  } else if (parseFloat(totalDuplicateRate) <= 15) {
    console.log('   🟡 重复率良好 (5-15%)');
  } else if (parseFloat(totalDuplicateRate) <= 30) {
    console.log('   🟠 重复率需要改进 (15-30%)');
  } else {
    console.log('   🔴 重复率过高 (>30%)，需要重点优化');
  }
  
  // 9. 去重建议
  console.log('\n💡 去重建议和词库扩展方案:');
  
  if (parseFloat(externalDuplicateRate) > 5) {
    console.log('\n🔧 示例文件重复问题:');
    console.log('   1. 检查生成逻辑是否直接使用了示例文件内容');
    console.log('   2. 确保生成算法的随机性和创新性');
    console.log('   3. 增加生成模式的多样性');
  }
  
  if (parseFloat(internalDuplicateRate) > 10) {
    console.log('\n🔧 内部重复问题:');
    console.log('   1. 扩展词库规模:');
    console.log('      • 权威级别词汇: 从12个扩展到30+个');
    console.log('      • 日常行为词汇: 从12个扩展到50+个');
    console.log('      • 职位后缀词汇: 从12个扩展到40+个');
    console.log('      • 古代人物词汇: 从12个扩展到30+个');
    console.log('      • 现代行为词汇: 从24个扩展到80+个');
    console.log('   2. 改进随机算法:');
    console.log('      • 实现加权随机选择');
    console.log('      • 避免短期内重复选择相同元素');
    console.log('      • 增加组合的复杂度和变化性');
    console.log('   3. 增加生成约束:');
    console.log('      • 检查最近生成的N个用户名，避免重复');
    console.log('      • 实现智能去重机制');
    console.log('      • 添加相似度检测');
  }
  
  console.log('\n📚 词库扩展具体建议:');
  console.log('   1. 权威修饰词扩展:');
  console.log('      现有: 首席、高级、资深、专业、认证、特级、顶级、超级、终极、至尊、王牌、精英');
  console.log('      建议增加: 御用、皇家、国际、全球、宇宙、银河、传奇、史诗、神级、仙级、圣级、帝级');
  
  console.log('\n   2. 行为动词扩展:');
  console.log('      现有: 吃、睡、玩、工作、学习、跑步、游泳、阅读、写作、思考、发呆、散步');
  console.log('      建议增加: 摸鱼、划水、躺平、内卷、刷剧、追星、吃瓜、种草、拔草、剁手、熬夜、早起');
  
  console.log('\n   3. 职位后缀扩展:');
  console.log('      现有: 官、师、专家、大使、代表、委员、顾问、总监、主任、经理、主管、负责人');
  console.log('      建议增加: 达人、高手、大神、大佬、选手、玩家、爱好者、收藏家、研究员、工程师');
  
  // 10. 保存分析结果
  const analysisResult = {
    timestamp: new Date().toISOString(),
    exampleUsernames: exampleUsernames.length,
    generatedUsernames: generatedUsernames.length,
    uniqueUsernames: generatedSet.size,
    externalDuplicates: duplicates.length,
    internalDuplicates: internalDuplicateCount,
    externalDuplicateRate: parseFloat(externalDuplicateRate),
    internalDuplicateRate: parseFloat(internalDuplicateRate),
    totalDuplicateRate: parseFloat(totalDuplicateRate),
    duplicateDetails: duplicateDetails,
    internalDuplicateDetails: internalDuplicates,
    qualityLevel: parseFloat(totalDuplicateRate) <= 5 ? 'excellent' : 
                  parseFloat(totalDuplicateRate) <= 15 ? 'good' : 
                  parseFloat(totalDuplicateRate) <= 30 ? 'needs_improvement' : 'poor'
  };
  
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const filename = `task2-duplication-analysis-${timestamp}.json`;
  
  fs.writeFileSync(filename, JSON.stringify(analysisResult, null, 2));
  console.log(`\n💾 分析结果已保存到: ${filename}`);
  
  return analysisResult;
}

// 运行重复性分析
performDuplicationAnalysis().then ? 
  performDuplicationAnalysis().then(result => {
    console.log('\n🏁 任务2: 重复性检查与去重分析完成');
    console.log(`最终评价: ${result.qualityLevel}`);
  }).catch(error => {
    console.error('任务2执行失败:', error);
  }) :
  (() => {
    try {
      const result = performDuplicationAnalysis();
      console.log('\n🏁 任务2: 重复性检查与去重分析完成');
      console.log(`最终评价: ${result.qualityLevel}`);
    } catch (error) {
      console.error('任务2执行失败:', error);
    }
  })();
