/**
 * 语素兼容性评估脚本
 * 评估新增1937个语素与现有语义维度的兼容性
 */

const fs = require('fs')
const path = require('path')

/**
 * 语素兼容性评估器
 */
class MorphemeCompatibilityAssessor {
  constructor() {
    this.configPath = path.join(__dirname, '..', 'config', 'element-library-config.ts')
    this.expandedConfigPath = path.join(__dirname, '..', 'config', 'expanded-element-library-config.ts')
    this.newMorphemes = []
    this.semanticDimensions = {}
    this.compatibilityResults = {}
  }

  /**
   * 执行兼容性评估
   */
  async executeCompatibilityAssessment() {
    console.log('🔬 开始语素兼容性评估...')
    console.log('=' .repeat(60))

    try {
      // 1. 识别新增语素
      this.identifyNewMorphemes()

      // 2. 加载语义维度配置
      this.loadSemanticDimensions()

      // 3. 分析新增语素的语义映射
      this.analyzeSemanticMapping()

      // 4. 识别覆盖盲区
      this.identifyCoverageBlindSpots()

      // 5. 检测语义冲突
      this.detectSemanticConflicts()

      // 6. 评估维度适配性
      this.evaluateDimensionAdaptability()

      // 7. 生成优化建议
      this.generateOptimizationSuggestions()

      // 8. 生成评估报告
      const report = this.generateAssessmentReport()

      console.log('\n✅ 语素兼容性评估完成！')
      console.log(`📊 新增语素总数: ${this.newMorphemes.length}个`)
      console.log(`🎯 兼容性评分: ${report.compatibilityScore.toFixed(2)}`)

      return report

    } catch (error) {
      console.error('❌ 语素兼容性评估失败:', error)
      throw error
    }
  }

  /**
   * 识别新增语素
   */
  identifyNewMorphemes() {
    console.log('🆕 识别新增语素...')
    
    const configContent = fs.readFileSync(this.configPath, 'utf8')
    const expandedContent = fs.readFileSync(this.expandedConfigPath, 'utf8')
    
    // 识别新增类别的语素
    const newCategories = [
      '职业扩展_传统', '职业扩展_现代', '职业扩展_创意', '职业扩展_新兴', '职业扩展_服务', '传统文化_人物',
      '情感扩展_基础', '情感扩展_积极', '情感扩展_深层', '情感扩展_文艺', '情感扩展_现代', '传统文化_概念', '传统文化_美德',
      '特征扩展_能力', '特征扩展_品质', '特征扩展_程度', '流行修饰_网络', '流行修饰_时尚'
    ]
    
    newCategories.forEach(category => {
      const morphemes = this.extractCategoryMorphemes(configContent, category)
      morphemes.forEach(morpheme => {
        this.newMorphemes.push({
          morpheme,
          category,
          type: this.categorizeNewMorpheme(category),
          source: 'vocabulary_expansion_engine'
        })
      })
    })
    
    // 从扩展配置中提取大规模语素
    const massiveCategories = ['MASSIVE_EMOTIONS', 'MASSIVE_PROFESSIONS', 'MASSIVE_CHARACTERISTICS']
    massiveCategories.forEach(category => {
      const morphemes = this.extractMassiveMorphemes(expandedContent, category)
      morphemes.forEach(morpheme => {
        this.newMorphemes.push({
          morpheme,
          category,
          type: 'massive_expansion',
          source: 'vocabulary_expansion_engine'
        })
      })
    })
    
    console.log(`   🆕 识别到 ${this.newMorphemes.length} 个新增语素`)
  }

  /**
   * 提取类别语素
   */
  extractCategoryMorphemes(content, category) {
    const regex = new RegExp(`${category}:\\s*\\[([^\\]]+)\\]`)
    const match = content.match(regex)
    
    if (!match) return []
    
    const morphemes = match[1].match(/'([^']+)'/g) || []
    return morphemes.map(m => m.replace(/'/g, ''))
  }

  /**
   * 提取大规模语素
   */
  extractMassiveMorphemes(content, category) {
    const regex = new RegExp(`${category}_CONFIG[\\s\\S]*?\\[([^\\]]+)\\]`)
    const match = content.match(regex)
    
    if (!match) return []
    
    const morphemes = match[1].match(/'([^']+)'/g) || []
    return morphemes.map(m => m.replace(/'/g, ''))
  }

  /**
   * 分类新增语素
   */
  categorizeNewMorpheme(category) {
    if (category.includes('职业')) return 'profession'
    if (category.includes('情感')) return 'emotion'
    if (category.includes('特征')) return 'characteristic'
    if (category.includes('传统')) return 'traditional'
    if (category.includes('流行')) return 'modern'
    return 'other'
  }

  /**
   * 加载语义维度配置
   */
  loadSemanticDimensions() {
    console.log('📚 加载语义维度配置...')
    
    this.semanticDimensions = {
      professionalism: {
        name: '专业性',
        keywords: ['专业', '精通', '资深', '专家', '高级', '顶级', '权威', '精湛', '熟练', '娴熟', '工程师', '分析师', '顾问'],
        weight: 0.25,
        targetTypes: ['profession', 'characteristic']
      },
      emotionality: {
        name: '情感性',
        keywords: ['温暖', '温柔', '深情', '真诚', '治愈', '佛系', '元气', '欢乐', '愉快', '深层', '情感', '心情'],
        weight: 0.20,
        targetTypes: ['emotion', 'characteristic']
      },
      innovation: {
        name: '创新性',
        keywords: ['创新', '创意', '原创', '独特', '新颖', '前卫', '先锋', '突破', '革新', '开创', '设计', '艺术'],
        weight: 0.20,
        targetTypes: ['profession', 'characteristic', 'modern']
      },
      traditionality: {
        name: '传统性',
        keywords: ['古', '典', '雅', '诗', '词', '文', '仁', '义', '智', '礼', '墨客', '书生', '传统', '文化'],
        weight: 0.15,
        targetTypes: ['traditional', 'profession']
      },
      modernity: {
        name: '现代性',
        keywords: ['AI', '数据', '智能', '网络', '数字', '云', '算法', '区块链', '科技', '技术', '现代', '时尚'],
        weight: 0.20,
        targetTypes: ['modern', 'profession', 'characteristic']
      }
    }
    
    console.log(`   📚 已加载 ${Object.keys(this.semanticDimensions).length} 个语义维度`)
  }

  /**
   * 分析语义映射
   */
  analyzeSemanticMapping() {
    console.log('🗺️ 分析新增语素的语义映射...')
    
    const mappingResults = {
      totalMapped: 0,
      unmapped: [],
      dimensionDistribution: {},
      typeDistribution: {}
    }
    
    // 初始化维度分布
    Object.keys(this.semanticDimensions).forEach(dim => {
      mappingResults.dimensionDistribution[dim] = 0
    })
    
    // 分析每个新增语素
    this.newMorphemes.forEach(item => {
      const mappedDimensions = this.mapMorphemeToSemanticDimensions(item)
      
      if (mappedDimensions.length > 0) {
        mappingResults.totalMapped++
        mappedDimensions.forEach(dim => {
          mappingResults.dimensionDistribution[dim]++
        })
      } else {
        mappingResults.unmapped.push(item)
      }
      
      // 统计类型分布
      mappingResults.typeDistribution[item.type] = (mappingResults.typeDistribution[item.type] || 0) + 1
    })
    
    const mappingRate = (mappingResults.totalMapped / this.newMorphemes.length * 100).toFixed(2)
    
    this.compatibilityResults.semanticMapping = mappingResults
    
    console.log(`   🗺️ 语义映射率: ${mappingRate}% (${mappingResults.totalMapped}/${this.newMorphemes.length})`)
    console.log(`   🗺️ 未映射语素: ${mappingResults.unmapped.length}个`)
  }

  /**
   * 映射语素到语义维度
   */
  mapMorphemeToSemanticDimensions(morphemeItem) {
    const mappedDimensions = []
    
    Object.entries(this.semanticDimensions).forEach(([dimKey, dimension]) => {
      // 检查类型匹配
      if (dimension.targetTypes.includes(morphemeItem.type)) {
        // 检查关键词匹配
        const hasKeywordMatch = dimension.keywords.some(keyword => 
          morphemeItem.morpheme.includes(keyword) || keyword.includes(morphemeItem.morpheme)
        )
        
        if (hasKeywordMatch) {
          mappedDimensions.push(dimKey)
        }
      }
    })
    
    return mappedDimensions
  }

  /**
   * 识别覆盖盲区
   */
  identifyCoverageBlindSpots() {
    console.log('🕳️ 识别覆盖盲区...')
    
    const blindSpots = {
      unmappedMorphemes: this.compatibilityResults.semanticMapping.unmapped,
      underrepresentedTypes: [],
      dimensionGaps: [],
      categoryGaps: []
    }
    
    // 分析类型代表性不足
    const typeDistribution = this.compatibilityResults.semanticMapping.typeDistribution
    const totalMorphemes = this.newMorphemes.length
    
    Object.entries(typeDistribution).forEach(([type, count]) => {
      const representation = count / totalMorphemes
      if (representation > 0.2) { // 如果某类型占比超过20%但映射率低
        const mappedCount = this.newMorphemes.filter(item => 
          item.type === type && this.mapMorphemeToSemanticDimensions(item).length > 0
        ).length
        const mappingRate = mappedCount / count
        
        if (mappingRate < 0.5) {
          blindSpots.underrepresentedTypes.push({
            type,
            count,
            mappedCount,
            mappingRate: (mappingRate * 100).toFixed(2)
          })
        }
      }
    })
    
    // 分析维度覆盖盲区
    const dimensionDistribution = this.compatibilityResults.semanticMapping.dimensionDistribution
    Object.entries(dimensionDistribution).forEach(([dimension, count]) => {
      const expectedCount = totalMorphemes * this.semanticDimensions[dimension].weight
      if (count < expectedCount * 0.5) {
        blindSpots.dimensionGaps.push({
          dimension: this.semanticDimensions[dimension].name,
          currentCount: count,
          expectedCount: Math.round(expectedCount),
          gap: Math.round(expectedCount - count)
        })
      }
    })
    
    this.compatibilityResults.coverageBlindSpots = blindSpots
    
    console.log(`   🕳️ 发现 ${blindSpots.underrepresentedTypes.length} 个代表性不足的类型`)
    console.log(`   🕳️ 发现 ${blindSpots.dimensionGaps.length} 个维度覆盖盲区`)
  }

  /**
   * 检测语义冲突
   */
  detectSemanticConflicts() {
    console.log('⚡ 检测语义冲突...')
    
    const conflicts = {
      traditionalModernConflicts: [],
      dimensionOverlaps: [],
      categoryMismatches: []
    }
    
    // 检测传统-现代冲突
    const traditionalMorphemes = this.newMorphemes.filter(item => item.type === 'traditional')
    const modernMorphemes = this.newMorphemes.filter(item => item.type === 'modern')
    
    traditionalMorphemes.forEach(trad => {
      modernMorphemes.forEach(mod => {
        if (this.hasSemanticConflict(trad.morpheme, mod.morpheme)) {
          conflicts.traditionalModernConflicts.push({
            traditional: trad.morpheme,
            modern: mod.morpheme,
            severity: this.assessConflictSeverity(trad.morpheme, mod.morpheme)
          })
        }
      })
    })
    
    // 检测维度重叠
    this.newMorphemes.forEach(item => {
      const mappedDimensions = this.mapMorphemeToSemanticDimensions(item)
      if (mappedDimensions.length > 2) {
        conflicts.dimensionOverlaps.push({
          morpheme: item.morpheme,
          dimensions: mappedDimensions,
          overlapCount: mappedDimensions.length
        })
      }
    })
    
    this.compatibilityResults.semanticConflicts = conflicts
    
    console.log(`   ⚡ 发现 ${conflicts.traditionalModernConflicts.length} 个传统-现代冲突`)
    console.log(`   ⚡ 发现 ${conflicts.dimensionOverlaps.length} 个维度重叠`)
  }

  /**
   * 判断语义冲突
   */
  hasSemanticConflict(word1, word2) {
    const conflictPairs = [
      ['古', 'AI'], ['典', '数字'], ['传统', '智能'], ['雅', '网络'],
      ['诗', '算法'], ['文', '科技'], ['书生', '程序员'], ['墨客', '工程师']
    ]
    
    return conflictPairs.some(([c1, c2]) => 
      (word1.includes(c1) && word2.includes(c2)) ||
      (word1.includes(c2) && word2.includes(c1))
    )
  }

  /**
   * 评估冲突严重性
   */
  assessConflictSeverity(word1, word2) {
    const highConflictPairs = [['古', 'AI'], ['典', '数字']]
    const mediumConflictPairs = [['传统', '智能'], ['雅', '网络']]
    
    if (highConflictPairs.some(([c1, c2]) => 
      (word1.includes(c1) && word2.includes(c2)) ||
      (word1.includes(c2) && word2.includes(c1))
    )) {
      return 'high'
    }
    
    if (mediumConflictPairs.some(([c1, c2]) => 
      (word1.includes(c1) && word2.includes(c2)) ||
      (word1.includes(c2) && word2.includes(c1))
    )) {
      return 'medium'
    }
    
    return 'low'
  }

  /**
   * 评估维度适配性
   */
  evaluateDimensionAdaptability() {
    console.log('🔄 评估维度适配性...')
    
    const adaptability = {
      dimensionFlexibility: {},
      expansionPotential: {},
      weightAdjustmentNeeds: {}
    }
    
    // 评估每个维度的灵活性
    Object.entries(this.semanticDimensions).forEach(([dimKey, dimension]) => {
      const mappedCount = this.compatibilityResults.semanticMapping.dimensionDistribution[dimKey] || 0
      const expectedCount = this.newMorphemes.length * dimension.weight
      
      adaptability.dimensionFlexibility[dimKey] = {
        name: dimension.name,
        currentMapped: mappedCount,
        expectedMapped: Math.round(expectedCount),
        adaptabilityScore: this.calculateAdaptabilityScore(mappedCount, expectedCount),
        needsExpansion: mappedCount < expectedCount * 0.7
      }
      
      // 评估扩展潜力
      const unmappedRelevant = this.compatibilityResults.semanticMapping.unmapped.filter(item =>
        dimension.targetTypes.includes(item.type)
      ).length
      
      adaptability.expansionPotential[dimKey] = {
        unmappedRelevant,
        expansionOpportunity: unmappedRelevant > 10 ? 'high' : unmappedRelevant > 5 ? 'medium' : 'low'
      }
      
      // 评估权重调整需求
      const actualRatio = mappedCount / this.newMorphemes.length
      const expectedRatio = dimension.weight
      const deviation = Math.abs(actualRatio - expectedRatio)
      
      if (deviation > 0.05) {
        adaptability.weightAdjustmentNeeds[dimKey] = {
          currentWeight: dimension.weight,
          suggestedWeight: Math.max(0.05, Math.min(0.35, actualRatio)),
          adjustment: actualRatio > expectedRatio ? 'increase' : 'decrease',
          deviation: (deviation * 100).toFixed(2)
        }
      }
    })
    
    this.compatibilityResults.dimensionAdaptability = adaptability
    
    const needsExpansion = Object.values(adaptability.dimensionFlexibility).filter(d => d.needsExpansion).length
    const needsWeightAdjustment = Object.keys(adaptability.weightAdjustmentNeeds).length
    
    console.log(`   🔄 ${needsExpansion} 个维度需要扩展`)
    console.log(`   🔄 ${needsWeightAdjustment} 个维度需要权重调整`)
  }

  /**
   * 计算适配性评分
   */
  calculateAdaptabilityScore(actual, expected) {
    if (expected === 0) return 100
    
    const ratio = actual / expected
    if (ratio >= 0.8 && ratio <= 1.2) return 100
    if (ratio >= 0.6 && ratio <= 1.4) return 80
    if (ratio >= 0.4 && ratio <= 1.6) return 60
    if (ratio >= 0.2 && ratio <= 1.8) return 40
    return 20
  }

  /**
   * 生成优化建议
   */
  generateOptimizationSuggestions() {
    console.log('💡 生成优化建议...')
    
    const suggestions = {
      dimensionEnhancements: [],
      newDimensionProposals: [],
      mappingImprovements: [],
      conflictResolutions: []
    }
    
    // 维度增强建议
    Object.entries(this.compatibilityResults.dimensionAdaptability.dimensionFlexibility).forEach(([dimKey, data]) => {
      if (data.needsExpansion) {
        suggestions.dimensionEnhancements.push({
          dimension: data.name,
          currentMapped: data.currentMapped,
          target: data.expectedMapped,
          action: '扩展关键词库，增加语素映射规则',
          priority: 'high'
        })
      }
    })
    
    // 新维度建议
    const unmappedTypes = this.compatibilityResults.coverageBlindSpots.underrepresentedTypes
    unmappedTypes.forEach(type => {
      if (type.mappingRate < 30) {
        suggestions.newDimensionProposals.push({
          basedOnType: type.type,
          proposedName: this.suggestDimensionName(type.type),
          justification: `${type.type}类型语素映射率仅${type.mappingRate}%，需要专门维度支持`,
          priority: 'medium'
        })
      }
    })
    
    // 映射改进建议
    const unmappedMorphemes = this.compatibilityResults.semanticMapping.unmapped
    if (unmappedMorphemes.length > 50) {
      suggestions.mappingImprovements.push({
        issue: '大量语素未映射到语义维度',
        count: unmappedMorphemes.length,
        action: '建立更灵活的语义映射规则，考虑语素的上下文和组合特征',
        priority: 'high'
      })
    }
    
    // 冲突解决建议
    const conflicts = this.compatibilityResults.semanticConflicts
    if (conflicts.traditionalModernConflicts.length > 0) {
      suggestions.conflictResolutions.push({
        conflictType: '传统-现代语义冲突',
        count: conflicts.traditionalModernConflicts.length,
        action: '建立文化融合策略，创建桥接语素，如"智慧科技"、"现代文人"',
        priority: 'medium'
      })
    }
    
    this.compatibilityResults.optimizationSuggestions = suggestions
    
    console.log(`   💡 生成 ${suggestions.dimensionEnhancements.length} 个维度增强建议`)
    console.log(`   💡 建议 ${suggestions.newDimensionProposals.length} 个新维度`)
  }

  /**
   * 建议维度名称
   */
  suggestDimensionName(type) {
    const nameMapping = {
      'profession': '职业专精性',
      'emotion': '情感丰富性',
      'characteristic': '特征独特性',
      'traditional': '文化传承性',
      'modern': '时代前沿性'
    }
    
    return nameMapping[type] || '未知维度'
  }

  /**
   * 生成评估报告
   */
  generateAssessmentReport() {
    const report = {
      timestamp: new Date().toISOString(),
      assessmentType: 'morpheme_compatibility_assessment',
      newMorphemesCount: this.newMorphemes.length,
      compatibilityScore: this.calculateCompatibilityScore(),
      semanticDimensions: this.semanticDimensions,
      compatibilityResults: this.compatibilityResults,
      summary: {
        mappingRate: (this.compatibilityResults.semanticMapping.totalMapped / this.newMorphemes.length * 100).toFixed(2),
        unmappedCount: this.compatibilityResults.semanticMapping.unmapped.length,
        conflictCount: this.compatibilityResults.semanticConflicts.traditionalModernConflicts.length,
        blindSpotCount: this.compatibilityResults.coverageBlindSpots.dimensionGaps.length
      },
      recommendations: this.generateFinalRecommendations()
    }

    // 保存报告
    const reportPath = path.join(__dirname, '..', 'morpheme-compatibility-assessment-report.json')
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2))

    return report
  }

  /**
   * 计算兼容性评分
   */
  calculateCompatibilityScore() {
    const mappingRate = this.compatibilityResults.semanticMapping.totalMapped / this.newMorphemes.length
    const conflictPenalty = this.compatibilityResults.semanticConflicts.traditionalModernConflicts.length / this.newMorphemes.length
    const coveragePenalty = this.compatibilityResults.coverageBlindSpots.dimensionGaps.length / Object.keys(this.semanticDimensions).length
    
    const score = (mappingRate * 0.6 - conflictPenalty * 0.2 - coveragePenalty * 0.2) * 100
    return Math.max(0, Math.min(100, score))
  }

  /**
   * 生成最终建议
   */
  generateFinalRecommendations() {
    const recommendations = []
    
    const suggestions = this.compatibilityResults.optimizationSuggestions
    
    // 高优先级建议
    if (suggestions.dimensionEnhancements.length > 0) {
      recommendations.push({
        type: 'dimension_enhancement',
        priority: 'high',
        title: '增强现有语义维度',
        description: `${suggestions.dimensionEnhancements.length}个维度需要扩展关键词库和映射规则`,
        actions: suggestions.dimensionEnhancements.map(e => e.action)
      })
    }
    
    if (suggestions.mappingImprovements.length > 0) {
      recommendations.push({
        type: 'mapping_improvement',
        priority: 'high',
        title: '改进语义映射机制',
        description: '建立更灵活的语义映射规则',
        actions: suggestions.mappingImprovements.map(m => m.action)
      })
    }
    
    // 中优先级建议
    if (suggestions.newDimensionProposals.length > 0) {
      recommendations.push({
        type: 'new_dimension',
        priority: 'medium',
        title: '增加新的语义维度',
        description: `建议增加${suggestions.newDimensionProposals.length}个新维度`,
        actions: suggestions.newDimensionProposals.map(p => `添加${p.proposedName}维度`)
      })
    }
    
    return recommendations
  }
}

/**
 * 主函数
 */
async function main() {
  try {
    const assessor = new MorphemeCompatibilityAssessor()
    const report = await assessor.executeCompatibilityAssessment()
    
    console.log('\n📄 语素兼容性评估报告已保存')
    console.log(`📊 新增语素: ${report.newMorphemesCount}个`)
    console.log(`🎯 兼容性评分: ${report.compatibilityScore.toFixed(2)}`)
    console.log(`🗺️ 映射率: ${report.summary.mappingRate}%`)
    console.log(`⚡ 冲突数量: ${report.summary.conflictCount}个`)
    console.log(`💡 优化建议: ${report.recommendations.length}条`)
    
  } catch (error) {
    console.error('❌ 语素兼容性评估失败:', error)
    process.exit(1)
  }
}

// 执行评估
if (require.main === module) {
  main()
}

module.exports = { MorphemeCompatibilityAssessor }
