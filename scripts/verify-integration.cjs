/**
 * 验证集成结果脚本
 * 验证词汇扩展引擎集成是否成功
 */

const fs = require('fs')
const path = require('path')

/**
 * 集成验证器
 */
class IntegrationVerifier {
  constructor() {
    this.configPath = path.join(__dirname, '..', 'config', 'element-library-config.ts')
    this.expandedConfigPath = path.join(__dirname, '..', 'config', 'expanded-element-library-config.ts')
  }

  /**
   * 执行集成验证
   */
  async executeVerification() {
    console.log('🔍 开始验证词汇扩展引擎集成结果...')
    console.log('=' .repeat(60))

    try {
      // 1. 验证配置文件存在
      this.verifyConfigFiles()

      // 2. 统计语素数量
      const stats = this.countMorphemes()

      // 3. 验证新增类别
      const newCategories = this.verifyNewCategories()

      // 4. 生成验证报告
      const report = this.generateVerificationReport(stats, newCategories)

      console.log('\n✅ 集成验证完成！')
      console.log(`📊 总语素数量: ${stats.totalMorphemes}个`)
      console.log(`🎯 目标完成度: ${stats.targetCompletion}`)

      return report

    } catch (error) {
      console.error('❌ 集成验证失败:', error)
      throw error
    }
  }

  /**
   * 验证配置文件存在
   */
  verifyConfigFiles() {
    console.log('📁 验证配置文件存在性...')
    
    if (!fs.existsSync(this.configPath)) {
      throw new Error('基础配置文件不存在')
    }
    
    if (!fs.existsSync(this.expandedConfigPath)) {
      throw new Error('扩展配置文件不存在')
    }
    
    console.log('   ✅ 配置文件存在性验证通过')
  }

  /**
   * 统计语素数量
   */
  countMorphemes() {
    console.log('📊 统计语素数量...')
    
    const configContent = fs.readFileSync(this.configPath, 'utf8')
    const expandedContent = fs.readFileSync(this.expandedConfigPath, 'utf8')
    
    // 统计基础配置中的语素
    const subjectsCount = this.countInConfig(configContent, 'SUBJECTS_CONFIG')
    const traitsCount = this.countInConfig(configContent, 'TRAITS_CONFIG')
    const modifiersCount = this.countInConfig(configContent, 'MODIFIERS_CONFIG')
    const actionsCount = this.countInConfig(configContent, 'ACTIONS_CONFIG')
    const connectorsCount = this.countInConfig(configContent, 'CONNECTORS_CONFIG')
    const suffixesCount = this.countInConfig(configContent, 'SUFFIXES_CONFIG')
    
    // 统计扩展配置中的语素
    const expandedCount = this.countInExpandedConfig(expandedContent)
    
    const totalMorphemes = subjectsCount + traitsCount + modifiersCount + 
                          actionsCount + connectorsCount + suffixesCount + expandedCount
    
    const stats = {
      subjects: subjectsCount,
      traits: traitsCount,
      modifiers: modifiersCount,
      actions: actionsCount,
      connectors: connectorsCount,
      suffixes: suffixesCount,
      expanded: expandedCount,
      totalMorphemes,
      targetCompletion: ((totalMorphemes / 3000) * 100).toFixed(1) + '%'
    }
    
    console.log(`   📊 主体词汇: ${subjectsCount}个`)
    console.log(`   📊 特质词汇: ${traitsCount}个`)
    console.log(`   📊 修饰词汇: ${modifiersCount}个`)
    console.log(`   📊 动作词汇: ${actionsCount}个`)
    console.log(`   📊 连接词汇: ${connectorsCount}个`)
    console.log(`   📊 后缀词汇: ${suffixesCount}个`)
    console.log(`   📊 扩展词汇: ${expandedCount}个`)
    console.log(`   📊 总计: ${totalMorphemes}个`)
    
    return stats
  }

  /**
   * 统计配置中的语素数量
   */
  countInConfig(content, configName) {
    const configMatch = content.match(new RegExp(`export const ${configName} = \\{([\\s\\S]*?)\\n\\} as const`))
    if (!configMatch) return 0
    
    const configContent = configMatch[1]
    const morphemes = configContent.match(/'[^']+'/g) || []
    return morphemes.length
  }

  /**
   * 统计扩展配置中的语素数量
   */
  countInExpandedConfig(content) {
    const morphemes = content.match(/'[^']+'/g) || []
    return morphemes.length
  }

  /**
   * 验证新增类别
   */
  verifyNewCategories() {
    console.log('🏷️ 验证新增类别...')
    
    const configContent = fs.readFileSync(this.configPath, 'utf8')
    const expandedContent = fs.readFileSync(this.expandedConfigPath, 'utf8')
    
    const newCategories = {
      subjects: [],
      traits: [],
      modifiers: [],
      expanded: []
    }
    
    // 检查新增的职业类别
    if (configContent.includes('职业扩展_传统')) {
      newCategories.subjects.push('职业扩展_传统')
    }
    if (configContent.includes('职业扩展_现代')) {
      newCategories.subjects.push('职业扩展_现代')
    }
    if (configContent.includes('职业扩展_创意')) {
      newCategories.subjects.push('职业扩展_创意')
    }
    if (configContent.includes('职业扩展_新兴')) {
      newCategories.subjects.push('职业扩展_新兴')
    }
    if (configContent.includes('职业扩展_服务')) {
      newCategories.subjects.push('职业扩展_服务')
    }
    if (configContent.includes('传统文化_人物')) {
      newCategories.subjects.push('传统文化_人物')
    }
    
    // 检查新增的特质类别
    if (configContent.includes('情感扩展_基础')) {
      newCategories.traits.push('情感扩展_基础')
    }
    if (configContent.includes('情感扩展_积极')) {
      newCategories.traits.push('情感扩展_积极')
    }
    if (configContent.includes('情感扩展_深层')) {
      newCategories.traits.push('情感扩展_深层')
    }
    if (configContent.includes('情感扩展_文艺')) {
      newCategories.traits.push('情感扩展_文艺')
    }
    if (configContent.includes('情感扩展_现代')) {
      newCategories.traits.push('情感扩展_现代')
    }
    if (configContent.includes('传统文化_概念')) {
      newCategories.traits.push('传统文化_概念')
    }
    if (configContent.includes('传统文化_美德')) {
      newCategories.traits.push('传统文化_美德')
    }
    
    // 检查新增的修饰类别
    if (configContent.includes('特征扩展_能力')) {
      newCategories.modifiers.push('特征扩展_能力')
    }
    if (configContent.includes('特征扩展_品质')) {
      newCategories.modifiers.push('特征扩展_品质')
    }
    if (configContent.includes('特征扩展_程度')) {
      newCategories.modifiers.push('特征扩展_程度')
    }
    if (configContent.includes('流行修饰_网络')) {
      newCategories.modifiers.push('流行修饰_网络')
    }
    if (configContent.includes('流行修饰_时尚')) {
      newCategories.modifiers.push('流行修饰_时尚')
    }
    
    // 检查扩展配置中的新类别
    if (expandedContent.includes('MASSIVE_EMOTIONS_CONFIG')) {
      newCategories.expanded.push('MASSIVE_EMOTIONS_CONFIG')
    }
    if (expandedContent.includes('MASSIVE_PROFESSIONS_CONFIG')) {
      newCategories.expanded.push('MASSIVE_PROFESSIONS_CONFIG')
    }
    if (expandedContent.includes('MASSIVE_CHARACTERISTICS_CONFIG')) {
      newCategories.expanded.push('MASSIVE_CHARACTERISTICS_CONFIG')
    }
    
    const totalNewCategories = newCategories.subjects.length + 
                              newCategories.traits.length + 
                              newCategories.modifiers.length + 
                              newCategories.expanded.length
    
    console.log(`   🏷️ 新增主体类别: ${newCategories.subjects.length}个`)
    console.log(`   🏷️ 新增特质类别: ${newCategories.traits.length}个`)
    console.log(`   🏷️ 新增修饰类别: ${newCategories.modifiers.length}个`)
    console.log(`   🏷️ 新增扩展类别: ${newCategories.expanded.length}个`)
    console.log(`   🏷️ 总计新增类别: ${totalNewCategories}个`)
    
    return newCategories
  }

  /**
   * 生成验证报告
   */
  generateVerificationReport(stats, newCategories) {
    const report = {
      timestamp: new Date().toISOString(),
      verification_type: 'integration_verification',
      status: 'completed',
      morpheme_statistics: stats,
      new_categories: newCategories,
      integration_summary: {
        original_morphemes: 636,
        estimated_integrated: stats.totalMorphemes - 636,
        current_total: stats.totalMorphemes,
        target_completion: stats.targetCompletion,
        integration_success: stats.totalMorphemes > 636
      },
      quality_assessment: {
        config_files_exist: true,
        new_categories_added: Object.values(newCategories).flat().length > 0,
        morpheme_count_increased: stats.totalMorphemes > 636,
        target_progress: parseFloat(stats.targetCompletion) > 21.2
      }
    }

    // 保存报告
    const reportPath = path.join(__dirname, '..', 'integration-verification-report.json')
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2))

    return report
  }

  /**
   * 测试V5引擎兼容性
   */
  async testV5EngineCompatibility() {
    console.log('🧪 测试V5引擎兼容性...')
    
    try {
      // 尝试读取配置文件
      const configContent = fs.readFileSync(this.configPath, 'utf8')
      
      // 检查基本语法
      if (!configContent.includes('export const SUBJECTS_CONFIG')) {
        throw new Error('SUBJECTS_CONFIG 配置缺失')
      }
      
      if (!configContent.includes('export const TRAITS_CONFIG')) {
        throw new Error('TRAITS_CONFIG 配置缺失')
      }
      
      if (!configContent.includes('export const MODIFIERS_CONFIG')) {
        throw new Error('MODIFIERS_CONFIG 配置缺失')
      }
      
      console.log('   ✅ V5引擎兼容性测试通过')
      return true
      
    } catch (error) {
      console.log(`   ❌ V5引擎兼容性测试失败: ${error.message}`)
      return false
    }
  }
}

/**
 * 主函数
 */
async function main() {
  try {
    const verifier = new IntegrationVerifier()
    const report = await verifier.executeVerification()
    
    // 测试V5引擎兼容性
    const compatibility = await verifier.testV5EngineCompatibility()
    
    console.log('\n📄 集成验证报告已保存')
    console.log(`📊 语素总数: ${report.morpheme_statistics.totalMorphemes}个`)
    console.log(`📈 集成增长: ${report.integration_summary.estimated_integrated}个`)
    console.log(`🎯 目标完成度: ${report.integration_summary.target_completion}`)
    console.log(`🔧 V5引擎兼容性: ${compatibility ? '✅ 通过' : '❌ 失败'}`)
    
    if (report.integration_summary.integration_success) {
      console.log('\n🎉 词汇扩展引擎集成成功！')
    } else {
      console.log('\n⚠️ 集成可能未完全成功，请检查配置文件')
    }
    
  } catch (error) {
    console.error('❌ 集成验证失败:', error)
    process.exit(1)
  }
}

// 执行验证
if (require.main === module) {
  main()
}

module.exports = { IntegrationVerifier }
