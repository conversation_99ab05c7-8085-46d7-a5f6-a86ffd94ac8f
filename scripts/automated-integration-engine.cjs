/**
 * 自动化集成引擎
 * 将去重后的高质量语素自动集成到V5引擎配置文件中
 */

const fs = require('fs')
const path = require('path')

/**
 * 配置文件集成管理器
 */
class ConfigurationIntegrationManager {
  constructor() {
    this.configPaths = {
      elementLibrary: path.join(__dirname, '..', 'config', 'element-library-config.ts'),
      expandedLibrary: path.join(__dirname, '..', 'config', 'expanded-element-library-config.ts')
    }
    this.backupPaths = {
      elementLibrary: this.configPaths.elementLibrary + '.backup',
      expandedLibrary: this.configPaths.expandedLibrary + '.backup'
    }
  }

  /**
   * 创建配置文件备份
   */
  createBackups() {
    console.log('💾 创建配置文件备份...')
    
    Object.keys(this.configPaths).forEach(key => {
      const originalPath = this.configPaths[key]
      const backupPath = this.backupPaths[key]
      
      if (fs.existsSync(originalPath)) {
        fs.copyFileSync(originalPath, backupPath)
        console.log(`   ✅ 已备份: ${path.basename(originalPath)}`)
      }
    })
  }

  /**
   * 恢复配置文件备份
   */
  restoreBackups() {
    console.log('🔄 恢复配置文件备份...')
    
    Object.keys(this.backupPaths).forEach(key => {
      const backupPath = this.backupPaths[key]
      const originalPath = this.configPaths[key]
      
      if (fs.existsSync(backupPath)) {
        fs.copyFileSync(backupPath, originalPath)
        console.log(`   ✅ 已恢复: ${path.basename(originalPath)}`)
      }
    })
  }

  /**
   * 集成语素到基础配置文件
   */
  integrateToElementLibrary(integratedMorphemes) {
    console.log('🔧 集成语素到基础配置文件...')
    
    const configPath = this.configPaths.elementLibrary
    let configContent = fs.readFileSync(configPath, 'utf8')
    
    // 扩展subjects配置
    if (integratedMorphemes.subjects_extensions) {
      configContent = this.addToSubjectsConfig(configContent, integratedMorphemes.subjects_extensions)
    }
    
    // 扩展traits配置
    if (integratedMorphemes.traits_extensions) {
      configContent = this.addToTraitsConfig(configContent, integratedMorphemes.traits_extensions)
    }
    
    // 扩展modifiers配置
    if (integratedMorphemes.modifiers_extensions) {
      configContent = this.addToModifiersConfig(configContent, integratedMorphemes.modifiers_extensions)
    }
    
    // 写入更新后的配置
    fs.writeFileSync(configPath, configContent)
    console.log(`   ✅ 基础配置文件更新完成`)
  }

  /**
   * 集成语素到扩展配置文件
   */
  integrateToExpandedLibrary(integratedMorphemes) {
    console.log('🔧 集成语素到扩展配置文件...')
    
    const configPath = this.configPaths.expandedLibrary
    let configContent = fs.readFileSync(configPath, 'utf8')
    
    // 添加新的扩展类别
    if (integratedMorphemes.new_categories) {
      configContent = this.addNewCategories(configContent, integratedMorphemes.new_categories)
    }
    
    // 扩展现有类别
    if (integratedMorphemes.expanded_extensions) {
      configContent = this.expandExistingCategories(configContent, integratedMorphemes.expanded_extensions)
    }
    
    // 写入更新后的配置
    fs.writeFileSync(configPath, configContent)
    console.log(`   ✅ 扩展配置文件更新完成`)
  }

  /**
   * 添加到subjects配置
   */
  addToSubjectsConfig(content, subjectsExtensions) {
    // 查找subjects配置的结束位置
    const subjectsMatch = content.match(/(export const SUBJECTS_CONFIG = \{[\s\S]*?\n\})/m)
    if (!subjectsMatch) {
      console.warn('⚠️ 未找到SUBJECTS_CONFIG，跳过subjects扩展')
      return content
    }

    const originalSubjects = subjectsMatch[1]
    let newSubjects = originalSubjects

    // 在最后一个类别后添加新类别
    Object.keys(subjectsExtensions).forEach(categoryName => {
      const morphemes = subjectsExtensions[categoryName]
      if (morphemes.length > 0) {
        const categoryConfig = `  ${categoryName}: [${morphemes.map(m => `'${m}'`).join(', ')}],\n`
        
        // 在最后的}前插入
        newSubjects = newSubjects.replace(/(\n\})$/, `,\n${categoryConfig}$1`)
      }
    })

    return content.replace(originalSubjects, newSubjects)
  }

  /**
   * 添加到traits配置
   */
  addToTraitsConfig(content, traitsExtensions) {
    const traitsMatch = content.match(/(export const TRAITS_CONFIG = \{[\s\S]*?\n\})/m)
    if (!traitsMatch) {
      console.warn('⚠️ 未找到TRAITS_CONFIG，跳过traits扩展')
      return content
    }

    const originalTraits = traitsMatch[1]
    let newTraits = originalTraits

    Object.keys(traitsExtensions).forEach(categoryName => {
      const morphemes = traitsExtensions[categoryName]
      if (morphemes.length > 0) {
        const categoryConfig = `  ${categoryName}: [${morphemes.map(m => `'${m}'`).join(', ')}],\n`
        newTraits = newTraits.replace(/(\n\})$/, `,\n${categoryConfig}$1`)
      }
    })

    return content.replace(originalTraits, newTraits)
  }

  /**
   * 添加到modifiers配置
   */
  addToModifiersConfig(content, modifiersExtensions) {
    const modifiersMatch = content.match(/(export const MODIFIERS_CONFIG = \{[\s\S]*?\n\})/m)
    if (!modifiersMatch) {
      console.warn('⚠️ 未找到MODIFIERS_CONFIG，跳过modifiers扩展')
      return content
    }

    const originalModifiers = modifiersMatch[1]
    let newModifiers = originalModifiers

    Object.keys(modifiersExtensions).forEach(categoryName => {
      const morphemes = modifiersExtensions[categoryName]
      if (morphemes.length > 0) {
        const categoryConfig = `  ${categoryName}: [${morphemes.map(m => `'${m}'`).join(', ')}],\n`
        newModifiers = newModifiers.replace(/(\n\})$/, `,\n${categoryConfig}$1`)
      }
    })

    return content.replace(originalModifiers, newModifiers)
  }

  /**
   * 添加新类别到扩展配置
   */
  addNewCategories(content, newCategories) {
    // 在文件末尾添加新的配置导出
    let additions = '\n// 新增语素类别 (词汇扩展引擎集成)\n'
    
    Object.keys(newCategories).forEach(categoryName => {
      const morphemes = newCategories[categoryName]
      if (morphemes.length > 0) {
        additions += `export const ${categoryName.toUpperCase()}_CONFIG = {\n`
        additions += `  ${categoryName}: [${morphemes.map(m => `'${m}'`).join(', ')}]\n`
        additions += `}\n\n`
      }
    })

    return content + additions
  }

  /**
   * 扩展现有类别
   */
  expandExistingCategories(content, expandedExtensions) {
    // 查找并扩展现有的扩展配置
    Object.keys(expandedExtensions).forEach(categoryName => {
      const morphemes = expandedExtensions[categoryName]
      if (morphemes.length > 0) {
        // 查找对应的配置并扩展
        const configPattern = new RegExp(`(${categoryName}:\\s*\\[[^\\]]*\\])`, 'g')
        content = content.replace(configPattern, (match) => {
          // 在数组末尾添加新元素
          return match.replace(/\]$/, `, ${morphemes.map(m => `'${m}'`).join(', ')}]`)
        })
      }
    })

    return content
  }
}

/**
 * 语素分类器
 */
class MorphemeClassifier {
  constructor() {
    this.classificationRules = {
      subjects: {
        patterns: [/师$/, /家$/, /者$/, /员$/, /人$/, /客$/, /手$/, /生$/],
        keywords: ['职业', '身份', '角色', '人物']
      },
      traits: {
        patterns: [/性$/, /感$/, /情$/, /心$/, /意$/],
        keywords: ['情感', '特质', '性格', '品质', '状态']
      },
      modifiers: {
        patterns: [/级$/, /度$/, /超/, /极/, /特/, /高/, /顶/],
        keywords: ['程度', '级别', '范围', '强度']
      }
    }
  }

  /**
   * 分类去重后的语素
   */
  classifyMorphemes(deduplicationResults) {
    console.log('🏷️ 开始语素智能分类...')
    
    const classified = {
      subjects_extensions: {},
      traits_extensions: {},
      modifiers_extensions: {},
      new_categories: {},
      expanded_extensions: {}
    }

    // 处理每个类别的去重结果
    Object.keys(deduplicationResults).forEach(category => {
      const result = deduplicationResults[category]
      const acceptedMorphemes = result.acceptedMorphemes.map(m => m.word)
      
      switch (category) {
        case 'professions':
          classified.subjects_extensions = this.classifyProfessions(acceptedMorphemes)
          break
        case 'emotions':
          classified.traits_extensions = {
            ...classified.traits_extensions,
            ...this.classifyEmotions(acceptedMorphemes)
          }
          break
        case 'characteristics':
          const charClassification = this.classifyCharacteristics(acceptedMorphemes)
          classified.traits_extensions = { ...classified.traits_extensions, ...charClassification.traits }
          classified.modifiers_extensions = { ...classified.modifiers_extensions, ...charClassification.modifiers }
          break
        case 'traditional':
          const tradClassification = this.classifyTraditional(acceptedMorphemes)
          classified.subjects_extensions = { ...classified.subjects_extensions, ...tradClassification.subjects }
          classified.traits_extensions = { ...classified.traits_extensions, ...tradClassification.traits }
          break
        case 'popular':
          classified.modifiers_extensions = {
            ...classified.modifiers_extensions,
            ...this.classifyPopular(acceptedMorphemes)
          }
          break
        default:
          // 大规模扩展词汇放入新类别
          if (acceptedMorphemes.length > 0) {
            classified.new_categories[category] = acceptedMorphemes
          }
      }
    })

    console.log('   ✅ 语素分类完成')
    this.printClassificationSummary(classified)
    
    return classified
  }

  /**
   * 分类职业词汇
   */
  classifyProfessions(morphemes) {
    return {
      现代职业扩展: morphemes.filter(m => /师$|员$|家$/.test(m) && !/传统|古/.test(m)).slice(0, 25),
      创意职业: morphemes.filter(m => /设计|创意|艺术|文案/.test(m)).slice(0, 20),
      新兴职业: morphemes.filter(m => /数字|网络|AI|区块链|云/.test(m)).slice(0, 15),
      服务职业: morphemes.filter(m => /服务|咨询|顾问/.test(m)).slice(0, 15)
    }
  }

  /**
   * 分类情感词汇
   */
  classifyEmotions(morphemes) {
    return {
      深层情感: morphemes.filter(m => /深|真|纯|专/.test(m)).slice(0, 20),
      文艺情感: morphemes.filter(m => /诗|雅|优|清/.test(m)).slice(0, 15),
      现代情感: morphemes.filter(m => /治愈|佛系|元气|活力/.test(m)).slice(0, 15)
    }
  }

  /**
   * 分类特征词汇
   */
  classifyCharacteristics(morphemes) {
    return {
      traits: {
        性格特征: morphemes.filter(m => /开朗|稳重|幽默|机智/.test(m)).slice(0, 20),
        品质特征: morphemes.filter(m => /诚信|可靠|积极|负责/.test(m)).slice(0, 20)
      },
      modifiers: {
        能力特征: morphemes.filter(m => /专业|精通|创新|高效/.test(m)).slice(0, 15),
        程度描述: morphemes.filter(m => /超|极|特|高|顶/.test(m)).slice(0, 10)
      }
    }
  }

  /**
   * 分类传统文化词汇
   */
  classifyTraditional(morphemes) {
    return {
      subjects: {
        文人雅士: morphemes.filter(m => /诗|词|文|雅|士|生/.test(m)).slice(0, 30),
        古典意象: morphemes.filter(m => /花|月|风|雨|雪|梅|兰|竹|菊/.test(m)).slice(0, 25)
      },
      traits: {
        传统美德: morphemes.filter(m => /仁|义|礼|智|信|孝|忠/.test(m)).slice(0, 25),
        文化概念: morphemes.filter(m => /书香|墨|茶|琴|棋|画/.test(m)).slice(0, 20)
      }
    }
  }

  /**
   * 分类流行词汇
   */
  classifyPopular(morphemes) {
    return {
      现代修饰: morphemes.filter(m => /时尚|潮流|酷|萌|可爱/.test(m)).slice(0, 20),
      网络流行: morphemes.filter(m => /给力|厉害|靠谱|治愈/.test(m)).slice(0, 15)
    }
  }

  /**
   * 打印分类摘要
   */
  printClassificationSummary(classified) {
    console.log('\n📊 语素分类摘要:')
    
    let totalClassified = 0
    
    Object.keys(classified).forEach(mainCategory => {
      const categories = classified[mainCategory]
      let categoryTotal = 0
      
      Object.keys(categories).forEach(subCategory => {
        const count = categories[subCategory].length
        categoryTotal += count
        totalClassified += count
      })
      
      if (categoryTotal > 0) {
        console.log(`   ${mainCategory}: ${categoryTotal}个`)
        Object.keys(categories).forEach(subCategory => {
          const count = categories[subCategory].length
          if (count > 0) {
            console.log(`     - ${subCategory}: ${count}个`)
          }
        })
      }
    })
    
    console.log(`   总计: ${totalClassified}个语素已分类`)
  }
}

/**
 * 主函数
 */
function main() {
  try {
    console.log('🚀 启动自动化集成引擎...')
    
    // 加载去重结果
    const deduplicationReportPath = path.join(__dirname, '..', 'intelligent-deduplication-report.json')
    if (!fs.existsSync(deduplicationReportPath)) {
      console.error('❌ 请先运行智能去重脚本')
      return
    }
    
    const deduplicationReport = JSON.parse(fs.readFileSync(deduplicationReportPath, 'utf8'))
    
    // 初始化管理器
    const integrationManager = new ConfigurationIntegrationManager()
    const classifier = new MorphemeClassifier()
    
    // 创建备份
    integrationManager.createBackups()
    
    try {
      // 分类语素
      const classifiedMorphemes = classifier.classifyMorphemes(deduplicationReport.deduplication_results)
      
      // 执行集成
      integrationManager.integrateToElementLibrary(classifiedMorphemes)
      integrationManager.integrateToExpandedLibrary(classifiedMorphemes)
      
      // 生成集成报告
      const integrationReport = {
        timestamp: new Date().toISOString(),
        integration_summary: {
          total_integrated: Object.values(classifiedMorphemes).reduce((total, category) => {
            return total + Object.values(category).reduce((catTotal, morphemes) => {
              return catTotal + (Array.isArray(morphemes) ? morphemes.length : 0)
            }, 0)
          }, 0),
          categories_updated: Object.keys(classifiedMorphemes).filter(key => 
            Object.keys(classifiedMorphemes[key]).length > 0
          ).length
        },
        classified_morphemes: classifiedMorphemes,
        backup_files: integrationManager.backupPaths
      }
      
      // 保存集成报告
      const integrationReportPath = path.join(__dirname, '..', 'automated-integration-report.json')
      fs.writeFileSync(integrationReportPath, JSON.stringify(integrationReport, null, 2))
      
      console.log(`\n✅ 自动化集成完成！`)
      console.log(`📄 集成报告已保存到: ${integrationReportPath}`)
      console.log(`📊 集成语素总数: ${integrationReport.integration_summary.total_integrated}个`)
      console.log(`🏷️ 更新类别数: ${integrationReport.integration_summary.categories_updated}个`)
      console.log(`💾 备份文件已创建，如需回滚请运行恢复命令`)
      
      return integrationReport
      
    } catch (integrationError) {
      console.error('❌ 集成过程中发生错误，正在恢复备份...', integrationError)
      integrationManager.restoreBackups()
      throw integrationError
    }
    
  } catch (error) {
    console.error('❌ 自动化集成失败:', error)
    process.exit(1)
  }
}

// 执行集成
if (require.main === module) {
  main()
}

module.exports = { 
  ConfigurationIntegrationManager, 
  MorphemeClassifier
}
