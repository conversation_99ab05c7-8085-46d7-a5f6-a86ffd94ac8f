/**
 * 词汇扩展引擎分析脚本
 * 分析词汇扩展引擎中的词汇，生成详细报告
 */

const fs = require('fs')
const path = require('path')

// 模拟词汇扩展引擎的词汇数据 (从实际文件中提取)
const vocabularyData = {
  // 情感词汇 (约250个)
  emotions: {
    basic_emotions: [
      '温暖', '温柔', '温馨', '温和', '温情',
      '热情', '热忱', '热心', '热烈', '热诚',
      '冷静', '冷淡', '冷酷', '冷漠', '冷峻',
      '平静', '平和', '平淡', '平稳', '平缓'
    ],
    positive_emotions: [
      '欢乐', '欢喜', '欢快', '欢悦', '欢腾',
      '愉快', '愉悦', '愉心', '愉情', '愉神',
      '快乐', '快意', '快慰', '快活', '快感',
      '喜悦', '喜乐', '喜庆', '喜气', '喜盈',
      '兴奋', '兴致', '兴趣', '兴高', '兴旺'
    ],
    deep_emotions: [
      '深情', '深爱', '深切', '深沉', '深邃',
      '真诚', '真挚', '真心', '真情', '真意',
      '纯真', '纯洁', '纯净', '纯朴', '纯粹',
      '专注', '专一', '专心', '专情', '专诚'
    ],
    artistic_emotions: [
      '诗意', '诗情', '诗韵', '诗心', '诗魂',
      '雅致', '雅韵', '雅趣', '雅兴', '雅意',
      '优雅', '优美', '优秀', '优质', '优良',
      '清雅', '清新', '清纯', '清澈', '清香'
    ],
    modern_emotions: [
      '治愈', '暖心', '贴心', '用心', '走心',
      '佛系', '淡然', '随性', '自在', '洒脱',
      '元气', '活力', '朝气', '生机', '青春',
      '文艺', '小清新', '慢生活', '正能量', '满满'
    ]
  },

  // 职业词汇 (约400个)
  professions: {
    traditional_professions: [
      '医师', '教师', '工程师', '律师', '会计师',
      '建筑师', '设计师', '艺术家', '音乐家', '作家',
      '记者', '编辑', '翻译', '导游', '厨师',
      '司机', '警察', '消防员', '护士', '药师'
    ],
    modern_professions: [
      '产品经理', '项目经理', '运营经理', '市场经理', '品牌经理',
      '数据分析师', '用户体验师', '界面设计师', '前端工程师', '后端工程师',
      '算法工程师', '测试工程师', '运维工程师', '安全工程师', '架构师',
      '咨询顾问', '投资顾问', '理财顾问', '心理咨询师', '营养师'
    ],
    creative_professions: [
      '插画师', '动画师', '游戏设计师', '影视制作人', '摄影师',
      '文案策划', '创意总监', '美术指导', '音效师', '剪辑师',
      '主播', '博主', '网红', 'UP主', '内容创作者',
      '独立开发者', '自由职业者', '斜杠青年', '创业者', '投资人'
    ],
    emerging_professions: [
      'AI训练师', '数据科学家', '区块链工程师', '云计算专家', '网络安全专家',
      '用户增长专家', '社群运营', '直播运营', '电商运营', '新媒体运营',
      '元宇宙设计师', '虚拟偶像制作人', '数字艺术家', 'NFT创作者', '加密货币分析师',
      '碳中和顾问', '可持续发展专家', '环保工程师', '新能源专家', '智能制造工程师'
    ]
  },

  // 特征词汇 (约300个)
  characteristics: {
    personality_traits: [
      '开朗', '活泼', '外向', '内向', '稳重',
      '成熟', '幽默', '风趣', '机智', '聪明',
      '智慧', '睿智', '博学', '渊博', '深刻',
      '细心', '耐心', '专心', '用心', '贴心',
      '善良', '友善', '和善', '慈善', '仁善'
    ],
    ability_traits: [
      '专业', '精通', '熟练', '娴熟', '精湛',
      '创新', '创意', '创造', '独创', '原创',
      '高效', '迅速', '敏捷', '灵活', '机敏',
      '严谨', '精确', '准确', '精准', '细致',
      '全面', '综合', '多元', '多样', '丰富'
    ],
    quality_traits: [
      '诚信', '诚实', '真诚', '坦诚', '忠诚',
      '可靠', '稳定', '踏实', '务实', '实在',
      '负责', '认真', '严肃', '正经', '端正',
      '积极', '主动', '进取', '上进', '奋进',
      '坚持', '坚定', '坚强', '坚韧', '顽强'
    ]
  },

  // 传统文化词汇 (约450个)
  traditional: {
    classical_poetry: [
      '诗仙', '词圣', '诗圣', '诗佛', '诗鬼', '诗豪', '诗杰', '诗魔',
      '墨客', '骚人', '词人', '诗人', '文人', '雅士', '才子', '佳人',
      '春花', '秋月', '夏雨', '冬雪', '晨露', '夕阳', '明月', '清风',
      '梅兰', '竹菊', '松柏', '荷莲', '桃李', '杨柳', '芙蓉', '牡丹'
    ],
    traditional_concepts: [
      '书香', '墨香', '茶香', '花香', '檀香', '兰香', '桂香', '梅香',
      '文房', '四宝', '笔墨', '纸砚', '丹青', '翰墨', '书画', '金石',
      '琴棋', '书画', '诗酒', '花茶', '香道', '茶道', '花道', '剑道'
    ],
    traditional_virtues: [
      '仁爱', '仁慈', '仁义', '仁德', '仁心', '仁者', '仁厚', '仁和',
      '义气', '义理', '义士', '义人', '义举', '义行', '正义', '大义',
      '礼貌', '礼仪', '礼节', '礼数', '礼让', '礼敬', '有礼', '知礼'
    ]
  },

  // 流行词汇 (约600个)
  popular: {
    daily_life: [
      '温馨', '舒适', '惬意', '悠闲', '轻松', '自在', '随意', '随性',
      '简单', '简约', '简洁', '简朴', '简易', '简明', '干净', '整洁',
      '自然', '天然', '纯然', '怡然', '悠然', '泰然', '坦然', '淡然'
    ],
    internet_popular: [
      '给力', '靠谱', '厉害', '牛逼', '强悍', '霸气', '威武', '彪悍',
      '萌萌', '可爱', '呆萌', '软萌', '奶萌', '甜萌', '酷萌', '帅萌',
      '甜美', '甜心', '甜蜜', '甜腻', '香甜', '清甜', '蜜糖', '糖果'
    ]
  },

  // 时代潮流词汇 (约800个)
  trend: {
    anime_culture: [
      '二次元', '三次元', '元宇宙', '虚拟', '数字', '赛博', '朋克', '蒸汽',
      '萌系', '萌娘', '萌妹', '萌汉', '萌物', '萌化', '萌属性', '萌点',
      '宅男', '宅女', '宅文化', '死宅', '肥宅', '技术宅', '游戏宅', '动漫宅'
    ],
    internet_subculture: [
      '破圈', '出圈', '圈层', '小圈子', '大圈子', '圈内', '圈外', '混圈',
      '内卷', '躺平', '摆烂', '佛系', '咸鱼', '划水', '摸鱼', '打工人',
      '社畜', '996', '007', '福报', '加班', '熬夜', '通宵', '肝'
    ],
    gen_z_culture: [
      'emo', 'emo了', 'emo时刻', 'emo文学', 'emo风', 'emo系',
      '精神内耗', '内耗怪', '内耗王', '反内耗', '躺平', '摆烂', '佛系', '咸鱼',
      '社恐', '社牛', '社死', '社交恐惧', '社交牛逼', '社交死亡', '社交废物'
    ]
  }
}

/**
 * 分析词汇数据
 */
function analyzeVocabularyData() {
  console.log('📊 开始分析词汇扩展引擎中的词汇数据...\n')

  const analysis = {
    categories: {},
    totalWords: 0,
    categoryStats: {},
    lengthDistribution: {},
    culturalDistribution: {
      traditional: 0,
      modern: 0,
      trendy: 0
    }
  }

  // 分析每个类别
  for (const [mainCategory, subcategories] of Object.entries(vocabularyData)) {
    analysis.categories[mainCategory] = {
      subcategories: Object.keys(subcategories).length,
      totalWords: 0,
      subcategoryDetails: {}
    }

    for (const [subCategory, words] of Object.entries(subcategories)) {
      const wordCount = words.length
      analysis.categories[mainCategory].totalWords += wordCount
      analysis.categories[mainCategory].subcategoryDetails[subCategory] = {
        count: wordCount,
        examples: words.slice(0, 5) // 前5个示例
      }

      // 统计词汇长度分布
      words.forEach(word => {
        const length = word.length
        analysis.lengthDistribution[length] = (analysis.lengthDistribution[length] || 0) + 1
      })

      // 统计文化分布
      if (mainCategory === 'traditional') {
        analysis.culturalDistribution.traditional += wordCount
      } else if (mainCategory === 'trend') {
        analysis.culturalDistribution.trendy += wordCount
      } else {
        analysis.culturalDistribution.modern += wordCount
      }
    }

    analysis.totalWords += analysis.categories[mainCategory].totalWords
  }

  return analysis
}

/**
 * 生成分析报告
 */
function generateAnalysisReport(analysis) {
  let report = `# 词汇扩展引擎分析报告

## 📊 总体统计

- **总词汇量**: ${analysis.totalWords}个
- **主要类别**: ${Object.keys(analysis.categories).length}个
- **子类别总数**: ${Object.values(analysis.categories).reduce((sum, cat) => sum + cat.subcategories, 0)}个

## 🏷️ 类别分布

`

  // 类别详情
  for (const [category, details] of Object.entries(analysis.categories)) {
    const categoryName = {
      emotions: '情感词汇',
      professions: '职业词汇', 
      characteristics: '特征词汇',
      traditional: '传统文化词汇',
      popular: '流行词汇',
      trend: '时代潮流词汇'
    }[category] || category

    report += `### ${categoryName}\n`
    report += `- **词汇总数**: ${details.totalWords}个\n`
    report += `- **子类别数**: ${details.subcategories}个\n`
    report += `- **占比**: ${((details.totalWords / analysis.totalWords) * 100).toFixed(1)}%\n\n`

    report += `#### 子类别详情\n`
    for (const [subCategory, subDetails] of Object.entries(details.subcategoryDetails)) {
      report += `- **${subCategory}**: ${subDetails.count}个词汇\n`
      report += `  - 示例: ${subDetails.examples.join('、')}\n`
    }
    report += '\n'
  }

  // 文化分布
  report += `## 🎭 文化分布

- **传统文化**: ${analysis.culturalDistribution.traditional}个 (${((analysis.culturalDistribution.traditional / analysis.totalWords) * 100).toFixed(1)}%)
- **现代文化**: ${analysis.culturalDistribution.modern}个 (${((analysis.culturalDistribution.modern / analysis.totalWords) * 100).toFixed(1)}%)
- **潮流文化**: ${analysis.culturalDistribution.trendy}个 (${((analysis.culturalDistribution.trendy / analysis.totalWords) * 100).toFixed(1)}%)

`

  // 长度分布
  report += `## 📏 词汇长度分布

`
  const sortedLengths = Object.entries(analysis.lengthDistribution)
    .sort(([a], [b]) => parseInt(a) - parseInt(b))

  for (const [length, count] of sortedLengths) {
    const percentage = ((count / analysis.totalWords) * 100).toFixed(1)
    report += `- **${length}字词汇**: ${count}个 (${percentage}%)\n`
  }

  report += `\n## 🎯 适配建议

### V5语素库集成策略

1. **情感词汇** → 特质配置 (TRAITS_CONFIG)
   - 基础情感、积极情感 → 正面特质
   - 深层情感、文艺情感 → 中性特质
   - 现代情感 → 现代特质

2. **职业词汇** → 主体配置 (SUBJECTS_CONFIG)
   - 传统职业 → 古代人物扩展
   - 现代职业 → 现代职业扩展
   - 创意职业 → 网络身份扩展
   - 新兴职业 → 新增类别

3. **特征词汇** → 修饰词配置 (MODIFIERS_CONFIG)
   - 能力特征 → 权威级别扩展
   - 品质特征 → 程度强化扩展
   - 性格特征 → 状态描述扩展

4. **传统文化词汇** → 多类别分布
   - 诗词文化 → 主体配置
   - 传统概念 → 特质配置
   - 传统美德 → 特质配置

5. **流行/潮流词汇** → 现代化扩展
   - 网络流行 → 网络身份扩展
   - 日常生活 → 现代生活扩展
   - 亚文化 → 新增类别

### 实施优先级

1. **高优先级**: 职业词汇、特征词汇 (直接提升生成质量)
2. **中优先级**: 情感词汇、流行词汇 (增加表达丰富度)
3. **低优先级**: 传统文化词汇、潮流词汇 (特色化补充)

---

*报告生成时间: ${new Date().toLocaleString('zh-CN')}*
`

  return report
}

/**
 * 主函数
 */
function main() {
  try {
    // 分析词汇数据
    const analysis = analyzeVocabularyData()
    
    // 生成报告
    const report = generateAnalysisReport(analysis)
    
    // 保存报告
    const reportPath = path.join(__dirname, '..', 'docs', 'vocabulary-expansion-engine-analysis.md')
    fs.writeFileSync(reportPath, report, 'utf8')
    
    console.log('✅ 词汇扩展引擎分析完成')
    console.log(`📄 报告已保存到: ${reportPath}`)
    console.log(`📊 总词汇量: ${analysis.totalWords}个`)
    console.log(`🏷️ 主要类别: ${Object.keys(analysis.categories).length}个`)
    
  } catch (error) {
    console.error('❌ 分析过程中发生错误:', error)
    process.exit(1)
  }
}

// 执行主函数
if (require.main === module) {
  main()
}

module.exports = {
  analyzeVocabularyData,
  generateAnalysisReport,
  vocabularyData
}
