/**
 * 词汇扩展引擎到V5语素库集成脚本
 * 直接将词汇扩展引擎中的词汇集成到V5语素库配置文件中
 */

const fs = require('fs')
const path = require('path')

// 从词汇扩展引擎提取的词汇数据
const vocabularyData = {
  // 情感词汇 → 特质配置
  emotions: {
    基础情感: ['温暖', '温柔', '温馨', '温和', '温情', '热情', '热忱', '热心', '热烈', '热诚', '冷静', '冷淡', '平静', '平和', '平稳'],
    积极情感: ['欢乐', '欢喜', '欢快', '愉快', '愉悦', '快乐', '快意', '喜悦', '喜乐', '兴奋', '兴致', '兴趣'],
    深层情感: ['深情', '深爱', '深切', '深沉', '真诚', '真挚', '真心', '纯真', '纯洁', '纯净', '专注', '专一'],
    文艺情感: ['诗意', '诗情', '雅致', '雅韵', '优雅', '优美', '清雅', '清新', '清纯', '清澈'],
    现代情感: ['治愈', '暖心', '贴心', '用心', '走心', '佛系', '淡然', '随性', '自在', '洒脱', '元气', '活力', '朝气']
  },

  // 职业词汇 → 主体配置
  professions: {
    传统职业扩展: ['医师', '教师', '工程师', '律师', '会计师', '建筑师', '艺术家', '音乐家', '作家', '记者', '编辑', '翻译'],
    现代职业扩展: ['产品经理', '项目经理', '运营经理', '数据分析师', '用户体验师', '前端工程师', '后端工程师', '算法工程师', '测试工程师'],
    创意职业: ['插画师', '动画师', '游戏设计师', '影视制作人', '摄影师', '文案策划', '创意总监', '内容创作者', '独立开发者', '自由职业者'],
    新兴职业: ['AI训练师', '数据科学家', '区块链工程师', '云计算专家', '用户增长专家', '社群运营', '元宇宙设计师', '数字艺术家']
  },

  // 特征词汇 → 修饰词配置
  characteristics: {
    能力特征: ['专业', '精通', '熟练', '精湛', '创新', '创意', '高效', '迅速', '敏捷', '严谨', '精确', '全面'],
    品质特征: ['诚信', '诚实', '真诚', '可靠', '稳定', '踏实', '负责', '认真', '积极', '主动', '坚持', '坚定'],
    性格特征: ['开朗', '活泼', '外向', '内向', '稳重', '成熟', '幽默', '机智', '聪明', '智慧', '善良', '友善']
  },

  // 传统文化词汇 → 多类别分布
  traditional: {
    文人雅士: ['诗仙', '词圣', '诗人', '文人', '雅士', '才子', '佳人', '墨客', '骚人', '书生'],
    传统概念: ['书香', '墨香', '茶香', '文房', '四宝', '笔墨', '丹青', '琴棋', '书画', '诗酒'],
    传统美德: ['仁爱', '仁慈', '义气', '义理', '礼貌', '礼仪', '智慧', '信义', '忠诚', '孝顺']
  },

  // 流行词汇 → 现代化扩展
  popular: {
    日常生活: ['温馨', '舒适', '惬意', '悠闲', '轻松', '自在', '简单', '简约', '自然', '天然'],
    网络流行: ['给力', '靠谱', '厉害', '霸气', '萌萌', '可爱', '甜美', '酷炫', '治愈系', '元气满满']
  },

  // 时代潮流词汇 → 新增类别
  trend: {
    二次元文化: ['二次元', '萌系', '宅男', '宅女', '中二', '颜值', '傲娇', '腹黑', '治愈系', '元气系'],
    网络亚文化: ['破圈', '出圈', '内卷', '躺平', '摆烂', '佛系', '社畜', '打工人', '凡尔赛', '茶艺'],
    Z世代文化: ['emo', '精神内耗', '社恐', '社牛', '社死', 'yyds', '绝绝子', '芭比Q', '破防', '上头']
  }
}

/**
 * 生成扩展的V5语素库配置
 */
function generateExpandedV5Config() {
  let configCode = `/**
 * V5引擎扩展语素库配置
 * 基于词汇扩展引擎集成的丰富词汇
 * 
 * @generated 自动生成于 ${new Date().toISOString()}
 * @source vocabulary-expansion-engine
 */

// ============ 扩展主体词汇配置 ============
export const EXPANDED_SUBJECTS_CONFIG = {
  // 原有配置保持不变...
  
  // 新增职业类别
`

  // 添加职业词汇到主体配置
  for (const [category, words] of Object.entries(vocabularyData.professions)) {
    configCode += `  ${category}: {
    category: '${category}',
    description: '${getDescription(category, 'profession')}',
    elements: ${JSON.stringify(words, null, 4).replace(/\n/g, '\n    ')},
    usage_patterns: ['identity_elevation', 'temporal_displacement'],
    cultural_weight: ${getCulturalWeight(category)}
  },
`
  }

  // 添加传统文化中的人物类别
  configCode += `  文人雅士: {
    category: '文人雅士',
    description: '传统文化中的文人学者',
    elements: ${JSON.stringify(vocabularyData.traditional.文人雅士, null, 4).replace(/\n/g, '\n    ')},
    usage_patterns: ['temporal_displacement', 'service_personification'],
    cultural_weight: 1.2
  },
`

  configCode += `} as const

// ============ 扩展特质词汇配置 ============
export const EXPANDED_TRAITS_CONFIG = {
  // 原有配置保持不变...
  
  // 新增情感特质
`

  // 添加情感词汇到特质配置
  for (const [category, words] of Object.entries(vocabularyData.emotions)) {
    configCode += `  ${category}: {
    category: '${category}',
    description: '${getDescription(category, 'emotion')}',
    elements: ${JSON.stringify(words, null, 4).replace(/\n/g, '\n    ')},
    usage_patterns: ['contradiction_unity', 'emotion_state'],
    cultural_weight: ${getCulturalWeight(category)},
    polarity: '${getPolarity(category)}'
  },
`
  }

  // 添加传统文化特质
  configCode += `  传统概念: {
    category: '传统概念',
    description: '传统文化概念词汇',
    elements: ${JSON.stringify(vocabularyData.traditional.传统概念, null, 4).replace(/\n/g, '\n    ')},
    usage_patterns: ['contradiction_unity'],
    cultural_weight: 1.2,
    polarity: 'positive'
  },
  传统美德: {
    category: '传统美德',
    description: '传统道德品德词汇',
    elements: ${JSON.stringify(vocabularyData.traditional.传统美德, null, 4).replace(/\n/g, '\n    ')},
    usage_patterns: ['contradiction_unity'],
    cultural_weight: 1.2,
    polarity: 'positive'
  },
`

  // 添加流行文化特质
  for (const [category, words] of Object.entries(vocabularyData.popular)) {
    configCode += `  ${category}: {
    category: '${category}',
    description: '${getDescription(category, 'popular')}',
    elements: ${JSON.stringify(words, null, 4).replace(/\n/g, '\n    ')},
    usage_patterns: ['emotion_state', 'contradiction_unity'],
    cultural_weight: 1.1,
    polarity: 'positive'
  },
`
  }

  // 添加时代潮流特质
  for (const [category, words] of Object.entries(vocabularyData.trend)) {
    configCode += `  ${category}: {
    category: '${category}',
    description: '${getDescription(category, 'trend')}',
    elements: ${JSON.stringify(words, null, 4).replace(/\n/g, '\n    ')},
    usage_patterns: ['emotion_state', 'tech_expression'],
    cultural_weight: 1.0,
    polarity: 'neutral'
  },
`
  }

  configCode += `} as const

// ============ 扩展修饰词配置 ============
export const EXPANDED_MODIFIERS_CONFIG = {
  // 原有配置保持不变...
  
  // 新增特征修饰词
`

  // 添加特征词汇到修饰词配置
  for (const [category, words] of Object.entries(vocabularyData.characteristics)) {
    configCode += `  ${category}: {
    category: '${category}',
    description: '${getDescription(category, 'characteristic')}',
    elements: ${JSON.stringify(words, null, 4).replace(/\n/g, '\n    ')},
    usage_patterns: ['identity_elevation', 'contradiction_unity'],
    authority_weight: ${getAuthorityWeight(category)}
  },
`
  }

  configCode += `} as const

// ============ 统计信息 ============
export const EXPANDED_LIBRARY_STATS = {
  total_categories_added: ${getTotalCategories()},
  total_elements_added: ${getTotalElements()},
  categories_breakdown: {
    subjects: ${Object.keys(vocabularyData.professions).length + 1},
    traits: ${Object.keys(vocabularyData.emotions).length + Object.keys(vocabularyData.popular).length + Object.keys(vocabularyData.trend).length + 2},
    modifiers: ${Object.keys(vocabularyData.characteristics).length}
  }
}

// ============ 工具函数 ============
export function getAllExpandedElements() {
  return {
    ...EXPANDED_SUBJECTS_CONFIG,
    ...EXPANDED_TRAITS_CONFIG,
    ...EXPANDED_MODIFIERS_CONFIG
  }
}

export function getExpandedElementCount() {
  const allElements = getAllExpandedElements()
  return Object.values(allElements).reduce((total, config) => total + config.elements.length, 0)
}
`

  return configCode
}

/**
 * 获取类别描述
 */
function getDescription(category, type) {
  const descriptions = {
    // 职业描述
    '传统职业扩展': '传统行业的专业职业',
    '现代职业扩展': '现代社会的专业职业',
    '创意职业': '创意产业的职业角色',
    '新兴职业': '新兴行业的职业身份',
    
    // 情感描述
    '基础情感': '基础的情感表达词汇',
    '积极情感': '积极正面的情感词汇',
    '深层情感': '深层次的情感体验',
    '文艺情感': '具有文艺气息的情感',
    '现代情感': '现代社会的情感表达',
    
    // 特征描述
    '能力特征': '能力和技能相关的特征',
    '品质特征': '品质和品格相关的特征',
    '性格特征': '个性和性格相关的特征',
    
    // 流行描述
    '日常生活': '日常生活中的流行表达',
    '网络流行': '网络流行文化词汇',
    
    // 潮流描述
    '二次元文化': '二次元动漫文化词汇',
    '网络亚文化': '网络亚文化群体词汇',
    'Z世代文化': 'Z世代文化特色词汇'
  }
  
  return descriptions[category] || `${type}相关的词汇集合`
}

/**
 * 获取文化权重
 */
function getCulturalWeight(category) {
  if (category.includes('传统') || category.includes('文人')) return 1.2
  if (category.includes('现代') || category.includes('新兴')) return 1.0
  if (category.includes('创意') || category.includes('流行')) return 1.1
  return 1.0
}

/**
 * 获取权威权重
 */
function getAuthorityWeight(category) {
  if (category.includes('能力')) return 1.0
  if (category.includes('品质')) return 0.9
  if (category.includes('性格')) return 0.8
  return 0.9
}

/**
 * 获取极性
 */
function getPolarity(category) {
  if (category.includes('积极') || category.includes('文艺') || category.includes('现代')) return 'positive'
  if (category.includes('基础') || category.includes('深层')) return 'neutral'
  return 'positive'
}

/**
 * 获取总类别数
 */
function getTotalCategories() {
  return Object.keys(vocabularyData.professions).length + 
         Object.keys(vocabularyData.emotions).length + 
         Object.keys(vocabularyData.characteristics).length + 
         Object.keys(vocabularyData.popular).length + 
         Object.keys(vocabularyData.trend).length + 3 // 传统文化3个类别
}

/**
 * 获取总元素数
 */
function getTotalElements() {
  let total = 0
  for (const category of Object.values(vocabularyData)) {
    for (const words of Object.values(category)) {
      total += words.length
    }
  }
  return total
}

/**
 * 主函数
 */
function main() {
  try {
    console.log('🚀 开始生成V5引擎扩展语素库配置...')
    
    // 生成配置代码
    const configCode = generateExpandedV5Config()
    
    // 保存配置文件
    const configPath = path.join(__dirname, '..', 'config', 'expanded-element-library-config.ts')
    fs.writeFileSync(configPath, configCode, 'utf8')
    
    console.log('✅ V5引擎扩展语素库配置生成完成')
    console.log(`📄 配置文件已保存到: ${configPath}`)
    console.log(`📊 新增类别: ${getTotalCategories()}个`)
    console.log(`🏷️ 新增语素: ${getTotalElements()}个`)
    
    // 生成集成报告
    const report = `# V5引擎语素库扩展报告

## 📊 扩展统计

- **新增类别**: ${getTotalCategories()}个
- **新增语素**: ${getTotalElements()}个
- **配置文件**: config/expanded-element-library-config.ts

## 🏷️ 类别分布

### 主体词汇扩展 (SUBJECTS_CONFIG)
- 传统职业扩展: ${vocabularyData.professions.传统职业扩展.length}个
- 现代职业扩展: ${vocabularyData.professions.现代职业扩展.length}个  
- 创意职业: ${vocabularyData.professions.创意职业.length}个
- 新兴职业: ${vocabularyData.professions.新兴职业.length}个
- 文人雅士: ${vocabularyData.traditional.文人雅士.length}个

### 特质词汇扩展 (TRAITS_CONFIG)
- 基础情感: ${vocabularyData.emotions.基础情感.length}个
- 积极情感: ${vocabularyData.emotions.积极情感.length}个
- 深层情感: ${vocabularyData.emotions.深层情感.length}个
- 文艺情感: ${vocabularyData.emotions.文艺情感.length}个
- 现代情感: ${vocabularyData.emotions.现代情感.length}个
- 传统概念: ${vocabularyData.traditional.传统概念.length}个
- 传统美德: ${vocabularyData.traditional.传统美德.length}个
- 日常生活: ${vocabularyData.popular.日常生活.length}个
- 网络流行: ${vocabularyData.popular.网络流行.length}个
- 二次元文化: ${vocabularyData.trend.二次元文化.length}个
- 网络亚文化: ${vocabularyData.trend.网络亚文化.length}个
- Z世代文化: ${vocabularyData.trend.Z世代文化.length}个

### 修饰词扩展 (MODIFIERS_CONFIG)
- 能力特征: ${vocabularyData.characteristics.能力特征.length}个
- 品质特征: ${vocabularyData.characteristics.品质特征.length}个
- 性格特征: ${vocabularyData.characteristics.性格特征.length}个

## 🎯 使用方法

1. 在V5引擎中导入扩展配置:
\`\`\`typescript
import { 
  EXPANDED_SUBJECTS_CONFIG, 
  EXPANDED_TRAITS_CONFIG, 
  EXPANDED_MODIFIERS_CONFIG 
} from '../config/expanded-element-library-config'
\`\`\`

2. 合并到现有配置中:
\`\`\`typescript
const allSubjects = { ...SUBJECTS_CONFIG, ...EXPANDED_SUBJECTS_CONFIG }
const allTraits = { ...TRAITS_CONFIG, ...EXPANDED_TRAITS_CONFIG }
const allModifiers = { ...MODIFIERS_CONFIG, ...EXPANDED_MODIFIERS_CONFIG }
\`\`\`

---

*报告生成时间: ${new Date().toLocaleString('zh-CN')}*
`
    
    const reportPath = path.join(__dirname, '..', 'docs', 'v5-morpheme-library-expansion-report.md')
    fs.writeFileSync(reportPath, report, 'utf8')
    
    console.log(`📋 扩展报告已保存到: ${reportPath}`)
    
  } catch (error) {
    console.error('❌ 生成过程中发生错误:', error)
    process.exit(1)
  }
}

// 执行主函数
if (require.main === module) {
  main()
}

module.exports = {
  generateExpandedV5Config,
  vocabularyData
}
