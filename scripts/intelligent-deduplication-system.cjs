/**
 * 智能去重和质量控制系统
 * 实施高级去重算法，建立语素质量评估机制
 */

const fs = require('fs')
const path = require('path')

/**
 * 加载现有V5引擎语素库
 */
function loadCurrentV5Morphemes() {
  console.log('📚 加载现有V5引擎语素库...')
  
  // 从之前的分析报告中获取现有语素
  const currentMorphemes = new Set([
    // subjects (224个)
    '贫僧', '道士', '书生', '侠客', '状元', '举人', '秀才', '太守', '县令', '师爷',
    '程序员', '设计师', '产品经理', '运营', '策划', '文案', '测试', '架构师', '前端', '后端',
    'UP主', '主播', '网红', '博主', '自媒体', '数字游民', '斜杠青年', 'KOL', '网络达人', '内容创作者',
    '猫', '狗', '猪', '鸟', '鱼', '蜗牛', '大象', '蚂蚁', '老鼠', '兔子', '熊猫', '企鹅', '海豚', '狮子',
    '月亮', '星星', '太阳', '云朵', '彩虹', '闪电', '银河', '宇宙', '星球', '流星', '黑洞', '星云',
    '快乐', '悲伤', '愤怒', '温柔', '智慧', '勇敢', '梦想', '希望', '爱情', '友情', '孤独', '自由',
    '芝士', '咖啡', '茶', '巧克力', '蛋糕', '面包', '牛奶', '果汁', '冰淇淋', '薯片', '火锅', '烧烤',
    'WiFi', '404', '500', '数据库', '服务器', '算法', 'AI', '云端', '区块链', '元宇宙', 'VR', 'AR',
    
    // traits (162个)
    '温柔', '理性', '冷静', '勤奋', '节约', '听劝', '乐观', '自信', '坚强', '独立',
    '强硬', '感性', '冲动', '懒惰', '挥霍', '反骨', '悲观', '自卑', '脆弱', '依赖',
    '人生', '梦想', '快乐', '悲伤', '爱情', '友情', '工作', '学习', '生活', '未来',
    
    // modifiers (94个)
    '资深', '专业', '高级', '首席', '顶级', '权威', '官方', '认证', '特级', '超级', '终极', '至尊',
    '全球', '全国', '全市', '全网', '全场', '全能', '万能', '通用', '专用', '独家', '私人',
    '超级', '极度', '非常', '特别', '格外', '异常', '十分', '相当', '颇为', '略微', '稍微', '有点',
    
    // actions (60个)
    '吃饭', '睡觉', '工作', '学习', '运动', '购物', '聊天', '游戏', '看剧', '听歌', '阅读', '写作',
    '飞翔', '游泳', '跳舞', '唱歌', '画画', '弹琴', '下棋', '钓鱼', '爬山', '跑步', '骑车', '滑雪',
    
    // connectors (48个)
    '但', '却', '然而', '不过', '可是', '只是', '偏偏', '反而', '竟然', '居然', '虽然', '尽管',
    '和', '与', '及', '以及', '还有', '另外', '同时', '一边', '一面', '既然', '不仅', '而且',
    
    // suffixes (48个)
    '专家', '大师', '达人', '高手', '能手', '好手', '选手', '玩家', '用户', '会员', '粉丝', '爱好者',
    '君', '哥', '姐', '兄', '弟', '妹', '叔', '阿姨', '老师', '同学', '朋友', '伙伴'
  ])
  
  console.log(`   ✅ 已加载现有V5语素: ${currentMorphemes.size}个`)
  return currentMorphemes
}

/**
 * 语素质量评估系统
 */
class MorphemeQualityAssessment {
  constructor() {
    this.qualityThreshold = 0.7
    this.culturalSensitivityPatterns = [
      /[暴力血腥]/, /[色情淫秽]/, /[政治敏感]/, /[宗教极端]/,
      /[歧视仇恨]/, /[违法犯罪]/, /[欺诈诈骗]/, /[恶意攻击]/
    ]
  }

  /**
   * 评估单个语素质量
   */
  assessMorpheme(word, category = 'general') {
    const assessment = {
      word,
      category,
      scores: {
        length: this.assessLength(word),
        phonetic: this.assessPhonetic(word),
        semantic: this.assessSemantic(word),
        cultural: this.assessCultural(word),
        uniqueness: this.assessUniqueness(word),
        usability: this.assessUsability(word, category)
      },
      overall: 0,
      passed: false,
      issues: [],
      recommendations: []
    }

    // 计算综合评分
    assessment.overall = (
      assessment.scores.length * 0.15 +
      assessment.scores.phonetic * 0.15 +
      assessment.scores.semantic * 0.20 +
      assessment.scores.cultural * 0.20 +
      assessment.scores.uniqueness * 0.15 +
      assessment.scores.usability * 0.15
    )

    // 判断是否通过
    assessment.passed = assessment.overall >= this.qualityThreshold

    // 生成问题和建议
    this.generateIssuesAndRecommendations(assessment)

    return assessment
  }

  /**
   * 长度评估
   */
  assessLength(word) {
    const length = word.length
    if (length >= 2 && length <= 4) return 1.0
    if (length === 1 || length === 5) return 0.8
    if (length === 6) return 0.6
    return 0.4
  }

  /**
   * 语音美感评估
   */
  assessPhonetic(word) {
    let score = 0.5
    
    // 检查音韵美感
    if (/[音韵美雅优清]/.test(word)) score += 0.2
    if (/[温柔软润]/.test(word)) score += 0.15
    if (/[亮明朗响]/.test(word)) score += 0.1
    
    // 避免不和谐音
    if (/[死病痛苦]/.test(word)) score -= 0.2
    
    return Math.min(1.0, Math.max(0.2, score))
  }

  /**
   * 语义清晰度评估
   */
  assessSemantic(word) {
    let score = 0.6
    
    // 语义明确性
    if (!/[模糊歧义]/.test(word)) score += 0.2
    
    // 积极性
    if (/[美好优秀精彩]/.test(word)) score += 0.15
    if (/[温暖治愈正能量]/.test(word)) score += 0.1
    
    // 避免消极词汇
    if (/[负面消极恶劣]/.test(word)) score -= 0.3
    
    return Math.min(1.0, Math.max(0.2, score))
  }

  /**
   * 文化适宜性评估
   */
  assessCultural(word) {
    let score = 0.8
    
    // 检查文化敏感性
    for (const pattern of this.culturalSensitivityPatterns) {
      if (pattern.test(word)) {
        score -= 0.5
        break
      }
    }
    
    // 传统文化加分
    if (/[诗词雅韵古典]/.test(word)) score += 0.15
    if (/[仁义礼智信]/.test(word)) score += 0.1
    
    // 现代文化适应性
    if (/[创新科技数字]/.test(word)) score += 0.05
    
    return Math.min(1.0, Math.max(0.1, score))
  }

  /**
   * 独特性评估
   */
  assessUniqueness(word) {
    // 基于词汇的稀有程度和创意性
    let score = 0.5
    
    // 常见词汇降分
    const commonWords = ['好', '很', '非常', '特别', '一般', '普通']
    if (commonWords.includes(word)) score -= 0.3
    
    // 创意词汇加分
    if (word.length >= 3 && /[师家者人]$/.test(word)) score += 0.2
    if (/^[超极顶]/.test(word)) score += 0.1
    
    return Math.min(1.0, Math.max(0.2, score))
  }

  /**
   * 可用性评估
   */
  assessUsability(word, category) {
    let score = 0.7
    
    // 根据类别调整评分
    switch (category) {
      case 'professions':
        if (/[师家者员]$/.test(word)) score += 0.2
        break
      case 'emotions':
        if (/[情感心意]/.test(word)) score += 0.15
        break
      case 'characteristics':
        if (/[性格特质]/.test(word)) score += 0.1
        break
    }
    
    // 生成友好性
    if (word.length <= 4) score += 0.1
    
    return Math.min(1.0, score)
  }

  /**
   * 生成问题和建议
   */
  generateIssuesAndRecommendations(assessment) {
    const { scores, word } = assessment
    
    if (scores.length < 0.6) {
      assessment.issues.push('词汇长度不理想')
      assessment.recommendations.push('建议使用2-4字词汇')
    }
    
    if (scores.cultural < 0.5) {
      assessment.issues.push('文化适宜性较低')
      assessment.recommendations.push('检查是否包含敏感内容')
    }
    
    if (scores.semantic < 0.5) {
      assessment.issues.push('语义清晰度不足')
      assessment.recommendations.push('选择语义更明确的词汇')
    }
    
    if (scores.uniqueness < 0.4) {
      assessment.issues.push('独特性不足')
      assessment.recommendations.push('考虑更有创意的表达')
    }
  }
}

/**
 * 智能去重系统
 */
class IntelligentDeduplication {
  constructor(currentMorphemes) {
    this.currentMorphemes = currentMorphemes
    this.qualityAssessment = new MorphemeQualityAssessment()
    this.semanticGroups = new Map()
  }

  /**
   * 执行智能去重
   */
  deduplicate(newMorphemes, category = 'general') {
    console.log(`🔍 开始智能去重处理 (${category})...`)
    
    const results = {
      original: newMorphemes.length,
      duplicates: 0,
      lowQuality: 0,
      accepted: 0,
      rejected: 0,
      acceptedMorphemes: [],
      rejectedMorphemes: [],
      qualityReports: []
    }

    for (const word of newMorphemes) {
      // 1. 检查重复
      if (this.currentMorphemes.has(word)) {
        results.duplicates++
        results.rejectedMorphemes.push({
          word,
          reason: 'duplicate',
          message: '与现有语素重复'
        })
        continue
      }

      // 2. 质量评估
      const qualityReport = this.qualityAssessment.assessMorpheme(word, category)
      results.qualityReports.push(qualityReport)

      if (!qualityReport.passed) {
        results.lowQuality++
        results.rejectedMorphemes.push({
          word,
          reason: 'low_quality',
          message: `质量评分: ${qualityReport.overall.toFixed(2)}`,
          issues: qualityReport.issues
        })
        continue
      }

      // 3. 语义去重
      const semanticKey = this.generateSemanticKey(word)
      if (this.semanticGroups.has(semanticKey)) {
        const existingGroup = this.semanticGroups.get(semanticKey)
        
        // 比较质量，保留更好的
        if (qualityReport.overall > existingGroup.quality) {
          // 替换现有的
          results.acceptedMorphemes = results.acceptedMorphemes.filter(m => m.word !== existingGroup.word)
          results.acceptedMorphemes.push({
            word,
            quality: qualityReport.overall,
            category,
            semanticKey
          })
          this.semanticGroups.set(semanticKey, { word, quality: qualityReport.overall })
        } else {
          results.rejectedMorphemes.push({
            word,
            reason: 'semantic_duplicate',
            message: `语义与"${existingGroup.word}"重复`
          })
        }
      } else {
        // 新的语义组
        results.acceptedMorphemes.push({
          word,
          quality: qualityReport.overall,
          category,
          semanticKey
        })
        this.semanticGroups.set(semanticKey, { word, quality: qualityReport.overall })
        results.accepted++
      }
    }

    results.rejected = results.duplicates + results.lowQuality + (results.original - results.accepted - results.duplicates - results.lowQuality)

    console.log(`   ✅ 去重完成: ${results.original}个 → ${results.accepted}个`)
    console.log(`      - 重复: ${results.duplicates}个`)
    console.log(`      - 低质量: ${results.lowQuality}个`)
    console.log(`      - 语义重复: ${results.rejected - results.duplicates - results.lowQuality}个`)

    return results
  }

  /**
   * 生成语义键
   */
  generateSemanticKey(word) {
    // 基于词汇的语义特征生成键
    let key = ''
    
    // 词汇长度
    key += word.length
    
    // 词汇类型
    if (/[师家者人员]$/.test(word)) key += '_profession'
    else if (/[情感心意]/.test(word)) key += '_emotion'
    else if (/[美好优秀]/.test(word)) key += '_positive'
    else if (/[古典传统]/.test(word)) key += '_traditional'
    else key += '_general'
    
    // 首字特征
    key += '_' + word.charAt(0)
    
    return key
  }
}

/**
 * 主函数
 */
function main() {
  try {
    console.log('🚀 启动智能去重和质量控制系统...')
    
    // 加载现有语素
    const currentMorphemes = loadCurrentV5Morphemes()
    
    // 加载提取的词汇数据
    const reportPath = path.join(__dirname, '..', 'comprehensive-vocabulary-extraction-report.json')
    if (!fs.existsSync(reportPath)) {
      console.error('❌ 请先运行词汇提取脚本')
      return
    }
    
    const extractionReport = JSON.parse(fs.readFileSync(reportPath, 'utf8'))
    const deduplication = new IntelligentDeduplication(currentMorphemes)
    
    const deduplicationResults = {}
    
    // 对每个类别进行去重处理
    Object.keys(extractionReport.extracted_vocabulary).forEach(category => {
      const categoryData = extractionReport.extracted_vocabulary[category]
      if (categoryData.allWords && categoryData.allWords.length > 0) {
        deduplicationResults[category] = deduplication.deduplicate(categoryData.allWords, category)
      }
    })
    
    // 生成最终报告
    const finalReport = {
      timestamp: new Date().toISOString(),
      original_extraction: extractionReport.summary,
      deduplication_results: deduplicationResults,
      summary: generateSummary(deduplicationResults),
      quality_standards: {
        threshold: 0.7,
        assessment_criteria: [
          '长度适宜性 (15%)',
          '语音美感 (15%)',
          '语义清晰度 (20%)',
          '文化适宜性 (20%)',
          '独特性 (15%)',
          '可用性 (15%)'
        ]
      }
    }
    
    // 保存去重报告
    const deduplicationReportPath = path.join(__dirname, '..', 'intelligent-deduplication-report.json')
    fs.writeFileSync(deduplicationReportPath, JSON.stringify(finalReport, null, 2))
    
    console.log(`\n✅ 智能去重和质量控制完成！`)
    console.log(`📄 详细报告已保存到: ${deduplicationReportPath}`)
    console.log(`📊 处理结果: ${finalReport.summary.total_original}个 → ${finalReport.summary.total_accepted}个`)
    console.log(`🎯 质量通过率: ${(finalReport.summary.total_accepted / finalReport.summary.total_original * 100).toFixed(1)}%`)
    
    return finalReport
    
  } catch (error) {
    console.error('❌ 处理过程中发生错误:', error)
    process.exit(1)
  }
}

/**
 * 生成汇总统计
 */
function generateSummary(deduplicationResults) {
  let totalOriginal = 0
  let totalAccepted = 0
  let totalDuplicates = 0
  let totalLowQuality = 0
  
  Object.values(deduplicationResults).forEach(result => {
    totalOriginal += result.original
    totalAccepted += result.accepted
    totalDuplicates += result.duplicates
    totalLowQuality += result.lowQuality
  })
  
  return {
    total_original: totalOriginal,
    total_accepted: totalAccepted,
    total_duplicates: totalDuplicates,
    total_low_quality: totalLowQuality,
    total_rejected: totalOriginal - totalAccepted,
    acceptance_rate: (totalAccepted / totalOriginal * 100).toFixed(1) + '%'
  }
}

// 执行去重处理
if (require.main === module) {
  main()
}

module.exports = { 
  IntelligentDeduplication, 
  MorphemeQualityAssessment,
  loadCurrentV5Morphemes
}
