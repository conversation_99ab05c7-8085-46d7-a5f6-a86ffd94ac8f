import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// __dirname replacement for ESM
const __dirname = path.dirname(fileURLToPath(import.meta.url));

const root = path.join(__dirname, '..');

/** Utility to walk directory recursively and return files */
function walk(dir: string, extFilter: string[] = []): string[] {
  const entries = fs.readdirSync(dir, { withFileTypes: true });
  let files: string[] = [];
  for (const e of entries) {
    const full = path.join(dir, e.name);
    if (e.isDirectory()) files = files.concat(walk(full, extFilter));
    else if (extFilter.length === 0 || extFilter.some((ext) => e.name.endsWith(ext))) files.push(full);
  }
  return files;
}

/** Phase0 Report builder */
function buildReport() {
  const lines: string[] = [];
  lines.push('# Phase 0 Report\n');

  // 1. data directory coverage
  const dataDir = path.join(root, 'data');
  const languages: Record<string, number> = {};
  if (fs.existsSync(dataDir)) {
    walk(dataDir, ['.json']).forEach((file) => {
      const parts = file.split(path.sep);
      // find language code (simple heuristic: en/zh/ja etc as direct subdir)
      const lang = parts.find((p) => /^[a-z]{2}(-[A-Z]{2})?$/.test(p));
      if (lang) languages[lang] = (languages[lang] || 0) + 1;
    });
    lines.push('## data/ 语言覆盖度');
    Object.entries(languages).forEach(([lang, count]) => {
      lines.push(`- ${lang}: ${count} 个 json 文件`);
    });
    lines.push('');
  }

  // 2. server/api endpoints
  const apiDir = path.join(root, 'server', 'api');
  lines.push('## server/api 接口一览');
  if (fs.existsSync(apiDir)) {
    const apiFiles = walk(apiDir, ['.ts']).map((f) => path.relative(root, f));
    apiFiles.forEach((f) => {
      const content = fs.readFileSync(path.join(root, f), 'utf8');
      const hasTodo = /TODO|FIXME/.test(content);
      lines.push(`- ${f}${hasTodo ? ' ⚠️ TODO' : ''}`);
    });
  }
  lines.push('');

  // 3. i18n keys diff
  const localeDir = path.join(root, 'i18n', 'locales');
  lines.push('## i18n 缺失翻译检测');
  if (fs.existsSync(localeDir)) {
    const localeFiles = fs.readdirSync(localeDir).filter((f) => f.endsWith('.json'));
    const allKeys = new Set<string>();
    const localeMap: Record<string, string[]> = {};
    localeFiles.forEach((file) => {
      const json = JSON.parse(fs.readFileSync(path.join(localeDir, file), 'utf8'));
      const keys = Object.keys(json);
      localeMap[file] = keys;
      keys.forEach((k) => allKeys.add(k));
    });

    for (const file of localeFiles) {
      const missing = [...allKeys].filter((k) => !localeMap[file].includes(k));
      lines.push(`- ${file}: ${localeMap[file].length} keys, 缺失 ${missing.length}`);
      if (missing.length) {
        lines.push(`  - missing: ${missing.join(', ')}`);
      }
    }
  }

  // write report
  const outPath = path.join(root, 'docs', 'phase0-report.md');
  fs.writeFileSync(outPath, lines.join('\n'));
  console.log(`Report generated at docs/phase0-report.md (${lines.length} lines)`);
}

buildReport();
