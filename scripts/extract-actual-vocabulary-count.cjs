/**
 * 从词汇扩展引擎文件中提取实际词汇数量
 * 直接解析vocabulary-expansion-engine.ts文件
 */

const fs = require('fs')
const path = require('path')

/**
 * 从文件中提取所有词汇数组
 */
function extractVocabularyFromFile() {
  const filePath = path.join(__dirname, '..', 'server', 'api', 'vocabulary', 'vocabulary-expansion-engine.ts')
  
  if (!fs.existsSync(filePath)) {
    console.error('❌ 文件不存在:', filePath)
    return null
  }

  const content = fs.readFileSync(filePath, 'utf8')
  
  console.log('🔍 开始提取词汇扩展引擎中的实际词汇...')
  
  const vocabularyCollections = {}
  let totalWords = 0

  // 1. 提取情感词汇 (expandEmotionVocabulary)
  const emotionMatch = content.match(/const emotionCategories = \{([\s\S]*?)\n    \}/m)
  if (emotionMatch) {
    const emotionWords = extractWordsFromCategoriesText(emotionMatch[1])
    vocabularyCollections.emotions = emotionWords
    console.log(`   ✅ 情感词汇: ${emotionWords.totalCount}个`)
    totalWords += emotionWords.totalCount
  }

  // 2. 提取职业词汇 (expandProfessionVocabulary)
  const professionMatch = content.match(/const professionCategories = \{([\s\S]*?)\n    \}/m)
  if (professionMatch) {
    const professionWords = extractWordsFromCategoriesText(professionMatch[1])
    vocabularyCollections.professions = professionWords
    console.log(`   ✅ 职业词汇: ${professionWords.totalCount}个`)
    totalWords += professionWords.totalCount
  }

  // 3. 提取特征词汇 (expandCharacteristicVocabulary)
  const characteristicMatch = content.match(/const characteristicCategories = \{([\s\S]*?)\n    \}/m)
  if (characteristicMatch) {
    const characteristicWords = extractWordsFromCategoriesText(characteristicMatch[1])
    vocabularyCollections.characteristics = characteristicWords
    console.log(`   ✅ 特征词汇: ${characteristicWords.totalCount}个`)
    totalWords += characteristicWords.totalCount
  }

  // 4. 提取传统文化词汇 (expandTraditionalCulturalVocabulary)
  const traditionalMatch = content.match(/const traditionalCategories = \{([\s\S]*?)\n    \}/m)
  if (traditionalMatch) {
    const traditionalWords = extractWordsFromCategoriesText(traditionalMatch[1])
    vocabularyCollections.traditional = traditionalWords
    console.log(`   ✅ 传统文化词汇: ${traditionalWords.totalCount}个`)
    totalWords += traditionalWords.totalCount
  }

  // 5. 提取流行词汇 (expandPopularVocabulary)
  const popularMatch = content.match(/const popularCategories = \{([\s\S]*?)\n    \}/m)
  if (popularMatch) {
    const popularWords = extractWordsFromCategoriesText(popularMatch[1])
    vocabularyCollections.popular = popularWords
    console.log(`   ✅ 流行词汇: ${popularWords.totalCount}个`)
    totalWords += popularWords.totalCount
  }

  // 6. 提取大规模情感词汇 (expandEmotionVocabularyMassive)
  const massiveEmotionMatch = content.match(/const massiveEmotionWords = \[([\s\S]*?)\n    \]/m)
  if (massiveEmotionMatch) {
    const massiveEmotionWords = extractWordsFromArrayText(massiveEmotionMatch[1])
    vocabularyCollections.massiveEmotions = {
      categories: { massive_emotions: massiveEmotionWords },
      totalCount: massiveEmotionWords.length,
      allWords: massiveEmotionWords
    }
    console.log(`   ✅ 大规模情感词汇: ${massiveEmotionWords.length}个`)
    totalWords += massiveEmotionWords.length
  }

  // 7. 提取大规模职业词汇 (expandProfessionVocabularyMassive)
  const massiveProfessionMatch = content.match(/const massiveProfessionWords = \[([\s\S]*?)\n    \]/m)
  if (massiveProfessionMatch) {
    const massiveProfessionWords = extractWordsFromArrayText(massiveProfessionMatch[1])
    vocabularyCollections.massiveProfessions = {
      categories: { massive_professions: massiveProfessionWords },
      totalCount: massiveProfessionWords.length,
      allWords: massiveProfessionWords
    }
    console.log(`   ✅ 大规模职业词汇: ${massiveProfessionWords.length}个`)
    totalWords += massiveProfessionWords.length
  }

  // 8. 提取大规模特征词汇 (expandCharacteristicVocabularyMassive)
  const massiveCharacteristicMatch = content.match(/const massiveCharacteristicWords = \[([\s\S]*?)\n    \]/m)
  if (massiveCharacteristicMatch) {
    const massiveCharacteristicWords = extractWordsFromArrayText(massiveCharacteristicMatch[1])
    vocabularyCollections.massiveCharacteristics = {
      categories: { massive_characteristics: massiveCharacteristicWords },
      totalCount: massiveCharacteristicWords.length,
      allWords: massiveCharacteristicWords
    }
    console.log(`   ✅ 大规模特征词汇: ${massiveCharacteristicWords.length}个`)
    totalWords += massiveCharacteristicWords.length
  }

  // 9. 提取时代潮流词汇 (expandTrendVocabulary)
  const trendMatch = content.match(/const trendCategories = \{([\s\S]*?)\n      \]/m)
  if (trendMatch) {
    const trendWords = extractWordsFromCategoriesText(trendMatch[1])
    vocabularyCollections.trend = trendWords
    console.log(`   ✅ 时代潮流词汇: ${trendWords.totalCount}个`)
    totalWords += trendWords.totalCount
  }

  return {
    collections: vocabularyCollections,
    totalWords,
    summary: generateSummary(vocabularyCollections, totalWords)
  }
}

/**
 * 从类别文本中提取词汇
 */
function extractWordsFromCategoriesText(categoriesText) {
  const categories = {}
  let totalCount = 0
  const allWords = []

  // 匹配每个类别的词汇数组
  const categoryRegex = /(\w+):\s*\[([\s\S]*?)\]/g
  let match

  while ((match = categoryRegex.exec(categoriesText)) !== null) {
    const categoryName = match[1]
    const wordsText = match[2]
    const words = extractWordsFromArrayText(wordsText)
    
    categories[categoryName] = words
    totalCount += words.length
    allWords.push(...words)
  }

  return {
    categories,
    totalCount,
    allWords
  }
}

/**
 * 从数组文本中提取词汇
 */
function extractWordsFromArrayText(arrayText) {
  const words = []
  
  // 匹配所有单引号包围的词汇
  const wordRegex = /'([^']+)'/g
  let match

  while ((match = wordRegex.exec(arrayText)) !== null) {
    const word = match[1].trim()
    if (word && !words.includes(word)) {
      words.push(word)
    }
  }

  return words
}

/**
 * 生成总结报告
 */
function generateSummary(collections, totalWords) {
  const summary = {
    totalWords,
    totalCollections: Object.keys(collections).length,
    totalCategories: 0,
    breakdown: {},
    comparisonWithV5: {
      currentV5: 636,
      expansionEngine: totalWords,
      difference: totalWords - 636,
      increaseRatio: ((totalWords - 636) / 636 * 100).toFixed(1) + '%'
    }
  }

  // 计算详细分解
  Object.keys(collections).forEach(collectionName => {
    const collection = collections[collectionName]
    const categoryCount = Object.keys(collection.categories).length
    
    summary.breakdown[collectionName] = {
      words: collection.totalCount,
      categories: categoryCount,
      percentage: (collection.totalCount / totalWords * 100).toFixed(1) + '%'
    }
    
    summary.totalCategories += categoryCount
  })

  return summary
}

/**
 * 生成集成方案
 */
function generateIntegrationPlan(data) {
  const plan = {
    phase1: {
      name: '核心语素集成 (第一阶段)',
      targetWords: 800,
      collections: ['emotions', 'professions', 'characteristics'],
      estimatedIncrease: 260, // 基于实际数据估算
      priority: 'HIGH',
      timeline: '1-2周',
      implementation: [
        '将情感词汇集成到TRAITS_CONFIG',
        '将职业词汇集成到SUBJECTS_CONFIG',
        '将特征词汇集成到MODIFIERS_CONFIG'
      ]
    },
    phase2: {
      name: '文化语素集成 (第二阶段)',
      targetWords: 1200,
      collections: ['traditional', 'popular'],
      estimatedIncrease: 400,
      priority: 'MEDIUM',
      timeline: '2-3周',
      implementation: [
        '将传统文化词汇分类集成到各配置',
        '将流行词汇集成到现代化配置',
        '建立文化标签体系'
      ]
    },
    phase3: {
      name: '扩展语素集成 (第三阶段)',
      targetWords: 2000,
      collections: ['massiveEmotions', 'massiveProfessions', 'massiveCharacteristics', 'trend'],
      estimatedIncrease: 800,
      priority: 'LOW',
      timeline: '1个月',
      implementation: [
        '集成大规模扩展词汇',
        '集成时代潮流词汇',
        '建立质量评估机制'
      ]
    }
  }

  return plan
}

/**
 * 主函数
 */
function main() {
  console.log('🔍 词汇扩展引擎实际词汇数量分析')
  console.log('=' .repeat(60))

  const data = extractVocabularyFromFile()
  
  if (!data) {
    console.error('❌ 提取失败')
    return
  }

  console.log('\n📊 统计结果:')
  console.log(`   总词汇数量: ${data.totalWords}个`)
  console.log(`   词汇集合: ${data.summary.totalCollections}个`)
  console.log(`   总类别数: ${data.summary.totalCategories}个`)

  console.log('\n📈 与V5引擎对比:')
  console.log(`   当前V5引擎: ${data.summary.comparisonWithV5.currentV5}个`)
  console.log(`   词汇扩展引擎: ${data.summary.comparisonWithV5.expansionEngine}个`)
  console.log(`   潜在增长: ${data.summary.comparisonWithV5.difference}个`)
  console.log(`   增长比例: ${data.summary.comparisonWithV5.increaseRatio}`)

  console.log('\n🏷️ 详细分解:')
  Object.keys(data.summary.breakdown).forEach(collection => {
    const info = data.summary.breakdown[collection]
    console.log(`   ${collection}: ${info.words}个 (${info.percentage})`)
  })

  // 生成集成方案
  const integrationPlan = generateIntegrationPlan(data)

  console.log('\n🎯 集成方案:')
  Object.keys(integrationPlan).forEach(phase => {
    const plan = integrationPlan[phase]
    console.log(`   ${plan.name}: ${plan.targetWords}个目标 (优先级: ${plan.priority})`)
  })

  // 保存详细报告
  const report = {
    timestamp: new Date().toISOString(),
    analysis: data,
    integrationPlan,
    recommendations: [
      '词汇扩展引擎包含大量高质量语素，建议分阶段集成',
      '优先集成核心语素(情感、职业、特征)，快速提升生成质量',
      '建立质量评估机制，确保集成词汇的适用性',
      '实施渐进式集成，避免一次性集成过多影响性能',
      '建立A/B测试框架，验证集成效果'
    ]
  }

  const reportPath = path.join(__dirname, '..', 'vocabulary-expansion-actual-analysis.json')
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2))

  console.log(`\n✅ 分析完成！详细报告已保存到: ${reportPath}`)
}

// 执行分析
if (require.main === module) {
  main()
}

module.exports = { extractVocabularyFromFile, generateIntegrationPlan }
