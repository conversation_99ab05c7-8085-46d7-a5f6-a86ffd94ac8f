/**
 * 分批集成管理器
 * 将词汇扩展引擎中的1823个语素分批安全集成到V5引擎中
 */

const fs = require('fs')
const path = require('path')

/**
 * 分批集成管理器
 */
class BatchIntegrationManager {
  constructor() {
    this.configPaths = {
      elementLibrary: path.join(__dirname, '..', 'config', 'element-library-config.ts'),
      expandedLibrary: path.join(__dirname, '..', 'config', 'expanded-element-library-config.ts')
    }
    this.backupPaths = {
      elementLibrary: this.configPaths.elementLibrary + '.batch-backup',
      expandedLibrary: this.configPaths.expandedLibrary + '.batch-backup'
    }
    this.extractedVocabulary = null
    this.integrationProgress = {
      totalBatches: 5,
      completedBatches: 0,
      totalIntegrated: 0,
      batchReports: []
    }
  }

  /**
   * 执行分批集成
   */
  async executeBatchIntegration() {
    console.log('🚀 开始词汇扩展引擎分批集成...')
    console.log('=' .repeat(60))
    console.log('📋 分批策略:')
    console.log('   第一批: 职业类词汇 (professions, massiveProfessions)')
    console.log('   第二批: 情感类词汇 (emotions, massiveEmotions)')
    console.log('   第三批: 特征类词汇 (characteristics, massiveCharacteristics)')
    console.log('   第四批: 传统文化词汇 (traditional)')
    console.log('   第五批: 流行词汇和其他类别 (popular, trend)')
    console.log('=' .repeat(60))

    try {
      // 1. 创建备份
      this.createBackups()

      // 2. 提取所有语素
      this.extractedVocabulary = this.extractAllVocabulary()

      // 3. 分批集成
      await this.integrateBatch1() // 职业类词汇
      await this.integrateBatch2() // 情感类词汇
      await this.integrateBatch3() // 特征类词汇
      await this.integrateBatch4() // 传统文化词汇
      await this.integrateBatch5() // 流行词汇和其他类别

      // 4. 生成最终报告
      const finalReport = this.generateFinalReport()

      console.log('\n🎉 分批集成全部完成！')
      console.log(`📊 总集成语素: ${this.integrationProgress.totalIntegrated}个`)
      console.log(`🎯 目标完成度: ${((636 + this.integrationProgress.totalIntegrated) / 3000 * 100).toFixed(1)}%`)

      return finalReport

    } catch (error) {
      console.error('❌ 分批集成失败，正在恢复备份...', error)
      this.restoreBackups()
      throw error
    }
  }

  /**
   * 创建配置文件备份
   */
  createBackups() {
    console.log('💾 创建配置文件备份...')
    
    Object.keys(this.configPaths).forEach(key => {
      const originalPath = this.configPaths[key]
      const backupPath = this.backupPaths[key]
      
      if (fs.existsSync(originalPath)) {
        fs.copyFileSync(originalPath, backupPath)
        console.log(`   ✅ 已备份: ${path.basename(originalPath)}`)
      }
    })
  }

  /**
   * 恢复配置文件备份
   */
  restoreBackups() {
    console.log('🔄 恢复配置文件备份...')
    
    Object.keys(this.backupPaths).forEach(key => {
      const backupPath = this.backupPaths[key]
      const originalPath = this.configPaths[key]
      
      if (fs.existsSync(backupPath)) {
        fs.copyFileSync(backupPath, originalPath)
        console.log(`   ✅ 已恢复: ${path.basename(originalPath)}`)
      }
    })
  }

  /**
   * 提取所有语素
   */
  extractAllVocabulary() {
    console.log('📚 提取词汇扩展引擎中的所有语素...')
    
    const engineFilePath = path.join(__dirname, '..', 'server', 'api', 'vocabulary', 'vocabulary-expansion-engine.ts')
    
    if (!fs.existsSync(engineFilePath)) {
      throw new Error('词汇扩展引擎文件不存在')
    }

    const fileContent = fs.readFileSync(engineFilePath, 'utf8')
    
    const extractedVocabulary = {
      professions: this.extractProfessionVocabulary(fileContent),
      massiveProfessions: this.extractMassiveProfessions(fileContent),
      emotions: this.extractEmotionVocabulary(fileContent),
      massiveEmotions: this.extractMassiveEmotions(fileContent),
      characteristics: this.extractCharacteristicVocabulary(fileContent),
      massiveCharacteristics: this.extractMassiveCharacteristics(fileContent),
      traditional: this.extractTraditionalVocabulary(fileContent),
      popular: this.extractPopularVocabulary(fileContent),
      trend: this.extractTrendVocabulary(fileContent)
    }

    let totalExtracted = 0
    Object.values(extractedVocabulary).forEach(words => {
      totalExtracted += words.length
    })

    console.log(`   ✅ 成功提取 ${totalExtracted} 个语素`)
    return extractedVocabulary
  }

  /**
   * 第一批：集成职业类词汇
   */
  async integrateBatch1() {
    console.log('\n📦 第一批集成: 职业类词汇')
    
    const batch1Words = [
      ...this.extractedVocabulary.professions,
      ...this.extractedVocabulary.massiveProfessions
    ]
    
    const uniqueWords = [...new Set(batch1Words)]
    console.log(`   📊 本批语素数量: ${uniqueWords.length}个`)
    
    // 集成到SUBJECTS_CONFIG
    this.addToSubjectsConfig('职业扩展_批次1', uniqueWords)
    
    // 验证配置文件
    this.validateConfigFile()
    
    // 记录批次报告
    const batchReport = {
      batchNumber: 1,
      category: '职业类词汇',
      wordsCount: uniqueWords.length,
      timestamp: new Date().toISOString(),
      status: 'completed'
    }
    
    this.integrationProgress.completedBatches++
    this.integrationProgress.totalIntegrated += uniqueWords.length
    this.integrationProgress.batchReports.push(batchReport)
    
    console.log(`   ✅ 第一批集成完成，集成${uniqueWords.length}个语素`)
    
    // 生成中间报告
    this.generateIntermediateReport(1)
  }

  /**
   * 第二批：集成情感类词汇
   */
  async integrateBatch2() {
    console.log('\n📦 第二批集成: 情感类词汇')
    
    const batch2Words = [
      ...this.extractedVocabulary.emotions,
      ...this.extractedVocabulary.massiveEmotions
    ]
    
    const uniqueWords = [...new Set(batch2Words)]
    console.log(`   📊 本批语素数量: ${uniqueWords.length}个`)
    
    // 集成到TRAITS_CONFIG
    this.addToTraitsConfig('情感扩展_批次2', uniqueWords)
    
    // 验证配置文件
    this.validateConfigFile()
    
    // 记录批次报告
    const batchReport = {
      batchNumber: 2,
      category: '情感类词汇',
      wordsCount: uniqueWords.length,
      timestamp: new Date().toISOString(),
      status: 'completed'
    }
    
    this.integrationProgress.completedBatches++
    this.integrationProgress.totalIntegrated += uniqueWords.length
    this.integrationProgress.batchReports.push(batchReport)
    
    console.log(`   ✅ 第二批集成完成，集成${uniqueWords.length}个语素`)
    
    // 生成中间报告
    this.generateIntermediateReport(2)
  }

  /**
   * 第三批：集成特征类词汇
   */
  async integrateBatch3() {
    console.log('\n📦 第三批集成: 特征类词汇')
    
    const batch3Words = [
      ...this.extractedVocabulary.characteristics,
      ...this.extractedVocabulary.massiveCharacteristics
    ]
    
    const uniqueWords = [...new Set(batch3Words)]
    console.log(`   📊 本批语素数量: ${uniqueWords.length}个`)
    
    // 集成到MODIFIERS_CONFIG
    this.addToModifiersConfig('特征扩展_批次3', uniqueWords)
    
    // 验证配置文件
    this.validateConfigFile()
    
    // 记录批次报告
    const batchReport = {
      batchNumber: 3,
      category: '特征类词汇',
      wordsCount: uniqueWords.length,
      timestamp: new Date().toISOString(),
      status: 'completed'
    }
    
    this.integrationProgress.completedBatches++
    this.integrationProgress.totalIntegrated += uniqueWords.length
    this.integrationProgress.batchReports.push(batchReport)
    
    console.log(`   ✅ 第三批集成完成，集成${uniqueWords.length}个语素`)
    
    // 生成中间报告
    this.generateIntermediateReport(3)
  }

  /**
   * 第四批：集成传统文化词汇
   */
  async integrateBatch4() {
    console.log('\n📦 第四批集成: 传统文化词汇')
    
    const batch4Words = [...this.extractedVocabulary.traditional]
    const uniqueWords = [...new Set(batch4Words)]
    console.log(`   📊 本批语素数量: ${uniqueWords.length}个`)
    
    // 分配到SUBJECTS_CONFIG和TRAITS_CONFIG
    const subjects = uniqueWords.filter(word => /师$|家$|者$|人$|客$|生$/.test(word))
    const traits = uniqueWords.filter(word => !subjects.includes(word))
    
    if (subjects.length > 0) {
      this.addToSubjectsConfig('传统文化_人物_批次4', subjects)
    }
    if (traits.length > 0) {
      this.addToTraitsConfig('传统文化_概念_批次4', traits)
    }
    
    // 验证配置文件
    this.validateConfigFile()
    
    // 记录批次报告
    const batchReport = {
      batchNumber: 4,
      category: '传统文化词汇',
      wordsCount: uniqueWords.length,
      distribution: { subjects: subjects.length, traits: traits.length },
      timestamp: new Date().toISOString(),
      status: 'completed'
    }
    
    this.integrationProgress.completedBatches++
    this.integrationProgress.totalIntegrated += uniqueWords.length
    this.integrationProgress.batchReports.push(batchReport)
    
    console.log(`   ✅ 第四批集成完成，集成${uniqueWords.length}个语素`)
    
    // 生成中间报告
    this.generateIntermediateReport(4)
  }

  /**
   * 第五批：集成流行词汇和其他类别
   */
  async integrateBatch5() {
    console.log('\n📦 第五批集成: 流行词汇和其他类别')
    
    const batch5Words = [
      ...this.extractedVocabulary.popular,
      ...this.extractedVocabulary.trend
    ]
    
    const uniqueWords = [...new Set(batch5Words)]
    console.log(`   📊 本批语素数量: ${uniqueWords.length}个`)
    
    // 分配到MODIFIERS_CONFIG和TRAITS_CONFIG
    const modifiers = uniqueWords.filter(word => /级$|度$|超|极|特|高|顶/.test(word))
    const traits = uniqueWords.filter(word => !modifiers.includes(word))
    
    if (modifiers.length > 0) {
      this.addToModifiersConfig('流行修饰_批次5', modifiers)
    }
    if (traits.length > 0) {
      this.addToTraitsConfig('流行表达_批次5', traits)
    }
    
    // 验证配置文件
    this.validateConfigFile()
    
    // 记录批次报告
    const batchReport = {
      batchNumber: 5,
      category: '流行词汇和其他类别',
      wordsCount: uniqueWords.length,
      distribution: { modifiers: modifiers.length, traits: traits.length },
      timestamp: new Date().toISOString(),
      status: 'completed'
    }
    
    this.integrationProgress.completedBatches++
    this.integrationProgress.totalIntegrated += uniqueWords.length
    this.integrationProgress.batchReports.push(batchReport)
    
    console.log(`   ✅ 第五批集成完成，集成${uniqueWords.length}个语素`)
    
    // 生成中间报告
    this.generateIntermediateReport(5)
  }

  /**
   * 添加到SUBJECTS_CONFIG
   */
  addToSubjectsConfig(categoryName, words) {
    const configPath = this.configPaths.elementLibrary
    let content = fs.readFileSync(configPath, 'utf8')
    
    const wordsArray = words.map(word => `'${word}'`).join(', ')
    const newCategoryLine = `  ${categoryName}: [${wordsArray}],\n`
    
    // 在SUBJECTS_CONFIG的最后一个配置项后添加
    content = content.replace(
      /(export const SUBJECTS_CONFIG = \{[\s\S]*?)\n\} as const/m,
      `$1,\n${newCategoryLine}} as const`
    )
    
    fs.writeFileSync(configPath, content)
  }

  /**
   * 添加到TRAITS_CONFIG
   */
  addToTraitsConfig(categoryName, words) {
    const configPath = this.configPaths.elementLibrary
    let content = fs.readFileSync(configPath, 'utf8')
    
    const wordsArray = words.map(word => `'${word}'`).join(', ')
    const newCategoryLine = `  ${categoryName}: [${wordsArray}],\n`
    
    // 在TRAITS_CONFIG的最后一个配置项后添加
    content = content.replace(
      /(export const TRAITS_CONFIG = \{[\s\S]*?)\n\} as const/m,
      `$1,\n${newCategoryLine}} as const`
    )
    
    fs.writeFileSync(configPath, content)
  }

  /**
   * 添加到MODIFIERS_CONFIG
   */
  addToModifiersConfig(categoryName, words) {
    const configPath = this.configPaths.elementLibrary
    let content = fs.readFileSync(configPath, 'utf8')
    
    const wordsArray = words.map(word => `'${word}'`).join(', ')
    const newCategoryLine = `  ${categoryName}: [${wordsArray}],\n`
    
    // 在MODIFIERS_CONFIG的最后一个配置项后添加
    content = content.replace(
      /(export const MODIFIERS_CONFIG = \{[\s\S]*?)\n\} as const/m,
      `$1,\n${newCategoryLine}} as const`
    )
    
    fs.writeFileSync(configPath, content)
  }

  /**
   * 验证配置文件语法
   */
  validateConfigFile() {
    try {
      const configPath = this.configPaths.elementLibrary
      const content = fs.readFileSync(configPath, 'utf8')
      
      // 简单的语法检查
      const openBraces = (content.match(/\{/g) || []).length
      const closeBraces = (content.match(/\}/g) || []).length
      const openBrackets = (content.match(/\[/g) || []).length
      const closeBrackets = (content.match(/\]/g) || []).length
      
      if (openBraces !== closeBraces || openBrackets !== closeBrackets) {
        throw new Error('配置文件语法错误：括号不匹配')
      }
      
      console.log('   ✅ 配置文件语法验证通过')
    } catch (error) {
      throw new Error(`配置文件验证失败: ${error.message}`)
    }
  }

  // 提取方法（与之前相同）
  extractProfessionVocabulary(content) {
    const match = content.match(/const professionCategories = \{([\s\S]*?)\n    \}/m)
    if (!match) return []
    return this.extractWordsFromCategoriesText(match[1])
  }

  extractMassiveProfessions(content) {
    const match = content.match(/const massiveProfessionWords = \[([\s\S]*?)\n    \]/m)
    if (!match) return []
    return this.extractWordsFromArrayText(match[1])
  }

  extractEmotionVocabulary(content) {
    const match = content.match(/const emotionCategories = \{([\s\S]*?)\n    \}/m)
    if (!match) return []
    return this.extractWordsFromCategoriesText(match[1])
  }

  extractMassiveEmotions(content) {
    const match = content.match(/const massiveEmotionWords = \[([\s\S]*?)\n    \]/m)
    if (!match) return []
    return this.extractWordsFromArrayText(match[1])
  }

  extractCharacteristicVocabulary(content) {
    const match = content.match(/const characteristicCategories = \{([\s\S]*?)\n    \}/m)
    if (!match) return []
    return this.extractWordsFromCategoriesText(match[1])
  }

  extractMassiveCharacteristics(content) {
    const match = content.match(/const massiveCharacteristicWords = \[([\s\S]*?)\n    \]/m)
    if (!match) return []
    return this.extractWordsFromArrayText(match[1])
  }

  extractTraditionalVocabulary(content) {
    const match = content.match(/const traditionalCategories = \{([\s\S]*?)\n    \}/m)
    if (!match) return []
    return this.extractWordsFromCategoriesText(match[1])
  }

  extractPopularVocabulary(content) {
    const match = content.match(/const popularCategories = \{([\s\S]*?)\n    \}/m)
    if (!match) return []
    return this.extractWordsFromCategoriesText(match[1])
  }

  extractTrendVocabulary(content) {
    let match = content.match(/const trendCategories = \{([\s\S]*?)\n      \]/m)
    if (!match) {
      match = content.match(/const trendCategories = \{([\s\S]*?)\n    \}/m)
    }
    if (!match) return []
    return this.extractWordsFromCategoriesText(match[1])
  }

  extractWordsFromCategoriesText(categoriesText) {
    const allWords = []
    const categoryRegex = /(\w+):\s*\[([\s\S]*?)\]/g
    let match

    while ((match = categoryRegex.exec(categoriesText)) !== null) {
      const wordsText = match[2]
      const words = this.extractWordsFromArrayText(wordsText)
      allWords.push(...words)
    }

    return allWords
  }

  extractWordsFromArrayText(arrayText) {
    const words = []
    const wordRegex = /'([^']+)'/g
    let match

    while ((match = wordRegex.exec(arrayText)) !== null) {
      const word = match[1].trim()
      if (word && !words.includes(word)) {
        words.push(word)
      }
    }

    return words
  }

  /**
   * 生成中间报告
   */
  generateIntermediateReport(batchNumber) {
    const report = {
      batchNumber,
      timestamp: new Date().toISOString(),
      progress: {
        completedBatches: this.integrationProgress.completedBatches,
        totalBatches: this.integrationProgress.totalBatches,
        progressPercentage: (this.integrationProgress.completedBatches / this.integrationProgress.totalBatches * 100).toFixed(1)
      },
      totalIntegrated: this.integrationProgress.totalIntegrated,
      currentTotal: 636 + this.integrationProgress.totalIntegrated,
      targetCompletion: ((636 + this.integrationProgress.totalIntegrated) / 3000 * 100).toFixed(1) + '%'
    }

    const reportPath = path.join(__dirname, '..', `batch-${batchNumber}-report.json`)
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2))

    console.log(`   📄 第${batchNumber}批中间报告已保存`)
    console.log(`   📊 当前进度: ${report.progress.progressPercentage}% (${report.progress.completedBatches}/${report.progress.totalBatches})`)
    console.log(`   🎯 目标完成度: ${report.targetCompletion}`)
  }

  /**
   * 生成最终报告
   */
  generateFinalReport() {
    const finalReport = {
      timestamp: new Date().toISOString(),
      integration_type: 'batch_integration',
      total_batches: this.integrationProgress.totalBatches,
      completed_batches: this.integrationProgress.completedBatches,
      total_integrated: this.integrationProgress.totalIntegrated,
      integration_rate: '100%',
      current_total: 636 + this.integrationProgress.totalIntegrated,
      target_completion: ((636 + this.integrationProgress.totalIntegrated) / 3000 * 100).toFixed(1) + '%',
      batch_reports: this.integrationProgress.batchReports,
      backup_files: this.backupPaths
    }

    const reportPath = path.join(__dirname, '..', 'batch-integration-final-report.json')
    fs.writeFileSync(reportPath, JSON.stringify(finalReport, null, 2))

    return finalReport
  }
}

/**
 * 主函数
 */
async function main() {
  try {
    const manager = new BatchIntegrationManager()
    const report = await manager.executeBatchIntegration()
    
    console.log('\n📄 分批集成最终报告已保存')
    console.log(`📊 集成前语素: 636个`)
    console.log(`📊 新增语素: ${report.total_integrated}个`)
    console.log(`📊 集成后总计: ${report.current_total}个`)
    console.log(`🎯 目标完成度: ${report.target_completion}`)
    
  } catch (error) {
    console.error('❌ 分批集成失败:', error)
    process.exit(1)
  }
}

// 执行分批集成
if (require.main === module) {
  main()
}

module.exports = { BatchIntegrationManager }
