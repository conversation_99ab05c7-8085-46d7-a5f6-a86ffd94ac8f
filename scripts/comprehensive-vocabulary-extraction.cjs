/**
 * 词汇扩展引擎全面语素提取脚本
 * 深度分析vocabulary-expansion-engine.ts，提取所有语素并进行分类整理
 */

const fs = require('fs')
const path = require('path')

/**
 * 从词汇扩展引擎文件中提取所有语素
 */
function extractAllVocabularyFromEngine() {
  const engineFilePath = path.join(__dirname, '..', 'server', 'api', 'vocabulary', 'vocabulary-expansion-engine.ts')
  
  if (!fs.existsSync(engineFilePath)) {
    console.error('❌ 词汇扩展引擎文件不存在:', engineFilePath)
    return null
  }

  console.log('🔍 开始深度分析词汇扩展引擎...')
  const fileContent = fs.readFileSync(engineFilePath, 'utf8')
  
  const extractedVocabulary = {
    emotions: extractEmotionVocabulary(fileContent),
    professions: extractProfessionVocabulary(fileContent),
    characteristics: extractCharacteristicVocabulary(fileContent),
    traditional: extractTraditionalVocabulary(fileContent),
    popular: extractPopularVocabulary(fileContent),
    massiveEmotions: extractMassiveEmotions(fileContent),
    massiveProfessions: extractMassiveProfessions(fileContent),
    massiveCharacteristics: extractMassiveCharacteristics(fileContent),
    trend: extractTrendVocabulary(fileContent)
  }

  return extractedVocabulary
}

/**
 * 提取情感词汇
 */
function extractEmotionVocabulary(content) {
  const match = content.match(/const emotionCategories = \{([\s\S]*?)\n    \}/m)
  if (!match) return { categories: {}, totalCount: 0, allWords: [] }
  
  return extractWordsFromCategoriesText(match[1], 'emotions')
}

/**
 * 提取职业词汇
 */
function extractProfessionVocabulary(content) {
  const match = content.match(/const professionCategories = \{([\s\S]*?)\n    \}/m)
  if (!match) return { categories: {}, totalCount: 0, allWords: [] }
  
  return extractWordsFromCategoriesText(match[1], 'professions')
}

/**
 * 提取特征词汇
 */
function extractCharacteristicVocabulary(content) {
  const match = content.match(/const characteristicCategories = \{([\s\S]*?)\n    \}/m)
  if (!match) return { categories: {}, totalCount: 0, allWords: [] }
  
  return extractWordsFromCategoriesText(match[1], 'characteristics')
}

/**
 * 提取传统文化词汇
 */
function extractTraditionalVocabulary(content) {
  const match = content.match(/const traditionalCategories = \{([\s\S]*?)\n    \}/m)
  if (!match) return { categories: {}, totalCount: 0, allWords: [] }
  
  return extractWordsFromCategoriesText(match[1], 'traditional')
}

/**
 * 提取流行词汇
 */
function extractPopularVocabulary(content) {
  const match = content.match(/const popularCategories = \{([\s\S]*?)\n    \}/m)
  if (!match) return { categories: {}, totalCount: 0, allWords: [] }
  
  return extractWordsFromCategoriesText(match[1], 'popular')
}

/**
 * 提取大规模情感词汇
 */
function extractMassiveEmotions(content) {
  const match = content.match(/const massiveEmotionWords = \[([\s\S]*?)\n    \]/m)
  if (!match) return { categories: {}, totalCount: 0, allWords: [] }
  
  const words = extractWordsFromArrayText(match[1])
  return {
    categories: { massive_emotions: words },
    totalCount: words.length,
    allWords: words
  }
}

/**
 * 提取大规模职业词汇
 */
function extractMassiveProfessions(content) {
  const match = content.match(/const massiveProfessionWords = \[([\s\S]*?)\n    \]/m)
  if (!match) return { categories: {}, totalCount: 0, allWords: [] }
  
  const words = extractWordsFromArrayText(match[1])
  return {
    categories: { massive_professions: words },
    totalCount: words.length,
    allWords: words
  }
}

/**
 * 提取大规模特征词汇
 */
function extractMassiveCharacteristics(content) {
  const match = content.match(/const massiveCharacteristicWords = \[([\s\S]*?)\n    \]/m)
  if (!match) return { categories: {}, totalCount: 0, allWords: [] }
  
  const words = extractWordsFromArrayText(match[1])
  return {
    categories: { massive_characteristics: words },
    totalCount: words.length,
    allWords: words
  }
}

/**
 * 提取时代潮流词汇
 */
function extractTrendVocabulary(content) {
  // 尝试多种匹配模式
  let match = content.match(/const trendCategories = \{([\s\S]*?)\n      \]/m)
  if (!match) {
    match = content.match(/const trendCategories = \{([\s\S]*?)\n    \}/m)
  }
  if (!match) return { categories: {}, totalCount: 0, allWords: [] }
  
  return extractWordsFromCategoriesText(match[1], 'trend')
}

/**
 * 从类别文本中提取词汇
 */
function extractWordsFromCategoriesText(categoriesText, categoryType) {
  const categories = {}
  let totalCount = 0
  const allWords = []

  // 匹配每个类别的词汇数组
  const categoryRegex = /(\w+):\s*\[([\s\S]*?)\]/g
  let match

  while ((match = categoryRegex.exec(categoriesText)) !== null) {
    const categoryName = match[1]
    const wordsText = match[2]
    const words = extractWordsFromArrayText(wordsText)
    
    if (words.length > 0) {
      categories[categoryName] = words
      totalCount += words.length
      allWords.push(...words)
    }
  }

  console.log(`   ✅ ${categoryType}: ${totalCount}个词汇，${Object.keys(categories).length}个子类别`)
  
  return {
    categories,
    totalCount,
    allWords
  }
}

/**
 * 从数组文本中提取词汇
 */
function extractWordsFromArrayText(arrayText) {
  const words = []
  
  // 匹配所有单引号包围的词汇
  const wordRegex = /'([^']+)'/g
  let match

  while ((match = wordRegex.exec(arrayText)) !== null) {
    const word = match[1].trim()
    if (word && !words.includes(word)) {
      words.push(word)
    }
  }

  return words
}

/**
 * 分析提取的词汇数据
 */
function analyzeExtractedVocabulary(extractedVocabulary) {
  console.log('\n📊 词汇扩展引擎全面分析结果:')
  console.log('=' .repeat(60))
  
  let totalWords = 0
  let totalCategories = 0
  const analysis = {}

  Object.keys(extractedVocabulary).forEach(collectionName => {
    const collection = extractedVocabulary[collectionName]
    const categoryCount = Object.keys(collection.categories).length
    
    analysis[collectionName] = {
      words: collection.totalCount,
      categories: categoryCount,
      percentage: 0, // 稍后计算
      categories_detail: collection.categories
    }
    
    totalWords += collection.totalCount
    totalCategories += categoryCount
    
    console.log(`\n📚 ${collectionName}:`)
    console.log(`   词汇总数: ${collection.totalCount}个`)
    console.log(`   子类别: ${categoryCount}个`)
    
    Object.keys(collection.categories).forEach(subcat => {
      const count = collection.categories[subcat].length
      console.log(`     - ${subcat}: ${count}个`)
    })
  })

  // 计算百分比
  Object.keys(analysis).forEach(key => {
    analysis[key].percentage = ((analysis[key].words / totalWords) * 100).toFixed(1) + '%'
  })

  console.log(`\n📈 总体统计:`)
  console.log(`   总词汇数量: ${totalWords}个`)
  console.log(`   总类别数量: ${totalCategories}个`)
  console.log(`   主要集合: ${Object.keys(extractedVocabulary).length}个`)

  return {
    totalWords,
    totalCategories,
    collections: Object.keys(extractedVocabulary).length,
    analysis,
    extractedVocabulary
  }
}

/**
 * 生成集成映射方案
 */
function generateIntegrationMapping(analysisResult) {
  console.log('\n🔧 生成V5引擎集成映射方案...')
  
  const mapping = {
    subjects_extensions: {
      // 职业词汇 → subjects
      现代职业扩展: [],
      创意职业: [],
      新兴职业: [],
      服务职业: [],
      传统职业扩展: [],
      // 传统文化 → subjects
      文人雅士: [],
      古典意象: []
    },
    traits_extensions: {
      // 情感词汇 → traits
      基础情感: [],
      积极情感: [],
      深层情感: [],
      文艺情感: [],
      现代情感: [],
      // 特征词汇 → traits
      性格特征: [],
      品质特征: [],
      风格特征: [],
      状态特征: [],
      // 传统文化 → traits
      传统美德: [],
      文化概念: []
    },
    modifiers_extensions: {
      // 特征词汇 → modifiers
      能力特征: [],
      专业级别: [],
      程度描述: [],
      // 流行词汇 → modifiers
      现代修饰: [],
      网络流行: []
    },
    new_categories: {
      // 新增类别
      传统文化主体: [],
      流行文化元素: [],
      高级扩展词汇: []
    }
  }

  // 填充映射数据
  const { extractedVocabulary } = analysisResult

  // 职业词汇映射
  if (extractedVocabulary.professions.categories.modern_professions) {
    mapping.subjects_extensions.现代职业扩展 = extractedVocabulary.professions.categories.modern_professions.slice(0, 20)
  }
  if (extractedVocabulary.professions.categories.creative_professions) {
    mapping.subjects_extensions.创意职业 = extractedVocabulary.professions.categories.creative_professions.slice(0, 20)
  }
  if (extractedVocabulary.professions.categories.emerging_professions) {
    mapping.subjects_extensions.新兴职业 = extractedVocabulary.professions.categories.emerging_professions.slice(0, 20)
  }

  // 情感词汇映射
  if (extractedVocabulary.emotions.categories.deep_emotions) {
    mapping.traits_extensions.深层情感 = extractedVocabulary.emotions.categories.deep_emotions
  }
  if (extractedVocabulary.emotions.categories.artistic_emotions) {
    mapping.traits_extensions.文艺情感 = extractedVocabulary.emotions.categories.artistic_emotions
  }
  if (extractedVocabulary.emotions.categories.modern_emotions) {
    mapping.traits_extensions.现代情感 = extractedVocabulary.emotions.categories.modern_emotions
  }

  // 特征词汇映射
  if (extractedVocabulary.characteristics.categories.ability_traits) {
    mapping.modifiers_extensions.能力特征 = extractedVocabulary.characteristics.categories.ability_traits
  }
  if (extractedVocabulary.characteristics.categories.quality_traits) {
    mapping.traits_extensions.品质特征 = extractedVocabulary.characteristics.categories.quality_traits
  }

  // 传统文化词汇映射
  if (extractedVocabulary.traditional.categories.scholar_titles) {
    mapping.subjects_extensions.文人雅士 = extractedVocabulary.traditional.categories.scholar_titles.slice(0, 30)
  }
  if (extractedVocabulary.traditional.categories.traditional_virtues) {
    mapping.traits_extensions.传统美德 = extractedVocabulary.traditional.categories.traditional_virtues.slice(0, 25)
  }

  return mapping
}

/**
 * 主函数
 */
function main() {
  try {
    console.log('🚀 开始词汇扩展引擎全面语素提取...')
    
    // 提取所有词汇
    const extractedVocabulary = extractAllVocabularyFromEngine()
    if (!extractedVocabulary) {
      console.error('❌ 词汇提取失败')
      return
    }

    // 分析词汇数据
    const analysisResult = analyzeExtractedVocabulary(extractedVocabulary)
    
    // 生成集成映射
    const integrationMapping = generateIntegrationMapping(analysisResult)
    
    // 保存完整分析结果
    const fullReport = {
      timestamp: new Date().toISOString(),
      summary: {
        totalWords: analysisResult.totalWords,
        totalCategories: analysisResult.totalCategories,
        collections: analysisResult.collections
      },
      detailed_analysis: analysisResult.analysis,
      extracted_vocabulary: extractedVocabulary,
      integration_mapping: integrationMapping
    }

    const reportPath = path.join(__dirname, '..', 'comprehensive-vocabulary-extraction-report.json')
    fs.writeFileSync(reportPath, JSON.stringify(fullReport, null, 2))

    console.log(`\n✅ 全面分析完成！`)
    console.log(`📄 详细报告已保存到: ${reportPath}`)
    console.log(`📊 总词汇量: ${analysisResult.totalWords}个`)
    console.log(`🏷️ 总类别: ${analysisResult.totalCategories}个`)
    
    return fullReport
    
  } catch (error) {
    console.error('❌ 分析过程中发生错误:', error)
    process.exit(1)
  }
}

// 执行分析
if (require.main === module) {
  main()
}

module.exports = { 
  extractAllVocabularyFromEngine, 
  analyzeExtractedVocabulary,
  generateIntegrationMapping
}
