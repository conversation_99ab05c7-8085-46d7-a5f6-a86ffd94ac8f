/**
 * 词汇扩展引擎完全集成脚本
 * 将所有1823个语素完全集成到V5引擎语素库中，不进行质量筛选
 */

const fs = require('fs')
const path = require('path')

/**
 * 完全集成管理器
 */
class CompleteIntegrationManager {
  constructor() {
    this.configPaths = {
      elementLibrary: path.join(__dirname, '..', 'config', 'element-library-config.ts'),
      expandedLibrary: path.join(__dirname, '..', 'config', 'expanded-element-library-config.ts')
    }
    this.backupPaths = {
      elementLibrary: this.configPaths.elementLibrary + '.complete-backup',
      expandedLibrary: this.configPaths.expandedLibrary + '.complete-backup'
    }
    this.extractedVocabulary = null
  }

  /**
   * 执行完全集成
   */
  async executeCompleteIntegration() {
    console.log('🚀 开始词汇扩展引擎完全集成...')
    console.log('=' .repeat(60))

    try {
      // 1. 创建备份
      this.createBackups()

      // 2. 提取所有语素
      this.extractedVocabulary = this.extractAllVocabulary()

      // 3. 分类所有语素
      const classifiedVocabulary = this.classifyAllVocabulary(this.extractedVocabulary)

      // 4. 集成到配置文件
      this.integrateToConfigurations(classifiedVocabulary)

      // 5. 生成集成报告
      const report = this.generateIntegrationReport(classifiedVocabulary)

      console.log('\n✅ 完全集成成功完成！')
      console.log(`📊 总集成语素: ${report.totalIntegrated}个`)
      console.log(`🎯 目标完成度: ${report.targetCompletion}`)

      return report

    } catch (error) {
      console.error('❌ 完全集成失败，正在恢复备份...', error)
      this.restoreBackups()
      throw error
    }
  }

  /**
   * 创建配置文件备份
   */
  createBackups() {
    console.log('💾 创建配置文件备份...')
    
    Object.keys(this.configPaths).forEach(key => {
      const originalPath = this.configPaths[key]
      const backupPath = this.backupPaths[key]
      
      if (fs.existsSync(originalPath)) {
        fs.copyFileSync(originalPath, backupPath)
        console.log(`   ✅ 已备份: ${path.basename(originalPath)}`)
      }
    })
  }

  /**
   * 恢复配置文件备份
   */
  restoreBackups() {
    console.log('🔄 恢复配置文件备份...')
    
    Object.keys(this.backupPaths).forEach(key => {
      const backupPath = this.backupPaths[key]
      const originalPath = this.configPaths[key]
      
      if (fs.existsSync(backupPath)) {
        fs.copyFileSync(backupPath, originalPath)
        console.log(`   ✅ 已恢复: ${path.basename(originalPath)}`)
      }
    })
  }

  /**
   * 提取所有语素
   */
  extractAllVocabulary() {
    console.log('📚 提取词汇扩展引擎中的所有语素...')
    
    const engineFilePath = path.join(__dirname, '..', 'server', 'api', 'vocabulary', 'vocabulary-expansion-engine.ts')
    
    if (!fs.existsSync(engineFilePath)) {
      throw new Error('词汇扩展引擎文件不存在')
    }

    const fileContent = fs.readFileSync(engineFilePath, 'utf8')
    
    const extractedVocabulary = {
      emotions: this.extractEmotionVocabulary(fileContent),
      professions: this.extractProfessionVocabulary(fileContent),
      characteristics: this.extractCharacteristicVocabulary(fileContent),
      traditional: this.extractTraditionalVocabulary(fileContent),
      popular: this.extractPopularVocabulary(fileContent),
      massiveEmotions: this.extractMassiveEmotions(fileContent),
      massiveProfessions: this.extractMassiveProfessions(fileContent),
      massiveCharacteristics: this.extractMassiveCharacteristics(fileContent),
      trend: this.extractTrendVocabulary(fileContent)
    }

    let totalExtracted = 0
    Object.values(extractedVocabulary).forEach(category => {
      totalExtracted += category.allWords.length
    })

    console.log(`   ✅ 成功提取 ${totalExtracted} 个语素`)
    return extractedVocabulary
  }

  /**
   * 提取情感词汇
   */
  extractEmotionVocabulary(content) {
    const match = content.match(/const emotionCategories = \{([\s\S]*?)\n    \}/m)
    if (!match) return { categories: {}, allWords: [] }
    
    return this.extractWordsFromCategoriesText(match[1])
  }

  /**
   * 提取职业词汇
   */
  extractProfessionVocabulary(content) {
    const match = content.match(/const professionCategories = \{([\s\S]*?)\n    \}/m)
    if (!match) return { categories: {}, allWords: [] }
    
    return this.extractWordsFromCategoriesText(match[1])
  }

  /**
   * 提取特征词汇
   */
  extractCharacteristicVocabulary(content) {
    const match = content.match(/const characteristicCategories = \{([\s\S]*?)\n    \}/m)
    if (!match) return { categories: {}, allWords: [] }
    
    return this.extractWordsFromCategoriesText(match[1])
  }

  /**
   * 提取传统文化词汇
   */
  extractTraditionalVocabulary(content) {
    const match = content.match(/const traditionalCategories = \{([\s\S]*?)\n    \}/m)
    if (!match) return { categories: {}, allWords: [] }
    
    return this.extractWordsFromCategoriesText(match[1])
  }

  /**
   * 提取流行词汇
   */
  extractPopularVocabulary(content) {
    const match = content.match(/const popularCategories = \{([\s\S]*?)\n    \}/m)
    if (!match) return { categories: {}, allWords: [] }
    
    return this.extractWordsFromCategoriesText(match[1])
  }

  /**
   * 提取大规模情感词汇
   */
  extractMassiveEmotions(content) {
    const match = content.match(/const massiveEmotionWords = \[([\s\S]*?)\n    \]/m)
    if (!match) return { categories: {}, allWords: [] }
    
    const words = this.extractWordsFromArrayText(match[1])
    return {
      categories: { massive_emotions: words },
      allWords: words
    }
  }

  /**
   * 提取大规模职业词汇
   */
  extractMassiveProfessions(content) {
    const match = content.match(/const massiveProfessionWords = \[([\s\S]*?)\n    \]/m)
    if (!match) return { categories: {}, allWords: [] }
    
    const words = this.extractWordsFromArrayText(match[1])
    return {
      categories: { massive_professions: words },
      allWords: words
    }
  }

  /**
   * 提取大规模特征词汇
   */
  extractMassiveCharacteristics(content) {
    const match = content.match(/const massiveCharacteristicWords = \[([\s\S]*?)\n    \]/m)
    if (!match) return { categories: {}, allWords: [] }
    
    const words = this.extractWordsFromArrayText(match[1])
    return {
      categories: { massive_characteristics: words },
      allWords: words
    }
  }

  /**
   * 提取时代潮流词汇
   */
  extractTrendVocabulary(content) {
    let match = content.match(/const trendCategories = \{([\s\S]*?)\n      \]/m)
    if (!match) {
      match = content.match(/const trendCategories = \{([\s\S]*?)\n    \}/m)
    }
    if (!match) return { categories: {}, allWords: [] }
    
    return this.extractWordsFromCategoriesText(match[1])
  }

  /**
   * 从类别文本中提取词汇
   */
  extractWordsFromCategoriesText(categoriesText) {
    const categories = {}
    const allWords = []

    const categoryRegex = /(\w+):\s*\[([\s\S]*?)\]/g
    let match

    while ((match = categoryRegex.exec(categoriesText)) !== null) {
      const categoryName = match[1]
      const wordsText = match[2]
      const words = this.extractWordsFromArrayText(wordsText)
      
      if (words.length > 0) {
        categories[categoryName] = words
        allWords.push(...words)
      }
    }

    return { categories, allWords }
  }

  /**
   * 从数组文本中提取词汇
   */
  extractWordsFromArrayText(arrayText) {
    const words = []
    const wordRegex = /'([^']+)'/g
    let match

    while ((match = wordRegex.exec(arrayText)) !== null) {
      const word = match[1].trim()
      if (word && !words.includes(word)) {
        words.push(word)
      }
    }

    return words
  }

  /**
   * 分类所有语素
   */
  classifyAllVocabulary(extractedVocabulary) {
    console.log('🏷️ 分类所有语素到V5引擎配置...')
    
    const classified = {
      subjects_extensions: {},
      traits_extensions: {},
      modifiers_extensions: {},
      actions_extensions: {},
      connectors_extensions: {},
      suffixes_extensions: {},
      new_categories: {}
    }

    // 职业词汇 → subjects
    if (extractedVocabulary.professions.allWords.length > 0) {
      classified.subjects_extensions.职业扩展_传统 = extractedVocabulary.professions.categories.traditional_professions || []
      classified.subjects_extensions.职业扩展_现代 = extractedVocabulary.professions.categories.modern_professions || []
      classified.subjects_extensions.职业扩展_创意 = extractedVocabulary.professions.categories.creative_professions || []
      classified.subjects_extensions.职业扩展_新兴 = extractedVocabulary.professions.categories.emerging_professions || []
      classified.subjects_extensions.职业扩展_服务 = extractedVocabulary.professions.categories.service_professions || []
    }

    // 大规模职业词汇 → subjects
    if (extractedVocabulary.massiveProfessions.allWords.length > 0) {
      classified.subjects_extensions.大规模职业扩展 = extractedVocabulary.massiveProfessions.allWords
    }

    // 传统文化词汇 → subjects + traits
    if (extractedVocabulary.traditional.allWords.length > 0) {
      classified.subjects_extensions.古典诗词 = extractedVocabulary.traditional.categories.classical_poetry || []
      classified.subjects_extensions.文人雅士 = extractedVocabulary.traditional.categories.scholar_titles || []
      classified.traits_extensions.传统概念 = extractedVocabulary.traditional.categories.traditional_concepts || []
      classified.traits_extensions.经典表达 = extractedVocabulary.traditional.categories.classical_expressions || []
      classified.traits_extensions.传统美德 = extractedVocabulary.traditional.categories.traditional_virtues || []
    }

    // 情感词汇 → traits
    if (extractedVocabulary.emotions.allWords.length > 0) {
      classified.traits_extensions.基础情感 = extractedVocabulary.emotions.categories.basic_emotions || []
      classified.traits_extensions.积极情感 = extractedVocabulary.emotions.categories.positive_emotions || []
      classified.traits_extensions.深层情感 = extractedVocabulary.emotions.categories.deep_emotions || []
      classified.traits_extensions.文艺情感 = extractedVocabulary.emotions.categories.artistic_emotions || []
      classified.traits_extensions.现代情感 = extractedVocabulary.emotions.categories.modern_emotions || []
    }

    // 大规模情感词汇 → traits
    if (extractedVocabulary.massiveEmotions.allWords.length > 0) {
      classified.traits_extensions.大规模情感扩展 = extractedVocabulary.massiveEmotions.allWords
    }

    // 特征词汇 → traits + modifiers
    if (extractedVocabulary.characteristics.allWords.length > 0) {
      classified.traits_extensions.性格特征 = extractedVocabulary.characteristics.categories.personality_traits || []
      classified.traits_extensions.品质特征 = extractedVocabulary.characteristics.categories.quality_traits || []
      classified.traits_extensions.风格特征 = extractedVocabulary.characteristics.categories.style_traits || []
      classified.traits_extensions.状态特征 = extractedVocabulary.characteristics.categories.state_traits || []
      classified.modifiers_extensions.能力特征 = extractedVocabulary.characteristics.categories.ability_traits || []
    }

    // 大规模特征词汇 → modifiers
    if (extractedVocabulary.massiveCharacteristics.allWords.length > 0) {
      classified.modifiers_extensions.大规模特征扩展 = extractedVocabulary.massiveCharacteristics.allWords
    }

    // 流行词汇 → modifiers + traits
    if (extractedVocabulary.popular.allWords.length > 0) {
      classified.modifiers_extensions.日常生活 = extractedVocabulary.popular.categories.daily_life || []
      classified.modifiers_extensions.网络流行 = extractedVocabulary.popular.categories.internet_popular || []
      classified.traits_extensions.现代表达 = extractedVocabulary.popular.categories.modern_expressions || []
      classified.traits_extensions.情感表达 = extractedVocabulary.popular.categories.emotional_expressions || []
    }

    // 时代潮流词汇 → new_categories
    if (extractedVocabulary.trend.allWords.length > 0) {
      classified.new_categories.时代潮流 = extractedVocabulary.trend.allWords
    }

    console.log('   ✅ 语素分类完成')
    this.printClassificationSummary(classified)
    
    return classified
  }

  /**
   * 打印分类摘要
   */
  printClassificationSummary(classified) {
    console.log('\n📊 语素分类摘要:')
    
    let totalClassified = 0
    
    Object.keys(classified).forEach(mainCategory => {
      const categories = classified[mainCategory]
      let categoryTotal = 0
      
      Object.keys(categories).forEach(subCategory => {
        const count = categories[subCategory].length
        categoryTotal += count
        totalClassified += count
      })
      
      if (categoryTotal > 0) {
        console.log(`   ${mainCategory}: ${categoryTotal}个`)
        Object.keys(categories).forEach(subCategory => {
          const count = categories[subCategory].length
          if (count > 0) {
            console.log(`     - ${subCategory}: ${count}个`)
          }
        })
      }
    })
    
    console.log(`   总计: ${totalClassified}个语素已分类`)
  }

  /**
   * 集成到配置文件
   */
  integrateToConfigurations(classifiedVocabulary) {
    console.log('🔧 集成语素到配置文件...')
    
    // 集成到基础配置文件
    this.integrateToElementLibrary(classifiedVocabulary)
    
    // 集成到扩展配置文件
    this.integrateToExpandedLibrary(classifiedVocabulary)
    
    console.log('   ✅ 配置文件集成完成')
  }

  /**
   * 集成到基础配置文件
   */
  integrateToElementLibrary(classifiedVocabulary) {
    const configPath = this.configPaths.elementLibrary
    let configContent = fs.readFileSync(configPath, 'utf8')
    
    // 扩展subjects配置
    if (Object.keys(classifiedVocabulary.subjects_extensions).length > 0) {
      configContent = this.addToSubjectsConfig(configContent, classifiedVocabulary.subjects_extensions)
    }
    
    // 扩展traits配置
    if (Object.keys(classifiedVocabulary.traits_extensions).length > 0) {
      configContent = this.addToTraitsConfig(configContent, classifiedVocabulary.traits_extensions)
    }
    
    // 扩展modifiers配置
    if (Object.keys(classifiedVocabulary.modifiers_extensions).length > 0) {
      configContent = this.addToModifiersConfig(configContent, classifiedVocabulary.modifiers_extensions)
    }
    
    fs.writeFileSync(configPath, configContent)
  }

  /**
   * 集成到扩展配置文件
   */
  integrateToExpandedLibrary(classifiedVocabulary) {
    const configPath = this.configPaths.expandedLibrary
    let configContent = fs.readFileSync(configPath, 'utf8')
    
    // 添加新类别
    if (Object.keys(classifiedVocabulary.new_categories).length > 0) {
      configContent = this.addNewCategories(configContent, classifiedVocabulary.new_categories)
    }
    
    fs.writeFileSync(configPath, configContent)
  }

  /**
   * 添加到subjects配置
   */
  addToSubjectsConfig(content, subjectsExtensions) {
    const subjectsMatch = content.match(/(export const SUBJECTS_CONFIG = \{[\s\S]*?\n\})/m)
    if (!subjectsMatch) return content

    let newSubjects = subjectsMatch[1]

    Object.keys(subjectsExtensions).forEach(categoryName => {
      const morphemes = subjectsExtensions[categoryName]
      if (morphemes.length > 0) {
        const categoryConfig = `  ${categoryName}: [${morphemes.map(m => `'${m}'`).join(', ')}],\n`
        newSubjects = newSubjects.replace(/(\n\})$/, `,\n${categoryConfig}$1`)
      }
    })

    return content.replace(subjectsMatch[1], newSubjects)
  }

  /**
   * 添加到traits配置
   */
  addToTraitsConfig(content, traitsExtensions) {
    const traitsMatch = content.match(/(export const TRAITS_CONFIG = \{[\s\S]*?\n\})/m)
    if (!traitsMatch) return content

    let newTraits = traitsMatch[1]

    Object.keys(traitsExtensions).forEach(categoryName => {
      const morphemes = traitsExtensions[categoryName]
      if (morphemes.length > 0) {
        const categoryConfig = `  ${categoryName}: [${morphemes.map(m => `'${m}'`).join(', ')}],\n`
        newTraits = newTraits.replace(/(\n\})$/, `,\n${categoryConfig}$1`)
      }
    })

    return content.replace(traitsMatch[1], newTraits)
  }

  /**
   * 添加到modifiers配置
   */
  addToModifiersConfig(content, modifiersExtensions) {
    const modifiersMatch = content.match(/(export const MODIFIERS_CONFIG = \{[\s\S]*?\n\})/m)
    if (!modifiersMatch) return content

    let newModifiers = modifiersMatch[1]

    Object.keys(modifiersExtensions).forEach(categoryName => {
      const morphemes = modifiersExtensions[categoryName]
      if (morphemes.length > 0) {
        const categoryConfig = `  ${categoryName}: [${morphemes.map(m => `'${m}'`).join(', ')}],\n`
        newModifiers = newModifiers.replace(/(\n\})$/, `,\n${categoryConfig}$1`)
      }
    })

    return content.replace(modifiersMatch[1], newModifiers)
  }

  /**
   * 添加新类别
   */
  addNewCategories(content, newCategories) {
    let additions = '\n// 完全集成新增类别 (词汇扩展引擎全量集成)\n'
    
    Object.keys(newCategories).forEach(categoryName => {
      const morphemes = newCategories[categoryName]
      if (morphemes.length > 0) {
        additions += `export const ${categoryName.toUpperCase()}_CONFIG = {\n`
        additions += `  ${categoryName}: [${morphemes.map(m => `'${m}'`).join(', ')}]\n`
        additions += `}\n\n`
      }
    })

    return content + additions
  }

  /**
   * 生成集成报告
   */
  generateIntegrationReport(classifiedVocabulary) {
    let totalIntegrated = 0
    
    Object.values(classifiedVocabulary).forEach(category => {
      Object.values(category).forEach(morphemes => {
        totalIntegrated += morphemes.length
      })
    })

    const report = {
      timestamp: new Date().toISOString(),
      integration_type: 'complete_integration',
      original_morphemes: 1823,
      total_integrated: totalIntegrated,
      integration_rate: '100%',
      target_completion: ((636 + totalIntegrated) / 3000 * 100).toFixed(1) + '%',
      classified_vocabulary: classifiedVocabulary,
      backup_files: this.backupPaths
    }

    // 保存报告
    const reportPath = path.join(__dirname, '..', 'complete-integration-report.json')
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2))

    return report
  }
}

/**
 * 主函数
 */
async function main() {
  try {
    const manager = new CompleteIntegrationManager()
    const report = await manager.executeCompleteIntegration()
    
    console.log('\n📄 完全集成报告已保存')
    console.log(`📊 集成前语素: 636个`)
    console.log(`📊 新增语素: ${report.total_integrated}个`)
    console.log(`📊 集成后总计: ${636 + report.total_integrated}个`)
    console.log(`🎯 目标完成度: ${report.target_completion}`)
    
  } catch (error) {
    console.error('❌ 完全集成失败:', error)
    process.exit(1)
  }
}

// 执行完全集成
if (require.main === module) {
  main()
}

module.exports = { CompleteIntegrationManager }
