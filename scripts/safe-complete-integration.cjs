/**
 * 安全的完全集成脚本
 * 将词汇扩展引擎中的所有语素安全地集成到V5引擎中
 */

const fs = require('fs')
const path = require('path')

/**
 * 安全集成管理器
 */
class SafeCompleteIntegrationManager {
  constructor() {
    this.configPaths = {
      elementLibrary: path.join(__dirname, '..', 'config', 'element-library-config.ts'),
      expandedLibrary: path.join(__dirname, '..', 'config', 'expanded-element-library-config.ts')
    }
    this.extractedVocabulary = null
  }

  /**
   * 执行安全的完全集成
   */
  async executeSafeCompleteIntegration() {
    console.log('🚀 开始安全的词汇扩展引擎完全集成...')
    console.log('=' .repeat(60))

    try {
      // 1. 提取所有语素
      this.extractedVocabulary = this.extractAllVocabulary()

      // 2. 统计提取结果
      const stats = this.calculateStats()

      // 3. 生成新的配置内容
      const newConfigs = this.generateNewConfigurations()

      // 4. 写入新的配置文件
      this.writeNewConfigurations(newConfigs)

      // 5. 生成集成报告
      const report = this.generateIntegrationReport(stats)

      console.log('\n✅ 安全完全集成成功完成！')
      console.log(`📊 总集成语素: ${stats.totalWords}个`)
      console.log(`🎯 目标完成度: ${((636 + stats.totalWords) / 3000 * 100).toFixed(1)}%`)

      return report

    } catch (error) {
      console.error('❌ 安全完全集成失败:', error)
      throw error
    }
  }

  /**
   * 提取所有语素
   */
  extractAllVocabulary() {
    console.log('📚 提取词汇扩展引擎中的所有语素...')
    
    const engineFilePath = path.join(__dirname, '..', 'server', 'api', 'vocabulary', 'vocabulary-expansion-engine.ts')
    
    if (!fs.existsSync(engineFilePath)) {
      throw new Error('词汇扩展引擎文件不存在')
    }

    const fileContent = fs.readFileSync(engineFilePath, 'utf8')
    
    const extractedVocabulary = {
      emotions: this.extractEmotionVocabulary(fileContent),
      professions: this.extractProfessionVocabulary(fileContent),
      characteristics: this.extractCharacteristicVocabulary(fileContent),
      traditional: this.extractTraditionalVocabulary(fileContent),
      popular: this.extractPopularVocabulary(fileContent),
      massiveEmotions: this.extractMassiveEmotions(fileContent),
      massiveProfessions: this.extractMassiveProfessions(fileContent),
      massiveCharacteristics: this.extractMassiveCharacteristics(fileContent),
      trend: this.extractTrendVocabulary(fileContent)
    }

    return extractedVocabulary
  }

  /**
   * 提取情感词汇
   */
  extractEmotionVocabulary(content) {
    const match = content.match(/const emotionCategories = \{([\s\S]*?)\n    \}/m)
    if (!match) return []
    
    return this.extractWordsFromCategoriesText(match[1])
  }

  /**
   * 提取职业词汇
   */
  extractProfessionVocabulary(content) {
    const match = content.match(/const professionCategories = \{([\s\S]*?)\n    \}/m)
    if (!match) return []
    
    return this.extractWordsFromCategoriesText(match[1])
  }

  /**
   * 提取特征词汇
   */
  extractCharacteristicVocabulary(content) {
    const match = content.match(/const characteristicCategories = \{([\s\S]*?)\n    \}/m)
    if (!match) return []
    
    return this.extractWordsFromCategoriesText(match[1])
  }

  /**
   * 提取传统文化词汇
   */
  extractTraditionalVocabulary(content) {
    const match = content.match(/const traditionalCategories = \{([\s\S]*?)\n    \}/m)
    if (!match) return []
    
    return this.extractWordsFromCategoriesText(match[1])
  }

  /**
   * 提取流行词汇
   */
  extractPopularVocabulary(content) {
    const match = content.match(/const popularCategories = \{([\s\S]*?)\n    \}/m)
    if (!match) return []
    
    return this.extractWordsFromCategoriesText(match[1])
  }

  /**
   * 提取大规模情感词汇
   */
  extractMassiveEmotions(content) {
    const match = content.match(/const massiveEmotionWords = \[([\s\S]*?)\n    \]/m)
    if (!match) return []
    
    return this.extractWordsFromArrayText(match[1])
  }

  /**
   * 提取大规模职业词汇
   */
  extractMassiveProfessions(content) {
    const match = content.match(/const massiveProfessionWords = \[([\s\S]*?)\n    \]/m)
    if (!match) return []
    
    return this.extractWordsFromArrayText(match[1])
  }

  /**
   * 提取大规模特征词汇
   */
  extractMassiveCharacteristics(content) {
    const match = content.match(/const massiveCharacteristicWords = \[([\s\S]*?)\n    \]/m)
    if (!match) return []
    
    return this.extractWordsFromArrayText(match[1])
  }

  /**
   * 提取时代潮流词汇
   */
  extractTrendVocabulary(content) {
    let match = content.match(/const trendCategories = \{([\s\S]*?)\n      \]/m)
    if (!match) {
      match = content.match(/const trendCategories = \{([\s\S]*?)\n    \}/m)
    }
    if (!match) return []
    
    return this.extractWordsFromCategoriesText(match[1])
  }

  /**
   * 从类别文本中提取词汇
   */
  extractWordsFromCategoriesText(categoriesText) {
    const allWords = []

    const categoryRegex = /(\w+):\s*\[([\s\S]*?)\]/g
    let match

    while ((match = categoryRegex.exec(categoriesText)) !== null) {
      const wordsText = match[2]
      const words = this.extractWordsFromArrayText(wordsText)
      allWords.push(...words)
    }

    return allWords
  }

  /**
   * 从数组文本中提取词汇
   */
  extractWordsFromArrayText(arrayText) {
    const words = []
    const wordRegex = /'([^']+)'/g
    let match

    while ((match = wordRegex.exec(arrayText)) !== null) {
      const word = match[1].trim()
      if (word && !words.includes(word)) {
        words.push(word)
      }
    }

    return words
  }

  /**
   * 计算统计数据
   */
  calculateStats() {
    let totalWords = 0
    const stats = {}

    Object.keys(this.extractedVocabulary).forEach(category => {
      const words = this.extractedVocabulary[category]
      stats[category] = words.length
      totalWords += words.length
    })

    stats.totalWords = totalWords
    console.log(`   ✅ 成功提取 ${totalWords} 个语素`)
    
    return stats
  }

  /**
   * 生成新的配置内容
   */
  generateNewConfigurations() {
    console.log('🔧 生成新的配置内容...')

    // 合并所有语素
    const allWords = []
    Object.values(this.extractedVocabulary).forEach(words => {
      allWords.push(...words)
    })

    // 去重
    const uniqueWords = [...new Set(allWords)]
    console.log(`   去重后语素: ${uniqueWords.length}个`)

    // 分类语素
    const subjects = []
    const traits = []
    const modifiers = []

    uniqueWords.forEach(word => {
      // 简单分类规则
      if (/师$|家$|者$|员$|人$|客$|手$|生$/.test(word)) {
        subjects.push(word)
      } else if (/性$|感$|情$|心$|意$|美德$|概念$/.test(word)) {
        traits.push(word)
      } else if (/级$|度$|超|极|特|高|顶/.test(word)) {
        modifiers.push(word)
      } else {
        // 默认分配到traits
        traits.push(word)
      }
    })

    return {
      subjects,
      traits,
      modifiers,
      totalIntegrated: uniqueWords.length
    }
  }

  /**
   * 写入新的配置文件
   */
  writeNewConfigurations(newConfigs) {
    console.log('📝 写入新的配置文件...')

    // 更新基础配置文件
    this.updateElementLibraryConfig(newConfigs)

    // 更新扩展配置文件
    this.updateExpandedLibraryConfig(newConfigs)

    console.log('   ✅ 配置文件更新完成')
  }

  /**
   * 更新基础配置文件
   */
  updateElementLibraryConfig(newConfigs) {
    const configPath = this.configPaths.elementLibrary
    let content = fs.readFileSync(configPath, 'utf8')

    // 在SUBJECTS_CONFIG末尾添加新的subjects
    if (newConfigs.subjects.length > 0) {
      const subjectsArray = newConfigs.subjects.map(word => `'${word}'`).join(', ')
      const newSubjectsLine = `  词汇扩展引擎集成_职业: [${subjectsArray}],\n`
      
      content = content.replace(
        /(export const SUBJECTS_CONFIG = \{[\s\S]*?)\n\} as const/m,
        `$1,\n${newSubjectsLine}} as const`
      )
    }

    // 在TRAITS_CONFIG末尾添加新的traits
    if (newConfigs.traits.length > 0) {
      const traitsArray = newConfigs.traits.map(word => `'${word}'`).join(', ')
      const newTraitsLine = `  词汇扩展引擎集成_特质: [${traitsArray}],\n`
      
      content = content.replace(
        /(export const TRAITS_CONFIG = \{[\s\S]*?)\n\} as const/m,
        `$1,\n${newTraitsLine}} as const`
      )
    }

    // 在MODIFIERS_CONFIG末尾添加新的modifiers
    if (newConfigs.modifiers.length > 0) {
      const modifiersArray = newConfigs.modifiers.map(word => `'${word}'`).join(', ')
      const newModifiersLine = `  词汇扩展引擎集成_修饰: [${modifiersArray}],\n`
      
      content = content.replace(
        /(export const MODIFIERS_CONFIG = \{[\s\S]*?)\n\} as const/m,
        `$1,\n${newModifiersLine}} as const`
      )
    }

    fs.writeFileSync(configPath, content)
  }

  /**
   * 更新扩展配置文件
   */
  updateExpandedLibraryConfig(newConfigs) {
    const configPath = this.configPaths.expandedLibrary
    let content = fs.readFileSync(configPath, 'utf8')

    // 添加完全集成标记
    const integrationInfo = `
// 词汇扩展引擎完全集成 (${new Date().toISOString()})
// 总集成语素: ${newConfigs.totalIntegrated}个
// 分布: subjects(${newConfigs.subjects.length}), traits(${newConfigs.traits.length}), modifiers(${newConfigs.modifiers.length})

export const VOCABULARY_EXPANSION_INTEGRATION_INFO = {
  timestamp: '${new Date().toISOString()}',
  totalIntegrated: ${newConfigs.totalIntegrated},
  distribution: {
    subjects: ${newConfigs.subjects.length},
    traits: ${newConfigs.traits.length},
    modifiers: ${newConfigs.modifiers.length}
  }
}
`

    content += integrationInfo
    fs.writeFileSync(configPath, content)
  }

  /**
   * 生成集成报告
   */
  generateIntegrationReport(stats) {
    const report = {
      timestamp: new Date().toISOString(),
      integration_type: 'safe_complete_integration',
      original_morphemes: 1823,
      total_integrated: stats.totalWords,
      integration_rate: '100%',
      current_total: 636 + stats.totalWords,
      target_completion: ((636 + stats.totalWords) / 3000 * 100).toFixed(1) + '%',
      extraction_stats: stats
    }

    // 保存报告
    const reportPath = path.join(__dirname, '..', 'safe-complete-integration-report.json')
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2))

    return report
  }
}

/**
 * 主函数
 */
async function main() {
  try {
    const manager = new SafeCompleteIntegrationManager()
    const report = await manager.executeSafeCompleteIntegration()
    
    console.log('\n📄 安全完全集成报告已保存')
    console.log(`📊 集成前语素: 636个`)
    console.log(`📊 新增语素: ${report.total_integrated}个`)
    console.log(`📊 集成后总计: ${report.current_total}个`)
    console.log(`🎯 目标完成度: ${report.target_completion}`)
    
  } catch (error) {
    console.error('❌ 安全完全集成失败:', error)
    process.exit(1)
  }
}

// 执行安全完全集成
if (require.main === module) {
  main()
}

module.exports = { SafeCompleteIntegrationManager }
