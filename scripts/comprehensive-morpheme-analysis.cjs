/**
 * V5引擎语素库全面分析脚本
 * 统计基础语素库和扩展语素库的完整信息
 */

const fs = require('fs')
const path = require('path')

// 基础语素库 (从element-library-config.ts提取)
const BASE_MORPHEME_LIBRARY = {
  subjects: {
    古代人物: ['贫僧', '道士', '书生', '侠客', '状元', '举人', '秀才', '太守', '县令', '师爷'],
    现代职业: ['程序员', '设计师', '产品经理', '运营', '策划', '文案', '测试', '架构师', '前端', '后端'],
    网络身份: ['UP主', '主播', '网红', '博主', '自媒体', '数字游民', '斜杠青年', 'KOL', '网络达人', '内容创作者'],
    动物世界: ['猫', '狗', '猪', '鸟', '鱼', '蜗牛', '大象', '蚂蚁', '老鼠', '兔子', '熊猫', '企鹅', '海豚', '狮子'],
    天体宇宙: ['月亮', '星星', '太阳', '云朵', '彩虹', '闪电', '银河', '宇宙', '星球', '流星', '黑洞', '星云'],
    抽象概念: ['快乐', '悲伤', '愤怒', '温柔', '智慧', '勇敢', '梦想', '希望', '爱情', '友情', '孤独', '自由'],
    食物美食: ['芝士', '咖啡', '茶', '巧克力', '蛋糕', '面包', '牛奶', '果汁', '冰淇淋', '薯片', '火锅', '烧烤'],
    技术概念: ['WiFi', '404', '500', '数据库', '服务器', '算法', 'AI', '云端', '区块链', '元宇宙', 'VR', 'AR'],
    情绪状态: ['社恐', '社牛', 'emo', '佛系', '躺平', '内卷', '摆烂', '破防', '上头', '下头', '治愈', '暖心', '贴心', '用心', '走心', '元气', '活力', '朝气', '满满', '爆棚', '淡然', '随性', '自在', '洒脱', '松弛', '从容', '优雅', '精致', '温暖', '温柔', '温馨', '温和', '温情', '热情', '热忱'],
    食物关联: ['奶茶', '咖啡', '火锅', '烧烤', '甜品', '蛋糕', '面包', '寿司', '拉面', '披萨', '汉堡', '薯条', '可乐', '雪糕', '布丁', '马卡龙', '泡芙', '司康', '贝果', '华夫饼', '煎饼', '包子', '饺子', '面条', '米饭', '粥', '汤', '茶', '果汁', '奶昔', '酸奶', '牛奶', '豆浆', '咖啡', '红茶', '绿茶', '乌龙茶', '花茶', '果茶', '奶茶', '珍珠奶茶', '芝士奶茶', '水果茶', '柠檬茶', '蜂蜜茶', '薄荷茶', '玫瑰茶', '茉莉花茶']
  },
  actions: {
    日常行为: ['吃饭', '睡觉', '工作', '学习', '运动', '购物', '聊天', '游戏', '看剧', '听歌', '阅读', '写作'],
    特殊动作: ['飞翔', '游泳', '跳舞', '唱歌', '画画', '弹琴', '下棋', '钓鱼', '爬山', '跑步', '骑车', '滑雪'],
    抽象动作: ['思考', '梦想', '希望', '相信', '坚持', '努力', '奋斗', '追求', '探索', '发现', '创造', '改变'],
    网络行为: ['刷屏', '点赞', '转发', '评论', '私信', '直播', '连麦', '开黑', '上分', '氪金', '肝游戏', '追番'],
    现代生活: ['洗头', '化妆', '健身', '减肥', '加班', '摸鱼', '划水', '躺平', '内卷', '社交', '约会', '旅游']
  },
  modifiers: {
    权威级别: ['资深', '专业', '高级', '首席', '顶级', '权威', '官方', '认证', '特级', '超级', '终极', '至尊'],
    空间范围: ['全球', '全国', '全市', '全网', '全场', '全能', '万能', '通用', '专用', '独家', '私人'],
    程度强化: ['超级', '极度', '非常', '特别', '格外', '异常', '十分', '相当', '颇为', '略微', '稍微', '有点'],
    时间频率: ['永远', '总是', '经常', '偶尔', '从不', '瞬间', '立即', '马上', '随时', '定期', '不定期'],
    状态描述: ['完美', '理想', '标准', '普通', '特殊', '独特', '罕见', '常见', '流行', '过时', '新潮', '经典']
  },
  connectors: {
    对比转折: ['但', '却', '然而', '不过', '可是', '只是', '偏偏', '反而', '竟然', '居然', '虽然', '尽管'],
    并列关系: ['和', '与', '及', '以及', '还有', '另外', '同时', '一边', '一面', '既然', '不仅', '而且'],
    递进强化: ['更', '还', '甚至', '尤其', '特别', '格外', '越来越', '愈发', '日益', '逐渐', '渐渐', '慢慢'],
    因果关系: ['因为', '由于', '所以', '因此', '故而', '于是', '从而', '进而', '继而', '然后', '接着', '随即']
  },
  suffixes: {
    职位后缀: ['专家', '大师', '达人', '高手', '能手', '好手', '选手', '玩家', '用户', '会员', '粉丝', '爱好者'],
    身份后缀: ['君', '哥', '姐', '兄', '弟', '妹', '叔', '阿姨', '老师', '同学', '朋友', '伙伴'],
    状态后缀: ['中', 'ing', '者', '人', '员', '师', '家', '手', '客', '迷', '控', '党'],
    网络后缀: ['er', 'man', 'girl', 'boy', 'baby', 'star', 'king', 'queen', 'pro', 'max', 'plus', 'mini']
  },
  traits: {
    正面特质: ['温柔', '理性', '冷静', '勤奋', '节约', '听劝', '乐观', '自信', '坚强', '独立'],
    负面特质: ['强硬', '感性', '冲动', '懒惰', '挥霍', '反骨', '悲观', '自卑', '脆弱', '依赖'],
    生活概念: ['人生', '梦想', '快乐', '悲伤', '爱情', '友情', '工作', '学习', '生活', '未来']
  }
}

// 扩展语素库 (从expanded-element-library-config.ts提取)
const EXPANDED_MORPHEME_LIBRARY = {
  subjects: {
    传统职业扩展: ['医师', '教师', '工程师', '律师', '会计师', '建筑师', '艺术家', '音乐家', '作家', '记者', '编辑', '翻译'],
    现代职业扩展: ['产品经理', '项目经理', '运营经理', '数据分析师', '用户体验师', '前端工程师', '后端工程师', '算法工程师', '测试工程师'],
    创意职业: ['插画师', '动画师', '游戏设计师', '影视制作人', '摄影师', '文案策划', '创意总监', '内容创作者', '独立开发者', '自由职业者'],
    新兴职业: ['AI训练师', '数据科学家', '区块链工程师', '云计算专家', '用户增长专家', '社群运营', '元宇宙设计师', '数字艺术家'],
    文人雅士: ['诗仙', '词圣', '诗人', '文人', '雅士', '才子', '佳人', '墨客', '骚人', '书生']
  },
  traits: {
    基础情感: ['温暖', '温柔', '温馨', '温和', '温情', '热情', '热忱', '热心', '热烈', '热诚', '冷静', '冷淡', '平静', '平和', '平稳'],
    积极情感: ['欢乐', '欢喜', '欢快', '愉快', '愉悦', '快乐', '快意', '喜悦', '喜乐', '兴奋', '兴致', '兴趣'],
    深层情感: ['深情', '深爱', '深切', '深沉', '真诚', '真挚', '真心', '纯真', '纯洁', '纯净', '专注', '专一'],
    文艺情感: ['诗意', '诗情', '雅致', '雅韵', '优雅', '优美', '清雅', '清新', '清纯', '清澈'],
    现代情感: ['治愈', '暖心', '贴心', '用心', '走心', '佛系', '淡然', '随性', '自在', '洒脱', '元气', '活力', '朝气'],
    传统概念: ['书香', '墨香', '茶香', '文房', '四宝', '笔墨', '丹青', '琴棋', '书画', '诗酒'],
    传统美德: ['仁爱', '仁慈', '义气', '义理', '礼貌', '礼仪', '智慧', '信义', '忠诚', '孝顺'],
    日常生活: ['温馨', '舒适', '惬意', '悠闲', '轻松', '自在', '简单', '简约', '自然', '天然'],
    网络流行: ['yyds', '绝绝子', '芭比Q', '破防', '上头', '下头', '拿捏', '整活', '摆烂', '躺平'],
    二次元文化: ['萌', '燃', '治愈', '致郁', '黑化', '洗白', '圈粉', '脱粉', '入坑', '出坑'],
    网络亚文化: ['社恐', '社牛', '社死', '内卷', '躺平', '摆烂', '破防', '上头', '下头', '拿捏'],
    Z世代文化: ['emo', '精神内耗', '社恐', '社牛', '社死', 'yyds', '绝绝子', '芭比Q', '破防', '上头']
  },
  modifiers: {
    能力特征: ['专业', '精通', '熟练', '精湛', '创新', '创意', '高效', '迅速', '敏捷', '严谨', '精确', '全面'],
    品质特征: ['诚信', '诚实', '真诚', '可靠', '稳定', '踏实', '负责', '认真', '积极', '主动', '坚持', '坚定'],
    性格特征: ['开朗', '活泼', '外向', '内向', '稳重', '成熟', '幽默', '机智', '聪明', '智慧', '善良', '友善']
  }
}

/**
 * 统计语素库规模
 */
function analyzeMorphemeLibrary() {
  console.log('🔍 V5引擎语素库全面分析')
  console.log('=' .repeat(60))
  
  // 统计基础语素库
  const baseStats = analyzeLibrary(BASE_MORPHEME_LIBRARY, '基础语素库')
  
  // 统计扩展语素库
  const expandedStats = analyzeLibrary(EXPANDED_MORPHEME_LIBRARY, '扩展语素库')
  
  // 计算总计
  const totalMorphemes = baseStats.total + expandedStats.total
  const totalCategories = baseStats.categories + expandedStats.categories
  
  console.log('\n📊 总体统计')
  console.log('-' .repeat(40))
  console.log(`总语素数量: ${totalMorphemes}个`)
  console.log(`总类别数量: ${totalCategories}个`)
  console.log(`目标完成度: ${(totalMorphemes / 3000 * 100).toFixed(1)}% (目标: 3000个)`)
  console.log(`缺口分析: 还需 ${3000 - totalMorphemes}个语素`)
  
  // 生成详细报告
  const report = {
    timestamp: new Date().toISOString(),
    summary: {
      total_morphemes: totalMorphemes,
      total_categories: totalCategories,
      target_completion: (totalMorphemes / 3000 * 100).toFixed(1) + '%',
      remaining_gap: 3000 - totalMorphemes
    },
    base_library: baseStats,
    expanded_library: expandedStats,
    category_distribution: getCategoryDistribution(baseStats, expandedStats),
    recommendations: generateRecommendations(totalMorphemes)
  }
  
  return report
}

/**
 * 分析单个语素库
 */
function analyzeLibrary(library, name) {
  console.log(`\n📚 ${name}`)
  console.log('-' .repeat(30))
  
  let total = 0
  let categories = 0
  const categoryBreakdown = {}
  
  Object.keys(library).forEach(mainCategory => {
    let categoryTotal = 0
    const subcategories = {}
    
    Object.keys(library[mainCategory]).forEach(subCategory => {
      const count = library[mainCategory][subCategory].length
      subcategories[subCategory] = count
      categoryTotal += count
      total += count
      categories++
    })
    
    categoryBreakdown[mainCategory] = {
      total: categoryTotal,
      subcategories: subcategories
    }
    
    console.log(`  ${mainCategory}: ${categoryTotal}个`)
    Object.keys(subcategories).forEach(sub => {
      console.log(`    - ${sub}: ${subcategories[sub]}个`)
    })
  })
  
  console.log(`  小计: ${total}个语素，${categories}个类别`)
  
  return {
    total,
    categories,
    breakdown: categoryBreakdown
  }
}

/**
 * 获取类别分布
 */
function getCategoryDistribution(baseStats, expandedStats) {
  const distribution = {}
  
  // 合并基础和扩展的类别统计
  Object.keys(baseStats.breakdown).forEach(category => {
    distribution[category] = {
      base: baseStats.breakdown[category].total,
      expanded: 0,
      total: baseStats.breakdown[category].total
    }
  })
  
  Object.keys(expandedStats.breakdown).forEach(category => {
    if (distribution[category]) {
      distribution[category].expanded = expandedStats.breakdown[category].total
      distribution[category].total += expandedStats.breakdown[category].total
    } else {
      distribution[category] = {
        base: 0,
        expanded: expandedStats.breakdown[category].total,
        total: expandedStats.breakdown[category].total
      }
    }
  })
  
  return distribution
}

/**
 * 生成改进建议
 */
function generateRecommendations(currentTotal) {
  const recommendations = []
  
  if (currentTotal < 1000) {
    recommendations.push('优先扩展基础词汇类别，确保每个类别有足够的词汇密度')
  }
  
  if (currentTotal < 2000) {
    recommendations.push('增加专业领域词汇，提升生成结果的专业性')
  }
  
  if (currentTotal < 3000) {
    recommendations.push('补充文化内涵词汇，增强生成结果的文化深度')
    recommendations.push('添加时代潮流词汇，保持生成结果的时代感')
  }
  
  recommendations.push('建立词汇质量评估机制，确保新增词汇的有趣程度')
  recommendations.push('实施A/B测试，验证扩展词汇对生成质量的影响')
  
  return recommendations
}

// 执行分析
if (require.main === module) {
  const report = analyzeMorphemeLibrary()
  
  // 保存报告
  fs.writeFileSync(
    path.join(__dirname, '..', 'morpheme-library-analysis-report.json'),
    JSON.stringify(report, null, 2)
  )
  
  console.log('\n✅ 分析完成！详细报告已保存到: morpheme-library-analysis-report.json')
}

module.exports = { analyzeMorphemeLibrary, BASE_MORPHEME_LIBRARY, EXPANDED_MORPHEME_LIBRARY }
