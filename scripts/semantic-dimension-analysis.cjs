/**
 * 语义维度分析脚本
 * 分析V5引擎中语义维度的作用机制和优化建议
 */

const fs = require('fs')
const path = require('path')

/**
 * 语义维度分析器
 */
class SemanticDimensionAnalyzer {
  constructor() {
    this.configPath = path.join(__dirname, '..', 'config', 'element-library-config.ts')
    this.semanticDimensions = {}
    this.morphemeMapping = {}
    this.analysisResults = {}
  }

  /**
   * 执行语义维度分析
   */
  async executeSemanticAnalysis() {
    console.log('🔍 开始语义维度分析...')
    console.log('=' .repeat(60))

    try {
      // 1. 加载和解析语义维度配置
      this.loadSemanticDimensions()

      // 2. 分析语义维度覆盖情况
      this.analyzeSemanticCoverage()

      // 3. 评估语义维度对生成质量的影响
      this.evaluateQualityImpact()

      // 4. 分析语义连贯性机制
      this.analyzeCoherenceMechanism()

      // 5. 识别优化机会
      this.identifyOptimizationOpportunities()

      // 6. 生成分析报告
      const report = this.generateAnalysisReport()

      console.log('\n✅ 语义维度分析完成！')
      console.log(`📊 语义维度总数: ${Object.keys(this.semanticDimensions).length}个`)
      console.log(`🎯 覆盖率评分: ${report.coverageScore.toFixed(2)}`)

      return report

    } catch (error) {
      console.error('❌ 语义维度分析失败:', error)
      throw error
    }
  }

  /**
   * 加载语义维度配置
   */
  loadSemanticDimensions() {
    console.log('📚 加载语义维度配置...')
    
    // 定义V5引擎的语义维度体系
    this.semanticDimensions = {
      // 专业性维度
      professionalism: {
        name: '专业性',
        description: '表达专业能力和职业素养的程度',
        keywords: ['专业', '精通', '资深', '专家', '高级', '顶级', '权威', '精湛', '熟练', '娴熟'],
        weight: 0.25,
        categories: ['subjects', 'modifiers'],
        culturalWeight: 0.8
      },
      
      // 情感性维度
      emotionality: {
        name: '情感性',
        description: '表达情感色彩和感情深度的程度',
        keywords: ['温暖', '温柔', '深情', '真诚', '治愈', '佛系', '元气', '欢乐', '愉快', '深层'],
        weight: 0.20,
        categories: ['traits', 'modifiers'],
        culturalWeight: 1.2
      },
      
      // 创新性维度
      innovation: {
        name: '创新性',
        description: '表达创新思维和前瞻性的程度',
        keywords: ['创新', '创意', '原创', '独特', '新颖', '前卫', '先锋', '突破', '革新', '开创'],
        weight: 0.20,
        categories: ['traits', 'modifiers', 'subjects'],
        culturalWeight: 0.9
      },
      
      // 传统性维度
      traditionality: {
        name: '传统性',
        description: '体现传统文化和经典价值的程度',
        keywords: ['古', '典', '雅', '诗', '词', '文', '仁', '义', '智', '礼', '墨客', '书生'],
        weight: 0.15,
        categories: ['subjects', 'traits'],
        culturalWeight: 1.5
      },
      
      // 现代性维度
      modernity: {
        name: '现代性',
        description: '体现现代科技和时代特征的程度',
        keywords: ['AI', '数据', '智能', '网络', '数字', '云', '算法', '区块链', '科技', '技术'],
        weight: 0.20,
        categories: ['subjects', 'traits', 'modifiers'],
        culturalWeight: 0.7
      }
    }
    
    console.log(`   ✅ 已定义 ${Object.keys(this.semanticDimensions).length} 个语义维度`)
  }

  /**
   * 分析语义维度覆盖情况
   */
  analyzeSemanticCoverage() {
    console.log('📊 分析语义维度覆盖情况...')
    
    const configContent = fs.readFileSync(this.configPath, 'utf8')
    
    // 提取各类别语素
    const morphemeCategories = {
      subjects: this.extractMorphemes(configContent, 'SUBJECTS_CONFIG'),
      traits: this.extractMorphemes(configContent, 'TRAITS_CONFIG'),
      modifiers: this.extractMorphemes(configContent, 'MODIFIERS_CONFIG'),
      actions: this.extractMorphemes(configContent, 'ACTIONS_CONFIG'),
      connectors: this.extractMorphemes(configContent, 'CONNECTORS_CONFIG'),
      suffixes: this.extractMorphemes(configContent, 'SUFFIXES_CONFIG')
    }
    
    // 分析每个语义维度的覆盖情况
    Object.entries(this.semanticDimensions).forEach(([dimensionKey, dimension]) => {
      const coverage = {
        totalMorphemes: 0,
        coveredMorphemes: 0,
        categoryDistribution: {},
        coverageRate: 0,
        examples: []
      }
      
      dimension.categories.forEach(category => {
        const morphemes = morphemeCategories[category] || []
        coverage.totalMorphemes += morphemes.length
        
        const coveredInCategory = morphemes.filter(morpheme => 
          dimension.keywords.some(keyword => 
            morpheme.includes(keyword) || keyword.includes(morpheme)
          )
        )
        
        coverage.coveredMorphemes += coveredInCategory.length
        coverage.categoryDistribution[category] = {
          total: morphemes.length,
          covered: coveredInCategory.length,
          rate: (coveredInCategory.length / morphemes.length * 100).toFixed(2)
        }
        
        // 收集示例
        coverage.examples.push(...coveredInCategory.slice(0, 3))
      })
      
      coverage.coverageRate = (coverage.coveredMorphemes / coverage.totalMorphemes * 100).toFixed(2)
      
      this.analysisResults[dimensionKey] = coverage
      console.log(`   📈 ${dimension.name}: 覆盖率 ${coverage.coverageRate}% (${coverage.coveredMorphemes}/${coverage.totalMorphemes})`)
    })
  }

  /**
   * 提取语素
   */
  extractMorphemes(content, configName) {
    const configMatch = content.match(new RegExp(`export const ${configName} = \\{([\\s\\S]*?)\\n\\} as const`))
    if (!configMatch) return []
    
    const morphemes = configMatch[1].match(/'([^']+)'/g) || []
    return morphemes.map(m => m.replace(/'/g, ''))
  }

  /**
   * 评估语义维度对生成质量的影响
   */
  evaluateQualityImpact() {
    console.log('🎯 评估语义维度对生成质量的影响...')
    
    const qualityImpact = {}
    
    Object.entries(this.semanticDimensions).forEach(([dimensionKey, dimension]) => {
      const impact = {
        culturalRelevance: this.calculateCulturalRelevance(dimension),
        emotionalExpression: this.calculateEmotionalExpression(dimension),
        personalization: this.calculatePersonalization(dimension),
        coherence: this.calculateCoherence(dimension),
        overallScore: 0
      }
      
      // 计算综合影响分数
      impact.overallScore = (
        impact.culturalRelevance * 0.3 +
        impact.emotionalExpression * 0.25 +
        impact.personalization * 0.25 +
        impact.coherence * 0.20
      ).toFixed(2)
      
      qualityImpact[dimensionKey] = impact
      console.log(`   🎯 ${dimension.name}: 质量影响分数 ${impact.overallScore}`)
    })
    
    this.analysisResults.qualityImpact = qualityImpact
  }

  /**
   * 计算文化相关性
   */
  calculateCulturalRelevance(dimension) {
    // 基于文化权重和覆盖率计算
    const coverage = this.analysisResults[Object.keys(this.semanticDimensions).find(key => 
      this.semanticDimensions[key] === dimension
    )]
    
    return (parseFloat(coverage.coverageRate) / 100 * dimension.culturalWeight * 100).toFixed(2)
  }

  /**
   * 计算情感表达能力
   */
  calculateEmotionalExpression(dimension) {
    // 基于情感相关关键词的比例
    const emotionalKeywords = ['温暖', '温柔', '深情', '真诚', '治愈', '欢乐', '愉快']
    const emotionalCount = dimension.keywords.filter(keyword => 
      emotionalKeywords.some(ek => keyword.includes(ek) || ek.includes(keyword))
    ).length
    
    return (emotionalCount / dimension.keywords.length * 100).toFixed(2)
  }

  /**
   * 计算个性化程度
   */
  calculatePersonalization(dimension) {
    // 基于独特性和多样性
    const uniqueKeywords = new Set(dimension.keywords).size
    const diversityScore = uniqueKeywords / dimension.keywords.length
    
    return (diversityScore * dimension.weight * 400).toFixed(2) // 放大到0-100范围
  }

  /**
   * 计算连贯性
   */
  calculateCoherence(dimension) {
    // 基于类别分布的均衡性
    const coverage = this.analysisResults[Object.keys(this.semanticDimensions).find(key => 
      this.semanticDimensions[key] === dimension
    )]
    
    if (!coverage || !coverage.categoryDistribution) return 0
    
    const rates = Object.values(coverage.categoryDistribution).map(cat => parseFloat(cat.rate))
    const avgRate = rates.reduce((sum, rate) => sum + rate, 0) / rates.length
    const variance = rates.reduce((sum, rate) => sum + Math.pow(rate - avgRate, 2), 0) / rates.length
    
    // 连贯性 = 平均覆盖率 - 方差惩罚
    return Math.max(0, avgRate - Math.sqrt(variance)).toFixed(2)
  }

  /**
   * 分析语义连贯性机制
   */
  analyzeCoherenceMechanism() {
    console.log('🔗 分析语义连贯性机制...')
    
    const coherenceMechanism = {
      dimensionInteraction: this.analyzeDimensionInteraction(),
      categoryBalance: this.analyzeCategoryBalance(),
      semanticConflicts: this.identifySemanticConflicts(),
      coherenceStrategies: this.defineCoherenceStrategies()
    }
    
    this.analysisResults.coherenceMechanism = coherenceMechanism
    console.log(`   🔗 识别到 ${coherenceMechanism.semanticConflicts.length} 个潜在语义冲突`)
  }

  /**
   * 分析维度交互
   */
  analyzeDimensionInteraction() {
    const interactions = []
    
    const dimensionKeys = Object.keys(this.semanticDimensions)
    
    for (let i = 0; i < dimensionKeys.length; i++) {
      for (let j = i + 1; j < dimensionKeys.length; j++) {
        const dim1 = this.semanticDimensions[dimensionKeys[i]]
        const dim2 = this.semanticDimensions[dimensionKeys[j]]
        
        // 计算关键词重叠度
        const overlap = dim1.keywords.filter(k1 => 
          dim2.keywords.some(k2 => k1.includes(k2) || k2.includes(k1))
        ).length
        
        const overlapRate = overlap / Math.min(dim1.keywords.length, dim2.keywords.length)
        
        interactions.push({
          dimension1: dim1.name,
          dimension2: dim2.name,
          overlapRate: (overlapRate * 100).toFixed(2),
          compatibility: this.assessCompatibility(dim1, dim2),
          synergy: this.calculateSynergy(dim1, dim2)
        })
      }
    }
    
    return interactions
  }

  /**
   * 评估兼容性
   */
  assessCompatibility(dim1, dim2) {
    // 传统性和现代性相对冲突
    if ((dim1.name === '传统性' && dim2.name === '现代性') ||
        (dim1.name === '现代性' && dim2.name === '传统性')) {
      return 'conflicting'
    }
    
    // 专业性和情感性可以协调
    if ((dim1.name === '专业性' && dim2.name === '情感性') ||
        (dim1.name === '情感性' && dim2.name === '专业性')) {
      return 'complementary'
    }
    
    return 'neutral'
  }

  /**
   * 计算协同效应
   */
  calculateSynergy(dim1, dim2) {
    const weightProduct = dim1.weight * dim2.weight
    const culturalHarmony = Math.abs(dim1.culturalWeight - dim2.culturalWeight) < 0.5 ? 1.2 : 0.8
    
    return (weightProduct * culturalHarmony * 100).toFixed(2)
  }

  /**
   * 分析类别平衡
   */
  analyzeCategoryBalance() {
    const categoryUsage = {}
    
    Object.values(this.semanticDimensions).forEach(dimension => {
      dimension.categories.forEach(category => {
        categoryUsage[category] = (categoryUsage[category] || 0) + 1
      })
    })
    
    const totalUsage = Object.values(categoryUsage).reduce((sum, count) => sum + count, 0)
    const balance = {}
    
    Object.entries(categoryUsage).forEach(([category, count]) => {
      balance[category] = {
        usage: count,
        percentage: (count / totalUsage * 100).toFixed(2),
        balance: count / (totalUsage / Object.keys(categoryUsage).length)
      }
    })
    
    return balance
  }

  /**
   * 识别语义冲突
   */
  identifySemanticConflicts() {
    const conflicts = []
    
    // 检查传统性和现代性的冲突
    const traditionalKeywords = this.semanticDimensions.traditionality.keywords
    const modernKeywords = this.semanticDimensions.modernity.keywords
    
    traditionalKeywords.forEach(trad => {
      modernKeywords.forEach(mod => {
        if (this.isConflicting(trad, mod)) {
          conflicts.push({
            type: 'traditional_modern_conflict',
            element1: trad,
            element2: mod,
            severity: 'medium'
          })
        }
      })
    })
    
    return conflicts
  }

  /**
   * 判断是否冲突
   */
  isConflicting(word1, word2) {
    const conflictPairs = [
      ['古', 'AI'], ['典', '数字'], ['传统', '智能'],
      ['雅', '网络'], ['诗', '算法'], ['文', '科技']
    ]
    
    return conflictPairs.some(([c1, c2]) => 
      (word1.includes(c1) && word2.includes(c2)) ||
      (word1.includes(c2) && word2.includes(c1))
    )
  }

  /**
   * 定义连贯性策略
   */
  defineCoherenceStrategies() {
    return {
      dimensionWeighting: {
        description: '根据用户偏好和上下文动态调整语义维度权重',
        implementation: '使用加权随机选择算法，优先选择高权重维度的语素'
      },
      conflictResolution: {
        description: '当检测到语义冲突时，采用文化融合策略',
        implementation: '在传统和现代元素之间建立桥梁，如"智慧诗人"、"数字文人"'
      },
      contextualAdaptation: {
        description: '根据生成上下文调整语义选择',
        implementation: '分析已选择的语素，确保后续选择在语义上保持一致'
      },
      balancedSelection: {
        description: '确保不同类别语素的平衡使用',
        implementation: '监控类别使用频率，避免某个类别过度使用'
      }
    }
  }

  /**
   * 识别优化机会
   */
  identifyOptimizationOpportunities() {
    console.log('🔧 识别优化机会...')
    
    const opportunities = {
      coverageGaps: this.identifyCoverageGaps(),
      dimensionEnhancements: this.suggestDimensionEnhancements(),
      newDimensionSuggestions: this.suggestNewDimensions(),
      weightOptimization: this.suggestWeightOptimization()
    }
    
    this.analysisResults.optimizationOpportunities = opportunities
    console.log(`   🔧 识别到 ${opportunities.coverageGaps.length} 个覆盖盲区`)
    console.log(`   🔧 建议 ${opportunities.newDimensionSuggestions.length} 个新语义维度`)
  }

  /**
   * 识别覆盖盲区
   */
  identifyCoverageGaps() {
    const gaps = []
    
    Object.entries(this.analysisResults).forEach(([dimensionKey, analysis]) => {
      if (analysis.coverageRate && parseFloat(analysis.coverageRate) < 30) {
        gaps.push({
          dimension: this.semanticDimensions[dimensionKey].name,
          currentCoverage: analysis.coverageRate,
          severity: 'high',
          recommendation: '增加相关关键词和语素映射'
        })
      }
    })
    
    return gaps
  }

  /**
   * 建议维度增强
   */
  suggestDimensionEnhancements() {
    const enhancements = []
    
    // 基于新增语素建议增强
    const newMorphemeCategories = [
      '职业扩展', '情感扩展', '特征扩展', '传统文化', '流行修饰'
    ]
    
    newMorphemeCategories.forEach(category => {
      enhancements.push({
        targetDimension: this.mapCategoryToDimension(category),
        enhancement: `为${category}类别增加专门的语义映射`,
        expectedImprovement: '15-25%覆盖率提升',
        priority: 'high'
      })
    })
    
    return enhancements
  }

  /**
   * 映射类别到维度
   */
  mapCategoryToDimension(category) {
    const mapping = {
      '职业扩展': 'professionalism',
      '情感扩展': 'emotionality',
      '特征扩展': 'innovation',
      '传统文化': 'traditionality',
      '流行修饰': 'modernity'
    }
    
    return mapping[category] || 'unknown'
  }

  /**
   * 建议新维度
   */
  suggestNewDimensions() {
    return [
      {
        name: '艺术性维度',
        description: '表达艺术创作和美学追求的程度',
        keywords: ['艺术', '美术', '设计', '创作', '美学', '审美'],
        justification: '新增大量艺术相关语素，需要专门维度支持',
        priority: 'medium'
      },
      {
        name: '社交性维度',
        description: '表达社交能力和人际关系的程度',
        keywords: ['社交', '沟通', '交流', '合作', '团队', '领导'],
        justification: '现代职场和生活中社交能力重要性增加',
        priority: 'medium'
      },
      {
        name: '生活方式维度',
        description: '表达生活态度和方式选择的程度',
        keywords: ['健康', '运动', '旅行', '美食', '休闲', '品质'],
        justification: '用户对生活方式表达需求增长',
        priority: 'low'
      }
    ]
  }

  /**
   * 建议权重优化
   */
  suggestWeightOptimization() {
    const suggestions = []
    
    // 基于覆盖率和质量影响调整权重
    Object.entries(this.analysisResults).forEach(([dimensionKey, analysis]) => {
      if (analysis.coverageRate) {
        const currentWeight = this.semanticDimensions[dimensionKey].weight
        const coverageRate = parseFloat(analysis.coverageRate)
        
        if (coverageRate > 80 && currentWeight < 0.25) {
          suggestions.push({
            dimension: this.semanticDimensions[dimensionKey].name,
            currentWeight,
            suggestedWeight: Math.min(0.30, currentWeight + 0.05),
            reason: '高覆盖率，建议增加权重'
          })
        } else if (coverageRate < 20 && currentWeight > 0.10) {
          suggestions.push({
            dimension: this.semanticDimensions[dimensionKey].name,
            currentWeight,
            suggestedWeight: Math.max(0.10, currentWeight - 0.05),
            reason: '低覆盖率，建议降低权重'
          })
        }
      }
    })
    
    return suggestions
  }

  /**
   * 生成分析报告
   */
  generateAnalysisReport() {
    const report = {
      timestamp: new Date().toISOString(),
      analysisType: 'semantic_dimension_analysis',
      semanticDimensions: this.semanticDimensions,
      analysisResults: this.analysisResults,
      coverageScore: this.calculateCoverageScore(),
      qualityScore: this.calculateQualityScore(),
      recommendations: this.generateRecommendations()
    }

    // 保存报告
    const reportPath = path.join(__dirname, '..', 'semantic-dimension-analysis-report.json')
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2))

    return report
  }

  /**
   * 计算覆盖率评分
   */
  calculateCoverageScore() {
    const coverageRates = Object.values(this.analysisResults)
      .filter(result => result.coverageRate)
      .map(result => parseFloat(result.coverageRate))
    
    return coverageRates.reduce((sum, rate) => sum + rate, 0) / coverageRates.length
  }

  /**
   * 计算质量评分
   */
  calculateQualityScore() {
    if (!this.analysisResults.qualityImpact) return 0
    
    const qualityScores = Object.values(this.analysisResults.qualityImpact)
      .map(impact => parseFloat(impact.overallScore))
    
    return qualityScores.reduce((sum, score) => sum + score, 0) / qualityScores.length
  }

  /**
   * 生成建议
   */
  generateRecommendations() {
    const recommendations = []
    
    // 基于分析结果生成建议
    if (this.analysisResults.optimizationOpportunities) {
      const opportunities = this.analysisResults.optimizationOpportunities
      
      if (opportunities.coverageGaps.length > 0) {
        recommendations.push({
          type: 'coverage_improvement',
          priority: 'high',
          description: '提升语义维度覆盖率',
          actions: opportunities.coverageGaps.map(gap => gap.recommendation)
        })
      }
      
      if (opportunities.newDimensionSuggestions.length > 0) {
        recommendations.push({
          type: 'dimension_expansion',
          priority: 'medium',
          description: '增加新的语义维度',
          actions: opportunities.newDimensionSuggestions.map(dim => `添加${dim.name}`)
        })
      }
    }
    
    return recommendations
  }
}

/**
 * 主函数
 */
async function main() {
  try {
    const analyzer = new SemanticDimensionAnalyzer()
    const report = await analyzer.executeSemanticAnalysis()
    
    console.log('\n📄 语义维度分析报告已保存')
    console.log(`📊 语义维度数量: ${Object.keys(report.semanticDimensions).length}个`)
    console.log(`🎯 覆盖率评分: ${report.coverageScore.toFixed(2)}`)
    console.log(`🏆 质量评分: ${report.qualityScore.toFixed(2)}`)
    console.log(`💡 优化建议: ${report.recommendations.length}条`)
    
  } catch (error) {
    console.error('❌ 语义维度分析失败:', error)
    process.exit(1)
  }
}

// 执行分析
if (require.main === module) {
  main()
}

module.exports = { SemanticDimensionAnalyzer }
