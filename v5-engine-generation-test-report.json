{"timestamp": "2025-06-19T14:38:45.818Z", "testType": "v5_engine_generation_test", "totalSamples": 160, "diversityScore": 55.813125, "testResults": {"generationSamples": {"modern_科技_3": [{"username": "数据工程师数字产品经理认证", "morphemes": ["产品经理", "工程师", "工程师", "数据工程师", "工程师", "产品经理", "产品经理", "数字产品经理", "认证", "师", "经理"], "categories": ["subjects", "subjects", "subjects", "subjects", "subjects", "subjects", "traits", "traits", "modifiers", "suffixes", "suffixes"], "culturalElements": ["现代", "现代"], "semanticDimensions": ["专业性", "现代性"]}, {"username": "前端工程师数据产品经理权威级别", "morphemes": ["产品经理", "前端", "工程师", "工程师", "前端工程师", "工程师", "产品经理", "前端工程师", "产品经理", "数据产品经理", "权威级别", "师", "经理"], "categories": ["subjects", "subjects", "subjects", "subjects", "subjects", "subjects", "subjects", "subjects", "traits", "traits", "modifiers", "suffixes", "suffixes"], "culturalElements": ["现代"], "semanticDimensions": ["专业性", "现代性"]}, {"username": "数字运营师祥云表示空间范围和影响力的修饰词", "morphemes": ["运营", "数字运营师", "祥云", "空间范围", "表示空间范围和影响力的修饰词", "和", "师"], "categories": ["subjects", "subjects", "traits", "modifiers", "modifiers", "connectors", "suffixes"], "culturalElements": ["传统", "现代", "现代"], "semanticDimensions": ["传统性", "现代性"]}, {"username": "游戏程序员数据产品经理权威级别", "morphemes": ["程序员", "产品经理", "游戏程序员", "产品经理", "产品经理", "数据产品经理", "权威级别", "经理"], "categories": ["subjects", "subjects", "subjects", "subjects", "traits", "traits", "modifiers", "suffixes"], "culturalElements": ["现代"], "semanticDimensions": ["现代性"]}, {"username": "后端工程师云雾钻石", "morphemes": ["后端", "工程师", "工程师", "后端工程师", "工程师", "后端工程师", "云雾", "钻石", "师"], "categories": ["subjects", "subjects", "subjects", "subjects", "subjects", "subjects", "traits", "modifiers", "suffixes"], "culturalElements": ["现代"], "semanticDimensions": ["专业性", "现代性"]}, {"username": "运维工程师祥云表示权威性和专业程度的修饰词", "morphemes": ["工程师", "运维工程师", "工程师", "运维工程师", "工程师", "运维工程师", "专业", "祥云", "表示权威性和专业程度的修饰词", "专业", "专业", "和", "师"], "categories": ["subjects", "subjects", "subjects", "subjects", "subjects", "subjects", "traits", "traits", "modifiers", "modifiers", "modifiers", "connectors", "suffixes"], "culturalElements": ["传统", "现代"], "semanticDimensions": ["专业性", "传统性", "现代性"]}, {"username": "数字艺术家数据产品经理表示权威性和专业程度的修饰词", "morphemes": ["产品经理", "艺术家", "艺术家", "艺术家", "数字艺术家", "艺术家", "产品经理", "产品经理", "专业", "数据产品经理", "表示权威性和专业程度的修饰词", "专业", "专业", "和", "经理"], "categories": ["subjects", "subjects", "subjects", "subjects", "subjects", "subjects", "subjects", "traits", "traits", "traits", "modifiers", "modifiers", "modifiers", "connectors", "suffixes"], "culturalElements": ["传统", "现代", "现代"], "semanticDimensions": ["专业性", "传统性", "现代性"]}, {"username": "测试工程师云产品经理官方", "morphemes": ["产品经理", "测试", "工程师", "测试工程师", "工程师", "测试工程师", "工程师", "产品经理", "测试工程师", "产品经理", "云产品经理", "官方", "官", "师", "经理"], "categories": ["subjects", "subjects", "subjects", "subjects", "subjects", "subjects", "subjects", "subjects", "subjects", "traits", "traits", "modifiers", "suffixes", "suffixes", "suffixes"], "culturalElements": ["现代"], "semanticDimensions": ["专业性", "现代性"]}, {"username": "云计算专家数字游民表示空间范围和影响力的修饰词", "morphemes": ["数字游民", "云计算专家", "云计算专家", "云计算专家", "数字游民", "空间范围", "表示空间范围和影响力的修饰词", "和", "专家", "专家"], "categories": ["subjects", "subjects", "subjects", "subjects", "traits", "modifiers", "modifiers", "connectors", "suffixes", "suffixes"], "culturalElements": ["传统", "现代", "现代"], "semanticDimensions": ["专业性", "传统性", "现代性"]}, {"username": "算法云产品经理identity_elevation", "morphemes": ["产品经理", "identity_elevation", "identity_elevation", "算法", "产品经理", "产品经理", "云产品经理", "identity_elevation", "identity_elevation", "identity_elevation", "identity_elevation", "identity_elevation", "identity_elevation", "identity_elevation", "identity_elevation", "identity_elevation", "经理", "identity_elevation"], "categories": ["subjects", "subjects", "subjects", "subjects", "subjects", "traits", "traits", "modifiers", "modifiers", "modifiers", "modifiers", "actions", "actions", "actions", "actions", "actions", "suffixes", "suffixes"], "culturalElements": ["现代", "现代"], "semanticDimensions": ["现代性"]}, {"username": "智能合约开发者祥云史诗", "morphemes": ["智能合约开发者", "祥云", "史诗", "史诗"], "categories": ["subjects", "traits", "modifiers", "modifiers"], "culturalElements": ["传统", "传统", "现代", "现代"], "semanticDimensions": ["传统性", "现代性"]}, {"username": "数字策展人祥云传奇", "morphemes": ["数字策展人", "祥云", "传奇"], "categories": ["subjects", "traits", "modifiers"], "culturalElements": ["现代", "现代"], "semanticDimensions": ["现代性"]}, {"username": "移动开发工程师数字游民首席", "morphemes": ["数字游民", "工程师", "工程师", "移动开发工程师", "工程师", "数字游民", "首席", "师"], "categories": ["subjects", "subjects", "subjects", "subjects", "subjects", "traits", "modifiers", "suffixes"], "culturalElements": ["现代"], "semanticDimensions": ["专业性", "现代性"]}, {"username": "网络时代的身份标识祥云史诗", "morphemes": ["网络时代的身份标识", "时代", "祥云", "史诗", "史诗"], "categories": ["subjects", "traits", "traits", "modifiers", "modifiers"], "culturalElements": ["传统", "现代", "现代"], "semanticDimensions": ["传统性", "现代性"]}, {"username": "前端工程师数字游民资深", "morphemes": ["前端", "数字游民", "工程师", "工程师", "前端工程师", "工程师", "前端工程师", "数字游民", "资深", "资深", "师"], "categories": ["subjects", "subjects", "subjects", "subjects", "subjects", "subjects", "subjects", "traits", "traits", "modifiers", "suffixes"], "culturalElements": ["现代"], "semanticDimensions": ["专业性", "现代性"]}, {"username": "云计算专家数字游民权威级别", "morphemes": ["数字游民", "云计算专家", "云计算专家", "云计算专家", "数字游民", "权威级别", "专家", "专家"], "categories": ["subjects", "subjects", "subjects", "subjects", "traits", "modifiers", "suffixes", "suffixes"], "culturalElements": ["现代", "现代"], "semanticDimensions": ["专业性", "现代性"]}, {"username": "数字艺术家数据产品经理表示权威性和专业程度的修饰词", "morphemes": ["产品经理", "艺术家", "艺术家", "艺术家", "数字艺术家", "艺术家", "产品经理", "产品经理", "专业", "数据产品经理", "表示权威性和专业程度的修饰词", "专业", "专业", "和", "经理"], "categories": ["subjects", "subjects", "subjects", "subjects", "subjects", "subjects", "subjects", "traits", "traits", "traits", "modifiers", "modifiers", "modifiers", "connectors", "suffixes"], "culturalElements": ["传统", "现代", "现代"], "semanticDimensions": ["专业性", "传统性", "现代性"]}, {"username": "星云数字产品经理identity_elevation", "morphemes": ["产品经理", "identity_elevation", "identity_elevation", "星云", "产品经理", "产品经理", "数字产品经理", "identity_elevation", "identity_elevation", "identity_elevation", "identity_elevation", "identity_elevation", "identity_elevation", "identity_elevation", "identity_elevation", "identity_elevation", "经理", "identity_elevation"], "categories": ["subjects", "subjects", "subjects", "subjects", "subjects", "traits", "traits", "modifiers", "modifiers", "modifiers", "modifiers", "actions", "actions", "actions", "actions", "actions", "suffixes", "suffixes"], "culturalElements": ["现代", "现代"], "semanticDimensions": ["现代性"]}, {"username": "移动开发工程师祥云表示空间范围和影响力的修饰词", "morphemes": ["工程师", "工程师", "移动开发工程师", "工程师", "祥云", "空间范围", "表示空间范围和影响力的修饰词", "和", "师"], "categories": ["subjects", "subjects", "subjects", "subjects", "traits", "modifiers", "modifiers", "connectors", "suffixes"], "culturalElements": ["传统", "现代"], "semanticDimensions": ["专业性", "传统性", "现代性"]}, {"username": "人工智能专家祥云权威级别", "morphemes": ["人工智能专家", "祥云", "权威级别", "专家", "专家"], "categories": ["subjects", "traits", "modifiers", "suffixes", "suffixes"], "culturalElements": ["传统", "现代", "现代"], "semanticDimensions": ["专业性", "现代性"]}], "modern_情感_4": [{"username": "治愈烘焙师温柔超治愈聊天", "morphemes": ["温柔", "治愈烘焙师", "烘焙师", "温柔", "温柔", "治愈", "温柔", "治愈", "超治愈", "聊天", "师"], "categories": ["subjects", "subjects", "subjects", "traits", "traits", "traits", "traits", "traits", "modifiers", "actions", "suffixes"], "culturalElements": [], "semanticDimensions": ["情感性"]}, {"username": "温暖粥品师小温暖超治愈散步", "morphemes": ["温暖粥品师", "温暖", "治愈", "小温暖", "温暖", "治愈", "超治愈", "散步", "师"], "categories": ["subjects", "traits", "traits", "traits", "traits", "traits", "modifiers", "actions", "suffixes"], "culturalElements": [], "semanticDimensions": ["情感性"]}, {"username": "迷糊但温暖真诚超治愈特殊或技能性的动作", "morphemes": ["迷糊但温暖", "真诚", "温暖", "真诚", "治愈", "温暖", "真诚", "真诚", "治愈", "超治愈", "特殊", "特殊或技能性的动作", "但"], "categories": ["subjects", "traits", "traits", "traits", "traits", "traits", "traits", "traits", "traits", "modifiers", "modifiers", "actions", "connectors"], "culturalElements": [], "semanticDimensions": ["情感性"]}, {"username": "甜品治愈师温柔超治愈购物", "morphemes": ["温柔", "甜品治愈师", "温柔", "温柔", "治愈", "温柔", "治愈", "超治愈", "购物", "师"], "categories": ["subjects", "subjects", "traits", "traits", "traits", "traits", "traits", "modifiers", "actions", "suffixes"], "culturalElements": [], "semanticDimensions": ["情感性"]}, {"username": "治愈烘焙师愉快超治愈日常生活中的基本行为", "morphemes": ["治愈烘焙师", "烘焙师", "生活", "愉快", "治愈", "生活", "愉快", "愉快", "治愈", "超治愈", "日常生活中的基本行为", "师"], "categories": ["subjects", "subjects", "traits", "traits", "traits", "traits", "traits", "traits", "traits", "modifiers", "actions", "suffixes"], "culturalElements": [], "semanticDimensions": ["情感性"]}, {"username": "温暖粥品师愉快超治愈发呆", "morphemes": ["温暖粥品师", "温暖", "愉快", "治愈", "温暖", "愉快", "愉快", "治愈", "超治愈", "发呆", "师"], "categories": ["subjects", "traits", "traits", "traits", "traits", "traits", "traits", "traits", "modifiers", "actions", "suffixes"], "culturalElements": [], "semanticDimensions": ["情感性"]}, {"username": "温暖粥品师治愈超治愈identity_elevation", "morphemes": ["identity_elevation", "identity_elevation", "温暖粥品师", "温暖", "治愈", "温暖", "治愈", "identity_elevation", "identity_elevation", "identity_elevation", "identity_elevation", "超治愈", "identity_elevation", "identity_elevation", "identity_elevation", "identity_elevation", "identity_elevation", "师", "identity_elevation"], "categories": ["subjects", "subjects", "subjects", "traits", "traits", "traits", "traits", "modifiers", "modifiers", "modifiers", "modifiers", "modifiers", "actions", "actions", "actions", "actions", "actions", "suffixes", "suffixes"], "culturalElements": [], "semanticDimensions": ["情感性"]}, {"username": "温柔佛系超治愈吃", "morphemes": ["温柔", "温柔", "佛系", "温柔", "治愈", "佛系", "温柔", "治愈", "佛系", "超治愈", "吃"], "categories": ["subjects", "traits", "traits", "traits", "traits", "traits", "traits", "traits", "traits", "modifiers", "actions"], "culturalElements": [], "semanticDimensions": ["情感性"]}, {"username": "温暖粥品师很治愈超治愈日常生活中的基本行为", "morphemes": ["温暖粥品师", "生活", "温暖", "治愈", "很治愈", "生活", "温暖", "治愈", "超治愈", "日常生活中的基本行为", "师"], "categories": ["subjects", "traits", "traits", "traits", "traits", "traits", "traits", "traits", "modifiers", "actions", "suffixes"], "culturalElements": [], "semanticDimensions": ["情感性"]}, {"username": "温柔治愈超治愈工作", "morphemes": ["温柔", "温柔", "工作", "温柔", "治愈", "温柔", "治愈", "超治愈", "工作"], "categories": ["subjects", "traits", "traits", "traits", "traits", "traits", "traits", "modifiers", "actions"], "culturalElements": [], "semanticDimensions": ["情感性"]}, {"username": "温柔元气超治愈特殊动作", "morphemes": ["温柔", "温柔", "元气", "温柔", "治愈", "元气", "温柔", "治愈", "元气", "超治愈", "特殊", "特殊动作"], "categories": ["subjects", "traits", "traits", "traits", "traits", "traits", "traits", "traits", "traits", "modifiers", "modifiers", "actions"], "culturalElements": [], "semanticDimensions": ["情感性"]}, {"username": "温暖包子师愉快超治愈玩", "morphemes": ["温暖包子师", "温暖", "愉快", "治愈", "温暖", "愉快", "愉快", "治愈", "超治愈", "玩", "师"], "categories": ["subjects", "traits", "traits", "traits", "traits", "traits", "traits", "traits", "modifiers", "actions", "suffixes"], "culturalElements": [], "semanticDimensions": ["情感性"]}, {"username": "甜品治愈师温暖超治愈聊天", "morphemes": ["甜品治愈师", "温暖", "治愈", "温暖", "治愈", "超治愈", "聊天", "师"], "categories": ["subjects", "traits", "traits", "traits", "traits", "modifiers", "actions", "suffixes"], "culturalElements": [], "semanticDimensions": ["情感性"]}, {"username": "温柔温柔超治愈睡", "morphemes": ["温柔", "温柔", "温柔", "治愈", "温柔", "治愈", "超治愈", "睡"], "categories": ["subjects", "traits", "traits", "traits", "traits", "traits", "modifiers", "actions"], "culturalElements": [], "semanticDimensions": ["情感性"]}, {"username": "温暖饺子师小治愈超治愈日常生活中的基本行为", "morphemes": ["温暖饺子师", "生活", "温暖", "治愈", "小治愈", "生活", "温暖", "治愈", "超治愈", "日常生活中的基本行为", "师"], "categories": ["subjects", "traits", "traits", "traits", "traits", "traits", "traits", "traits", "modifiers", "actions", "suffixes"], "culturalElements": [], "semanticDimensions": ["情感性"]}, {"username": "温暖粥品师治愈系超治愈identity_elevation", "morphemes": ["identity_elevation", "identity_elevation", "温暖粥品师", "温暖", "治愈", "治愈系", "温暖", "治愈", "identity_elevation", "identity_elevation", "identity_elevation", "identity_elevation", "超治愈", "identity_elevation", "identity_elevation", "identity_elevation", "identity_elevation", "identity_elevation", "师", "identity_elevation"], "categories": ["subjects", "subjects", "subjects", "traits", "traits", "traits", "traits", "traits", "modifiers", "modifiers", "modifiers", "modifiers", "modifiers", "actions", "actions", "actions", "actions", "actions", "suffixes", "suffixes"], "culturalElements": [], "semanticDimensions": ["情感性"]}, {"username": "温柔愉快超治愈购物", "morphemes": ["温柔", "温柔", "温柔", "愉快", "治愈", "温柔", "愉快", "愉快", "治愈", "超治愈", "购物"], "categories": ["subjects", "traits", "traits", "traits", "traits", "traits", "traits", "traits", "traits", "modifiers", "actions"], "culturalElements": [], "semanticDimensions": ["情感性"]}, {"username": "迷糊但温暖温暖超治愈聊天", "morphemes": ["迷糊但温暖", "温暖", "治愈", "温暖", "治愈", "超治愈", "聊天", "但"], "categories": ["subjects", "traits", "traits", "traits", "traits", "modifiers", "actions", "connectors"], "culturalElements": [], "semanticDimensions": ["情感性"]}, {"username": "温柔元气超治愈日常生活中的基本行为", "morphemes": ["温柔", "温柔", "生活", "元气", "温柔", "治愈", "元气", "生活", "温柔", "治愈", "元气", "超治愈", "日常生活中的基本行为"], "categories": ["subjects", "traits", "traits", "traits", "traits", "traits", "traits", "traits", "traits", "traits", "traits", "modifiers", "actions"], "culturalElements": [], "semanticDimensions": ["情感性"]}, {"username": "温暖饺子师很治愈超治愈思考", "morphemes": ["温暖饺子师", "温暖", "治愈", "很治愈", "温暖", "治愈", "超治愈", "思考", "师"], "categories": ["subjects", "traits", "traits", "traits", "traits", "traits", "modifiers", "actions", "suffixes"], "culturalElements": [], "semanticDimensions": ["情感性"]}], "classic_传统_3": [{"username": "表示权威性和专业程度的修饰词义人雅人", "morphemes": ["雅人", "义人", "雅人", "义人", "雅人", "义人", "专业", "表示权威性和专业程度的修饰词", "专业", "专业", "和"], "categories": ["subjects", "subjects", "subjects", "subjects", "subjects", "traits", "traits", "modifiers", "modifiers", "modifiers", "connectors"], "culturalElements": ["传统", "传统", "传统"], "semanticDimensions": ["专业性", "传统性"]}, {"username": "史诗文友传统文化中的人物角色", "morphemes": ["传统文化中的人物角色", "传统", "文友", "史诗", "史诗"], "categories": ["subjects", "traits", "traits", "modifiers", "modifiers"], "culturalElements": ["传统", "传统"], "semanticDimensions": ["传统性"]}, {"username": "表示状态和情况的修饰词智勇义人", "morphemes": ["义人", "义人", "义人", "智勇", "表示状态和情况的修饰词", "和"], "categories": ["subjects", "subjects", "traits", "traits", "modifiers", "connectors"], "culturalElements": ["传统", "传统", "传统"], "semanticDimensions": ["传统性"]}, {"username": "高雅明智智能合约开发者", "morphemes": ["高雅", "智能合约开发者", "明智", "明智", "高雅", "高雅"], "categories": ["subjects", "subjects", "traits", "traits", "traits", "modifiers"], "culturalElements": ["传统", "传统", "现代"], "semanticDimensions": ["传统性", "现代性"]}, {"username": "表示状态和情况的修饰词静雅智能合约开发者", "morphemes": ["智能合约开发者", "静雅", "表示状态和情况的修饰词", "和"], "categories": ["subjects", "traits", "modifiers", "connectors"], "culturalElements": ["传统", "传统", "传统", "现代"], "semanticDimensions": ["传统性", "现代性"]}, {"username": "高雅优雅文人", "morphemes": ["文人", "高雅", "文人", "文人", "优雅", "优雅", "优雅", "高雅", "高雅"], "categories": ["subjects", "subjects", "subjects", "subjects", "traits", "traits", "traits", "traits", "modifiers"], "culturalElements": ["传统", "传统"], "semanticDimensions": ["传统性"]}, {"username": "表示时间频率和持续性的修饰词文艺墨客", "morphemes": ["墨客", "墨客", "墨客", "文艺", "时间频率", "表示时间频率和持续性的修饰词", "和"], "categories": ["subjects", "subjects", "traits", "traits", "modifiers", "modifiers", "connectors"], "culturalElements": ["传统", "传统"], "semanticDimensions": ["传统性"]}, {"username": "史诗睿智智慧", "morphemes": ["智慧", "智慧", "睿智", "智慧", "史诗", "史诗"], "categories": ["subjects", "traits", "traits", "traits", "modifiers", "modifiers"], "culturalElements": ["传统", "传统"], "semanticDimensions": ["传统性"]}, {"username": "表示状态和情况的修饰词义行典雅", "morphemes": ["典雅", "典雅", "义行", "典雅", "表示状态和情况的修饰词", "和"], "categories": ["subjects", "traits", "traits", "traits", "modifiers", "connectors"], "culturalElements": ["传统", "传统", "传统", "传统"], "semanticDimensions": ["传统性"]}, {"username": "史诗礼仪儒雅", "morphemes": ["儒雅", "儒雅", "礼仪", "儒雅", "史诗", "史诗"], "categories": ["subjects", "traits", "traits", "traits", "modifiers", "modifiers"], "culturalElements": ["传统", "传统", "传统"], "semanticDimensions": ["传统性"]}, {"username": "表示空间范围和影响力的修饰词有礼智能制造工程师", "morphemes": ["工程师", "工程师", "智能制造工程师", "工程师", "有礼", "有礼", "空间范围", "表示空间范围和影响力的修饰词", "制造", "和", "师"], "categories": ["subjects", "subjects", "subjects", "subjects", "traits", "traits", "modifiers", "modifiers", "actions", "connectors", "suffixes"], "culturalElements": ["传统", "传统", "传统", "现代"], "semanticDimensions": ["专业性", "传统性", "现代性"]}, {"username": "表示空间范围和影响力的修饰词如诗诗客", "morphemes": ["如诗", "诗客", "诗客", "如诗", "空间范围", "表示空间范围和影响力的修饰词", "和"], "categories": ["subjects", "subjects", "subjects", "traits", "modifiers", "modifiers", "connectors"], "culturalElements": ["传统", "传统"], "semanticDimensions": ["传统性"]}, {"username": "表示状态和情况的修饰词智识儒雅", "morphemes": ["儒雅", "智识", "儒雅", "智识", "儒雅", "表示状态和情况的修饰词", "和"], "categories": ["subjects", "traits", "traits", "traits", "traits", "modifiers", "connectors"], "culturalElements": ["传统", "传统", "传统"], "semanticDimensions": ["传统性"]}, {"username": "表示权威性和专业程度的修饰词礼敬理想主义现实派", "morphemes": ["理想主义现实派", "专业", "礼敬", "理想", "理想主义", "表示权威性和专业程度的修饰词", "专业", "专业", "和"], "categories": ["subjects", "traits", "traits", "traits", "traits", "modifiers", "modifiers", "modifiers", "connectors"], "culturalElements": ["传统", "传统", "传统"], "semanticDimensions": ["专业性", "传统性"]}, {"username": "表示空间范围和影响力的修饰词理想主义儒雅", "morphemes": ["儒雅", "儒雅", "理想", "理想主义", "儒雅", "空间范围", "表示空间范围和影响力的修饰词", "和"], "categories": ["subjects", "traits", "traits", "traits", "traits", "modifiers", "modifiers", "connectors"], "culturalElements": ["传统", "传统", "传统"], "semanticDimensions": ["传统性"]}, {"username": "表示权威性和专业程度的修饰词如诗古代人物", "morphemes": ["古代人物", "如诗", "专业", "如诗", "表示权威性和专业程度的修饰词", "专业", "专业", "和"], "categories": ["subjects", "subjects", "traits", "traits", "modifiers", "modifiers", "modifiers", "connectors"], "culturalElements": ["传统", "传统", "传统"], "semanticDimensions": ["专业性", "传统性"]}, {"username": "表示时间频率和持续性的修饰词诗魂诗人", "morphemes": ["诗人", "诗人", "诗人", "诗魂", "时间频率", "表示时间频率和持续性的修饰词", "和"], "categories": ["subjects", "subjects", "subjects", "traits", "modifiers", "modifiers", "connectors"], "culturalElements": ["传统", "传统"], "semanticDimensions": ["传统性"]}, {"username": "表示时间频率和持续性的修饰词义理理想主义现实派", "morphemes": ["理想主义现实派", "义理", "理想", "理想主义", "时间频率", "表示时间频率和持续性的修饰词", "和"], "categories": ["subjects", "traits", "traits", "traits", "modifiers", "modifiers", "connectors"], "culturalElements": ["传统", "传统"], "semanticDimensions": ["传统性"]}, {"username": "表示时间频率和持续性的修饰词明智古代人物", "morphemes": ["古代人物", "明智", "明智", "时间频率", "表示时间频率和持续性的修饰词", "和"], "categories": ["subjects", "traits", "traits", "modifiers", "modifiers", "connectors"], "culturalElements": ["传统", "传统", "传统"], "semanticDimensions": ["传统性"]}, {"username": "表示权威性和专业程度的修饰词智者如诗", "morphemes": ["如诗", "智者", "智者", "智者", "专业", "如诗", "表示权威性和专业程度的修饰词", "专业", "专业", "和"], "categories": ["subjects", "subjects", "subjects", "traits", "traits", "traits", "modifiers", "modifiers", "modifiers", "connectors"], "culturalElements": ["传统", "传统", "传统"], "semanticDimensions": ["专业性", "传统性"]}], "classic_文化_5": [{"username": "服务角色发呆首席古典儒雅", "morphemes": ["儒雅", "古典", "儒雅", "儒雅", "首席", "发呆", "服务角色"], "categories": ["subjects", "traits", "traits", "traits", "modifiers", "actions", "suffixes"], "culturalElements": ["传统", "传统", "传统"], "semanticDimensions": ["传统性"]}, {"username": "经理散步国际风雅风雅", "morphemes": ["风雅", "风雅", "风雅", "风雅", "国际", "散步", "经理"], "categories": ["subjects", "subjects", "traits", "traits", "modifiers", "actions", "suffixes"], "culturalElements": ["传统"], "semanticDimensions": ["传统性"]}, {"username": "经理特殊或技能性的动作全球雅士文人", "morphemes": ["文人", "文人", "文人", "雅士", "全球", "特殊", "特殊或技能性的动作", "经理"], "categories": ["subjects", "subjects", "subjects", "traits", "modifiers", "modifiers", "actions", "suffixes"], "culturalElements": ["传统", "传统"], "semanticDimensions": ["传统性"]}, {"username": "委员购物表示空间范围和影响力的修饰词风雅儒雅", "morphemes": ["风雅", "儒雅", "风雅", "风雅", "儒雅", "儒雅", "风雅", "空间范围", "表示空间范围和影响力的修饰词", "购物", "和", "委员"], "categories": ["subjects", "subjects", "subjects", "traits", "traits", "traits", "traits", "modifiers", "modifiers", "actions", "connectors", "suffixes"], "culturalElements": ["传统", "传统"], "semanticDimensions": ["传统性"]}, {"username": "顾问休息资深韵味儒雅", "morphemes": ["儒雅", "儒雅", "韵味", "资深", "儒雅", "韵味", "资深", "休息", "顾问"], "categories": ["subjects", "traits", "traits", "traits", "traits", "traits", "modifiers", "actions", "suffixes"], "culturalElements": ["传统"], "semanticDimensions": ["专业性", "传统性"]}, {"username": "专家聊天高级韵味风雅", "morphemes": ["风雅", "风雅", "风雅", "韵味", "风雅", "韵味", "高级", "高级", "聊天", "专家", "专家"], "categories": ["subjects", "subjects", "traits", "traits", "traits", "traits", "modifiers", "modifiers", "actions", "suffixes", "suffixes"], "culturalElements": ["传统"], "semanticDimensions": ["专业性", "传统性"]}, {"username": "服务角色飞国际儒雅风雅", "morphemes": ["风雅", "儒雅", "风雅", "风雅", "儒雅", "儒雅", "风雅", "国际", "飞", "服务角色"], "categories": ["subjects", "subjects", "subjects", "traits", "traits", "traits", "traits", "modifiers", "actions", "suffixes"], "culturalElements": ["传统"], "semanticDimensions": ["传统性"]}, {"username": "专家特殊动作认证古典风雅", "morphemes": ["风雅", "风雅", "古典", "风雅", "风雅", "认证", "特殊", "特殊动作", "专家", "专家"], "categories": ["subjects", "subjects", "traits", "traits", "traits", "modifiers", "modifiers", "actions", "suffixes", "suffixes"], "culturalElements": ["传统", "传统", "传统"], "semanticDimensions": ["专业性", "传统性"]}, {"username": "职位后缀玩大师级韵味儒雅", "morphemes": ["儒雅", "大师", "儒雅", "韵味", "儒雅", "韵味", "大师级", "玩", "职位后缀", "师"], "categories": ["subjects", "subjects", "traits", "traits", "traits", "traits", "modifiers", "actions", "suffixes", "suffixes"], "culturalElements": ["传统"], "semanticDimensions": ["传统性"]}, {"username": "邮递员飞顶级禅意风雅", "morphemes": ["风雅", "风雅", "风雅", "禅意", "风雅", "禅意", "顶级", "顶级", "飞", "邮递员"], "categories": ["subjects", "subjects", "traits", "traits", "traits", "traits", "modifiers", "modifiers", "actions", "suffixes"], "culturalElements": ["传统"], "semanticDimensions": ["传统性"]}, {"username": "表示职位和身份的后缀刷手机世界级古典儒雅", "morphemes": ["儒雅", "古典", "儒雅", "儒雅", "世界级", "刷手机", "和", "表示职位和身份的后缀"], "categories": ["subjects", "traits", "traits", "traits", "modifiers", "actions", "connectors", "suffixes"], "culturalElements": ["传统", "传统", "传统"], "semanticDimensions": ["传统性"]}, {"username": "表示职位和身份的后缀玩史诗儒雅风雅", "morphemes": ["风雅", "儒雅", "风雅", "风雅", "儒雅", "儒雅", "风雅", "史诗", "史诗", "玩", "和", "表示职位和身份的后缀"], "categories": ["subjects", "subjects", "subjects", "traits", "traits", "traits", "traits", "modifiers", "modifiers", "actions", "connectors", "suffixes"], "culturalElements": ["传统", "传统"], "semanticDimensions": ["传统性"]}, {"username": "表示服务类型的角色后缀玩表示空间范围和影响力的修饰词韵味风雅", "morphemes": ["风雅", "风雅", "风雅", "韵味", "风雅", "韵味", "空间范围", "表示空间范围和影响力的修饰词", "玩", "和", "表示服务类型的角色后缀"], "categories": ["subjects", "subjects", "traits", "traits", "traits", "traits", "modifiers", "modifiers", "actions", "connectors", "suffixes"], "culturalElements": ["传统", "传统"], "semanticDimensions": ["传统性"]}, {"username": "代表特殊动作表示权威性和专业程度的修饰词诗意儒雅", "morphemes": ["儒雅", "诗意", "专业", "儒雅", "诗意", "儒雅", "诗意", "表示权威性和专业程度的修饰词", "专业", "特殊", "专业", "特殊动作", "和", "代表", "代表"], "categories": ["subjects", "traits", "traits", "traits", "traits", "traits", "traits", "modifiers", "modifiers", "modifiers", "modifiers", "actions", "connectors", "suffixes", "suffixes"], "culturalElements": ["传统", "传统", "传统"], "semanticDimensions": ["专业性", "传统性"]}, {"username": "贩卖机特殊动作高级雅士儒雅", "morphemes": ["儒雅", "雅士", "儒雅", "儒雅", "高级", "特殊", "高级", "特殊动作", "贩卖", "贩卖机"], "categories": ["subjects", "traits", "traits", "traits", "modifiers", "modifiers", "modifiers", "actions", "actions", "suffixes"], "culturalElements": ["传统"], "semanticDimensions": ["传统性"]}, {"username": "顾问日常生活中的基本行为表示空间范围和影响力的修饰词诗意文人", "morphemes": ["文人", "文人", "文人", "生活", "诗意", "生活", "诗意", "诗意", "空间范围", "表示空间范围和影响力的修饰词", "日常生活中的基本行为", "和", "顾问"], "categories": ["subjects", "subjects", "subjects", "traits", "traits", "traits", "traits", "traits", "modifiers", "modifiers", "actions", "connectors", "suffixes"], "culturalElements": ["传统", "传统", "传统"], "semanticDimensions": ["传统性"]}, {"username": "收集员吃资深古典风雅", "morphemes": ["风雅", "风雅", "古典", "风雅", "资深", "风雅", "资深", "吃", "收集", "收集员"], "categories": ["subjects", "subjects", "traits", "traits", "traits", "traits", "modifiers", "actions", "actions", "suffixes"], "culturalElements": ["传统", "传统", "传统"], "semanticDimensions": ["专业性", "传统性"]}, {"username": "服务角色玩特级风雅文人", "morphemes": ["文人", "风雅", "风雅", "文人", "文人", "风雅", "风雅", "特级", "玩", "服务角色"], "categories": ["subjects", "subjects", "subjects", "subjects", "subjects", "traits", "traits", "modifiers", "actions", "suffixes"], "culturalElements": ["传统", "传统"], "semanticDimensions": ["传统性"]}, {"username": "表示职位和身份的后缀日常生活中的基本行为全球儒雅风雅", "morphemes": ["风雅", "儒雅", "风雅", "生活", "风雅", "儒雅", "生活", "儒雅", "风雅", "全球", "日常生活中的基本行为", "和", "表示职位和身份的后缀"], "categories": ["subjects", "subjects", "subjects", "traits", "traits", "traits", "traits", "traits", "traits", "modifiers", "actions", "connectors", "suffixes"], "culturalElements": ["传统"], "semanticDimensions": ["传统性"]}, {"username": "顾问学习钻石儒雅儒雅", "morphemes": ["儒雅", "学习", "儒雅", "儒雅", "钻石", "学习", "顾问"], "categories": ["subjects", "traits", "traits", "traits", "modifiers", "actions", "suffixes"], "culturalElements": ["传统"], "semanticDimensions": ["传统性"]}], "creative_艺术_4": [{"username": "创意总监创作创意设计", "morphemes": ["创意总监", "创意总监", "创意", "创作", "创意", "设计", "总监"], "categories": ["subjects", "traits", "traits", "traits", "modifiers", "actions", "suffixes"], "culturalElements": [], "semanticDimensions": ["创新性"]}, {"username": "设计创意美术指导交互设计师", "morphemes": ["设计师", "设计师", "交互设计师", "设计师", "美术指导", "美术指导", "创意", "创意", "设计", "师", "设计师"], "categories": ["subjects", "subjects", "subjects", "subjects", "subjects", "traits", "traits", "modifiers", "actions", "suffixes", "suffixes"], "culturalElements": [], "semanticDimensions": ["创新性"]}, {"username": "体验设计师创意舞蹈创意", "morphemes": ["设计师", "设计师", "体验设计师", "设计师", "创意", "创意", "舞蹈", "设计", "师", "设计师"], "categories": ["subjects", "subjects", "subjects", "subjects", "traits", "modifiers", "actions", "actions", "suffixes", "suffixes"], "culturalElements": [], "semanticDimensions": ["创新性"]}, {"username": "设计创作创意元宇宙设计师", "morphemes": ["设计师", "宇宙", "元宇宙", "元宇宙设计师", "设计师", "元宇宙设计师", "设计师", "元宇宙设计师", "创意", "创作", "创意", "设计", "师", "设计师"], "categories": ["subjects", "subjects", "subjects", "subjects", "subjects", "subjects", "subjects", "subjects", "traits", "traits", "modifiers", "actions", "suffixes", "suffixes"], "culturalElements": [], "semanticDimensions": ["创新性"]}, {"username": "创意创意设计产品设计师", "morphemes": ["设计师", "设计师", "产品设计师", "设计师", "创意", "创意", "设计", "师", "设计师"], "categories": ["subjects", "subjects", "subjects", "subjects", "traits", "modifiers", "actions", "suffixes", "suffixes"], "culturalElements": [], "semanticDimensions": ["创新性"]}, {"username": "设计创意创意总监视觉设计师", "morphemes": ["设计师", "设计师", "视觉设计师", "设计师", "创意总监", "创意总监", "创意", "创意", "设计", "师", "总监", "设计师"], "categories": ["subjects", "subjects", "subjects", "subjects", "subjects", "traits", "traits", "modifiers", "actions", "suffixes", "suffixes", "suffixes"], "culturalElements": [], "semanticDimensions": ["创新性"]}, {"username": "创意设计创意总监视觉设计师", "morphemes": ["设计师", "设计师", "视觉设计师", "设计师", "创意总监", "创意总监", "创意", "创意", "设计", "师", "总监", "设计师"], "categories": ["subjects", "subjects", "subjects", "subjects", "subjects", "traits", "traits", "modifiers", "actions", "suffixes", "suffixes", "suffixes"], "culturalElements": [], "semanticDimensions": ["创新性"]}, {"username": "设计创意创意动画师", "morphemes": ["动画师", "动画师", "画师", "动画师", "画师", "创意", "创意", "设计", "师"], "categories": ["subjects", "subjects", "subjects", "subjects", "traits", "traits", "modifiers", "actions", "suffixes"], "culturalElements": [], "semanticDimensions": ["创新性"]}, {"username": "美术老师设计创意创意", "morphemes": ["美术老师", "老师", "美术老师", "创意", "创意", "设计", "师"], "categories": ["subjects", "subjects", "subjects", "traits", "modifiers", "actions", "suffixes"], "culturalElements": [], "semanticDimensions": ["创新性"]}, {"username": "创意舞蹈创意总监创意总监", "morphemes": ["创意总监", "创意总监", "创意", "创意", "舞蹈", "总监"], "categories": ["subjects", "traits", "traits", "modifiers", "actions", "suffixes"], "culturalElements": [], "semanticDimensions": ["创新性"]}, {"username": "数字艺术家创意设计创意", "morphemes": ["艺术家", "艺术家", "艺术家", "数字艺术家", "艺术家", "创意", "创意", "设计"], "categories": ["subjects", "subjects", "subjects", "subjects", "subjects", "traits", "modifiers", "actions"], "culturalElements": ["现代"], "semanticDimensions": ["创新性", "现代性"]}, {"username": "舞蹈创意创作插画师", "morphemes": ["插画师", "插画师", "画师", "插画师", "画师", "创意", "创作", "创意", "舞蹈", "师"], "categories": ["subjects", "subjects", "subjects", "subjects", "traits", "traits", "traits", "modifiers", "actions", "suffixes"], "culturalElements": [], "semanticDimensions": ["创新性"]}, {"username": "舞蹈创意创意总监音乐老师", "morphemes": ["音乐老师", "老师", "创意总监", "音乐老师", "创意总监", "创意", "创意", "舞蹈", "师", "总监"], "categories": ["subjects", "subjects", "subjects", "subjects", "traits", "traits", "modifiers", "actions", "suffixes", "suffixes"], "culturalElements": [], "semanticDimensions": ["创新性"]}, {"username": "创意型创意元宇宙设计师舞蹈", "morphemes": ["设计师", "宇宙", "元宇宙", "元宇宙设计师", "设计师", "元宇宙设计师", "设计师", "元宇宙设计师", "创意", "创意型", "创意", "舞蹈", "设计", "师", "设计师"], "categories": ["subjects", "subjects", "subjects", "subjects", "subjects", "subjects", "subjects", "subjects", "traits", "traits", "modifiers", "actions", "actions", "suffixes", "suffixes"], "culturalElements": [], "semanticDimensions": ["创新性"]}, {"username": "舞蹈创意创意体验设计师", "morphemes": ["设计师", "设计师", "体验设计师", "设计师", "创意", "创意", "舞蹈", "设计", "师", "设计师"], "categories": ["subjects", "subjects", "subjects", "subjects", "traits", "modifiers", "actions", "actions", "suffixes", "suffixes"], "culturalElements": [], "semanticDimensions": ["创新性"]}, {"username": "音乐家美术指导创意舞蹈", "morphemes": ["音乐家", "音乐家", "音乐家", "美术指导", "美术指导", "创意", "创意", "舞蹈"], "categories": ["subjects", "subjects", "subjects", "subjects", "traits", "traits", "modifiers", "actions"], "culturalElements": [], "semanticDimensions": ["创新性"]}, {"username": "产品设计师创意创意设计", "morphemes": ["设计师", "设计师", "产品设计师", "设计师", "创意", "创意", "设计", "师", "设计师"], "categories": ["subjects", "subjects", "subjects", "subjects", "traits", "modifiers", "actions", "suffixes", "suffixes"], "culturalElements": [], "semanticDimensions": ["创新性"]}, {"username": "创意创意总监设计体验设计师", "morphemes": ["设计师", "设计师", "体验设计师", "设计师", "创意总监", "创意总监", "创意", "创意", "设计", "师", "总监", "设计师"], "categories": ["subjects", "subjects", "subjects", "subjects", "subjects", "traits", "traits", "modifiers", "actions", "suffixes", "suffixes", "suffixes"], "culturalElements": [], "semanticDimensions": ["创新性"]}, {"username": "创作教学设计师创意设计", "morphemes": ["设计师", "设计师", "教学设计师", "设计师", "创意", "创作", "创意", "设计", "师", "设计师"], "categories": ["subjects", "subjects", "subjects", "subjects", "traits", "traits", "modifiers", "actions", "suffixes", "suffixes"], "culturalElements": [], "semanticDimensions": ["创新性"]}, {"username": "设计创意创作美术老师", "morphemes": ["美术老师", "老师", "美术老师", "创意", "创作", "创意", "设计", "师"], "categories": ["subjects", "subjects", "subjects", "traits", "traits", "modifiers", "actions", "suffixes"], "culturalElements": [], "semanticDimensions": ["创新性"]}], "creative_个性_2": [{"username": "创新型独家", "morphemes": ["独家", "创新", "创新型", "新型", "创新", "创新"], "categories": ["subjects", "traits", "traits", "traits", "modifiers", "modifiers"], "culturalElements": [], "semanticDimensions": ["创新性"]}, {"username": "潮流独家", "morphemes": ["独家", "潮流", "潮流"], "categories": ["subjects", "traits", "modifiers"], "culturalElements": [], "semanticDimensions": []}, {"username": "潮流独家", "morphemes": ["独家", "潮流", "潮流"], "categories": ["subjects", "traits", "modifiers"], "culturalElements": [], "semanticDimensions": []}, {"username": "前卫独家", "morphemes": ["独家", "前卫", "前卫"], "categories": ["subjects", "traits", "modifiers"], "culturalElements": [], "semanticDimensions": ["创新性"]}, {"username": "潮流独家", "morphemes": ["独家", "潮流", "潮流"], "categories": ["subjects", "traits", "modifiers"], "culturalElements": [], "semanticDimensions": []}, {"username": "独家独特性", "morphemes": ["独家", "特性", "独特性", "独特", "独特"], "categories": ["subjects", "traits", "traits", "modifiers", "modifiers"], "culturalElements": [], "semanticDimensions": ["创新性"]}, {"username": "独家创新", "morphemes": ["独家", "创新", "创新", "创新"], "categories": ["subjects", "traits", "modifiers", "modifiers"], "culturalElements": [], "semanticDimensions": ["创新性"]}, {"username": "独家创新型", "morphemes": ["独家", "创新", "创新型", "新型", "创新", "创新"], "categories": ["subjects", "traits", "traits", "traits", "modifiers", "modifiers"], "culturalElements": [], "semanticDimensions": ["创新性"]}, {"username": "独家原创", "morphemes": ["独家", "原创", "原创"], "categories": ["subjects", "traits", "modifiers"], "culturalElements": [], "semanticDimensions": ["创新性"]}, {"username": "潮流范独家", "morphemes": ["独家", "潮流", "潮流范", "潮流"], "categories": ["subjects", "traits", "traits", "modifiers"], "culturalElements": [], "semanticDimensions": []}, {"username": "独家前卫性", "morphemes": ["独家", "前卫", "前卫性", "前卫"], "categories": ["subjects", "traits", "traits", "modifiers"], "culturalElements": [], "semanticDimensions": ["创新性"]}, {"username": "原创型独家", "morphemes": ["独家", "原创", "原创型", "原创"], "categories": ["subjects", "traits", "traits", "modifiers"], "culturalElements": [], "semanticDimensions": ["创新性"]}, {"username": "独特性独家", "morphemes": ["独家", "特性", "独特性", "独特", "独特"], "categories": ["subjects", "traits", "traits", "modifiers", "modifiers"], "culturalElements": [], "semanticDimensions": ["创新性"]}, {"username": "独家原创型", "morphemes": ["独家", "原创", "原创型", "原创"], "categories": ["subjects", "traits", "traits", "modifiers"], "culturalElements": [], "semanticDimensions": ["创新性"]}, {"username": "独家前卫", "morphemes": ["独家", "前卫", "前卫"], "categories": ["subjects", "traits", "modifiers"], "culturalElements": [], "semanticDimensions": ["创新性"]}, {"username": "独家潮流", "morphemes": ["独家", "潮流", "潮流"], "categories": ["subjects", "traits", "modifiers"], "culturalElements": [], "semanticDimensions": []}, {"username": "独家新颖", "morphemes": ["独家", "新颖"], "categories": ["subjects", "traits"], "culturalElements": [], "semanticDimensions": ["创新性"]}, {"username": "独家创新型", "morphemes": ["独家", "创新", "创新型", "新型", "创新", "创新"], "categories": ["subjects", "traits", "traits", "traits", "modifiers", "modifiers"], "culturalElements": [], "semanticDimensions": ["创新性"]}, {"username": "独家创新型", "morphemes": ["独家", "创新", "创新型", "新型", "创新", "创新"], "categories": ["subjects", "traits", "traits", "traits", "modifiers", "modifiers"], "culturalElements": [], "semanticDimensions": ["创新性"]}, {"username": "潮流独家", "morphemes": ["独家", "潮流", "潮流"], "categories": ["subjects", "traits", "modifiers"], "culturalElements": [], "semanticDimensions": []}], "professional_职场_3": [{"username": "内容分析师学习顾问表示权威性和专业程度的修饰词", "morphemes": ["内容分析师", "学习", "专业", "学习顾问", "表示权威性和专业程度的修饰词", "专业", "专业", "学习", "和", "师", "顾问"], "categories": ["subjects", "traits", "traits", "traits", "modifiers", "modifiers", "modifiers", "actions", "connectors", "suffixes", "suffixes"], "culturalElements": ["传统"], "semanticDimensions": ["专业性", "传统性"]}, {"username": "运营经理安全顾问表示权威性和专业程度的修饰词", "morphemes": ["运营", "运营经理", "运营经理", "专业", "安全", "安全顾问", "表示权威性和专业程度的修饰词", "专业", "专业", "和", "顾问", "经理"], "categories": ["subjects", "subjects", "traits", "traits", "traits", "traits", "modifiers", "modifiers", "modifiers", "connectors", "suffixes", "suffixes"], "culturalElements": ["传统"], "semanticDimensions": ["专业性", "传统性"]}, {"username": "新媒体专家数据产品经理精通", "morphemes": ["产品经理", "新媒体专家", "产品经理", "产品经理", "精通", "数据产品经理", "精通", "精通", "专家", "经理", "专家"], "categories": ["subjects", "subjects", "subjects", "traits", "traits", "traits", "modifiers", "modifiers", "suffixes", "suffixes", "suffixes"], "culturalElements": ["现代"], "semanticDimensions": ["专业性", "现代性"]}, {"username": "信息安全工程师数字产品经理资深级", "morphemes": ["产品经理", "工程师", "安全工程师", "工程师", "安全工程师", "信息安全工程师", "工程师", "产品经理", "安全工程师", "产品经理", "安全", "数字产品经理", "资深", "资深", "资深级", "师", "经理"], "categories": ["subjects", "subjects", "subjects", "subjects", "subjects", "subjects", "subjects", "subjects", "subjects", "traits", "traits", "traits", "traits", "modifiers", "modifiers", "suffixes", "suffixes"], "culturalElements": ["现代"], "semanticDimensions": ["专业性", "现代性"]}, {"username": "小龙虾专家精通精通", "morphemes": ["小龙虾专家", "精通", "精通", "精通", "专家", "专家"], "categories": ["subjects", "traits", "modifiers", "modifiers", "suffixes", "suffixes"], "culturalElements": [], "semanticDimensions": ["专业性"]}, {"username": "安全分析师精通资深级", "morphemes": ["安全分析师", "精通", "安全", "资深", "资深", "精通", "资深级", "精通", "师"], "categories": ["subjects", "traits", "traits", "traits", "modifiers", "modifiers", "modifiers", "modifiers", "suffixes"], "culturalElements": [], "semanticDimensions": ["专业性"]}, {"username": "云运维工程师品牌经理资深", "morphemes": ["工程师", "运维工程师", "工程师", "运维工程师", "云运维工程师", "工程师", "品牌经理", "运维工程师", "品牌经理", "资深", "资深", "师", "经理"], "categories": ["subjects", "subjects", "subjects", "subjects", "subjects", "subjects", "subjects", "subjects", "traits", "traits", "modifiers", "suffixes", "suffixes"], "culturalElements": ["现代"], "semanticDimensions": ["专业性", "现代性"]}, {"username": "环保工程师产品经理表示权威性和专业程度的修饰词", "morphemes": ["产品经理", "工程师", "工程师", "环保工程师", "工程师", "产品经理", "产品经理", "专业", "表示权威性和专业程度的修饰词", "专业", "专业", "和", "师", "经理"], "categories": ["subjects", "subjects", "subjects", "subjects", "subjects", "subjects", "traits", "traits", "modifiers", "modifiers", "modifiers", "connectors", "suffixes", "suffixes"], "culturalElements": ["传统"], "semanticDimensions": ["专业性", "传统性"]}, {"username": "市场经理安全顾问表示权威性和专业程度的修饰词", "morphemes": ["市场经理", "市场经理", "专业", "安全", "安全顾问", "表示权威性和专业程度的修饰词", "专业", "专业", "和", "顾问", "经理"], "categories": ["subjects", "traits", "traits", "traits", "traits", "modifiers", "modifiers", "modifiers", "connectors", "suffixes", "suffixes"], "culturalElements": ["传统"], "semanticDimensions": ["专业性", "传统性"]}, {"username": "市场经理咨询顾问专业", "morphemes": ["咨询顾问", "市场经理", "市场经理", "咨询顾问", "专业", "专业", "专业", "顾问", "经理"], "categories": ["subjects", "subjects", "traits", "traits", "traits", "modifiers", "modifiers", "suffixes", "suffixes"], "culturalElements": [], "semanticDimensions": ["专业性"]}, {"username": "加密货币分析师色彩顾问专业", "morphemes": ["加密货币分析师", "专业", "色彩顾问", "专业", "专业", "师", "顾问", "货"], "categories": ["subjects", "traits", "traits", "modifiers", "modifiers", "suffixes", "suffixes", "suffixes"], "culturalElements": [], "semanticDimensions": ["专业性"]}, {"username": "市场专家云产品经理精通", "morphemes": ["产品经理", "市场专家", "产品经理", "产品经理", "精通", "云产品经理", "精通", "精通", "专家", "经理", "专家"], "categories": ["subjects", "subjects", "subjects", "traits", "traits", "traits", "modifiers", "modifiers", "suffixes", "suffixes", "suffixes"], "culturalElements": ["现代"], "semanticDimensions": ["专业性", "现代性"]}, {"username": "市场经理精通资深级", "morphemes": ["市场经理", "市场经理", "精通", "资深", "资深", "精通", "资深级", "精通", "经理"], "categories": ["subjects", "traits", "traits", "traits", "modifiers", "modifiers", "modifiers", "modifiers", "suffixes"], "culturalElements": [], "semanticDimensions": ["专业性"]}, {"username": "电商分析师运营经理精通", "morphemes": ["运营", "电商分析师", "运营经理", "运营经理", "精通", "精通", "精通", "师", "经理"], "categories": ["subjects", "subjects", "subjects", "traits", "traits", "modifiers", "modifiers", "suffixes", "suffixes"], "culturalElements": [], "semanticDimensions": ["专业性"]}, {"username": "加密货币分析师精通资深", "morphemes": ["加密货币分析师", "精通", "资深", "资深", "精通", "精通", "师", "货"], "categories": ["subjects", "traits", "traits", "modifiers", "modifiers", "modifiers", "suffixes", "suffixes"], "culturalElements": [], "semanticDimensions": ["专业性"]}, {"username": "精神内耗专业户项目经理资深", "morphemes": ["精神内耗专业户", "项目经理", "项目经理", "专业", "精神", "资深", "精神", "资深", "专业", "专业", "经理"], "categories": ["subjects", "subjects", "traits", "traits", "traits", "traits", "traits", "modifiers", "modifiers", "modifiers", "suffixes"], "culturalElements": [], "semanticDimensions": ["专业性"]}, {"username": "网络安全专家理财顾问资深级", "morphemes": ["网络安全专家", "理财顾问", "网络安全专家", "网络安全专家", "理财顾问", "安全", "资深", "资深", "资深级", "专家", "顾问", "专家"], "categories": ["subjects", "subjects", "subjects", "subjects", "traits", "traits", "traits", "modifiers", "modifiers", "suffixes", "suffixes", "suffixes"], "culturalElements": ["现代"], "semanticDimensions": ["专业性", "现代性"]}, {"username": "区块链专家资深专业", "morphemes": ["区块链", "区块链专家", "专业", "资深", "资深", "专业", "专业", "专家", "专家"], "categories": ["subjects", "subjects", "traits", "traits", "modifiers", "modifiers", "modifiers", "suffixes", "suffixes"], "culturalElements": [], "semanticDimensions": ["专业性"]}, {"username": "质量保证工程师旅游顾问精通", "morphemes": ["工程师", "工程师", "质量保证工程师", "工程师", "精通", "旅游顾问", "精通", "精通", "师", "顾问"], "categories": ["subjects", "subjects", "subjects", "subjects", "traits", "traits", "modifiers", "modifiers", "suffixes", "suffixes"], "culturalElements": [], "semanticDimensions": ["专业性"]}, {"username": "音频工程师创意总监专业", "morphemes": ["工程师", "工程师", "音频工程师", "工程师", "创意总监", "创意总监", "专业", "创意", "专业", "专业", "创意", "师", "总监"], "categories": ["subjects", "subjects", "subjects", "subjects", "subjects", "traits", "traits", "traits", "modifiers", "modifiers", "modifiers", "suffixes", "suffixes"], "culturalElements": [], "semanticDimensions": ["专业性", "创新性"]}], "casual_生活_2": [{"username": "健康饮食者舒适", "morphemes": ["健康饮食者", "舒适", "健康", "舒适"], "categories": ["subjects", "traits", "traits", "traits"], "culturalElements": [], "semanticDimensions": []}, {"username": "健康管理师阳光", "morphemes": ["健康管理师", "健康", "阳光", "阳光", "师"], "categories": ["subjects", "traits", "traits", "traits", "suffixes"], "culturalElements": [], "semanticDimensions": []}, {"username": "健康饮食者轻松", "morphemes": ["健康饮食者", "轻松", "健康"], "categories": ["subjects", "traits", "traits"], "culturalElements": [], "semanticDimensions": []}, {"username": "运动失败但健康轻松", "morphemes": ["运动失败但健康", "轻松", "健康", "但"], "categories": ["subjects", "traits", "traits", "connectors"], "culturalElements": [], "semanticDimensions": []}, {"username": "健康管理师悠闲", "morphemes": ["健康管理师", "悠闲", "健康", "师"], "categories": ["subjects", "traits", "traits", "suffixes"], "culturalElements": [], "semanticDimensions": []}, {"username": "运动失败但健康健康", "morphemes": ["运动失败但健康", "健康", "但"], "categories": ["subjects", "traits", "connectors"], "culturalElements": [], "semanticDimensions": []}, {"username": "健康管理师舒适", "morphemes": ["健康管理师", "舒适", "健康", "舒适", "师"], "categories": ["subjects", "traits", "traits", "traits", "suffixes"], "culturalElements": [], "semanticDimensions": []}, {"username": "运动失败但健康悠闲", "morphemes": ["运动失败但健康", "悠闲", "健康", "但"], "categories": ["subjects", "traits", "traits", "connectors"], "culturalElements": [], "semanticDimensions": []}, {"username": "健康饮食者轻松", "morphemes": ["健康饮食者", "轻松", "健康"], "categories": ["subjects", "traits", "traits"], "culturalElements": [], "semanticDimensions": []}, {"username": "运动失败但健康悠闲", "morphemes": ["运动失败但健康", "悠闲", "健康", "但"], "categories": ["subjects", "traits", "traits", "connectors"], "culturalElements": [], "semanticDimensions": []}, {"username": "健康管理师阳光", "morphemes": ["健康管理师", "健康", "阳光", "阳光", "师"], "categories": ["subjects", "traits", "traits", "traits", "suffixes"], "culturalElements": [], "semanticDimensions": []}, {"username": "健康饮食者简单", "morphemes": ["健康饮食者", "简单", "健康"], "categories": ["subjects", "traits", "traits"], "culturalElements": [], "semanticDimensions": []}, {"username": "运动失败但健康温馨", "morphemes": ["运动失败但健康", "温馨", "健康", "温馨", "但"], "categories": ["subjects", "traits", "traits", "traits", "connectors"], "culturalElements": [], "semanticDimensions": []}, {"username": "运动失败但健康活力", "morphemes": ["运动失败但健康", "活力", "健康", "活力", "但"], "categories": ["subjects", "traits", "traits", "traits", "connectors"], "culturalElements": [], "semanticDimensions": []}, {"username": "健康饮食者轻松", "morphemes": ["健康饮食者", "轻松", "健康"], "categories": ["subjects", "traits", "traits"], "culturalElements": [], "semanticDimensions": []}, {"username": "健康管理师轻松", "morphemes": ["健康管理师", "轻松", "健康", "师"], "categories": ["subjects", "traits", "traits", "suffixes"], "culturalElements": [], "semanticDimensions": []}, {"username": "健康饮食者舒适", "morphemes": ["健康饮食者", "舒适", "健康", "舒适"], "categories": ["subjects", "traits", "traits", "traits"], "culturalElements": [], "semanticDimensions": []}, {"username": "健康饮食者简单", "morphemes": ["健康饮食者", "简单", "健康"], "categories": ["subjects", "traits", "traits"], "culturalElements": [], "semanticDimensions": []}, {"username": "健康管理师活力", "morphemes": ["健康管理师", "活力", "健康", "活力", "师"], "categories": ["subjects", "traits", "traits", "traits", "suffixes"], "culturalElements": [], "semanticDimensions": []}, {"username": "健康管理师阳光", "morphemes": ["健康管理师", "健康", "阳光", "阳光", "师"], "categories": ["subjects", "traits", "traits", "traits", "suffixes"], "culturalElements": [], "semanticDimensions": []}]}, "diversityAnalysis": {"modern_科技_3": {"totalSamples": 20, "uniqueCount": 19, "duplicateRate": "5.00", "averageLength": "16.8", "morphemeUsage": {"totalUniqueMorphemes": 49, "mostUsed": [["产品经理", 24], ["工程师", 24], ["identity_elevation", 24], ["师", 9], ["专业", 9]], "averageUsagePerMorpheme": 4.183673469387755}, "categoryDistribution": {"subjects": 93, "traits": 33, "modifiers": 37, "suffixes": 26, "connectors": 6, "actions": 10}}, "modern_情感_4": {"totalSamples": 20, "uniqueCount": 20, "duplicateRate": "0.00", "averageLength": "14.8", "morphemeUsage": {"totalUniqueMorphemes": 36, "mostUsed": [["治愈", 40], ["温柔", 32], ["identity_elevation", 24], ["温暖", 22], ["超治愈", 20]], "averageUsagePerMorpheme": 6.305555555555555}, "categoryDistribution": {"subjects": 28, "traits": 125, "modifiers": 30, "actions": 28, "suffixes": 14, "connectors": 2}}, "classic_传统_3": {"totalSamples": 20, "uniqueCount": 20, "duplicateRate": "0.00", "averageLength": "16.4", "morphemeUsage": {"totalUniqueMorphemes": 46, "mostUsed": [["和", 15], ["专业", 12], ["儒雅", 9], ["义人", 6], ["史诗", 6]], "averageUsagePerMorpheme": 3.1739130434782608}, "categoryDistribution": {"subjects": 40, "traits": 51, "modifiers": 38, "connectors": 15, "actions": 1, "suffixes": 1}}, "classic_文化_5": {"totalSamples": 20, "uniqueCount": 20, "duplicateRate": "0.00", "averageLength": "15.8", "morphemeUsage": {"totalUniqueMorphemes": 56, "mostUsed": [["风雅", 44], ["儒雅", 33], ["文人", 9], ["韵味", 8], ["和", 7]], "averageUsagePerMorpheme": 3.625}, "categoryDistribution": {"subjects": 43, "traits": 74, "modifiers": 33, "actions": 22, "suffixes": 24, "connectors": 7}}, "creative_艺术_4": {"totalSamples": 20, "uniqueCount": 20, "duplicateRate": "0.00", "averageLength": "11.3", "morphemeUsage": {"totalUniqueMorphemes": 27, "mostUsed": [["设计师", 44], ["创意", 40], ["设计", 16], ["师", 16], ["创意总监", 12]], "averageUsagePerMorpheme": 7.296296296296297}, "categoryDistribution": {"subjects": 85, "traits": 36, "modifiers": 20, "actions": 23, "suffixes": 33}}, "creative_个性_2": {"totalSamples": 20, "uniqueCount": 15, "duplicateRate": "25.00", "averageLength": "4.5", "morphemeUsage": {"totalUniqueMorphemes": 14, "mostUsed": [["独家", 20], ["创新", 15], ["潮流", 12], ["前卫", 6], ["原创", 6]], "averageUsagePerMorpheme": 5.714285714285714}, "categoryDistribution": {"subjects": 20, "traits": 34, "modifiers": 26}}, "professional_职场_3": {"totalSamples": 20, "uniqueCount": 20, "duplicateRate": "0.00", "averageLength": "13.7", "morphemeUsage": {"totalUniqueMorphemes": 51, "mostUsed": [["专业", 27], ["精通", 24], ["资深", 16], ["工程师", 15], ["产品经理", 12]], "averageUsagePerMorpheme": 4.176470588235294}, "categoryDistribution": {"subjects": 60, "traits": 56, "modifiers": 51, "actions": 1, "connectors": 4, "suffixes": 41}}, "casual_生活_2": {"totalSamples": 20, "uniqueCount": 13, "duplicateRate": "35.00", "averageLength": "7.6", "morphemeUsage": {"totalUniqueMorphemes": 13, "mostUsed": [["健康", 20], ["健康饮食者", 7], ["健康管理师", 7], ["师", 7], ["舒适", 6]], "averageUsagePerMorpheme": 6.230769230769231}, "categoryDistribution": {"subjects": 20, "traits": 48, "suffixes": 7, "connectors": 6}}}, "newMorphemeUsage": {"modern_科技_3": {"newMorphemeCount": 28, "usageRate": "46.67"}, "modern_情感_4": {"newMorphemeCount": 93, "usageRate": "155.00"}, "classic_传统_3": {"newMorphemeCount": 19, "usageRate": "31.67"}, "classic_文化_5": {"newMorphemeCount": 15, "usageRate": "25.00"}, "creative_艺术_4": {"newMorphemeCount": 16, "usageRate": "26.67"}, "creative_个性_2": {"newMorphemeCount": 39, "usageRate": "65.00"}, "professional_职场_3": {"newMorphemeCount": 52, "usageRate": "86.67"}, "casual_生活_2": {"newMorphemeCount": 7, "usageRate": "11.67"}}, "culturalFusion": {"modern_科技_3": {"traditionalOnly": 0, "modernOnly": 11, "fusion": 9, "fusionRate": "45.00"}, "modern_情感_4": {"traditionalOnly": 0, "modernOnly": 0, "fusion": 0, "fusionRate": "0.00"}, "classic_传统_3": {"traditionalOnly": 17, "modernOnly": 0, "fusion": 3, "fusionRate": "15.00"}, "classic_文化_5": {"traditionalOnly": 20, "modernOnly": 0, "fusion": 0, "fusionRate": "0.00"}, "creative_艺术_4": {"traditionalOnly": 0, "modernOnly": 1, "fusion": 0, "fusionRate": "0.00"}, "creative_个性_2": {"traditionalOnly": 0, "modernOnly": 0, "fusion": 0, "fusionRate": "0.00"}, "professional_职场_3": {"traditionalOnly": 4, "modernOnly": 5, "fusion": 0, "fusionRate": "0.00"}, "casual_生活_2": {"traditionalOnly": 0, "modernOnly": 0, "fusion": 0, "fusionRate": "0.00"}}, "comparisonAnalysis": {"before": {"totalMorphemes": 636, "averageDuplicateRate": 25.5, "averageUniqueness": 74.5, "newMorphemeUsageRate": 0, "culturalFusionRate": 15.2}, "after": {"totalMorphemes": 2573, "averageDuplicateRate": 8.125, "averageUniqueness": 91.875, "newMorphemeUsageRate": 56.04375000000001, "culturalFusionRate": 7.5}, "improvements": {"morphemeIncrease": "304.6", "duplicateRateReduction": "17.4", "uniquenessImprovement": "17.4", "culturalFusionImprovement": "-7.7"}}}, "summary": {"averageDuplicateRate": "8.13", "averageUniqueness": "91.88", "newMorphemeUsageRate": "56.04", "culturalFusionRate": "7.50"}, "improvements": {"morphemeIncrease": "304.6", "duplicateRateReduction": "17.4", "uniquenessImprovement": "17.4", "culturalFusionImprovement": "-7.7"}}