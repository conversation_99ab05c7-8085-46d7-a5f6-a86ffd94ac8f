/**
 * 测试增强词汇库的有趣效果
 */

// 模拟增强词汇库数据
const ENHANCED_VOCABULARY = {
  // 反差萌系列
  contrast_cute: [
    { word: '佛系程序猿', interest_score: 0.95, creativity_boost: 0.9, cultural_depth: 0.8 },
    { word: '社恐主播', interest_score: 0.9, creativity_boost: 0.85, cultural_depth: 0.6 },
    { word: '躺平侠客', interest_score: 0.88, creativity_boost: 0.82, cultural_depth: 0.85 },
    { word: '内卷书生', interest_score: 0.85, creativity_boost: 0.8, cultural_depth: 0.9 },
    { word: '摆烂诗仙', interest_score: 0.92, creativity_boost: 0.88, cultural_depth: 0.95 }
  ],
  
  // 文化梗系列
  cultural_memes: [
    { word: '赛博朋克', interest_score: 0.9, creativity_boost: 0.95, cultural_depth: 0.7 },
    { word: '蒸汽波', interest_score: 0.85, creativity_boost: 0.9, cultural_depth: 0.75 },
    { word: '赛博道士', interest_score: 0.95, creativity_boost: 0.92, cultural_depth: 0.9 },
    { word: '数字游民', interest_score: 0.8, creativity_boost: 0.85, cultural_depth: 0.6 },
    { word: '元宇宙原住民', interest_score: 0.88, creativity_boost: 0.9, cultural_depth: 0.65 }
  ],
  
  // 情感共鸣系列
  emotional_resonance: [
    { word: '精神内耗怪', interest_score: 0.9, creativity_boost: 0.75, cultural_depth: 0.7 },
    { word: 'emo文学少女', interest_score: 0.85, creativity_boost: 0.8, cultural_depth: 0.75 },
    { word: '治愈系大叔', interest_score: 0.82, creativity_boost: 0.75, cultural_depth: 0.6 },
    { word: '破防专家', interest_score: 0.88, creativity_boost: 0.8, cultural_depth: 0.65 },
    { word: '松弛感大师', interest_score: 0.85, creativity_boost: 0.78, cultural_depth: 0.7 }
  ],
  
  // 创意职业系列
  creative_professions: [
    { word: '氛围感制造师', interest_score: 0.9, creativity_boost: 0.95, cultural_depth: 0.6 },
    { word: '仪式感设计师', interest_score: 0.88, creativity_boost: 0.9, cultural_depth: 0.75 },
    { word: '情绪价值输出者', interest_score: 0.85, creativity_boost: 0.8, cultural_depth: 0.7 },
    { word: '多巴胺调色师', interest_score: 0.92, creativity_boost: 0.88, cultural_depth: 0.65 },
    { word: '灵魂摆渡人', interest_score: 0.9, creativity_boost: 0.85, cultural_depth: 0.9 }
  ],
  
  // 网络文化系列
  internet_culture: [
    { word: '表情包大师', interest_score: 0.9, creativity_boost: 0.85, cultural_depth: 0.6 },
    { word: '弹幕诗人', interest_score: 0.88, creativity_boost: 0.9, cultural_depth: 0.75 },
    { word: '热梗考古学家', interest_score: 0.92, creativity_boost: 0.88, cultural_depth: 0.7 },
    { word: '流量密码破译师', interest_score: 0.85, creativity_boost: 0.82, cultural_depth: 0.6 },
    { word: '算法驯兽师', interest_score: 0.9, creativity_boost: 0.88, cultural_depth: 0.65 }
  ]
}

// 计算综合有趣程度
function calculateInterestScore(entry) {
  const weights = {
    interest_score: 0.4,
    creativity_boost: 0.3,
    cultural_depth: 0.2,
    combination_potential: 0.1
  }
  
  return (
    entry.interest_score * weights.interest_score +
    entry.creativity_boost * weights.creativity_boost +
    entry.cultural_depth * weights.cultural_depth +
    (entry.combination_potential || 0.8) * weights.combination_potential
  )
}

// 生成有趣的用户名组合
function generateInterestingUsernames() {
  const results = []
  
  // 从每个类别选择最有趣的词汇
  Object.entries(ENHANCED_VOCABULARY).forEach(([category, words]) => {
    const topWords = words
      .sort((a, b) => calculateInterestScore(b) - calculateInterestScore(a))
      .slice(0, 3)
    
    topWords.forEach(word => {
      results.push({
        username: word.word,
        category,
        overall_score: calculateInterestScore(word),
        interest_level: getInterestLevel(calculateInterestScore(word)),
        appeal_factors: getAppealFactors(word)
      })
    })
  })
  
  return results.sort((a, b) => b.overall_score - a.overall_score)
}

// 获取有趣程度等级
function getInterestLevel(score) {
  if (score >= 0.9) return '极度有趣 🔥'
  if (score >= 0.85) return '非常有趣 ⭐'
  if (score >= 0.8) return '很有趣 👍'
  if (score >= 0.75) return '比较有趣 😊'
  return '一般有趣 😐'
}

// 获取吸引力因素
function getAppealFactors(word) {
  const factors = []
  
  if (word.interest_score >= 0.9) factors.push('超高趣味性')
  if (word.creativity_boost >= 0.9) factors.push('极强创意感')
  if (word.cultural_depth >= 0.8) factors.push('深厚文化底蕴')
  if (word.interest_score >= 0.85 && word.creativity_boost >= 0.85) factors.push('创意与趣味并存')
  
  // 特殊组合判断
  if (word.word.includes('佛系') || word.word.includes('躺平')) factors.push('现代生活共鸣')
  if (word.word.includes('赛博') || word.word.includes('元宇宙')) factors.push('科技未来感')
  if (word.word.includes('诗仙') || word.word.includes('侠客')) factors.push('传统文化魅力')
  if (word.word.includes('emo') || word.word.includes('社恐')) factors.push('年轻人情感共鸣')
  
  return factors
}

// 分析反差萌效果
function analyzeContrastEffect() {
  console.log('\n🎭 反差萌效果分析:')
  console.log('='.repeat(50))
  
  const contrastWords = ENHANCED_VOCABULARY.contrast_cute
  
  contrastWords.forEach((word, index) => {
    const score = calculateInterestScore(word)
    console.log(`${index + 1}. ${word.word}`)
    console.log(`   综合评分: ${(score * 100).toFixed(1)}%`)
    console.log(`   反差效果: ${analyzeContrastElements(word.word)}`)
    console.log(`   记忆点: ${getMemoryPoints(word.word)}`)
    console.log('')
  })
}

// 分析反差元素
function analyzeContrastElements(username) {
  const traditional = ['佛系', '侠客', '书生', '诗仙', '道士']
  const modern = ['程序猿', '主播', '内卷', '躺平', '摆烂']
  
  const hasTraditional = traditional.some(t => username.includes(t))
  const hasModern = modern.some(m => username.includes(m))
  
  if (hasTraditional && hasModern) {
    return '古今碰撞，反差强烈'
  } else if (hasTraditional) {
    return '传统文化韵味'
  } else if (hasModern) {
    return '现代网络特色'
  }
  return '中性表达'
}

// 获取记忆点
function getMemoryPoints(username) {
  const points = []
  
  if (username.length <= 4) points.push('简短易记')
  if (/[仙圣侠客]/.test(username)) points.push('武侠风格')
  if (/[佛系躺平摆烂]/.test(username)) points.push('生活态度')
  if (/[程序猿主播]/.test(username)) points.push('职业特色')
  if (/[emo社恐内卷]/.test(username)) points.push('网络热词')
  
  return points.join(', ')
}

// 主测试函数
function testEnhancedVocabulary() {
  console.log('🎯 增强词汇库有趣效果测试')
  console.log('='.repeat(60))
  
  // 生成有趣用户名
  const interestingUsernames = generateInterestingUsernames()
  
  console.log('📊 测试统计:')
  console.log(`总测试词汇: ${interestingUsernames.length}个`)
  console.log(`平均有趣程度: ${(interestingUsernames.reduce((sum, u) => sum + u.overall_score, 0) / interestingUsernames.length * 100).toFixed(1)}%`)
  
  // 展示Top 10最有趣的用户名
  console.log('\n🏆 Top 10 最有趣用户名:')
  console.log('-'.repeat(50))
  
  interestingUsernames.slice(0, 10).forEach((result, index) => {
    console.log(`${index + 1}. ${result.username}`)
    console.log(`   类别: ${result.category}`)
    console.log(`   综合评分: ${(result.overall_score * 100).toFixed(1)}%`)
    console.log(`   有趣程度: ${result.interest_level}`)
    console.log(`   吸引力: ${result.appeal_factors.join(', ')}`)
    console.log('')
  })
  
  // 按类别分析
  console.log('\n📈 各类别表现分析:')
  console.log('-'.repeat(50))
  
  Object.keys(ENHANCED_VOCABULARY).forEach(category => {
    const categoryWords = interestingUsernames.filter(u => u.category === category)
    const avgScore = categoryWords.reduce((sum, u) => sum + u.overall_score, 0) / categoryWords.length
    
    console.log(`${getCategoryName(category)}: ${(avgScore * 100).toFixed(1)}% (${categoryWords.length}个词汇)`)
  })
  
  // 反差萌效果分析
  analyzeContrastEffect()
  
  // 应用建议
  console.log('\n💡 应用建议:')
  console.log('-'.repeat(50))
  console.log('1. 优先集成反差萌系列 - 平均评分最高，用户接受度好')
  console.log('2. 重点开发文化梗系列 - 创意性强，传播潜力大')
  console.log('3. 情感共鸣系列适合年轻用户 - 贴近现代人心理状态')
  console.log('4. 创意职业系列具有前瞻性 - 符合新兴职业趋势')
  console.log('5. 网络文化系列紧跟热点 - 时效性强，需定期更新')
  
  console.log('\n✅ 增强词汇库测试完成！')
  console.log('🚀 建议立即集成到V5引擎中，提升生成趣味性！')
}

// 获取类别中文名
function getCategoryName(category) {
  const names = {
    contrast_cute: '反差萌系列',
    cultural_memes: '文化梗系列',
    emotional_resonance: '情感共鸣系列',
    creative_professions: '创意职业系列',
    internet_culture: '网络文化系列'
  }
  return names[category] || category
}

// 运行测试
testEnhancedVocabulary()
