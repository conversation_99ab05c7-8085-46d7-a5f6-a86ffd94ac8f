// https://nuxt.com/docs/api/configuration/nuxt-config
import { defineNuxtConfig } from 'nuxt/config';

export default defineNuxtConfig({
  compatibilityDate: '2025-05-15',
  devtools: { 
    enabled: true,
    vscode: {
      enabled: true
    }
  },
  
  // Vue 配置
  vue: {
    compilerOptions: {
      isCustomElement: (tag: string) => ['content'].includes(tag),
      whitespace: 'condense',
      comments: false,
      // 禁用生产环境下的警告
      onWarn: () => null
    },
    runtimeCompiler: false
  },
  
  // 模块配置
  modules: [
    '@nuxtjs/color-mode',
    '@unocss/nuxt',
    '@nuxtjs/i18n',
    '@nuxt/image',
  ],
  
  // 静态资源配置
  nitro: {
    publicAssets: [
      {
        dir: 'public',
        baseURL: '/'
      }
    ]
  },
  
  // 构建配置
  build: {
    transpile: ['@headlessui/vue'],
  },
  
  // 组件自动导入
  components: [
    {
      path: '~/components',
      pathPrefix: false
    }
  ],
  
  // UnoCSS 配置
  unocss: {
    // 预设
    preflight: true, // 启用基础样式重置
    uno: true, // 启用默认预设
    attributify: true, // 启用属性化模式
    icons: {
      scale: 1.2,
      extraProperties: {
        'display': 'inline-block',
        'vertical-align': 'middle',
      }
    }, // 启用图标支持
    

    rules: [
      // 自定义阴影
      ['shadow-soft', { 'box-shadow': '0 4px 12px -2px rgba(0, 0, 0, 0.05), 0 2px 4px -2px rgba(0, 0, 0, 0.03)' }],
      // 自定义过渡
      ['transition-all-200', { 'transition': 'all 0.2s ease-in-out' }],
    ],
    
    // 主题配置
    theme: {
      colors: {
        primary: {
          DEFAULT: '#3B82F6',
          50: '#EFF6FF',
          100: '#DBEAFE',
          200: '#BFDBFE',
          300: '#93C5FD',
          400: '#60A5FA',
          500: '#3B82F6',
          600: '#2563EB',
          700: '#1D4ED8',
          800: '#1E40AF',
          900: '#1E3A8A',
        },
      },
      fontFamily: {
        sans: ['Inter var', 'Inter', 'system-ui', 'sans-serif'],
        mono: ['Fira Code', 'monospace'],
      },
    },
    
    // 安全列表
    safelist: [
      // 动态类名
      ...Array.from({ length: 10 }, (_, i) => `opacity-${(i + 1) * 10}`),
      // 动态间距
      ...['p', 'm', 'w', 'h', 'text', 'bg'].map(prefix => 
        Array.from({ length: 24 }, (_, i) => `${prefix}-${(i + 1) * 4}`)
      ).flat(),
    ],
  },
  
  // 颜色模式配置
  colorMode: {
    classSuffix: '',
    preference: 'system', // 默认跟随系统
    fallback: 'light',   // 默认使用亮色主题
  },
  
  // 国际化配置
  i18n: {
    locales: [
      { code: 'en', file: 'en.json', name: 'English' },
      { code: 'zh', file: 'zh.json', name: '中文' }
    ],
    bundle: {
      optimizeTranslationDirective: false // 明确设置为 false
    },
    defaultLocale: 'en',
    strategy: 'prefix_except_default',
    lazy: false,
    langDir: 'locales',
    detectBrowserLanguage: {
      useCookie: true,
      cookieKey: 'i18n_redirected',
      redirectOn: 'root'
    }
  },
  
  // 类型检查 - 调整配置以支持V2系统
  typescript: {
    strict: false,
    shim: false,
    typeCheck: false
  },
  
  // 源目录配置
  srcDir: '.',
  
  // 实验性功能
  experimental: {
    payloadExtraction: false,
    renderJsonPayloads: true,
    viewTransition: true,
    typedPages: true
  },
  // Vite 配置
  vite: {
    logLevel: 'warn',
    server: {
      hmr: {
        overlay: false
      }
    },
    // 优化依赖预构建
    // optimizeDeps: {
    //   include: ['vue', 'vue-router', '@vueuse/core']
    // },
    // 构建优化
    build: {
      rollupOptions: {
        output: {
          manualChunks: {
            'vue-vendor': ['vue', 'vue-router', 'pinia']
          }
        }
      },
      chunkSizeWarningLimit: 1000
    }
  }
})
