<script setup lang="ts">
// 定义组件名称
defineOptions({
  name: 'App'
});
</script>

<template>
  <div class="min-h-screen bg-white dark:bg-gray-900 text-gray-800 dark:text-gray-100 transition-colors duration-200">
    <NuxtLayout>
      <NuxtPage />
    </NuxtLayout>
  </div>
</template>

<style>
/* 基础样式重置 */
:root {
  --color-primary: #3B82F6;
  --color-primary-light: #60A5FA;
  --color-primary-dark: #2563EB;
  --color-text: #1F2937;
  --color-text-light: #6B7280;
  --color-bg: #FFFFFF;
  --color-bg-secondary: #F9FAFB;
  --color-border: #E5E7EB;
}

.dark {
  --color-text: #F9FAFB;
  --color-text-light: #9CA3AF;
  --color-bg: #111827;
  --color-bg-secondary: #1F2937;
  --color-border: #374151;
}

/* 平滑滚动 */
html {
  scroll-behavior: smooth;
}

/* 基础样式 */
body {
  font-family: 'Inter var', system-ui, -apple-system, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</style>