/* eslint-disable no-restricted-globals */
import { generateCulturalUsername } from '../core/UsernameGenerator'

interface WorkerRequest {
  id: string
  cmd: 'generate'
  options: Record<string, any>
}

self.addEventListener('message', async (event: MessageEvent<WorkerRequest>) => {
  const { id, cmd, options } = event.data
  if (cmd === 'generate') {
    try {
      const name = await generateCulturalUsername(options)
      ;(self as DedicatedWorkerGlobalScope).postMessage({ id, name })
    } catch (error) {
      ;(self as DedicatedWorkerGlobalScope).postMessage({ id, error: String(error) })
    }
  }
})
