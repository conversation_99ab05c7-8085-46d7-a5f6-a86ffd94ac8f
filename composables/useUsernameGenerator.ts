import { ref } from 'vue'
import { generateCulturalUsername, type GenerateOptions } from '../core/UsernameGenerator'
// Vite 内置 ?worker 语法
// @ts-ignore
import UsernameWorker from '~/workers/usernameWorker?worker'

export function useUsernameGenerator() {
  const loading = ref(false)
  const username = ref('')

  async function generate(options: GenerateOptions = {}) {
    loading.value = true
    try {
      if (process.client) {
        const worker = new UsernameWorker()
        const id = Math.random().toString(36).slice(2)
        return await new Promise<string>((resolve, reject) => {
          const listener = (e: MessageEvent<any>) => {
            if (e.data?.id !== id) return
            worker.removeEventListener('message', listener)
            if (e.data.error) reject(new Error(e.data.error))
            else resolve(e.data.name)
          }
          worker.addEventListener('message', listener)
          worker.postMessage({ id, cmd: 'generate', options })
        }).then((name) => {
          username.value = name
          return name
        })
      } else {
        const name = await generateCulturalUsername(options)
        username.value = name
        return name
      }
    } finally {
      loading.value = false
    }
  }

  return {
    loading,
    username,
    generate
  }
}
