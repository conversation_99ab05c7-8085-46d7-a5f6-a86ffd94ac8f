import { ref, reactive, computed } from 'vue'

// 缓存对象，用于存储已获取的数据
const cache = reactive<Record<string, { data: any, timestamp: number }>>({})

// 缓存过期时间（毫秒）
const CACHE_EXPIRY = {
  trends: 24 * 60 * 60 * 1000, // 1天
  cultural: 24 * 60 * 60 * 1000, // 1天
  base: 7 * 24 * 60 * 60 * 1000 // 7天
}

export function useDataFetcher() {
  const isLoading = ref(false)
  const error = ref<string | null>(null)
  
  // 检查缓存是否有效
  function isCacheValid(cacheKey: string, type: 'trends' | 'cultural' | 'base'): boolean {
    if (!cache[cacheKey]) return false
    
    const now = Date.now()
    const cacheTime = cache[cacheKey].timestamp
    return (now - cacheTime) < CACHE_EXPIRY[type]
  }
  
  // 获取趋势数据
  async function fetchTrendData(language: string) {
    const cacheKey = `trends_${language}`
    
    // 如果缓存有效，直接返回缓存数据
    if (isCacheValid(cacheKey, 'trends')) {
      return cache[cacheKey].data
    }
    
    isLoading.value = true
    error.value = null
    
    try {
      const response = await fetch(`/api/trends/${language}`)
      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error || '获取趋势数据失败')
      }
      
      // 更新缓存
      cache[cacheKey] = {
        data: result.data,
        timestamp: Date.now()
      }
      
      return result.data
    } catch (err: any) {
      error.value = err.message || '获取趋势数据失败'
      return null
    } finally {
      isLoading.value = false
    }
  }
  
  // 获取文化数据
  async function fetchCulturalData(language: string, category: string) {
    const cacheKey = `cultural_${language}_${category}`
    
    // 如果缓存有效，直接返回缓存数据
    if (isCacheValid(cacheKey, 'cultural')) {
      return cache[cacheKey].data
    }
    
    isLoading.value = true
    error.value = null
    
    try {
      const response = await fetch(`/api/cultural/${language}/${category}`)
      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error || '获取文化数据失败')
      }
      
      // 更新缓存
      cache[cacheKey] = {
        data: result.data,
        timestamp: Date.now()
      }
      
      return result.data
    } catch (err: any) {
      error.value = err.message || '获取文化数据失败'
      return null
    } finally {
      isLoading.value = false
    }
  }
  
  // 获取基础词汇数据
  async function fetchBaseData(language: string, type: string) {
    const cacheKey = `base_${language}_${type}`
    
    // 如果缓存有效，直接返回缓存数据
    if (isCacheValid(cacheKey, 'base')) {
      return cache[cacheKey].data
    }
    
    isLoading.value = true
    error.value = null
    
    try {
      const response = await fetch(`/api/base/${language}/${type}`)
      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error || '获取基础词汇数据失败')
      }
      
      // 更新缓存
      cache[cacheKey] = {
        data: result.data,
        timestamp: Date.now()
      }
      
      return result.data
    } catch (err: any) {
      error.value = err.message || '获取基础词汇数据失败'
      return null
    } finally {
      isLoading.value = false
    }
  }
  
  // 清除指定缓存
  function clearCache(type?: 'trends' | 'cultural' | 'base', language?: string) {
    if (!type) {
      // 清除所有缓存
      Object.keys(cache).forEach(key => delete cache[key])
      return
    }
    
    if (!language) {
      // 清除指定类型的所有缓存
      Object.keys(cache)
        .filter(key => key.startsWith(`${type}_`))
        .forEach(key => delete cache[key])
      return
    }
    
    // 清除指定类型和语言的缓存
    Object.keys(cache)
      .filter(key => key.startsWith(`${type}_${language}`))
      .forEach(key => delete cache[key])
  }
  
  return {
    isLoading: computed(() => isLoading.value),
    error: computed(() => error.value),
    fetchTrendData,
    fetchCulturalData,
    fetchBaseData,
    clearCache
  }
} 