import fs from 'fs'
import path from 'path'
import { defineEventHand<PERSON>, readBody, createError } from 'h3'

// 简单的API密钥验证（实际生产环境应使用更安全的方法）
const API_KEY = process.env.ADMIN_API_KEY || 'namer-admin-key'

export default defineEventHandler(async (event) => {
  try {
    // 获取请求体
    const body = await readBody(event)
    
    // 验证API密钥
    const apiKey = event.node.req.headers['x-api-key']
    if (apiKey !== API_KEY) {
      throw createError({
        statusCode: 401,
        message: '未授权访问'
      })
    }
    
    // 验证请求数据
    if (!body.language || !body.data) {
      throw createError({
        statusCode: 400,
        message: '缺少必要参数'
      })
    }
    
    // 验证语言参数
    const supportedLanguages = ['en', 'zh', 'ja', 'ru']
    if (!supportedLanguages.includes(body.language)) {
      throw createError({
        statusCode: 400,
        message: `不支持的语言: ${body.language}`
      })
    }
    
    // 构建数据文件路径
    const dataPath = path.resolve(process.cwd(), 'data', 'trends', 'current', `${body.language}.json`)
    
    // 更新数据文件
    try {
      // 确保数据格式正确
      const trendData = {
        meta: {
          language: body.language,
          period: body.data.meta?.period || `${new Date().getFullYear()}-Q${Math.floor(new Date().getMonth() / 3) + 1}`,
          version: body.data.meta?.version || '1.0.0',
          updated_at: body.data.meta?.updated_at || new Date().toISOString().split('T')[0]
        },
        trending_words: Array.isArray(body.data.trending_words) ? body.data.trending_words : [],
        trending_phrases: Array.isArray(body.data.trending_phrases) ? body.data.trending_phrases : [],
        cultural_references: Array.isArray(body.data.cultural_references) ? body.data.cultural_references : []
      }
      
      // 写入文件
      fs.writeFileSync(dataPath, JSON.stringify(trendData, null, 2), 'utf-8')
      
      return {
        success: true,
        message: `成功更新${body.language}语言的趋势数据`
      }
    } catch (writeError: any) {
      throw createError({
        statusCode: 500,
        message: `写入文件失败: ${writeError.message}`
      })
    }
  } catch (error: any) {
    // 错误处理
    return {
      success: false,
      error: error.message || '更新趋势数据失败'
    }
  }
}) 