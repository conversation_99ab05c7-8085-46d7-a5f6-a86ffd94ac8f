import fs from 'fs'
import path from 'path'
import { defineEventHand<PERSON>, readBody, createError } from 'h3'

// 简单的API密钥验证（实际生产环境应使用更安全的方法）
const API_KEY = process.env.ADMIN_API_KEY || 'namer-admin-key'

export default defineEventHandler(async (event) => {
  try {
    // 获取请求体
    const body = await readBody(event)
    
    // 验证API密钥
    const apiKey = event.node.req.headers['x-api-key']
    if (apiKey !== API_KEY) {
      throw createError({
        statusCode: 401,
        message: '未授权访问'
      })
    }
    
    // 验证请求数据
    if (!body.language || !body.category || !body.data) {
      throw createError({
        statusCode: 400,
        message: '缺少必要参数'
      })
    }
    
    // 验证语言参数
    const supportedLanguages = ['en', 'zh', 'ja', 'ru']
    if (!supportedLanguages.includes(body.language)) {
      throw createError({
        statusCode: 400,
        message: `不支持的语言: ${body.language}`
      })
    }
    
    // 验证类别参数
    const supportedCategories = ['internet', 'traditional', 'pop']
    if (!supportedCategories.includes(body.category)) {
      throw createError({
        statusCode: 400,
        message: `不支持的类别: ${body.category}`
      })
    }
    
    // 确保目录存在
    const dirPath = path.resolve(process.cwd(), 'data', 'cultural', body.language)
    if (!fs.existsSync(dirPath)) {
      fs.mkdirSync(dirPath, { recursive: true })
    }
    
    // 构建数据文件路径
    const dataPath = path.resolve(dirPath, `${body.category}.json`)
    
    // 更新数据文件
    try {
      // 确保数据格式正确
      const culturalData = {
        meta: {
          language: body.language,
          category: body.category,
          version: body.data.meta?.version || '1.0.0',
          updated_at: body.data.meta?.updated_at || new Date().toISOString().split('T')[0]
        },
        patterns: Array.isArray(body.data.patterns) ? body.data.patterns : []
      }
      
      // 写入文件
      fs.writeFileSync(dataPath, JSON.stringify(culturalData, null, 2), 'utf-8')
      
      return {
        success: true,
        message: `成功更新${body.language}语言的${body.category}文化数据`
      }
    } catch (writeError: any) {
      throw createError({
        statusCode: 500,
        message: `写入文件失败: ${writeError.message}`
      })
    }
  } catch (error: any) {
    // 错误处理
    return {
      success: false,
      error: error.message || '更新文化数据失败'
    }
  }
}) 