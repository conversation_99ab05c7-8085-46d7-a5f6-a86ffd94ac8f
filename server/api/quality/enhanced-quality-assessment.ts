/**
 * 增强质量评估引擎
 * 实现权重优化和新维度评估
 * 
 * @version 2.0.0
 * @created 2025-06-16
 */

/**
 * 优化后的质量评估权重配置
 */
export interface OptimizedQualityWeights {
  novelty: number              // 新颖性 (15% → 18%)
  relevance: number            // 相关性 (15% → 14%)
  comprehensibility: number    // 可理解性 (12% → 12%)
  memorability: number         // 记忆性 (12% → 13%)
  cultural_fit: number         // 文化适配 (13% → 12%)
  target_audience: number      // 目标受众 (13% → 11%)
  semantic_coherence: number   // 语义连贯 (10% → 8%)
  cultural_depth: number       // 文化深度 (10% → 12%)
}

/**
 * 扩展质量评估结果接口
 */
export interface EnhancedQualityAssessment {
  // 原有8维度
  novelty: number
  relevance: number
  comprehensibility: number
  memorability: number
  cultural_fit: number
  target_audience: number
  semantic_coherence: number
  cultural_depth: number
  
  // 新增2维度
  creativity_breakthrough: number    // 创意突破度
  emotional_resonance: number       // 情感共鸣度
  
  // 综合评估
  overall_score: number
  quality_grade: 'A+' | 'A' | 'B+' | 'B' | 'C'
  
  // 详细分析
  strengths: string[]
  improvement_suggestions: string[]
  optimization_recommendations: string[]
}

/**
 * 增强质量评估引擎类
 */
export class EnhancedQualityAssessmentEngine {
  private weights: OptimizedQualityWeights
  private initialized: boolean

  constructor() {
    // 优化后的权重配置
    this.weights = {
      novelty: 0.18,              // 提升新颖性权重
      relevance: 0.14,            // 略降相关性权重
      comprehensibility: 0.12,    // 保持可理解性权重
      memorability: 0.13,         // 略提升记忆性权重
      cultural_fit: 0.12,         // 略降文化适配权重
      target_audience: 0.11,      // 略降目标受众权重
      semantic_coherence: 0.08,   // 略降语义连贯权重
      cultural_depth: 0.12        // 提升文化深度权重
    }
    this.initialized = false
  }

  /**
   * 初始化增强质量评估引擎
   */
  async initialize(): Promise<void> {
    if (this.initialized) return

    console.log('🎯 初始化增强质量评估引擎...')
    
    // 验证权重总和
    const totalWeight = Object.values(this.weights).reduce((sum, weight) => sum + weight, 0)
    if (Math.abs(totalWeight - 1.0) > 0.001) {
      throw new Error(`权重总和不等于1.0: ${totalWeight}`)
    }
    
    this.initialized = true
    console.log('✅ 增强质量评估引擎初始化完成')
    console.log('📊 优化后权重分布:')
    Object.entries(this.weights).forEach(([dimension, weight]) => {
      console.log(`   ${dimension}: ${(weight * 100).toFixed(1)}%`)
    })
  }

  /**
   * 执行增强质量评估
   */
  async assessEnhancedQuality(
    username: string,
    pattern: string,
    elements: string[],
    context?: any
  ): Promise<EnhancedQualityAssessment> {
    if (!this.initialized) {
      await this.initialize()
    }

    // 评估原有8维度
    const originalDimensions = await this.assessOriginalDimensions(username, pattern, elements, context)
    
    // 评估新增2维度
    const newDimensions = await this.assessNewDimensions(username, pattern, elements, context)
    
    // 计算综合评分
    const overallScore = this.calculateOverallScore(originalDimensions, newDimensions)
    
    // 确定质量等级
    const qualityGrade = this.determineQualityGrade(overallScore)
    
    // 生成分析和建议
    const analysis = this.generateAnalysisAndRecommendations(
      username, 
      { ...originalDimensions, ...newDimensions }, 
      overallScore
    )

    return {
      ...originalDimensions,
      ...newDimensions,
      overall_score: overallScore,
      quality_grade: qualityGrade,
      ...analysis
    }
  }

  /**
   * 评估原有8维度
   */
  private async assessOriginalDimensions(
    username: string,
    pattern: string,
    elements: string[],
    context?: any
  ): Promise<{
    novelty: number
    relevance: number
    comprehensibility: number
    memorability: number
    cultural_fit: number
    target_audience: number
    semantic_coherence: number
    cultural_depth: number
  }> {
    return {
      novelty: await this.assessNovelty(username, elements),
      relevance: await this.assessRelevance(username, context),
      comprehensibility: await this.assessComprehensibility(username),
      memorability: await this.assessMemorability(username),
      cultural_fit: await this.assessCulturalFit(username, elements),
      target_audience: await this.assessTargetAudience(username, context),
      semantic_coherence: await this.assessSemanticCoherence(username, elements),
      cultural_depth: await this.assessCulturalDepth(username, elements)
    }
  }

  /**
   * 评估新增2维度
   */
  private async assessNewDimensions(
    username: string,
    pattern: string,
    elements: string[],
    context?: any
  ): Promise<{
    creativity_breakthrough: number
    emotional_resonance: number
  }> {
    return {
      creativity_breakthrough: await this.assessCreativityBreakthrough(username, elements, pattern),
      emotional_resonance: await this.assessEmotionalResonance(username, elements, context)
    }
  }

  /**
   * 评估新颖性 (权重提升至18%)
   */
  private async assessNovelty(username: string, elements: string[]): Promise<number> {
    let score = 0.5 // 基础分

    // 元素组合新颖性
    const combinationNovelty = this.assessCombinationNovelty(elements)
    score += combinationNovelty * 0.4

    // 词汇新颖性
    const wordNovelty = this.assessWordNovelty(username)
    score += wordNovelty * 0.3

    // 结构新颖性
    const structureNovelty = this.assessStructureNovelty(username)
    score += structureNovelty * 0.3

    return Math.min(1, score)
  }

  /**
   * 评估文化深度 (权重提升至12%)
   */
  private async assessCulturalDepth(username: string, elements: string[]): Promise<number> {
    let score = 0.3 // 基础分

    // 文化元素丰富度
    const culturalRichness = this.assessCulturalRichness(elements)
    score += culturalRichness * 0.4

    // 文化内涵深度
    const culturalConnotation = this.assessCulturalConnotation(username)
    score += culturalConnotation * 0.3

    // 文化传承价值
    const culturalValue = this.assessCulturalValue(username, elements)
    score += culturalValue * 0.3

    return Math.min(1, score)
  }

  /**
   * 评估创意突破度 (新维度)
   */
  private async assessCreativityBreakthrough(username: string, elements: string[], pattern: string): Promise<number> {
    let score = 0.4 // 基础分

    // 跨界融合创意
    const crossDomainCreativity = this.assessCrossDomainCreativity(elements)
    score += crossDomainCreativity * 0.3

    // 概念创新程度
    const conceptualInnovation = this.assessConceptualInnovation(username)
    score += conceptualInnovation * 0.3

    // 表达方式突破
    const expressionBreakthrough = this.assessExpressionBreakthrough(username, pattern)
    score += expressionBreakthrough * 0.4

    return Math.min(1, score)
  }

  /**
   * 评估情感共鸣度 (新维度)
   */
  private async assessEmotionalResonance(username: string, elements: string[], context?: any): Promise<number> {
    let score = 0.4 // 基础分

    // 情感表达强度
    const emotionalIntensity = this.assessEmotionalIntensity(username, elements)
    score += emotionalIntensity * 0.4

    // 情感传达准确性
    const emotionalAccuracy = this.assessEmotionalAccuracy(username, context)
    score += emotionalAccuracy * 0.3

    // 情感感染力
    const emotionalImpact = this.assessEmotionalImpact(username)
    score += emotionalImpact * 0.3

    return Math.min(1, score)
  }

  /**
   * 计算综合评分
   */
  private calculateOverallScore(
    originalDimensions: any,
    newDimensions: any
  ): number {
    let totalScore = 0

    // 原有8维度加权
    for (const [dimension, weight] of Object.entries(this.weights)) {
      if (originalDimensions[dimension] !== undefined) {
        totalScore += originalDimensions[dimension] * weight
      }
    }

    // 新增2维度 (各占2%权重)
    totalScore += newDimensions.creativity_breakthrough * 0.02
    totalScore += newDimensions.emotional_resonance * 0.02

    return Math.min(1, totalScore)
  }

  /**
   * 确定质量等级
   */
  private determineQualityGrade(score: number): 'A+' | 'A' | 'B+' | 'B' | 'C' {
    if (score >= 0.95) return 'A+'
    if (score >= 0.90) return 'A'
    if (score >= 0.85) return 'B+'
    if (score >= 0.75) return 'B'
    return 'C'
  }

  /**
   * 生成分析和建议
   */
  private generateAnalysisAndRecommendations(
    username: string,
    dimensions: any,
    overallScore: number
  ): {
    strengths: string[]
    improvement_suggestions: string[]
    optimization_recommendations: string[]
  } {
    const strengths: string[] = []
    const improvements: string[] = []
    const optimizations: string[] = []

    // 识别优势
    if (dimensions.novelty >= 0.85) strengths.push('新颖性突出')
    if (dimensions.cultural_depth >= 0.85) strengths.push('文化内涵深厚')
    if (dimensions.creativity_breakthrough >= 0.85) strengths.push('创意突破性强')
    if (dimensions.emotional_resonance >= 0.85) strengths.push('情感共鸣度高')

    // 识别改进点
    if (dimensions.novelty < 0.7) improvements.push('可增强新颖性表达')
    if (dimensions.cultural_depth < 0.7) improvements.push('可深化文化内涵')
    if (dimensions.creativity_breakthrough < 0.7) improvements.push('可提升创意突破度')
    if (dimensions.emotional_resonance < 0.7) improvements.push('可增强情感共鸣')

    // 生成优化建议
    if (overallScore < 0.9) {
      optimizations.push('建议增加更多创新元素组合')
      optimizations.push('建议深化文化元素的融合程度')
    }
    if (dimensions.novelty < 0.8) {
      optimizations.push('建议尝试跨领域元素组合')
    }
    if (dimensions.cultural_depth < 0.8) {
      optimizations.push('建议增加传统文化元素')
    }

    return {
      strengths,
      improvement_suggestions: improvements,
      optimization_recommendations: optimizations
    }
  }

  // 辅助评估方法
  private assessCombinationNovelty(elements: string[]): number {
    // 评估元素组合的新颖性
    return Math.min(1, 0.3 + elements.length * 0.1 + Math.random() * 0.3)
  }

  private assessWordNovelty(username: string): number {
    // 评估词汇本身的新颖性
    return Math.min(1, 0.4 + (username.length > 3 ? 0.2 : 0) + Math.random() * 0.3)
  }

  private assessStructureNovelty(username: string): number {
    // 评估结构的新颖性
    return Math.min(1, 0.3 + Math.random() * 0.4)
  }

  private assessCulturalRichness(elements: string[]): number {
    // 评估文化元素丰富度
    return Math.min(1, 0.2 + elements.length * 0.15 + Math.random() * 0.3)
  }

  private assessCulturalConnotation(username: string): number {
    // 评估文化内涵
    const culturalKeywords = ['师', '者', '人', '士', '家', '客', '仙', '君']
    const hasCultural = culturalKeywords.some(keyword => username.includes(keyword))
    return hasCultural ? 0.6 + Math.random() * 0.3 : 0.3 + Math.random() * 0.3
  }

  private assessCulturalValue(username: string, elements: string[]): number {
    // 评估文化传承价值
    return Math.min(1, 0.4 + Math.random() * 0.4)
  }

  private assessCrossDomainCreativity(elements: string[]): number {
    // 评估跨界融合创意
    return Math.min(1, 0.3 + elements.length * 0.1 + Math.random() * 0.4)
  }

  private assessConceptualInnovation(username: string): number {
    // 评估概念创新程度
    return Math.min(1, 0.4 + Math.random() * 0.4)
  }

  private assessExpressionBreakthrough(username: string, pattern: string): number {
    // 评估表达方式突破
    const patternBonus = pattern === 'creative' ? 0.2 : pattern === 'cultural_fusion' ? 0.15 : 0.1
    return Math.min(1, 0.3 + patternBonus + Math.random() * 0.3)
  }

  private assessEmotionalIntensity(username: string, elements: string[]): number {
    // 评估情感表达强度
    const emotionalKeywords = ['温暖', '热情', '温柔', '深情', '真诚', '纯真']
    const hasEmotional = emotionalKeywords.some(keyword => 
      elements.some(element => element.includes(keyword)) || username.includes(keyword)
    )
    return hasEmotional ? 0.6 + Math.random() * 0.3 : 0.3 + Math.random() * 0.4
  }

  private assessEmotionalAccuracy(username: string, context?: any): number {
    // 评估情感传达准确性
    const theme = context?.theme || ''
    const isEmotionalTheme = ['情感', '温暖', '治愈'].includes(theme)
    return isEmotionalTheme ? 0.6 + Math.random() * 0.3 : 0.4 + Math.random() * 0.4
  }

  private assessEmotionalImpact(username: string): number {
    // 评估情感感染力
    return Math.min(1, 0.4 + Math.random() * 0.4)
  }

  // 其他原有维度的简化实现
  private async assessRelevance(username: string, context?: any): Promise<number> {
    return 0.8 + Math.random() * 0.15
  }

  private async assessComprehensibility(username: string): Promise<number> {
    return username.length <= 4 ? 0.85 + Math.random() * 0.1 : 0.7 + Math.random() * 0.15
  }

  private async assessMemorability(username: string): Promise<number> {
    return 0.75 + Math.random() * 0.2
  }

  private async assessCulturalFit(username: string, elements: string[]): Promise<number> {
    return 0.8 + Math.random() * 0.15
  }

  private async assessTargetAudience(username: string, context?: any): Promise<number> {
    return 0.78 + Math.random() * 0.17
  }

  private async assessSemanticCoherence(username: string, elements: string[]): Promise<number> {
    return 0.85 + Math.random() * 0.1
  }

  /**
   * 获取权重配置
   */
  getWeights(): OptimizedQualityWeights {
    return { ...this.weights }
  }

  /**
   * 更新权重配置
   */
  updateWeights(newWeights: Partial<OptimizedQualityWeights>): void {
    this.weights = { ...this.weights, ...newWeights }
    
    // 验证权重总和
    const totalWeight = Object.values(this.weights).reduce((sum, weight) => sum + weight, 0)
    if (Math.abs(totalWeight - 1.0) > 0.001) {
      throw new Error(`权重总和不等于1.0: ${totalWeight}`)
    }
  }
}

/**
 * 全局增强质量评估引擎实例
 */
export const enhancedQualityAssessmentEngine = new EnhancedQualityAssessmentEngine()
