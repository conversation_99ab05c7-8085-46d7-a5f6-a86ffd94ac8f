/**
 * V5引擎质量优化引擎
 * 实现多维度质量评估和智能优化
 * 
 * @version 1.0.0
 * @created 2025-06-16
 */

/**
 * 扩展质量评估接口
 */
export interface ExtendedQualityAssessment {
  // 原有6维度
  novelty: number               // 新颖性 [0, 1]
  relevance: number            // 相关性 [0, 1]
  comprehensibility: number    // 可理解性 [0, 1]
  memorability: number         // 记忆性 [0, 1]
  cultural_fit: number         // 文化适配度 [0, 1]
  target_audience: number      // 目标受众匹配度 [0, 1]
  
  // 新增2维度
  semantic_coherence: number   // 语义连贯性 [0, 1]
  cultural_depth: number       // 文化内涵深度 [0, 1]
  
  // 综合评分
  overall_score: number        // 整体质量评分 [0, 1]
  quality_grade: string        // 质量等级 (A+, A, B+, B, C)
  
  // 详细分析
  strengths: string[]          // 优势分析
  weaknesses: string[]         // 不足分析
  improvement_suggestions: string[]  // 改进建议
}

/**
 * 质量预测结果接口
 */
export interface QualityPrediction {
  predicted_score: number      // 预测质量分数
  confidence: number           // 预测置信度 [0, 1]
  risk_factors: string[]       // 风险因素
  optimization_potential: number  // 优化潜力 [0, 1]
  recommended_adjustments: string[]  // 推荐调整
}

/**
 * 去重检查结果接口
 */
export interface DuplicationCheck {
  is_duplicate: boolean        // 是否重复
  similarity_score: number     // 相似度分数 [0, 1]
  similar_usernames: string[]  // 相似用户名列表
  uniqueness_score: number     // 独特性分数 [0, 1]
  recommendation: string       // 建议
}

/**
 * 质量优化引擎类
 */
export class QualityOptimizationEngine {
  private historicalData: Map<string, ExtendedQualityAssessment>
  private qualityThresholds: { [key: string]: number }
  private initialized: boolean

  constructor() {
    this.historicalData = new Map()
    this.qualityThresholds = this.initializeThresholds()
    this.initialized = false
  }

  /**
   * 初始化质量优化引擎
   */
  async initialize(): Promise<void> {
    if (this.initialized) return

    console.log('🎯 初始化质量优化引擎...')
    
    // 加载历史质量数据
    await this.loadHistoricalData()
    
    this.initialized = true
    console.log('✅ 质量优化引擎初始化完成')
  }

  /**
   * 8维度质量评估
   */
  assessQuality(
    username: string,
    pattern: string,
    elementsUsed: string[],
    context?: any
  ): ExtendedQualityAssessment {
    // 计算8个维度的质量分数
    const novelty = this.calculateNovelty(username, pattern)
    const relevance = this.calculateRelevance(username, context)
    const comprehensibility = this.calculateComprehensibility(username)
    const memorability = this.calculateMemorability(username, pattern)
    const cultural_fit = this.calculateCulturalFit(username, context)
    const target_audience = this.calculateTargetAudience(username, context)
    const semantic_coherence = this.calculateSemanticCoherence(username, elementsUsed)
    const cultural_depth = this.calculateCulturalDepth(username, pattern)

    // 计算综合评分 (加权平均)
    const overall_score = this.calculateOverallScore({
      novelty, relevance, comprehensibility, memorability,
      cultural_fit, target_audience, semantic_coherence, cultural_depth
    })

    // 确定质量等级
    const quality_grade = this.determineQualityGrade(overall_score)

    // 生成分析和建议
    const analysis = this.generateQualityAnalysis({
      novelty, relevance, comprehensibility, memorability,
      cultural_fit, target_audience, semantic_coherence, cultural_depth
    }, username, pattern)

    const assessment: ExtendedQualityAssessment = {
      novelty, relevance, comprehensibility, memorability,
      cultural_fit, target_audience, semantic_coherence, cultural_depth,
      overall_score, quality_grade,
      strengths: analysis.strengths,
      weaknesses: analysis.weaknesses,
      improvement_suggestions: analysis.suggestions
    }

    // 存储到历史数据
    this.historicalData.set(username, assessment)

    return assessment
  }

  /**
   * 智能质量预测
   */
  predictQuality(
    username: string,
    pattern: string,
    elementsUsed: string[],
    context?: any
  ): QualityPrediction {
    // 基于历史数据和模式分析预测质量
    const patternHistory = this.getPatternHistory(pattern)
    const elementHistory = this.getElementHistory(elementsUsed)
    
    // 预测基础分数
    let predicted_score = 0.7 // 基础分数
    
    // 基于模式历史调整
    if (patternHistory.length > 0) {
      const avgPatternScore = patternHistory.reduce((sum, score) => sum + score, 0) / patternHistory.length
      predicted_score = 0.6 * predicted_score + 0.4 * avgPatternScore
    }
    
    // 基于元素历史调整
    if (elementHistory.length > 0) {
      const avgElementScore = elementHistory.reduce((sum, score) => sum + score, 0) / elementHistory.length
      predicted_score = 0.7 * predicted_score + 0.3 * avgElementScore
    }
    
    // 计算置信度
    const confidence = Math.min(1.0, (patternHistory.length + elementHistory.length) / 20)
    
    // 识别风险因素
    const risk_factors = this.identifyRiskFactors(username, pattern, elementsUsed)
    
    // 计算优化潜力
    const optimization_potential = Math.max(0, 0.95 - predicted_score)
    
    // 生成推荐调整
    const recommended_adjustments = this.generateOptimizationRecommendations(
      username, pattern, predicted_score, risk_factors
    )

    return {
      predicted_score,
      confidence,
      risk_factors,
      optimization_potential,
      recommended_adjustments
    }
  }

  /**
   * 高级去重检查
   */
  checkDuplication(username: string, threshold: number = 0.8): DuplicationCheck {
    const similar_usernames: string[] = []
    let max_similarity = 0
    
    // 检查与历史用户名的相似度
    for (const [historicalUsername] of this.historicalData) {
      const similarity = this.calculateStringSimilarity(username, historicalUsername)
      
      if (similarity > threshold) {
        similar_usernames.push(historicalUsername)
      }
      
      max_similarity = Math.max(max_similarity, similarity)
    }
    
    const is_duplicate = similar_usernames.length > 0
    const uniqueness_score = 1 - max_similarity
    
    let recommendation = ''
    if (is_duplicate) {
      recommendation = `发现${similar_usernames.length}个相似用户名，建议调整以提高独特性`
    } else if (max_similarity > 0.6) {
      recommendation = '用户名较为常见，建议增加创意元素'
    } else {
      recommendation = '用户名独特性良好'
    }

    return {
      is_duplicate,
      similarity_score: max_similarity,
      similar_usernames,
      uniqueness_score,
      recommendation
    }
  }

  /**
   * 批量质量优化
   */
  optimizeBatch(usernames: Array<{
    username: string
    pattern: string
    elementsUsed: string[]
    context?: any
  }>): Array<{
    original: any
    assessment: ExtendedQualityAssessment
    prediction: QualityPrediction
    duplication: DuplicationCheck
    optimization_score: number
  }> {
    const results = usernames.map(item => {
      const assessment = this.assessQuality(item.username, item.pattern, item.elementsUsed, item.context)
      const prediction = this.predictQuality(item.username, item.pattern, item.elementsUsed, item.context)
      const duplication = this.checkDuplication(item.username)
      
      // 计算综合优化分数
      const optimization_score = (
        assessment.overall_score * 0.5 +
        prediction.predicted_score * 0.3 +
        duplication.uniqueness_score * 0.2
      )
      
      return {
        original: item,
        assessment,
        prediction,
        duplication,
        optimization_score
      }
    })
    
    // 按优化分数排序
    return results.sort((a, b) => b.optimization_score - a.optimization_score)
  }

  /**
   * 计算新颖性
   */
  private calculateNovelty(username: string, pattern: string): number {
    let base = 0.7
    
    // 基于模式的新颖性
    const patternNoveltyMap: { [key: string]: number } = {
      'homophone_creative': 0.15,
      'contradiction_unity': 0.12,
      'temporal_displacement': 0.18,
      'emotion_state': 0.10,
      'food_association': 0.08
    }
    
    base += patternNoveltyMap[pattern] || 0.05
    
    // 基于长度的新颖性
    if (username.length > 6) base += 0.05
    if (username.length > 8) base += 0.03
    
    // 基于字符多样性
    const uniqueChars = new Set(username).size
    base += (uniqueChars / username.length) * 0.1
    
    return Math.min(1.0, base + Math.random() * 0.05)
  }

  /**
   * 计算相关性
   */
  private calculateRelevance(username: string, context?: any): number {
    let base = 0.75
    
    if (context?.theme) {
      // 基于主题的相关性检查
      const themeKeywords: { [key: string]: string[] } = {
        '情感': ['心', '情', '爱', '温', '暖', '治', '愈'],
        '食物': ['茶', '食', '味', '香', '甜', '辣', '鲜'],
        '技术': ['码', '算', '数', '智', '云', '网', '程'],
        '职场': ['专', '师', '家', '员', '长', '总', '管']
      }
      
      const keywords = themeKeywords[context.theme] || []
      const matchCount = keywords.filter(keyword => username.includes(keyword)).length
      base += (matchCount / keywords.length) * 0.2
    }
    
    return Math.min(1.0, base + Math.random() * 0.1)
  }

  /**
   * 计算语义连贯性
   */
  private calculateSemanticCoherence(username: string, elementsUsed: string[]): number {
    let base = 0.8
    
    // 检查元素间的语义关联
    if (elementsUsed.length >= 2) {
      // 简化的语义关联检查
      const hasLogicalConnection = this.checkLogicalConnection(elementsUsed)
      if (hasLogicalConnection) base += 0.15
    }
    
    // 检查用户名的语法正确性
    const isGrammaticallyCorrect = this.checkGrammar(username)
    if (isGrammaticallyCorrect) base += 0.05
    
    return Math.min(1.0, base)
  }

  /**
   * 计算文化内涵深度
   */
  private calculateCulturalDepth(username: string, pattern: string): number {
    let base = 0.6
    
    // 基于模式的文化深度
    const culturalDepthMap: { [key: string]: number } = {
      'homophone_creative': 0.25,
      'cultural_fusion': 0.35,
      'ancient_modern': 0.30,
      'philosophical': 0.40
    }
    
    base += culturalDepthMap[pattern] || 0.1
    
    // 检查文化元素
    const culturalElements = ['诗', '仙', '道', '禅', '雅', '韵', '墨', '书']
    const culturalCount = culturalElements.filter(element => username.includes(element)).length
    base += culturalCount * 0.05
    
    return Math.min(1.0, base)
  }

  /**
   * 其他辅助方法的简化实现
   */
  private calculateComprehensibility(username: string): number {
    return 0.85 + Math.random() * 0.1
  }

  private calculateMemorability(username: string, pattern: string): number {
    return 0.80 + Math.random() * 0.15
  }

  private calculateCulturalFit(username: string, context?: any): number {
    return 0.82 + Math.random() * 0.13
  }

  private calculateTargetAudience(username: string, context?: any): number {
    return 0.78 + Math.random() * 0.17
  }

  private calculateOverallScore(scores: any): number {
    const weights = {
      novelty: 0.15, relevance: 0.15, comprehensibility: 0.12, memorability: 0.12,
      cultural_fit: 0.13, target_audience: 0.13, semantic_coherence: 0.10, cultural_depth: 0.10
    }
    
    return Object.entries(weights).reduce((sum, [key, weight]) => {
      return sum + scores[key] * weight
    }, 0)
  }

  private determineQualityGrade(score: number): string {
    if (score >= 0.95) return 'A+'
    if (score >= 0.90) return 'A'
    if (score >= 0.85) return 'B+'
    if (score >= 0.80) return 'B'
    return 'C'
  }

  private generateQualityAnalysis(scores: any, username: string, pattern: string) {
    const strengths: string[] = []
    const weaknesses: string[] = []
    const suggestions: string[] = []
    
    // 分析优势
    if (scores.novelty > 0.85) strengths.push('创新性突出')
    if (scores.cultural_depth > 0.80) strengths.push('文化内涵丰富')
    if (scores.semantic_coherence > 0.85) strengths.push('语义连贯性强')
    
    // 分析不足
    if (scores.relevance < 0.75) weaknesses.push('主题相关性有待提升')
    if (scores.memorability < 0.75) weaknesses.push('记忆性需要加强')
    
    // 生成建议
    if (scores.cultural_fit < 0.80) suggestions.push('建议增加文化元素')
    if (scores.target_audience < 0.80) suggestions.push('建议优化目标受众匹配')
    
    return { strengths, weaknesses, suggestions }
  }

  private initializeThresholds(): { [key: string]: number } {
    return {
      excellent: 0.90,
      good: 0.80,
      acceptable: 0.70,
      poor: 0.60
    }
  }

  private async loadHistoricalData(): Promise<void> {
    // 模拟加载历史数据
    console.log('📊 加载历史质量数据...')
  }

  private getPatternHistory(pattern: string): number[] {
    // 模拟获取模式历史数据
    return [0.85, 0.82, 0.88, 0.79, 0.91]
  }

  private getElementHistory(elements: string[]): number[] {
    // 模拟获取元素历史数据
    return [0.83, 0.87, 0.81, 0.89]
  }

  private identifyRiskFactors(username: string, pattern: string, elements: string[]): string[] {
    const risks: string[] = []
    
    if (username.length > 10) risks.push('用户名过长，可能影响记忆性')
    if (elements.length > 4) risks.push('元素过多，可能影响连贯性')
    
    return risks
  }

  private generateOptimizationRecommendations(
    username: string, pattern: string, score: number, risks: string[]
  ): string[] {
    const recommendations: string[] = []
    
    if (score < 0.80) recommendations.push('建议优化元素组合')
    if (risks.length > 0) recommendations.push('建议简化结构')
    
    return recommendations
  }

  private calculateStringSimilarity(str1: string, str2: string): number {
    // 简化的字符串相似度计算
    const longer = str1.length > str2.length ? str1 : str2
    const shorter = str1.length > str2.length ? str2 : str1
    
    if (longer.length === 0) return 1.0
    
    const editDistance = this.levenshteinDistance(longer, shorter)
    return (longer.length - editDistance) / longer.length
  }

  private levenshteinDistance(str1: string, str2: string): number {
    const matrix = []
    
    for (let i = 0; i <= str2.length; i++) {
      matrix[i] = [i]
    }
    
    for (let j = 0; j <= str1.length; j++) {
      matrix[0][j] = j
    }
    
    for (let i = 1; i <= str2.length; i++) {
      for (let j = 1; j <= str1.length; j++) {
        if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
          matrix[i][j] = matrix[i - 1][j - 1]
        } else {
          matrix[i][j] = Math.min(
            matrix[i - 1][j - 1] + 1,
            matrix[i][j - 1] + 1,
            matrix[i - 1][j] + 1
          )
        }
      }
    }
    
    return matrix[str2.length][str1.length]
  }

  private checkLogicalConnection(elements: string[]): boolean {
    // 简化的逻辑关联检查
    return elements.length >= 2 && Math.random() > 0.3
  }

  private checkGrammar(username: string): boolean {
    // 简化的语法检查
    return username.length >= 2 && username.length <= 12
  }

  /**
   * 获取统计信息
   */
  getStatistics() {
    return {
      initialized: this.initialized,
      historical_data_count: this.historicalData.size,
      quality_thresholds: this.qualityThresholds,
      assessment_dimensions: 8
    }
  }
}

/**
 * 全局质量优化引擎实例
 */
export const qualityOptimizationEngine = new QualityOptimizationEngine()
