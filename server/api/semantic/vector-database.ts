/**
 * V5引擎语义向量数据库
 * 实现多维度语义特征表示和相似度计算
 * 
 * @version 1.0.0
 * @created 2025-06-16
 */

/**
 * 语义向量接口定义
 * 20维语义特征的全面表示
 */
export interface SemanticVector {
  // 基础语义维度 (5个特征)
  semantic: {
    emotion: number        // 情感倾向 [-1, 1] (-1:负面, 0:中性, 1:积极)
    formality: number      // 正式程度 [0, 1] (0:非正式, 1:正式)
    intensity: number      // 强度程度 [0, 1] (0:温和, 1:强烈)
    positivity: number     // 积极性 [-1, 1] (-1:消极, 0:中性, 1:积极)
    abstractness: number   // 抽象程度 [0, 1] (0:具象, 1:抽象)
  }
  
  // 文化语境维度 (5个特征)
  cultural: {
    traditional: number    // 传统文化关联度 [0, 1]
    modern: number        // 现代文化关联度 [0, 1]
    professional: number  // 职业相关度 [0, 1]
    casual: number        // 日常生活关联度 [0, 1]
    humorous: number      // 幽默程度 [0, 1]
  }
  
  // 使用场景维度 (5个特征)
  context: {
    age_group: number[]   // 适用年龄群体权重 [16-25, 26-35, 36-45, 46+]
    gender_neutral: number // 性别中性程度 [0, 1]
    social_media: number  // 社交媒体适用度 [0, 1]
    workplace: number     // 职场适用度 [0, 1]
    gaming: number        // 游戏场景适用度 [0, 1]
  }
  
  // 语言学特征 (4个特征)
  linguistic: {
    syllable_count: number    // 音节数量
    phonetic_beauty: number   // 语音美感 [0, 1]
    memorability: number      // 记忆性 [0, 1]
    uniqueness: number        // 独特性 [0, 1]
  }
}

/**
 * 词汇条目接口
 */
export interface VocabularyEntry {
  word: string              // 词汇
  vector: SemanticVector    // 语义向量
  category: string          // 分类标签
  frequency: number         // 使用频率
  quality_score: number     // 质量评分
  created_at: string        // 创建时间
  updated_at: string        // 更新时间
}

/**
 * 相似度计算结果接口
 */
export interface SimilarityResult {
  word: string              // 词汇
  similarity: number        // 相似度分数 [0, 1]
  dimensions: {             // 各维度相似度
    semantic: number
    cultural: number
    context: number
    linguistic: number
  }
}

/**
 * 语义向量数据库类
 */
export class SemanticVectorDatabase {
  private vectors: Map<string, VocabularyEntry>
  private categoryIndex: Map<string, string[]>
  private similarityCache: Map<string, Map<string, number>>
  private initialized: boolean

  constructor() {
    this.vectors = new Map()
    this.categoryIndex = new Map()
    this.similarityCache = new Map()
    this.initialized = false
  }

  /**
   * 初始化数据库
   */
  async initialize(): Promise<void> {
    if (this.initialized) return

    console.log('🔄 初始化语义向量数据库...')
    
    // 初始化各类词汇的语义向量
    await this.initializeEmotionStateVectors()
    await this.initializeFoodAssociationVectors()
    await this.initializeExistingVocabularyVectors()
    
    // 构建索引
    this.buildCategoryIndex()
    
    this.initialized = true
    console.log(`✅ 语义向量数据库初始化完成，共加载 ${this.vectors.size} 个词汇`)
  }

  /**
   * 添加词汇向量
   */
  addVector(word: string, vector: SemanticVector, category: string, qualityScore: number): void {
    const entry: VocabularyEntry = {
      word,
      vector,
      category,
      frequency: 0,
      quality_score: qualityScore,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }
    
    this.vectors.set(word, entry)
    
    // 更新分类索引
    if (!this.categoryIndex.has(category)) {
      this.categoryIndex.set(category, [])
    }
    this.categoryIndex.get(category)!.push(word)
  }

  /**
   * 获取词汇向量
   */
  getVector(word: string): SemanticVector | null {
    const entry = this.vectors.get(word)
    return entry ? entry.vector : null
  }

  /**
   * 获取词汇条目
   */
  getEntry(word: string): VocabularyEntry | null {
    return this.vectors.get(word) || null
  }

  /**
   * 获取所有词汇
   */
  getAllWords(): string[] {
    return Array.from(this.vectors.keys())
  }

  /**
   * 按分类获取词汇
   */
  getWordsByCategory(category: string): string[] {
    return this.categoryIndex.get(category) || []
  }

  /**
   * 计算两个词汇的语义相似度
   */
  calculateSimilarity(word1: string, word2: string): number {
    // 检查缓存
    const cacheKey = `${word1}-${word2}`
    const reverseCacheKey = `${word2}-${word1}`
    
    if (this.similarityCache.has(word1) && this.similarityCache.get(word1)!.has(word2)) {
      return this.similarityCache.get(word1)!.get(word2)!
    }
    
    const vector1 = this.getVector(word1)
    const vector2 = this.getVector(word2)
    
    if (!vector1 || !vector2) return 0
    
    // 计算各维度相似度
    const semanticSim = this.cosineSimilarity(vector1.semantic, vector2.semantic)
    const culturalSim = this.cosineSimilarity(vector1.cultural, vector2.cultural)
    const contextSim = this.cosineSimilarity(vector1.context, vector2.context)
    const linguisticSim = this.cosineSimilarity(vector1.linguistic, vector2.linguistic)
    
    // 加权平均 (语义维度权重最高)
    const similarity = 0.4 * semanticSim + 0.3 * culturalSim + 0.2 * contextSim + 0.1 * linguisticSim
    
    // 缓存结果
    if (!this.similarityCache.has(word1)) {
      this.similarityCache.set(word1, new Map())
    }
    this.similarityCache.get(word1)!.set(word2, similarity)
    
    return similarity
  }

  /**
   * 余弦相似度计算
   */
  private cosineSimilarity(vectorA: any, vectorB: any): number {
    const keysA = Object.keys(vectorA)
    const keysB = Object.keys(vectorB)
    const commonKeys = keysA.filter(key => keysB.includes(key))
    
    if (commonKeys.length === 0) return 0
    
    let dotProduct = 0
    let normA = 0
    let normB = 0
    
    for (const key of commonKeys) {
      const valueA = Array.isArray(vectorA[key]) 
        ? vectorA[key].reduce((sum: number, val: number) => sum + val, 0) / vectorA[key].length
        : vectorA[key]
      const valueB = Array.isArray(vectorB[key])
        ? vectorB[key].reduce((sum: number, val: number) => sum + val, 0) / vectorB[key].length
        : vectorB[key]
      
      dotProduct += valueA * valueB
      normA += valueA * valueA
      normB += valueB * valueB
    }
    
    if (normA === 0 || normB === 0) return 0
    
    return dotProduct / (Math.sqrt(normA) * Math.sqrt(normB))
  }

  /**
   * 查找相似词汇
   */
  findSimilarWords(word: string, threshold: number = 0.6, limit: number = 10): SimilarityResult[] {
    const results: SimilarityResult[] = []
    const baseVector = this.getVector(word)
    
    if (!baseVector) return results
    
    for (const [otherWord, entry] of this.vectors) {
      if (otherWord === word) continue
      
      const similarity = this.calculateSimilarity(word, otherWord)
      
      if (similarity >= threshold) {
        // 计算各维度相似度
        const dimensions = {
          semantic: this.cosineSimilarity(baseVector.semantic, entry.vector.semantic),
          cultural: this.cosineSimilarity(baseVector.cultural, entry.vector.cultural),
          context: this.cosineSimilarity(baseVector.context, entry.vector.context),
          linguistic: this.cosineSimilarity(baseVector.linguistic, entry.vector.linguistic)
        }
        
        results.push({
          word: otherWord,
          similarity,
          dimensions
        })
      }
    }
    
    // 按相似度排序并限制数量
    return results
      .sort((a, b) => b.similarity - a.similarity)
      .slice(0, limit)
  }

  /**
   * 构建分类索引
   */
  private buildCategoryIndex(): void {
    this.categoryIndex.clear()
    
    for (const [word, entry] of this.vectors) {
      if (!this.categoryIndex.has(entry.category)) {
        this.categoryIndex.set(entry.category, [])
      }
      this.categoryIndex.get(entry.category)!.push(word)
    }
  }

  /**
   * 初始化情绪状态词汇向量
   */
  private async initializeEmotionStateVectors(): Promise<void> {
    console.log('📝 初始化情绪状态词汇向量...')

    // 情绪状态词汇的语义向量数据
    const emotionStateVectors = [
      {
        word: '间歇性努力',
        vector: {
          semantic: { emotion: 0.3, formality: 0.2, intensity: 0.6, positivity: 0.4, abstractness: 0.7 },
          cultural: { traditional: 0.2, modern: 0.9, professional: 0.3, casual: 0.8, humorous: 0.7 },
          context: { age_group: [0.2, 0.8, 0.6, 0.3], gender_neutral: 0.9, social_media: 0.9, workplace: 0.4, gaming: 0.5 },
          linguistic: { syllable_count: 5, phonetic_beauty: 0.6, memorability: 0.8, uniqueness: 0.9 }
        },
        quality_score: 8.6
      },
      {
        word: '积极废人',
        vector: {
          semantic: { emotion: 0.5, formality: 0.1, intensity: 0.7, positivity: 0.6, abstractness: 0.6 },
          cultural: { traditional: 0.1, modern: 0.9, professional: 0.2, casual: 0.9, humorous: 0.8 },
          context: { age_group: [0.1, 0.9, 0.7, 0.2], gender_neutral: 0.8, social_media: 0.9, workplace: 0.3, gaming: 0.6 },
          linguistic: { syllable_count: 4, phonetic_beauty: 0.7, memorability: 0.9, uniqueness: 0.8 }
        },
        quality_score: 8.5
      },
      {
        word: '外向孤独症',
        vector: {
          semantic: { emotion: -0.3, formality: 0.3, intensity: 0.8, positivity: -0.2, abstractness: 0.8 },
          cultural: { traditional: 0.2, modern: 0.8, professional: 0.4, casual: 0.7, humorous: 0.6 },
          context: { age_group: [0.3, 0.8, 0.6, 0.3], gender_neutral: 0.9, social_media: 0.8, workplace: 0.5, gaming: 0.4 },
          linguistic: { syllable_count: 5, phonetic_beauty: 0.6, memorability: 0.8, uniqueness: 0.9 }
        },
        quality_score: 8.7
      },
      {
        word: '理性但感性',
        vector: {
          semantic: { emotion: 0.4, formality: 0.6, intensity: 0.5, positivity: 0.3, abstractness: 0.9 },
          cultural: { traditional: 0.4, modern: 0.7, professional: 0.6, casual: 0.6, humorous: 0.5 },
          context: { age_group: [0.2, 0.7, 0.8, 0.5], gender_neutral: 0.8, social_media: 0.7, workplace: 0.6, gaming: 0.3 },
          linguistic: { syllable_count: 5, phonetic_beauty: 0.7, memorability: 0.7, uniqueness: 0.8 }
        },
        quality_score: 8.4
      },
      {
        word: '想独立又想被照顾',
        vector: {
          semantic: { emotion: 0.2, formality: 0.2, intensity: 0.6, positivity: 0.1, abstractness: 0.7 },
          cultural: { traditional: 0.3, modern: 0.8, professional: 0.2, casual: 0.9, humorous: 0.6 },
          context: { age_group: [0.4, 0.9, 0.6, 0.2], gender_neutral: 0.7, social_media: 0.8, workplace: 0.3, gaming: 0.4 },
          linguistic: { syllable_count: 8, phonetic_beauty: 0.5, memorability: 0.8, uniqueness: 0.9 }
        },
        quality_score: 8.3
      }
    ]

    // 添加到数据库
    for (const item of emotionStateVectors) {
      this.addVector(item.word, item.vector, '情绪状态', item.quality_score)
    }

    console.log(`✅ 情绪状态词汇向量初始化完成，共 ${emotionStateVectors.length} 个词汇`)
  }

  /**
   * 初始化食物关联词汇向量
   */
  private async initializeFoodAssociationVectors(): Promise<void> {
    console.log('🍜 初始化食物关联词汇向量...')

    // 食物关联词汇的语义向量数据
    const foodAssociationVectors = [
      {
        word: '奶茶星人',
        vector: {
          semantic: { emotion: 0.7, formality: 0.2, intensity: 0.6, positivity: 0.8, abstractness: 0.3 },
          cultural: { traditional: 0.1, modern: 0.9, professional: 0.2, casual: 0.9, humorous: 0.7 },
          context: { age_group: [0.1, 0.8, 0.6, 0.2], gender_neutral: 0.8, social_media: 0.9, workplace: 0.3, gaming: 0.6 },
          linguistic: { syllable_count: 4, phonetic_beauty: 0.7, memorability: 0.8, uniqueness: 0.6 }
        },
        quality_score: 8.1
      },
      {
        word: '火锅爱好者',
        vector: {
          semantic: { emotion: 0.8, formality: 0.3, intensity: 0.7, positivity: 0.9, abstractness: 0.2 },
          cultural: { traditional: 0.6, modern: 0.8, professional: 0.3, casual: 0.9, humorous: 0.6 },
          context: { age_group: [0.2, 0.7, 0.8, 0.6], gender_neutral: 0.9, social_media: 0.8, workplace: 0.4, gaming: 0.5 },
          linguistic: { syllable_count: 5, phonetic_beauty: 0.6, memorability: 0.7, uniqueness: 0.5 }
        },
        quality_score: 7.9
      },
      {
        word: '烧烤达人',
        vector: {
          semantic: { emotion: 0.6, formality: 0.2, intensity: 0.6, positivity: 0.8, abstractness: 0.2 },
          cultural: { traditional: 0.4, modern: 0.8, professional: 0.3, casual: 0.9, humorous: 0.6 },
          context: { age_group: [0.3, 0.8, 0.7, 0.4], gender_neutral: 0.7, social_media: 0.8, workplace: 0.3, gaming: 0.6 },
          linguistic: { syllable_count: 4, phonetic_beauty: 0.7, memorability: 0.7, uniqueness: 0.6 }
        },
        quality_score: 8.0
      },
      {
        word: '小龙虾专家',
        vector: {
          semantic: { emotion: 0.7, formality: 0.3, intensity: 0.6, positivity: 0.8, abstractness: 0.3 },
          cultural: { traditional: 0.3, modern: 0.9, professional: 0.4, casual: 0.8, humorous: 0.7 },
          context: { age_group: [0.2, 0.8, 0.7, 0.3], gender_neutral: 0.8, social_media: 0.9, workplace: 0.3, gaming: 0.5 },
          linguistic: { syllable_count: 5, phonetic_beauty: 0.6, memorability: 0.8, uniqueness: 0.7 }
        },
        quality_score: 8.2
      },
      {
        word: '螺蛳粉忠粉',
        vector: {
          semantic: { emotion: 0.8, formality: 0.1, intensity: 0.8, positivity: 0.9, abstractness: 0.2 },
          cultural: { traditional: 0.2, modern: 0.9, professional: 0.2, casual: 0.9, humorous: 0.8 },
          context: { age_group: [0.1, 0.9, 0.6, 0.2], gender_neutral: 0.8, social_media: 0.9, workplace: 0.2, gaming: 0.4 },
          linguistic: { syllable_count: 5, phonetic_beauty: 0.5, memorability: 0.9, uniqueness: 0.8 }
        },
        quality_score: 8.3
      }
    ]

    // 添加到数据库
    for (const item of foodAssociationVectors) {
      this.addVector(item.word, item.vector, '食物关联', item.quality_score)
    }

    console.log(`✅ 食物关联词汇向量初始化完成，共 ${foodAssociationVectors.length} 个词汇`)
  }

  /**
   * 初始化现有词汇向量
   */
  private async initializeExistingVocabularyVectors(): Promise<void> {
    console.log('📚 初始化现有词汇向量...')

    // 现有词汇的语义向量数据示例
    const existingVectors = [
      // 技术类词汇
      {
        word: '代码',
        vector: {
          semantic: { emotion: 0.1, formality: 0.8, intensity: 0.6, positivity: 0.3, abstractness: 0.7 },
          cultural: { traditional: 0.1, modern: 0.9, professional: 0.9, casual: 0.3, humorous: 0.2 },
          context: { age_group: [0.2, 0.8, 0.7, 0.3], gender_neutral: 0.9, social_media: 0.6, workplace: 0.9, gaming: 0.7 },
          linguistic: { syllable_count: 2, phonetic_beauty: 0.5, memorability: 0.7, uniqueness: 0.6 }
        },
        quality_score: 7.8
      },
      {
        word: '算法',
        vector: {
          semantic: { emotion: 0.0, formality: 0.9, intensity: 0.7, positivity: 0.2, abstractness: 0.9 },
          cultural: { traditional: 0.2, modern: 0.9, professional: 0.9, casual: 0.2, humorous: 0.1 },
          context: { age_group: [0.1, 0.7, 0.8, 0.4], gender_neutral: 0.9, social_media: 0.5, workplace: 0.9, gaming: 0.6 },
          linguistic: { syllable_count: 2, phonetic_beauty: 0.6, memorability: 0.6, uniqueness: 0.7 }
        },
        quality_score: 7.5
      },
      // 情感类词汇
      {
        word: '温暖',
        vector: {
          semantic: { emotion: 0.9, formality: 0.4, intensity: 0.6, positivity: 0.9, abstractness: 0.6 },
          cultural: { traditional: 0.7, modern: 0.6, professional: 0.3, casual: 0.8, humorous: 0.3 },
          context: { age_group: [0.4, 0.7, 0.8, 0.7], gender_neutral: 0.8, social_media: 0.7, workplace: 0.4, gaming: 0.3 },
          linguistic: { syllable_count: 2, phonetic_beauty: 0.8, memorability: 0.8, uniqueness: 0.4 }
        },
        quality_score: 8.5
      },
      {
        word: '治愈',
        vector: {
          semantic: { emotion: 0.8, formality: 0.5, intensity: 0.7, positivity: 0.9, abstractness: 0.7 },
          cultural: { traditional: 0.4, modern: 0.8, professional: 0.6, casual: 0.7, humorous: 0.2 },
          context: { age_group: [0.3, 0.8, 0.7, 0.5], gender_neutral: 0.8, social_media: 0.8, workplace: 0.3, gaming: 0.4 },
          linguistic: { syllable_count: 2, phonetic_beauty: 0.7, memorability: 0.8, uniqueness: 0.6 }
        },
        quality_score: 8.7
      },
      // 职场类词汇
      {
        word: '专家',
        vector: {
          semantic: { emotion: 0.2, formality: 0.8, intensity: 0.6, positivity: 0.6, abstractness: 0.4 },
          cultural: { traditional: 0.6, modern: 0.7, professional: 0.9, casual: 0.3, humorous: 0.2 },
          context: { age_group: [0.2, 0.6, 0.8, 0.7], gender_neutral: 0.9, social_media: 0.5, workplace: 0.9, gaming: 0.3 },
          linguistic: { syllable_count: 2, phonetic_beauty: 0.6, memorability: 0.7, uniqueness: 0.3 }
        },
        quality_score: 7.6
      }
    ]

    // 添加到数据库
    for (const item of existingVectors) {
      this.addVector(item.word, item.vector, '现有词汇', item.quality_score)
    }

    console.log(`✅ 现有词汇向量初始化完成，共 ${existingVectors.length} 个词汇`)
  }

  /**
   * 获取数据库统计信息
   */
  getStatistics() {
    const categories = Array.from(this.categoryIndex.keys())
    const categoryStats = categories.map(category => ({
      category,
      count: this.categoryIndex.get(category)!.length
    }))
    
    return {
      total_words: this.vectors.size,
      categories: categories.length,
      category_breakdown: categoryStats,
      cache_size: this.similarityCache.size,
      initialized: this.initialized
    }
  }
}

/**
 * 全局语义向量数据库实例
 */
export const semanticDatabase = new SemanticVectorDatabase()
