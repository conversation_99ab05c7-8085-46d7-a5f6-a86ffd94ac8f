/**
 * V5引擎语义关联引擎
 * 实现智能词汇关联和选择算法
 * 
 * @version 1.0.0
 * @created 2025-06-16
 */

import { SemanticVectorDatabase, SimilarityResult } from './vector-database'

/**
 * 关联模式枚举
 */
export enum AssociationMode {
  SIMILAR = 'similar',      // 相似关联
  CONTRAST = 'contrast',    // 对比关联
  BALANCED = 'balanced'     // 平衡关联
}

/**
 * 关联结果接口
 */
export interface AssociationResult {
  word: string              // 词汇
  score: number            // 关联评分
  mode: AssociationMode    // 关联模式
  reasoning: string        // 关联理由
  dimensions: {            // 各维度评分
    semantic: number
    cultural: number
    context: number
    linguistic: number
  }
}

/**
 * 语义关联引擎类
 */
export class SemanticAssociationEngine {
  private database: SemanticVectorDatabase
  private initialized: boolean

  constructor(database: SemanticVectorDatabase) {
    this.database = database
    this.initialized = false
  }

  /**
   * 初始化引擎
   */
  async initialize(): Promise<void> {
    if (this.initialized) return

    // 确保数据库已初始化
    await this.database.initialize()
    
    this.initialized = true
    console.log('🧠 语义关联引擎初始化完成')
  }

  /**
   * 计算两个词汇的语义相似度
   */
  calculateSimilarity(word1: string, word2: string): number {
    return this.database.calculateSimilarity(word1, word2)
  }

  /**
   * 选择语义相关的词汇
   */
  selectRelatedElements(baseWord: string, count: number = 5, threshold: number = 0.6): AssociationResult[] {
    const similarWords = this.database.findSimilarWords(baseWord, threshold, count * 2)
    
    return similarWords.slice(0, count).map(result => ({
      word: result.word,
      score: result.similarity,
      mode: AssociationMode.SIMILAR,
      reasoning: this.generateReasoningForSimilar(baseWord, result),
      dimensions: result.dimensions
    }))
  }

  /**
   * 选择语义对比的词汇
   */
  selectContrastElements(baseWord: string, count: number = 3, threshold: number = 0.3): AssociationResult[] {
    const allWords = this.database.getAllWords()
    const contrastResults: AssociationResult[] = []
    
    const baseVector = this.database.getVector(baseWord)
    if (!baseVector) return contrastResults
    
    for (const word of allWords) {
      if (word === baseWord) continue
      
      const contrast = this.calculateContrast(baseWord, word)
      if (contrast >= threshold) {
        const similarity = this.calculateSimilarity(baseWord, word)
        const targetVector = this.database.getVector(word)!
        
        contrastResults.push({
          word,
          score: contrast,
          mode: AssociationMode.CONTRAST,
          reasoning: this.generateReasoningForContrast(baseWord, word, baseVector, targetVector),
          dimensions: {
            semantic: this.database['cosineSimilarity'](baseVector.semantic, targetVector.semantic),
            cultural: this.database['cosineSimilarity'](baseVector.cultural, targetVector.cultural),
            context: this.database['cosineSimilarity'](baseVector.context, targetVector.context),
            linguistic: this.database['cosineSimilarity'](baseVector.linguistic, targetVector.linguistic)
          }
        })
      }
    }
    
    return contrastResults
      .sort((a, b) => b.score - a.score)
      .slice(0, count)
  }

  /**
   * 平衡选择相关和对比词汇
   */
  selectBalancedElements(baseWord: string, totalCount: number = 8): AssociationResult[] {
    const similarCount = Math.ceil(totalCount * 0.6)  // 60%相似
    const contrastCount = totalCount - similarCount    // 40%对比
    
    const similarElements = this.selectRelatedElements(baseWord, similarCount)
    const contrastElements = this.selectContrastElements(baseWord, contrastCount)
    
    return [...similarElements, ...contrastElements]
      .sort((a, b) => b.score - a.score)
  }

  /**
   * 基于主题的智能词汇选择
   */
  selectByTheme(theme: string, mode: AssociationMode = AssociationMode.BALANCED, count: number = 8): AssociationResult[] {
    switch (mode) {
      case AssociationMode.SIMILAR:
        return this.selectRelatedElements(theme, count)
      case AssociationMode.CONTRAST:
        return this.selectContrastElements(theme, count)
      case AssociationMode.BALANCED:
        return this.selectBalancedElements(theme, count)
      default:
        return this.selectBalancedElements(theme, count)
    }
  }

  /**
   * 计算对比度
   */
  private calculateContrast(word1: string, word2: string): number {
    const vector1 = this.database.getVector(word1)
    const vector2 = this.database.getVector(word2)
    
    if (!vector1 || !vector2) return 0
    
    // 计算各维度的差异度
    const semanticContrast = this.calculateDimensionContrast(vector1.semantic, vector2.semantic)
    const culturalContrast = this.calculateDimensionContrast(vector1.cultural, vector2.cultural)
    const contextContrast = this.calculateDimensionContrast(vector1.context, vector2.context)
    
    // 加权平均 (语义对比权重最高)
    return 0.5 * semanticContrast + 0.3 * culturalContrast + 0.2 * contextContrast
  }

  /**
   * 计算维度对比度
   */
  private calculateDimensionContrast(dim1: any, dim2: any): number {
    const keys = Object.keys(dim1)
    let totalContrast = 0
    let count = 0
    
    for (const key of keys) {
      const val1 = Array.isArray(dim1[key]) 
        ? dim1[key].reduce((sum: number, val: number) => sum + val, 0) / dim1[key].length
        : dim1[key]
      const val2 = Array.isArray(dim2[key])
        ? dim2[key].reduce((sum: number, val: number) => sum + val, 0) / dim2[key].length
        : dim2[key]
      
      // 计算差异度 (值越不同，对比度越高)
      totalContrast += Math.abs(val1 - val2)
      count++
    }
    
    return count > 0 ? totalContrast / count : 0
  }

  /**
   * 生成相似关联的理由
   */
  private generateReasoningForSimilar(baseWord: string, result: SimilarityResult): string {
    const { dimensions } = result
    const strongestDimension = Object.entries(dimensions)
      .sort(([,a], [,b]) => b - a)[0]
    
    const dimensionNames = {
      semantic: '语义特征',
      cultural: '文化背景',
      context: '使用场景',
      linguistic: '语言特征'
    }
    
    return `与"${baseWord}"在${dimensionNames[strongestDimension[0] as keyof typeof dimensionNames]}上高度相似 (${(strongestDimension[1] * 100).toFixed(0)}%)`
  }

  /**
   * 生成对比关联的理由
   */
  private generateReasoningForContrast(baseWord: string, targetWord: string, baseVector: any, targetVector: any): string {
    // 找出差异最大的维度
    const semanticDiff = Math.abs(baseVector.semantic.emotion - targetVector.semantic.emotion)
    const formalityDiff = Math.abs(baseVector.semantic.formality - targetVector.semantic.formality)
    const intensityDiff = Math.abs(baseVector.semantic.intensity - targetVector.semantic.intensity)
    
    if (semanticDiff > 0.5) {
      return `与"${baseWord}"形成情感对比，创造层次感`
    } else if (formalityDiff > 0.5) {
      return `与"${baseWord}"在正式程度上形成对比`
    } else if (intensityDiff > 0.5) {
      return `与"${baseWord}"在强度上形成对比`
    } else {
      return `与"${baseWord}"形成有趣的反差效果`
    }
  }

  /**
   * 获取引擎统计信息
   */
  getStatistics() {
    return {
      initialized: this.initialized,
      database_stats: this.database.getStatistics(),
      supported_modes: Object.values(AssociationMode)
    }
  }

  /**
   * 测试关联效果
   */
  async testAssociation(word: string): Promise<{
    similar: AssociationResult[]
    contrast: AssociationResult[]
    balanced: AssociationResult[]
  }> {
    await this.initialize()
    
    return {
      similar: this.selectRelatedElements(word, 5),
      contrast: this.selectContrastElements(word, 3),
      balanced: this.selectBalancedElements(word, 8)
    }
  }
}

/**
 * 创建语义关联引擎实例
 */
export function createSemanticAssociationEngine(database: SemanticVectorDatabase): SemanticAssociationEngine {
  return new SemanticAssociationEngine(database)
}
