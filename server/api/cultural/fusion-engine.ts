/**
 * V5引擎文化融合引擎
 * 实现古今文化元素的智能融合和用户名生成
 * 
 * @version 1.0.0
 * @created 2025-06-16
 */

import { CulturalKnowledgeBase, CulturalElement, CulturalFusionRule } from './knowledge-base'

/**
 * 融合结果接口
 */
export interface CulturalFusionResult {
  username: string              // 生成的用户名
  fusion_type: string           // 融合类型
  cultural_depth: number        // 文化深度 [0, 1]
  ancient_element: CulturalElement    // 古代文化元素
  modern_element: CulturalElement     // 现代文化元素
  fusion_rule: CulturalFusionRule     // 使用的融合规则
  explanation: string           // 融合说明
  quality_score: number         // 质量评分 [0, 1]
  target_audience_match: number // 目标受众匹配度 [0, 1]
}

/**
 * 融合策略枚举
 */
export enum FusionStrategy {
  CONTRAST = 'contrast',        // 对比融合
  HARMONY = 'harmony',          // 和谐融合
  EVOLUTION = 'evolution',      // 演进融合
  CREATIVE = 'creative'         // 创意融合
}

/**
 * 文化融合引擎类
 */
export class CulturalFusionEngine {
  private knowledgeBase: CulturalKnowledgeBase
  private initialized: boolean

  constructor(knowledgeBase: CulturalKnowledgeBase) {
    this.knowledgeBase = knowledgeBase
    this.initialized = false
  }

  /**
   * 初始化融合引擎
   */
  async initialize(): Promise<void> {
    if (this.initialized) return

    // 确保知识库已初始化
    await this.knowledgeBase.initialize()
    
    this.initialized = true
    console.log('🎭 文化融合引擎初始化完成')
  }

  /**
   * 生成文化融合用户名
   */
  async generateCulturalFusion(
    ancientCategory?: string,
    modernCategory?: string,
    strategy: FusionStrategy = FusionStrategy.CONTRAST
  ): Promise<CulturalFusionResult | null> {
    await this.initialize()

    // 如果没有指定类别，随机选择
    if (!ancientCategory || !modernCategory) {
      const categories = this.getAvailableCategories()
      ancientCategory = ancientCategory || this.randomSelect(categories.ancient)
      modernCategory = modernCategory || this.randomSelect(categories.modern)
    }

    const ancientElements = this.knowledgeBase.getElementsByCategory(ancientCategory, 'ancient')
    const modernElements = this.knowledgeBase.getElementsByCategory(modernCategory, 'modern')
    const fusionRules = this.knowledgeBase.findFusionOpportunities(ancientCategory, modernCategory)

    if (ancientElements.length === 0 || modernElements.length === 0) {
      return null
    }

    // 选择最佳的古代和现代元素
    const ancientElement = this.selectBestElement(ancientElements, strategy)
    const modernElement = this.selectBestElement(modernElements, strategy)
    
    // 选择或创建融合规则
    const fusionRule = fusionRules.length > 0 
      ? fusionRules[0] 
      : this.createDefaultFusionRule(ancientCategory, modernCategory, strategy)

    // 执行融合
    const fusionResult = this.executeFusion(ancientElement, modernElement, fusionRule, strategy)
    
    return fusionResult
  }

  /**
   * 批量生成文化融合用户名
   */
  async generateBatchCulturalFusion(
    count: number = 5,
    strategy: FusionStrategy = FusionStrategy.CONTRAST
  ): Promise<CulturalFusionResult[]> {
    const results: CulturalFusionResult[] = []
    
    for (let i = 0; i < count; i++) {
      const result = await this.generateCulturalFusion(undefined, undefined, strategy)
      if (result) {
        results.push(result)
      }
    }
    
    // 按质量评分排序
    return results.sort((a, b) => b.quality_score - a.quality_score)
  }

  /**
   * 基于主题的文化融合
   */
  async generateThematicFusion(
    theme: string,
    strategy: FusionStrategy = FusionStrategy.HARMONY
  ): Promise<CulturalFusionResult | null> {
    // 主题到文化类别的映射
    const themeMapping: { [key: string]: { ancient: string[], modern: string[] } } = {
      '文学': {
        ancient: ['诗词文学', '哲学思想'],
        modern: ['网络文化', '流行文化']
      },
      '职场': {
        ancient: ['成语典故', '哲学思想'],
        modern: ['职业文化', '科技文化']
      },
      '科技': {
        ancient: ['哲学思想', '成语典故'],
        modern: ['科技文化', '网络文化']
      },
      '生活': {
        ancient: ['诗词文学', '成语典故'],
        modern: ['流行文化', '生活方式']
      }
    }

    const mapping = themeMapping[theme]
    if (!mapping) {
      return this.generateCulturalFusion(undefined, undefined, strategy)
    }

    const ancientCategory = this.randomSelect(mapping.ancient)
    const modernCategory = this.randomSelect(mapping.modern)
    
    return this.generateCulturalFusion(ancientCategory, modernCategory, strategy)
  }

  /**
   * 执行文化融合
   */
  private executeFusion(
    ancientElement: CulturalElement,
    modernElement: CulturalElement,
    fusionRule: CulturalFusionRule,
    strategy: FusionStrategy
  ): CulturalFusionResult {
    let username = ''
    let explanation = ''

    switch (strategy) {
      case FusionStrategy.CONTRAST:
        username = `${ancientElement.name}${modernElement.name}`
        explanation = `${ancientElement.name}(${ancientElement.era})与${modernElement.name}(${modernElement.era})形成时空对比，展现传统与现代的碰撞`
        break

      case FusionStrategy.HARMONY:
        username = `${modernElement.name}${ancientElement.name}`
        explanation = `${modernElement.name}与${ancientElement.name}和谐融合，体现文化传承与创新的统一`
        break

      case FusionStrategy.EVOLUTION:
        username = `新时代${ancientElement.name}`
        explanation = `${ancientElement.name}在现代的演进形态，传统文化的时代新生`
        break

      case FusionStrategy.CREATIVE:
        // 创意性组合，可能包含修饰词
        const modifiers = ['超级', '智能', '数字', '云端', '极客', '新派']
        const modifier = this.randomSelect(modifiers)
        username = `${modifier}${ancientElement.name}`
        explanation = `${ancientElement.name}的创意现代化表达，融入时代特色`
        break

      default:
        username = `${ancientElement.name}${modernElement.name}`
        explanation = `古今文化的创意融合`
    }

    // 计算质量评分
    const quality_score = this.calculateQualityScore(ancientElement, modernElement, fusionRule)
    
    // 计算文化深度
    const cultural_depth = (ancientElement.cultural_weight + modernElement.cultural_weight + fusionRule.compatibility_score) / 3
    
    // 计算目标受众匹配度
    const target_audience_match = this.calculateAudienceMatch(ancientElement, modernElement)

    return {
      username,
      fusion_type: fusionRule.fusion_type,
      cultural_depth,
      ancient_element: ancientElement,
      modern_element: modernElement,
      fusion_rule: fusionRule,
      explanation,
      quality_score,
      target_audience_match
    }
  }

  /**
   * 选择最佳元素
   */
  private selectBestElement(elements: CulturalElement[], strategy: FusionStrategy): CulturalElement {
    switch (strategy) {
      case FusionStrategy.CONTRAST:
        // 选择文化权重高的元素
        return elements.reduce((best, current) => 
          current.cultural_weight > best.cultural_weight ? current : best
        )
        
      case FusionStrategy.HARMONY:
        // 选择使用频率高的元素
        return elements.reduce((best, current) => 
          current.usage_frequency > best.usage_frequency ? current : best
        )
        
      case FusionStrategy.EVOLUTION:
        // 选择融合潜力高的元素
        return elements.reduce((best, current) => 
          current.fusion_potential > best.fusion_potential ? current : best
        )
        
      default:
        return this.randomSelect(elements)
    }
  }

  /**
   * 创建默认融合规则
   */
  private createDefaultFusionRule(
    ancientCategory: string,
    modernCategory: string,
    strategy: FusionStrategy
  ): CulturalFusionRule {
    return {
      id: `default_${Date.now()}`,
      ancient_category: ancientCategory,
      modern_category: modernCategory,
      fusion_type: strategy,
      compatibility_score: 0.7, // 默认兼容性
      fusion_formula: `[${ancientCategory}] + [${modernCategory}]`,
      examples: []
    }
  }

  /**
   * 计算质量评分
   */
  private calculateQualityScore(
    ancientElement: CulturalElement,
    modernElement: CulturalElement,
    fusionRule: CulturalFusionRule
  ): number {
    // 基础分数
    let score = 0.6

    // 文化权重加分
    score += (ancientElement.cultural_weight + modernElement.cultural_weight) * 0.15

    // 融合兼容性加分
    score += fusionRule.compatibility_score * 0.2

    // 使用频率平衡加分
    const frequencyBalance = 1 - Math.abs(ancientElement.usage_frequency - modernElement.usage_frequency)
    score += frequencyBalance * 0.1

    // 情感色调和谐度加分
    const emotionalHarmony = 1 - Math.abs(ancientElement.emotional_tone - modernElement.emotional_tone) / 2
    score += emotionalHarmony * 0.05

    return Math.min(1.0, score)
  }

  /**
   * 计算目标受众匹配度
   */
  private calculateAudienceMatch(
    ancientElement: CulturalElement,
    modernElement: CulturalElement
  ): number {
    const ancientAudience = new Set(ancientElement.target_audience)
    const modernAudience = new Set(modernElement.target_audience)
    
    // 计算交集
    const intersection = new Set([...ancientAudience].filter(x => modernAudience.has(x)))
    
    // 计算并集
    const union = new Set([...ancientAudience, ...modernAudience])
    
    // 返回Jaccard相似度
    return union.size > 0 ? intersection.size / union.size : 0
  }

  /**
   * 获取可用类别
   */
  private getAvailableCategories(): { ancient: string[], modern: string[] } {
    const stats = this.knowledgeBase.getStatistics()
    const ancientCategories: string[] = []
    const modernCategories: string[] = []
    
    for (const categoryInfo of stats.category_breakdown) {
      if (categoryInfo.ancient_count > 0) {
        ancientCategories.push(categoryInfo.category)
      }
      if (categoryInfo.modern_count > 0) {
        modernCategories.push(categoryInfo.category)
      }
    }
    
    return { ancient: ancientCategories, modern: modernCategories }
  }

  /**
   * 随机选择
   */
  private randomSelect<T>(array: T[]): T {
    return array[Math.floor(Math.random() * array.length)]
  }

  /**
   * 获取引擎统计信息
   */
  getStatistics() {
    return {
      initialized: this.initialized,
      knowledge_base_stats: this.knowledgeBase.getStatistics(),
      supported_strategies: Object.values(FusionStrategy)
    }
  }

  /**
   * 测试文化融合效果
   */
  async testCulturalFusion(): Promise<{
    contrast: CulturalFusionResult[]
    harmony: CulturalFusionResult[]
    evolution: CulturalFusionResult[]
    creative: CulturalFusionResult[]
  }> {
    await this.initialize()
    
    return {
      contrast: await this.generateBatchCulturalFusion(3, FusionStrategy.CONTRAST),
      harmony: await this.generateBatchCulturalFusion(3, FusionStrategy.HARMONY),
      evolution: await this.generateBatchCulturalFusion(3, FusionStrategy.EVOLUTION),
      creative: await this.generateBatchCulturalFusion(3, FusionStrategy.CREATIVE)
    }
  }
}

/**
 * 创建文化融合引擎实例
 */
export function createCulturalFusionEngine(knowledgeBase: CulturalKnowledgeBase): CulturalFusionEngine {
  return new CulturalFusionEngine(knowledgeBase)
}
