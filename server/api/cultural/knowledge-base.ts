/**
 * V5引擎文化知识库
 * 实现古今文化元素的收集、管理和融合
 * 
 * @version 1.0.0
 * @created 2025-06-16
 */

/**
 * 文化元素接口定义
 */
export interface CulturalElement {
  id: string                    // 唯一标识
  name: string                  // 元素名称
  category: string              // 文化分类
  era: 'ancient' | 'modern'     // 时代归属
  description: string           // 描述说明
  cultural_weight: number       // 文化权重 [0, 1]
  usage_frequency: number       // 使用频率 [0, 1]
  emotional_tone: number        // 情感色调 [-1, 1]
  formality_level: number       // 正式程度 [0, 1]
  target_audience: string[]     // 目标受众
  related_concepts: string[]    // 相关概念
  fusion_potential: number      // 融合潜力 [0, 1]
}

/**
 * 文化融合规则接口
 */
export interface CulturalFusionRule {
  id: string                    // 规则ID
  ancient_category: string      // 古代文化类别
  modern_category: string       // 现代文化类别
  fusion_type: 'contrast' | 'harmony' | 'evolution'  // 融合类型
  compatibility_score: number   // 兼容性评分 [0, 1]
  fusion_formula: string        // 融合公式
  examples: string[]            // 融合示例
}

/**
 * 文化知识库类
 */
export class CulturalKnowledgeBase {
  private ancientElements: Map<string, CulturalElement>
  private modernElements: Map<string, CulturalElement>
  private fusionRules: Map<string, CulturalFusionRule>
  private categoryIndex: Map<string, string[]>
  private initialized: boolean

  constructor() {
    this.ancientElements = new Map()
    this.modernElements = new Map()
    this.fusionRules = new Map()
    this.categoryIndex = new Map()
    this.initialized = false
  }

  /**
   * 初始化文化知识库
   */
  async initialize(): Promise<void> {
    if (this.initialized) return

    console.log('🏛️ 初始化文化知识库...')
    
    // 初始化古代文化元素
    await this.initializeAncientElements()
    
    // 初始化现代文化元素
    await this.initializeModernElements()
    
    // 初始化融合规则
    await this.initializeFusionRules()
    
    // 构建索引
    this.buildCategoryIndex()
    
    this.initialized = true
    console.log(`✅ 文化知识库初始化完成`)
    console.log(`   古代文化元素: ${this.ancientElements.size}个`)
    console.log(`   现代文化元素: ${this.modernElements.size}个`)
    console.log(`   融合规则: ${this.fusionRules.size}条`)
  }

  /**
   * 添加文化元素
   */
  addElement(element: CulturalElement): void {
    if (element.era === 'ancient') {
      this.ancientElements.set(element.id, element)
    } else {
      this.modernElements.set(element.id, element)
    }
    
    // 更新分类索引
    if (!this.categoryIndex.has(element.category)) {
      this.categoryIndex.set(element.category, [])
    }
    this.categoryIndex.get(element.category)!.push(element.id)
  }

  /**
   * 获取文化元素
   */
  getElement(id: string): CulturalElement | null {
    return this.ancientElements.get(id) || this.modernElements.get(id) || null
  }

  /**
   * 按类别获取元素
   */
  getElementsByCategory(category: string, era?: 'ancient' | 'modern'): CulturalElement[] {
    const elementIds = this.categoryIndex.get(category) || []
    const elements: CulturalElement[] = []
    
    for (const id of elementIds) {
      const element = this.getElement(id)
      if (element && (!era || element.era === era)) {
        elements.push(element)
      }
    }
    
    return elements
  }

  /**
   * 查找文化融合机会
   */
  findFusionOpportunities(ancientCategory: string, modernCategory: string): CulturalFusionRule[] {
    const opportunities: CulturalFusionRule[] = []
    
    for (const [ruleId, rule] of this.fusionRules) {
      if (rule.ancient_category === ancientCategory && rule.modern_category === modernCategory) {
        opportunities.push(rule)
      }
    }
    
    return opportunities.sort((a, b) => b.compatibility_score - a.compatibility_score)
  }

  /**
   * 生成文化融合用户名
   */
  generateCulturalFusion(ancientCategory: string, modernCategory: string): {
    username: string
    fusion_type: string
    cultural_depth: number
    explanation: string
  } | null {
    const ancientElements = this.getElementsByCategory(ancientCategory, 'ancient')
    const modernElements = this.getElementsByCategory(modernCategory, 'modern')
    const fusionRules = this.findFusionOpportunities(ancientCategory, modernCategory)
    
    if (ancientElements.length === 0 || modernElements.length === 0 || fusionRules.length === 0) {
      return null
    }
    
    const ancientElement = this.randomSelect(ancientElements)
    const modernElement = this.randomSelect(modernElements)
    const fusionRule = fusionRules[0] // 选择兼容性最高的规则
    
    let username = ''
    let explanation = ''
    
    switch (fusionRule.fusion_type) {
      case 'contrast':
        username = `${ancientElement.name}${modernElement.name}`
        explanation = `${ancientElement.name}(古代)与${modernElement.name}(现代)形成时空对比`
        break
        
      case 'harmony':
        username = `${modernElement.name}${ancientElement.name}`
        explanation = `${modernElement.name}与${ancientElement.name}和谐融合，体现文化传承`
        break
        
      case 'evolution':
        username = `新时代${ancientElement.name}`
        explanation = `${ancientElement.name}在现代的演进形态`
        break
        
      default:
        username = `${ancientElement.name}${modernElement.name}`
        explanation = `古今文化的创意融合`
    }
    
    const cultural_depth = (ancientElement.cultural_weight + modernElement.cultural_weight + fusionRule.compatibility_score) / 3
    
    return {
      username,
      fusion_type: fusionRule.fusion_type,
      cultural_depth,
      explanation
    }
  }

  /**
   * 随机选择元素
   */
  private randomSelect<T>(array: T[]): T {
    return array[Math.floor(Math.random() * array.length)]
  }

  /**
   * 初始化古代文化元素
   */
  private async initializeAncientElements(): Promise<void> {
    console.log('📚 初始化古代文化元素...')
    
    const ancientElements: Omit<CulturalElement, 'id'>[] = [
      // 古代诗词类
      {
        name: '诗仙',
        category: '诗词文学',
        era: 'ancient',
        description: '李白的雅号，代表浪漫主义诗歌的巅峰',
        cultural_weight: 0.95,
        usage_frequency: 0.8,
        emotional_tone: 0.8,
        formality_level: 0.7,
        target_audience: ['文学爱好者', '传统文化爱好者'],
        related_concepts: ['浪漫', '才华', '自由'],
        fusion_potential: 0.9
      },
      {
        name: '词圣',
        category: '诗词文学',
        era: 'ancient',
        description: '苏轼的称号，代表词的艺术成就',
        cultural_weight: 0.9,
        usage_frequency: 0.7,
        emotional_tone: 0.6,
        formality_level: 0.8,
        target_audience: ['文学爱好者', '知识分子'],
        related_concepts: ['才华', '豁达', '人生智慧'],
        fusion_potential: 0.85
      },
      {
        name: '青莲居士',
        category: '诗词文学',
        era: 'ancient',
        description: '李白的号，体现隐逸文化',
        cultural_weight: 0.85,
        usage_frequency: 0.6,
        emotional_tone: 0.7,
        formality_level: 0.6,
        target_audience: ['文艺青年', '隐逸爱好者'],
        related_concepts: ['清雅', '隐逸', '自然'],
        fusion_potential: 0.8
      },
      // 古代成语类
      {
        name: '卧薪尝胆',
        category: '成语典故',
        era: 'ancient',
        description: '越王勾践的故事，代表坚韧不拔',
        cultural_weight: 0.9,
        usage_frequency: 0.8,
        emotional_tone: 0.3,
        formality_level: 0.8,
        target_audience: ['励志人群', '职场人士'],
        related_concepts: ['坚持', '复仇', '励志'],
        fusion_potential: 0.9
      },
      {
        name: '破釜沉舟',
        category: '成语典故',
        era: 'ancient',
        description: '项羽的决心，代表背水一战的勇气',
        cultural_weight: 0.85,
        usage_frequency: 0.7,
        emotional_tone: 0.5,
        formality_level: 0.7,
        target_audience: ['创业者', '冒险者'],
        related_concepts: ['决心', '勇气', '孤注一掷'],
        fusion_potential: 0.85
      },
      // 古代哲学类
      {
        name: '道法自然',
        category: '哲学思想',
        era: 'ancient',
        description: '老子的哲学思想，顺应自然规律',
        cultural_weight: 0.95,
        usage_frequency: 0.6,
        emotional_tone: 0.4,
        formality_level: 0.9,
        target_audience: ['哲学爱好者', '修行者'],
        related_concepts: ['自然', '无为', '智慧'],
        fusion_potential: 0.8
      },
      {
        name: '知行合一',
        category: '哲学思想',
        era: 'ancient',
        description: '王阳明的心学思想，理论与实践统一',
        cultural_weight: 0.9,
        usage_frequency: 0.7,
        emotional_tone: 0.5,
        formality_level: 0.8,
        target_audience: ['学者', '实践者'],
        related_concepts: ['实践', '理论', '统一'],
        fusion_potential: 0.9
      }
    ]
    
    // 添加到数据库
    ancientElements.forEach((element, index) => {
      this.addElement({
        id: `ancient_${index + 1}`,
        ...element
      })
    })
    
    console.log(`✅ 古代文化元素初始化完成，共 ${ancientElements.length} 个元素`)
  }

  /**
   * 初始化现代文化元素
   */
  private async initializeModernElements(): Promise<void> {
    console.log('💻 初始化现代文化元素...')
    
    const modernElements: Omit<CulturalElement, 'id'>[] = [
      // 网络文化类
      {
        name: 'UP主',
        category: '网络文化',
        era: 'modern',
        description: 'B站内容创作者，代表新媒体文化',
        cultural_weight: 0.8,
        usage_frequency: 0.9,
        emotional_tone: 0.6,
        formality_level: 0.3,
        target_audience: ['年轻人', '网络用户'],
        related_concepts: ['创作', '分享', '互动'],
        fusion_potential: 0.9
      },
      {
        name: '程序猿',
        category: '职业文化',
        era: 'modern',
        description: '程序员的自嘲称呼，体现技术文化',
        cultural_weight: 0.7,
        usage_frequency: 0.8,
        emotional_tone: 0.2,
        formality_level: 0.4,
        target_audience: ['技术人员', 'IT从业者'],
        related_concepts: ['技术', '加班', '代码'],
        fusion_potential: 0.85
      },
      {
        name: '斜杠青年',
        category: '生活方式',
        era: 'modern',
        description: '多重身份的现代青年，代表多元化生活',
        cultural_weight: 0.75,
        usage_frequency: 0.7,
        emotional_tone: 0.7,
        formality_level: 0.5,
        target_audience: ['年轻人', '自由职业者'],
        related_concepts: ['多元', '自由', '创新'],
        fusion_potential: 0.8
      },
      // 流行文化类
      {
        name: '佛系',
        category: '流行文化',
        era: 'modern',
        description: '不争不抢的生活态度，网络流行词',
        cultural_weight: 0.6,
        usage_frequency: 0.9,
        emotional_tone: 0.3,
        formality_level: 0.2,
        target_audience: ['年轻人', '佛系人群'],
        related_concepts: ['淡然', '不争', '随缘'],
        fusion_potential: 0.9
      },
      {
        name: '社畜',
        category: '职场文化',
        era: 'modern',
        description: '上班族的自嘲，体现现代职场压力',
        cultural_weight: 0.65,
        usage_frequency: 0.8,
        emotional_tone: -0.3,
        formality_level: 0.2,
        target_audience: ['上班族', '职场人士'],
        related_concepts: ['压力', '无奈', '现实'],
        fusion_potential: 0.7
      },
      // 科技文化类
      {
        name: 'AI',
        category: '科技文化',
        era: 'modern',
        description: '人工智能，代表科技前沿',
        cultural_weight: 0.9,
        usage_frequency: 0.9,
        emotional_tone: 0.5,
        formality_level: 0.8,
        target_audience: ['科技爱好者', '未来主义者'],
        related_concepts: ['智能', '未来', '科技'],
        fusion_potential: 0.95
      },
      {
        name: '元宇宙',
        category: '科技文化',
        era: 'modern',
        description: '虚拟世界概念，代表数字化未来',
        cultural_weight: 0.8,
        usage_frequency: 0.7,
        emotional_tone: 0.6,
        formality_level: 0.6,
        target_audience: ['科技爱好者', '游戏玩家'],
        related_concepts: ['虚拟', '未来', '沉浸'],
        fusion_potential: 0.85
      }
    ]
    
    // 添加到数据库
    modernElements.forEach((element, index) => {
      this.addElement({
        id: `modern_${index + 1}`,
        ...element
      })
    })
    
    console.log(`✅ 现代文化元素初始化完成，共 ${modernElements.length} 个元素`)
  }

  /**
   * 初始化融合规则
   */
  private async initializeFusionRules(): Promise<void> {
    console.log('🔗 初始化文化融合规则...')
    
    const fusionRules: Omit<CulturalFusionRule, 'id'>[] = [
      {
        ancient_category: '诗词文学',
        modern_category: '网络文化',
        fusion_type: 'contrast',
        compatibility_score: 0.85,
        fusion_formula: '[古代诗人] + [现代网络身份]',
        examples: ['诗仙UP主', '词圣博主', '青莲居士主播']
      },
      {
        ancient_category: '成语典故',
        modern_category: '职业文化',
        fusion_type: 'evolution',
        compatibility_score: 0.9,
        fusion_formula: '[成语精神] + [现代职业]',
        examples: ['卧薪尝胆程序猿', '破釜沉舟创业者']
      },
      {
        ancient_category: '哲学思想',
        modern_category: '科技文化',
        fusion_type: 'harmony',
        compatibility_score: 0.8,
        fusion_formula: '[古代智慧] + [现代科技]',
        examples: ['道法自然AI', '知行合一元宇宙']
      },
      {
        ancient_category: '诗词文学',
        modern_category: '流行文化',
        fusion_type: 'contrast',
        compatibility_score: 0.75,
        fusion_formula: '[诗词雅致] + [流行随性]',
        examples: ['佛系诗仙', '佛系词圣']
      }
    ]
    
    // 添加到数据库
    fusionRules.forEach((rule, index) => {
      this.fusionRules.set(`rule_${index + 1}`, {
        id: `rule_${index + 1}`,
        ...rule
      })
    })
    
    console.log(`✅ 文化融合规则初始化完成，共 ${fusionRules.length} 条规则`)
  }

  /**
   * 构建分类索引
   */
  private buildCategoryIndex(): void {
    this.categoryIndex.clear()
    
    // 索引古代元素
    for (const [id, element] of this.ancientElements) {
      if (!this.categoryIndex.has(element.category)) {
        this.categoryIndex.set(element.category, [])
      }
      this.categoryIndex.get(element.category)!.push(id)
    }
    
    // 索引现代元素
    for (const [id, element] of this.modernElements) {
      if (!this.categoryIndex.has(element.category)) {
        this.categoryIndex.set(element.category, [])
      }
      this.categoryIndex.get(element.category)!.push(id)
    }
  }

  /**
   * 获取统计信息
   */
  getStatistics() {
    const categories = Array.from(this.categoryIndex.keys())
    const categoryStats = categories.map(category => ({
      category,
      ancient_count: this.getElementsByCategory(category, 'ancient').length,
      modern_count: this.getElementsByCategory(category, 'modern').length,
      total_count: this.getElementsByCategory(category).length
    }))
    
    return {
      total_ancient_elements: this.ancientElements.size,
      total_modern_elements: this.modernElements.size,
      total_elements: this.ancientElements.size + this.modernElements.size,
      total_categories: categories.length,
      total_fusion_rules: this.fusionRules.size,
      category_breakdown: categoryStats,
      initialized: this.initialized
    }
  }
}

/**
 * 全局文化知识库实例
 */
export const culturalKnowledgeBase = new CulturalKnowledgeBase()
