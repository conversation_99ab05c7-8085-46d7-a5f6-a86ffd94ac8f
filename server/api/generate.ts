import { getQuery, setResponseStatus } from 'h3'
import { generateUsername } from '../../utils/generateUsername'

export default defineEventHandler(async (event) => {
  try {
    const query = getQuery(event)

    // 解析 & 默认
    const length = query.length ? Number(query.length) : 8
    const lang = typeof query.lang === 'string' ? query.lang : 'en'
    const type = (query.type as any) || 'Random'
    const allowNumber = query.allowNumber !== 'false'
    const allowSymbol = query.allowSymbol === 'true'

    if (Number.isNaN(length) || length < 2 || length > 32) {
      setResponseStatus(event, 400)
      return { success: false, error: 'INVALID_LENGTH' }
    }

    const username = await generateUsername({ length, lang, type, allowNumber, allowSymbol })
    return { success: true, username }
  } catch (error) {
    console.error('username generation error', error)
    setResponseStatus(event, 500)
    return { success: false, error: 'GENERATION_ERROR' }
  }
})