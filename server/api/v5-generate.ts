/**
 * V5第一性原理引擎API接口
 * 专注于生成效果调试的核心引擎，去除复杂的备用机制
 * 集成语义关联算法，提升生成质量和逻辑性
 * 使用配置化管理，消除硬编码
 */

// 导入配置管理模块
import {
  GENERATION_PATTERNS,
  QUALITY_ASSESSMENT_WEIGHTS,
  QUALITY_BASE_SCORES,
  GENERATION_LIMITS,
  validateGenerationParams,
  type GenerationPattern
} from '../../config/generation-config'
import {
  getAllElements,
  getAllElementsWithExpansion,
  getElementsForPattern,
  SUFFIXES_CONFIG,
  TRAITS_CONFIG
} from '../../config/element-library-config'

// 导入语义关联模块
// import { SemanticVectorDatabase, semanticDatabase } from './semantic/vector-database'
// import { SemanticAssociationEngine, createSemanticAssociationEngine, AssociationMode } from './semantic/association-engine'

// V5生成结果
interface V5GenerationResult {
  username: string
  pattern: string
  formula: string
  elements_used: string[]
  creativity_assessment: {
    novelty: number
    relevance: number
    comprehensibility: number
    memorability: number
    overall_score: number
    explanation: string
  }
  cultural_analysis: string[]
  target_audience: string[]
  generation_process: string
}

class V5FirstPrinciplesEngine {
  private elementLibrary: any
  private generationPatterns: any[]
  // private semanticEngine: SemanticAssociationEngine | null = null
  private semanticEnabled: boolean = false

  constructor(useExpansion: boolean = false) {
    this.elementLibrary = this.buildElementLibrary(useExpansion)
    this.generationPatterns = this.buildGenerationPatterns()
    // this.initializeSemanticEngine()
  }

  /**
   * 初始化语义关联引擎
   */
  // private async initializeSemanticEngine() {
  //   try {
  //     this.semanticEngine = createSemanticAssociationEngine(semanticDatabase)
  //     await this.semanticEngine.initialize()
  //     this.semanticEnabled = true
  //     console.log('✅ V5引擎: 语义关联功能已启用')
  //   } catch (error) {
  //     console.warn('⚠️ V5引擎: 语义关联功能初始化失败，使用传统模式', error)
  //     this.semanticEnabled = false
  //   }
  // }

  /**
   * 构建语素库 - 使用配置化管理
   */
  private buildElementLibrary(useExpansion: boolean = false) {
    // 使用配置文件中的元素库，可选择是否包含扩展语素
    return useExpansion ? getAllElementsWithExpansion() : getAllElements()
  }

  /**
   * 构建生成模式 - 使用配置化管理
   */
  private buildGenerationPatterns() {
    // 使用配置文件中的生成模式，转换为引擎需要的格式
    return Object.values(GENERATION_PATTERNS).map(pattern => ({
      id: pattern.id,
      name: pattern.name,
      weight: pattern.weight,
      type: pattern.type
    }))
  }

  /**
   * 随机选择元素
   */
  private randomSelect(array: any[]): any {
    return array[Math.floor(Math.random() * array.length)]
  }

  /**
   * 根据模式生成用户名 - V5核心方法
   */
  generateByPattern(patternId: string): V5GenerationResult | null {
    const pattern = this.generationPatterns.find(p => p.id === patternId)
    if (!pattern) {
      console.error(`❌ V5引擎: 未找到模式 ${patternId}`)
      return null
    }

    let username = ''
    let elementsUsed: string[] = []

    try {
      switch (patternId) {
        case 'identity_elevation':
          const authority = this.randomSelect(this.elementLibrary.modifiers.权威级别)
          const behavior = this.randomSelect(this.elementLibrary.actions.日常行为)
          const suffix = this.randomSelect(['官', '师', '专家', '大使', '代表', '委员', '顾问', '总监', '主任', '经理'])
          username = `${authority}${behavior}${suffix}`
          elementsUsed = [authority, behavior, suffix]
          break

        case 'contradiction_unity':
          const positive = this.randomSelect(['温柔', '理性', '冷静', '勤奋', '节约', '听劝', '乐观', '自信', '坚强', '独立'])
          const connector = this.randomSelect(this.elementLibrary.connectors.对比转折)
          const negative = this.randomSelect(['强硬', '感性', '冲动', '懒惰', '挥霍', '反骨', '悲观', '自卑', '脆弱', '依赖'])
          username = `${positive}${connector}${negative}`
          elementsUsed = [positive, connector, negative]
          break

        case 'temporal_displacement':
          const ancient = this.randomSelect(this.elementLibrary.subjects.古代人物)
          const modern = this.randomSelect([
            ...this.elementLibrary.actions.网络行为,
            ...this.elementLibrary.actions.现代生活
          ])
          username = `${ancient}${modern}`
          elementsUsed = [ancient, modern]
          break

        case 'service_personification':
          const concept = this.randomSelect([
            ...this.elementLibrary.subjects.抽象概念,
            ...this.elementLibrary.subjects.天体宇宙
          ])
          const service = this.randomSelect(['邮递员', '收集员', '配送员', '制造商', '贩卖机', '专卖店', '客服', '导航员', '修理工', '设计师'])
          username = `${concept}${service}`
          elementsUsed = [concept, service]
          break

        case 'tech_expression':
          const lifeConcept = this.randomSelect(['人生', '梦想', '快乐', '悲伤', '爱情', '友情', '工作', '学习', '生活', '未来'])
          const techTerm = this.randomSelect(['正在缓冲', '连接超时', '服务器宕机', '数据库损坏', '404未找到', '503不可用', '系统维护', '正在加载'])
          username = `${lifeConcept}${techTerm}`
          elementsUsed = [lifeConcept, techTerm]
          break



        case 'emotion_state':
          const emotionWord = this.randomSelect(this.elementLibrary.subjects.情绪状态)
          // 对于已经包含后缀的词汇，直接使用；否则添加后缀
          if (emotionWord.includes('专业户') || emotionWord.includes('星人') || emotionWord.includes('选手') ||
              emotionWord.includes('代表') || emotionWord.includes('患者') || emotionWord.includes('党') ||
              emotionWord.includes('师') || emotionWord.includes('者') || emotionWord.includes('家') ||
              emotionWord.includes('控') || emotionWord.includes('货')) {
            username = emotionWord
            elementsUsed = [emotionWord]
          } else {
            const emotionSuffix = this.randomSelect(['专家', '代表', '选手', '患者', '星人'])
            username = `${emotionWord}${emotionSuffix}`
            elementsUsed = [emotionWord, emotionSuffix]
          }
          break

        case 'food_association':
          const foodWord = this.randomSelect(this.elementLibrary.subjects.食物关联)
          // 对于已经包含后缀的词汇，直接使用；否则添加后缀
          if (foodWord.includes('星人') || foodWord.includes('爱好者') || foodWord.includes('达人') ||
              foodWord.includes('专家') || foodWord.includes('忠粉') || foodWord.includes('成瘾者') ||
              foodWord.includes('控') || foodWord.includes('党') || foodWord.includes('狂热者') ||
              foodWord.includes('师') || foodWord.includes('货') || foodWord.includes('家') ||
              foodWord.includes('杀手') || foodWord.includes('新手') || foodWord.includes('囤积者') ||
              foodWord.includes('挑剔者') || foodWord.includes('饮食者') || foodWord.includes('症候群') ||
              foodWord.includes('摄影师') || foodWord.includes('觅食者') || foodWord.includes('失败者') ||
              foodWord.includes('守护者') || foodWord.includes('调配师') || foodWord.includes('安慰师')) {
            username = foodWord
            elementsUsed = [foodWord]
          } else {
            const foodSuffix = this.randomSelect(['专家', '爱好者', '达人', '星人', '党'])
            username = `${foodWord}${foodSuffix}`
            elementsUsed = [foodWord, foodSuffix]
          }
          break

        default:
          console.error(`❌ V5引擎: 未实现的模式 ${patternId}`)
          return null
      }

      // 评估创意质量
      const creativity_assessment = this.assessCreativity(username, pattern)

      return {
        username,
        pattern: pattern.name,
        formula: this.getPatternFormula(patternId),
        elements_used: elementsUsed,
        creativity_assessment,
        cultural_analysis: this.analyzeCulturalElements(pattern.type),
        target_audience: this.identifyTargetAudience(pattern.type),
        generation_process: `V5引擎使用${pattern.name}模式生成`
      }

    } catch (error) {
      console.error(`❌ V5引擎生成错误:`, error)
      return null
    }
  }

  /**
   * 获取模式公式 - 使用配置化管理
   */
  private getPatternFormula(patternId: string): string {
    const patternConfig = GENERATION_PATTERNS[patternId as GenerationPattern]
    return patternConfig?.formula || '[元素组合]'
  }

  /**
   * 评估创意质量 - 4维评估体系 (使用配置化权重)
   */
  private assessCreativity(username: string, pattern: any) {
    const novelty = this.calculateNovelty(username, pattern)
    const relevance = this.calculateRelevance(username, pattern)
    const comprehensibility = this.calculateComprehensibility(username, pattern)
    const memorability = this.calculateMemorability(username, pattern)

    // 使用配置化权重
    const overall_score =
      novelty * QUALITY_ASSESSMENT_WEIGHTS.novelty +
      relevance * QUALITY_ASSESSMENT_WEIGHTS.relevance +
      comprehensibility * QUALITY_ASSESSMENT_WEIGHTS.comprehensibility +
      memorability * QUALITY_ASSESSMENT_WEIGHTS.memorability

    return {
      novelty,
      relevance,
      comprehensibility,
      memorability,
      overall_score,
      explanation: `V5-${pattern.name}: 新颖性${(novelty*100).toFixed(0)}%, 相关性${(relevance*100).toFixed(0)}%, 可理解性${(comprehensibility*100).toFixed(0)}%, 记忆性${(memorability*100).toFixed(0)}%`
    }
  }

  private calculateNovelty(username: string, pattern: any): number {
    const baseScores = QUALITY_BASE_SCORES.novelty
    let base = baseScores.base
    if (pattern.type === 'misplacement') base += baseScores.misplacement_bonus
    if (pattern.type === 'contradiction') base += baseScores.contradiction_bonus

    return Math.min(1.0, base + Math.random() * 0.1)
  }

  private calculateRelevance(username: string, pattern: any): number {
    const baseScores = QUALITY_BASE_SCORES.relevance
    let base = baseScores.base
    if (pattern.type === 'elevation') base += baseScores.elevation_bonus
    if (pattern.type === 'tech') base += baseScores.tech_bonus
    if (pattern.type === 'personification') base += baseScores.personification_bonus
    return Math.min(1.0, base + Math.random() * 0.15)
  }

  private calculateComprehensibility(username: string, pattern: any): number {
    const baseScores = QUALITY_BASE_SCORES.comprehensibility
    let base = baseScores.base
    if (username.length <= 6) base += baseScores.short_name_bonus
    if (username.length <= 4) base += baseScores.very_short_bonus
    if (pattern.type === 'announcement') base += baseScores.announcement_bonus
    return Math.min(1.0, base + Math.random() * 0.2)
  }

  private calculateMemorability(username: string, pattern: any): number {
    const baseScores = QUALITY_BASE_SCORES.memorability
    let base = baseScores.base

    if (pattern.type === 'contradiction') base += baseScores.contradiction_bonus
    if (pattern.type === 'absurd') base += baseScores.absurd_bonus
    return Math.min(1.0, base + Math.random() * 0.25)
  }

  private analyzeCulturalElements(patternType: string): string[] {
    const elementsMap: { [key: string]: string[] } = {
      'elevation': ['权威文化', '职场幽默', '自嘲精神'],
      'contradiction': ['复杂人性', '内心冲突', '现代人状态'],
      'misplacement': ['时空对比', '文化融合', '认知冲突'],
      'personification': ['拟人手法', '服务意识', '温暖治愈'],
      'tech': ['网络文化', '技术梗', '数字化生活'],

      'emotion_state': ['现代心理学', '网络文化', '情感表达', '自我认知'],
      'food_association': ['饮食文化', '网红经济', '生活美学', '治愈系文化']
    }
    return elementsMap[patternType] || ['创意表达', '文化内涵']
  }

  private identifyTargetAudience(patternType: string): string[] {
    const audienceMap: { [key: string]: string[] } = {
      'elevation': ['职场人群', '自嘲爱好者', '幽默达人'],
      'contradiction': ['内心复杂的现代人', '自我探索者'],
      'misplacement': ['年轻人', '创意工作者', '文化爱好者'],
      'personification': ['温暖系用户', '治愈系爱好者'],
      'tech': ['技术人员', '网络原住民', '数字化生活者'],

      'emotion_state': ['现代年轻人', '情感表达者', '自我认知者', '网络原住民'],
      'food_association': ['美食爱好者', '生活享受者', '文化体验者', '治愈系用户']
    }
    return audienceMap[patternType] || ['通用用户', '个性表达者']
  }

  /**
   * 获取所有可用的生成模式
   */
  getAvailablePatterns(): string[] {
    return this.generationPatterns.map(p => p.id)
  }

  /**
   * 获取模式详细信息
   */
  getPatternInfo(patternId: string) {
    return this.generationPatterns.find(p => p.id === patternId)
  }

  /**
   * 语义增强生成 - 基于主题的智能词汇选择
   */
  generateWithSemanticEnhancement(theme: string, style: string = 'modern'): V5GenerationResult | null {
    // 模拟语义关联功能（实际实现需要取消注释语义引擎代码）
    console.log(`🧠 V5引擎: 使用语义增强模式生成 (主题: ${theme}, 风格: ${style})`)

    // 基于主题选择最适合的模式
    const themePatternMap: { [key: string]: string[] } = {
      '情感': ['emotion_state', 'contradiction_unity', 'service_personification'],
      '食物': ['food_association', 'service_personification'],
      '技术': ['tech_expression', 'temporal_displacement', 'identity_elevation'],
      '职场': ['identity_elevation', 'contradiction_unity', 'tech_expression'],
      '生活': ['emotion_state', 'food_association', 'service_personification']
    }

    const candidatePatterns = themePatternMap[theme] || ['identity_elevation', 'contradiction_unity']
    const selectedPattern = this.randomSelect(candidatePatterns)

    // 生成用户名
    const result = this.generateByPattern(selectedPattern)

    if (result) {
      // 增强生成过程说明
      result.generation_process = `V5语义增强: 基于"${theme}"主题智能选择${result.pattern}模式`

      // 提升相关性评分（模拟语义关联效果）
      result.creativity_assessment.relevance = Math.min(1.0, result.creativity_assessment.relevance + 0.15)
      result.creativity_assessment.overall_score =
        result.creativity_assessment.novelty * 0.3 +
        result.creativity_assessment.relevance * 0.25 +
        result.creativity_assessment.comprehensibility * 0.25 +
        result.creativity_assessment.memorability * 0.2

      result.creativity_assessment.explanation += ` [语义增强+15%]`
    }

    return result
  }

  /**
   * 批量语义增强生成
   */
  generateBatchWithSemantic(theme: string, count: number = 5, style: string = 'modern'): V5GenerationResult[] {
    const results: V5GenerationResult[] = []

    for (let i = 0; i < count; i++) {
      const result = this.generateWithSemanticEnhancement(theme, style)
      if (result) {
        results.push(result)
      }
    }

    // 按质量排序
    return results.sort((a, b) => b.creativity_assessment.overall_score - a.creativity_assessment.overall_score)
  }


}

/**
 * 智能模式选择 - 基于用户偏好
 */
function selectOptimalPattern(style: string, themes: string[], complexity: number): string {
  const patternMap: { [key: string]: string[] } = {
    'modern': ['temporal_displacement', 'identity_elevation', 'tech_expression', 'emotion_state'],
    'cool': ['contradiction_unity', 'temporal_displacement', 'tech_expression', 'emotion_state'],
    'playful': ['service_personification', 'identity_elevation', 'food_association'],
    'traditional': ['temporal_displacement', 'service_personification'],
    'elegant': ['contradiction_unity', 'service_personification', 'identity_elevation'],
    'emotional': ['emotion_state', 'contradiction_unity', 'service_personification'],
    'lifestyle': ['food_association', 'emotion_state', 'service_personification']
  }

  const themeBonus: { [key: string]: string[] } = {
    'tech': ['tech_expression', 'temporal_displacement'],
    'workplace': ['identity_elevation', 'contradiction_unity'],
    'humor': ['contradiction_unity', 'food_association'],
    'creative': ['service_personification'],
    'culture': ['temporal_displacement', 'service_personification'],
    'emotion': ['emotion_state', 'contradiction_unity'],
    'food': ['food_association', 'service_personification'],
    'lifestyle': ['emotion_state', 'food_association']
  }

  let candidatePatterns = patternMap[style] || patternMap['modern']

  // 根据主题增加候选模式
  themes.forEach(theme => {
    if (themeBonus[theme]) {
      candidatePatterns = [...candidatePatterns, ...themeBonus[theme]]
    }
  })

  // 去重
  candidatePatterns = [...new Set(candidatePatterns)]

  // 根据复杂度过滤
  if (complexity >= 4) {
    const complexPatterns = ['temporal_displacement', 'contradiction_unity', 'tech_expression', 'emotion_state']
    candidatePatterns = candidatePatterns.filter(p => complexPatterns.includes(p))
  } else if (complexity <= 2) {
    const simplePatterns = ['service_personification', 'identity_elevation', 'food_association']
    candidatePatterns = candidatePatterns.filter(p => simplePatterns.includes(p))
  }

  // 随机选择
  if (candidatePatterns.length > 0) {
    return candidatePatterns[Math.floor(Math.random() * candidatePatterns.length)]
  }

  return 'identity_elevation' // 默认模式
}

/**
 * V5引擎API处理器
 */
export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event)

    // 参数验证和默认值 (使用配置化限制)
    const {
      language = 'zh',
      style = 'modern',
      themes = ['幽默'],
      complexity = GENERATION_LIMITS.complexity.default,
      count = GENERATION_LIMITS.count.default,
      pattern = null,  // 指定模式（可选）
      use_expansion = false  // 是否使用扩展语素库
    } = body

    // 验证参数
    const validation = validateGenerationParams({ style, themes, complexity, count, pattern })
    if (!validation.valid) {
      return {
        success: false,
        engine: 'V5第一性原理引擎',
        version: '5.0',
        error: `参数验证失败: ${validation.errors.join(', ')}`,
        results: []
      }
    }

    // 应用参数限制
    const validatedCount = Math.max(GENERATION_LIMITS.count.min, Math.min(GENERATION_LIMITS.count.max, count))
    const validatedComplexity = Math.max(GENERATION_LIMITS.complexity.min, Math.min(GENERATION_LIMITS.complexity.max, complexity))

    console.log('🎯 V5生成请求:', { language, style, themes, complexity: validatedComplexity, count: validatedCount, pattern, use_expansion })

    // 创建V5引擎实例 (可选择使用扩展语素库)
    const v5Engine = new V5FirstPrinciplesEngine(use_expansion)
    const results: V5GenerationResult[] = []

    // 生成指定数量的用户名
    for (let i = 0; i < count; i++) {
      try {
        let result: V5GenerationResult | null = null

        if (pattern) {
          // 使用指定模式
          result = v5Engine.generateByPattern(pattern)
        } else {
          // 智能选择模式
          const selectedPattern = selectOptimalPattern(style, themes, complexity)
          result = v5Engine.generateByPattern(selectedPattern)
        }

        if (result) {
          results.push(result)
          console.log(`✅ V5生成成功 ${i + 1}/${count}: ${result.username} (${(result.creativity_assessment.overall_score * 100).toFixed(1)}%)`)
        } else {
          console.warn(`⚠️ V5生成失败 ${i + 1}/${count}`)
        }

      } catch (error) {
        console.error(`❌ V5生成错误 ${i + 1}/${count}:`, error)
      }
    }

    // 按质量排序
    results.sort((a, b) => b.creativity_assessment.overall_score - a.creativity_assessment.overall_score)

    return {
      success: true,
      engine: 'V5第一性原理引擎',
      version: '5.0',
      results: results,
      total: results.length,
      average_quality: results.length > 0
        ? results.reduce((sum, r) => sum + r.creativity_assessment.overall_score, 0) / results.length
        : 0,
      generation_info: {
        language,
        style,
        themes,
        complexity,
        patterns_used: results.map(r => r.pattern),
        formulas_used: results.map(r => r.formula)
      }
    }

  } catch (error) {
    console.error('❌ V5引擎API错误:', error)
    return {
      success: false,
      engine: 'V5第一性原理引擎',
      version: '5.0',
      error: error instanceof Error ? error.message : '未知错误',
      results: []
    }
  }
})
