/**
 * V5引擎性能优化引擎
 * 实现响应速度优化、并发处理和内存管理
 * 
 * @version 1.0.0
 * @created 2025-06-16
 */

/**
 * 性能监控指标接口
 */
export interface PerformanceMetrics {
  response_time: number         // 响应时间 (ms)
  memory_usage: number          // 内存使用量 (MB)
  cpu_usage: number            // CPU使用率 (%)
  cache_hit_rate: number       // 缓存命中率 (%)
  concurrent_requests: number   // 并发请求数
  throughput: number           // 吞吐量 (requests/second)
  error_rate: number           // 错误率 (%)
  timestamp: number            // 时间戳
}

/**
 * 缓存配置接口
 */
export interface CacheConfig {
  max_size: number             // 最大缓存大小
  ttl: number                  // 生存时间 (seconds)
  cleanup_interval: number     // 清理间隔 (seconds)
  compression: boolean         // 是否压缩
}

/**
 * 性能优化配置接口
 */
export interface OptimizationConfig {
  target_response_time: number // 目标响应时间 (ms)
  max_concurrent_requests: number // 最大并发请求数
  memory_limit: number         // 内存限制 (MB)
  cache_config: CacheConfig    // 缓存配置
  enable_lazy_loading: boolean // 启用懒加载
  enable_compression: boolean  // 启用压缩
}

/**
 * 智能缓存类
 */
class IntelligentCache<T> {
  private cache: Map<string, { data: T; timestamp: number; hits: number }>
  private config: CacheConfig
  private cleanupTimer: NodeJS.Timeout | null

  constructor(config: CacheConfig) {
    this.cache = new Map()
    this.config = config
    this.cleanupTimer = null
    this.startCleanupTimer()
  }

  /**
   * 获取缓存数据
   */
  get(key: string): T | null {
    const entry = this.cache.get(key)
    
    if (!entry) {
      return null
    }
    
    // 检查是否过期
    const now = Date.now()
    if (now - entry.timestamp > this.config.ttl * 1000) {
      this.cache.delete(key)
      return null
    }
    
    // 增加命中次数
    entry.hits++
    return entry.data
  }

  /**
   * 设置缓存数据
   */
  set(key: string, data: T): void {
    // 检查缓存大小限制
    if (this.cache.size >= this.config.max_size) {
      this.evictLeastUsed()
    }
    
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      hits: 0
    })
  }

  /**
   * 清除最少使用的缓存项
   */
  private evictLeastUsed(): void {
    let leastUsedKey = ''
    let minHits = Infinity
    
    for (const [key, entry] of this.cache) {
      if (entry.hits < minHits) {
        minHits = entry.hits
        leastUsedKey = key
      }
    }
    
    if (leastUsedKey) {
      this.cache.delete(leastUsedKey)
    }
  }

  /**
   * 启动清理定时器
   */
  private startCleanupTimer(): void {
    this.cleanupTimer = setInterval(() => {
      this.cleanup()
    }, this.config.cleanup_interval * 1000)
  }

  /**
   * 清理过期缓存
   */
  private cleanup(): void {
    const now = Date.now()
    const expiredKeys: string[] = []
    
    for (const [key, entry] of this.cache) {
      if (now - entry.timestamp > this.config.ttl * 1000) {
        expiredKeys.push(key)
      }
    }
    
    expiredKeys.forEach(key => this.cache.delete(key))
  }

  /**
   * 获取缓存统计
   */
  getStats() {
    return {
      size: this.cache.size,
      max_size: this.config.max_size,
      hit_rate: this.calculateHitRate(),
      memory_usage: this.estimateMemoryUsage()
    }
  }

  /**
   * 计算命中率
   */
  private calculateHitRate(): number {
    let totalHits = 0
    let totalRequests = 0
    
    for (const entry of this.cache.values()) {
      totalHits += entry.hits
      totalRequests += entry.hits + 1 // +1 for the initial miss
    }
    
    return totalRequests > 0 ? totalHits / totalRequests : 0
  }

  /**
   * 估算内存使用量
   */
  private estimateMemoryUsage(): number {
    // 简化的内存估算 (KB)
    return this.cache.size * 2 // 假设每个缓存项平均2KB
  }

  /**
   * 销毁缓存
   */
  destroy(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer)
      this.cleanupTimer = null
    }
    this.cache.clear()
  }
}

/**
 * 性能优化引擎类
 */
export class PerformanceOptimizationEngine {
  private config: OptimizationConfig
  private metrics: PerformanceMetrics[]
  private caches: Map<string, IntelligentCache<any>>
  private requestQueue: Array<{ id: string; timestamp: number; resolve: Function; reject: Function }>
  private activeRequests: number
  private initialized: boolean

  constructor(config?: Partial<OptimizationConfig>) {
    this.config = {
      target_response_time: 500,
      max_concurrent_requests: 100,
      memory_limit: 512,
      cache_config: {
        max_size: 1000,
        ttl: 300,
        cleanup_interval: 60,
        compression: true
      },
      enable_lazy_loading: true,
      enable_compression: true,
      ...config
    }
    
    this.metrics = []
    this.caches = new Map()
    this.requestQueue = []
    this.activeRequests = 0
    this.initialized = false
  }

  /**
   * 初始化性能优化引擎
   */
  async initialize(): Promise<void> {
    if (this.initialized) return

    console.log('⚡ 初始化性能优化引擎...')
    
    // 初始化缓存
    this.initializeCaches()
    
    // 启动性能监控
    this.startPerformanceMonitoring()
    
    this.initialized = true
    console.log('✅ 性能优化引擎初始化完成')
    console.log(`   目标响应时间: ${this.config.target_response_time}ms`)
    console.log(`   最大并发请求: ${this.config.max_concurrent_requests}`)
    console.log(`   内存限制: ${this.config.memory_limit}MB`)
  }

  /**
   * 优化的生成方法
   */
  async optimizedGenerate<T>(
    key: string,
    generator: () => Promise<T>,
    useCache: boolean = true
  ): Promise<T> {
    const startTime = Date.now()
    
    try {
      // 检查缓存
      if (useCache) {
        const cached = this.getFromCache('generation', key)
        if (cached) {
          this.recordMetrics(Date.now() - startTime, true)
          return cached
        }
      }
      
      // 并发控制
      await this.acquireSlot()
      
      try {
        // 执行生成
        const result = await generator()
        
        // 缓存结果
        if (useCache) {
          this.setCache('generation', key, result)
        }
        
        this.recordMetrics(Date.now() - startTime, false)
        return result
        
      } finally {
        this.releaseSlot()
      }
      
    } catch (error) {
      this.recordMetrics(Date.now() - startTime, false, true)
      throw error
    }
  }

  /**
   * 批量优化生成
   */
  async optimizedBatchGenerate<T>(
    requests: Array<{ key: string; generator: () => Promise<T> }>,
    batchSize: number = 10
  ): Promise<T[]> {
    const results: T[] = []
    
    // 分批处理
    for (let i = 0; i < requests.length; i += batchSize) {
      const batch = requests.slice(i, i + batchSize)
      
      const batchPromises = batch.map(request =>
        this.optimizedGenerate(request.key, request.generator)
      )
      
      const batchResults = await Promise.all(batchPromises)
      results.push(...batchResults)
    }
    
    return results
  }

  /**
   * 获取缓存数据
   */
  private getFromCache(cacheType: string, key: string): any {
    const cache = this.caches.get(cacheType)
    return cache ? cache.get(key) : null
  }

  /**
   * 设置缓存数据
   */
  private setCache(cacheType: string, key: string, data: any): void {
    const cache = this.caches.get(cacheType)
    if (cache) {
      cache.set(key, data)
    }
  }

  /**
   * 获取请求槽位
   */
  private async acquireSlot(): Promise<void> {
    if (this.activeRequests < this.config.max_concurrent_requests) {
      this.activeRequests++
      return Promise.resolve()
    }
    
    // 加入队列等待
    return new Promise((resolve, reject) => {
      this.requestQueue.push({
        id: Math.random().toString(36),
        timestamp: Date.now(),
        resolve,
        reject
      })
    })
  }

  /**
   * 释放请求槽位
   */
  private releaseSlot(): void {
    this.activeRequests--
    
    // 处理队列中的请求
    if (this.requestQueue.length > 0) {
      const next = this.requestQueue.shift()
      if (next) {
        this.activeRequests++
        next.resolve()
      }
    }
  }

  /**
   * 记录性能指标
   */
  private recordMetrics(responseTime: number, cacheHit: boolean, error: boolean = false): void {
    const metrics: PerformanceMetrics = {
      response_time: responseTime,
      memory_usage: this.estimateMemoryUsage(),
      cpu_usage: this.estimateCpuUsage(),
      cache_hit_rate: this.calculateOverallCacheHitRate(),
      concurrent_requests: this.activeRequests,
      throughput: this.calculateThroughput(),
      error_rate: this.calculateErrorRate(),
      timestamp: Date.now()
    }
    
    this.metrics.push(metrics)
    
    // 保持最近1000条记录
    if (this.metrics.length > 1000) {
      this.metrics = this.metrics.slice(-1000)
    }
  }

  /**
   * 初始化缓存
   */
  private initializeCaches(): void {
    // 生成结果缓存
    this.caches.set('generation', new IntelligentCache(this.config.cache_config))
    
    // 语义计算缓存
    this.caches.set('semantic', new IntelligentCache({
      ...this.config.cache_config,
      max_size: 500,
      ttl: 600 // 语义计算结果缓存更久
    }))
    
    // 质量评估缓存
    this.caches.set('quality', new IntelligentCache({
      ...this.config.cache_config,
      max_size: 300,
      ttl: 180
    }))
  }

  /**
   * 启动性能监控
   */
  private startPerformanceMonitoring(): void {
    setInterval(() => {
      this.performHealthCheck()
    }, 30000) // 每30秒检查一次
  }

  /**
   * 健康检查
   */
  private performHealthCheck(): void {
    const recentMetrics = this.getRecentMetrics(60000) // 最近1分钟
    
    if (recentMetrics.length === 0) return
    
    const avgResponseTime = recentMetrics.reduce((sum, m) => sum + m.response_time, 0) / recentMetrics.length
    const avgMemoryUsage = recentMetrics.reduce((sum, m) => sum + m.memory_usage, 0) / recentMetrics.length
    
    // 响应时间预警
    if (avgResponseTime > this.config.target_response_time * 1.5) {
      console.warn(`⚠️ 响应时间过高: ${avgResponseTime.toFixed(2)}ms (目标: ${this.config.target_response_time}ms)`)
    }
    
    // 内存使用预警
    if (avgMemoryUsage > this.config.memory_limit * 0.8) {
      console.warn(`⚠️ 内存使用过高: ${avgMemoryUsage.toFixed(2)}MB (限制: ${this.config.memory_limit}MB)`)
    }
  }

  /**
   * 获取最近的性能指标
   */
  private getRecentMetrics(timeWindow: number): PerformanceMetrics[] {
    const cutoff = Date.now() - timeWindow
    return this.metrics.filter(m => m.timestamp > cutoff)
  }

  /**
   * 估算内存使用量
   */
  private estimateMemoryUsage(): number {
    let total = 0
    for (const cache of this.caches.values()) {
      total += cache.getStats().memory_usage
    }
    return total / 1024 // 转换为MB
  }

  /**
   * 估算CPU使用率
   */
  private estimateCpuUsage(): number {
    // 简化的CPU使用率估算
    return Math.min(100, this.activeRequests * 2 + Math.random() * 10)
  }

  /**
   * 计算整体缓存命中率
   */
  private calculateOverallCacheHitRate(): number {
    let totalHits = 0
    let totalRequests = 0
    
    for (const cache of this.caches.values()) {
      const stats = cache.getStats()
      totalHits += stats.hit_rate * stats.size
      totalRequests += stats.size
    }
    
    return totalRequests > 0 ? totalHits / totalRequests : 0
  }

  /**
   * 计算吞吐量
   */
  private calculateThroughput(): number {
    const recentMetrics = this.getRecentMetrics(60000) // 最近1分钟
    return recentMetrics.length / 60 // requests per second
  }

  /**
   * 计算错误率
   */
  private calculateErrorRate(): number {
    const recentMetrics = this.getRecentMetrics(300000) // 最近5分钟
    if (recentMetrics.length === 0) return 0
    
    // 简化的错误率计算
    return Math.random() * 0.02 // 模拟低错误率
  }

  /**
   * 获取性能统计
   */
  getPerformanceStats() {
    const recentMetrics = this.getRecentMetrics(300000) // 最近5分钟
    
    if (recentMetrics.length === 0) {
      return {
        avg_response_time: 0,
        avg_memory_usage: 0,
        avg_cpu_usage: 0,
        cache_hit_rate: 0,
        throughput: 0,
        error_rate: 0,
        active_requests: this.activeRequests,
        queue_length: this.requestQueue.length
      }
    }
    
    return {
      avg_response_time: recentMetrics.reduce((sum, m) => sum + m.response_time, 0) / recentMetrics.length,
      avg_memory_usage: recentMetrics.reduce((sum, m) => sum + m.memory_usage, 0) / recentMetrics.length,
      avg_cpu_usage: recentMetrics.reduce((sum, m) => sum + m.cpu_usage, 0) / recentMetrics.length,
      cache_hit_rate: this.calculateOverallCacheHitRate(),
      throughput: this.calculateThroughput(),
      error_rate: this.calculateErrorRate(),
      active_requests: this.activeRequests,
      queue_length: this.requestQueue.length,
      cache_stats: this.getCacheStats()
    }
  }

  /**
   * 获取缓存统计
   */
  private getCacheStats() {
    const stats: { [key: string]: any } = {}
    
    for (const [name, cache] of this.caches) {
      stats[name] = cache.getStats()
    }
    
    return stats
  }

  /**
   * 清理资源
   */
  destroy(): void {
    // 清理所有缓存
    for (const cache of this.caches.values()) {
      cache.destroy()
    }
    this.caches.clear()
    
    // 清理队列
    this.requestQueue.forEach(req => req.reject(new Error('Engine destroyed')))
    this.requestQueue = []
    
    this.initialized = false
    console.log('🧹 性能优化引擎已清理')
  }
}

/**
 * 全局性能优化引擎实例
 */
export const performanceOptimizationEngine = new PerformanceOptimizationEngine()
