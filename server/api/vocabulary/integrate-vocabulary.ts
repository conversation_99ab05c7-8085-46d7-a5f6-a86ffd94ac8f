/**
 * 词汇集成API接口
 * 将词汇扩展引擎的词汇集成到V5语素库中
 */

import { VocabularyToV5MorphemeIntegrator } from './integrate-to-v5-morpheme-library'

export default defineEventHandler(async (event) => {
  try {
    console.log('🚀 开始执行词汇扩展引擎到V5语素库集成...')

    // 创建集成器
    const integrator = new VocabularyToV5MorphemeIntegrator()

    // 执行完整集成
    const integrationResult = await integrator.executeFullIntegration()

    // 生成V5配置代码
    const configCode = await integrator.generateV5ConfigCode(integrationResult)

    console.log('✅ 词汇集成完成')

    return {
      success: true,
      message: '词汇扩展引擎到V5语素库集成成功',
      integration_result: integrationResult,
      config_code: configCode,
      summary: {
        total_categories: integrationResult.statistics.total_categories_added,
        total_elements: integrationResult.statistics.total_elements_added,
        breakdown: {
          subjects: integrationResult.statistics.subjects_added,
          actions: integrationResult.statistics.actions_added,
          modifiers: integrationResult.statistics.modifiers_added,
          suffixes: integrationResult.statistics.suffixes_added,
          traits: integrationResult.statistics.traits_added
        }
      }
    }

  } catch (error) {
    console.error('❌ 词汇集成失败:', error)
    
    return {
      success: false,
      error: error instanceof Error ? error.message : '未知错误',
      message: '词汇集成过程中发生错误'
    }
  }
})
