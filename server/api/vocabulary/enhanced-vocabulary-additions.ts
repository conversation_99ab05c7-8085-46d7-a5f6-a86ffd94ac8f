/**
 * 增强词汇库补充 - 基于第一性原理的"有趣"词汇扩展
 * 目标：让生成结果更有趣、更具创意、更有个性
 */

export interface EnhancedVocabularyEntry {
  word: string
  category: string
  subcategory: string
  interest_score: number  // 有趣程度评分 (0-1)
  creativity_boost: number  // 创意加成 (0-1)
  cultural_depth: number   // 文化深度 (0-1)
  usage_context: string[]  // 使用语境
  combination_potential: number  // 组合潜力 (0-1)
}

/**
 * 基于第一性原理的"有趣"词汇库
 * 核心原则：
 * 1. 反差萌 - 意想不到的组合
 * 2. 文化梗 - 有文化内涵的趣味
 * 3. 时代感 - 紧跟潮流但不过时
 * 4. 个性化 - 独特而不怪异
 * 5. 记忆点 - 容易记住和传播
 */
export const ENHANCED_INTERESTING_VOCABULARY: { [category: string]: EnhancedVocabularyEntry[] } = {
  
  // 反差萌系列 - 传统与现代的有趣碰撞
  contrast_cute: [
    {
      word: '佛系程序猿',
      category: 'contrast_profession',
      subcategory: 'zen_tech',
      interest_score: 0.95,
      creativity_boost: 0.9,
      cultural_depth: 0.8,
      usage_context: ['程序员', '佛系', '工作状态'],
      combination_potential: 0.85
    },
    {
      word: '社恐主播',
      category: 'contrast_personality',
      subcategory: 'paradox_identity',
      interest_score: 0.9,
      creativity_boost: 0.85,
      cultural_depth: 0.6,
      usage_context: ['社交恐惧', '直播', '矛盾'],
      combination_potential: 0.8
    },
    {
      word: '躺平侠客',
      category: 'contrast_lifestyle',
      subcategory: 'modern_ancient',
      interest_score: 0.88,
      creativity_boost: 0.82,
      cultural_depth: 0.85,
      usage_context: ['躺平', '武侠', '生活态度'],
      combination_potential: 0.9
    },
    {
      word: '内卷书生',
      category: 'contrast_lifestyle',
      subcategory: 'modern_ancient',
      interest_score: 0.85,
      creativity_boost: 0.8,
      cultural_depth: 0.9,
      usage_context: ['内卷', '传统文人', '现代压力'],
      combination_potential: 0.88
    },
    {
      word: '摆烂诗仙',
      category: 'contrast_lifestyle',
      subcategory: 'modern_ancient',
      interest_score: 0.92,
      creativity_boost: 0.88,
      cultural_depth: 0.95,
      usage_context: ['摆烂', '诗仙', '文化反差'],
      combination_potential: 0.9
    }
  ],

  // 文化梗系列 - 有文化内涵的趣味表达
  cultural_memes: [
    {
      word: '赛博朋克',
      category: 'cultural_fusion',
      subcategory: 'tech_culture',
      interest_score: 0.9,
      creativity_boost: 0.95,
      cultural_depth: 0.7,
      usage_context: ['科技', '朋克', '未来感'],
      combination_potential: 0.85
    },
    {
      word: '蒸汽波',
      category: 'cultural_fusion',
      subcategory: 'retro_future',
      interest_score: 0.85,
      creativity_boost: 0.9,
      cultural_depth: 0.75,
      usage_context: ['复古', '未来', '美学'],
      combination_potential: 0.8
    },
    {
      word: '赛博道士',
      category: 'cultural_fusion',
      subcategory: 'tech_tradition',
      interest_score: 0.95,
      creativity_boost: 0.92,
      cultural_depth: 0.9,
      usage_context: ['科技', '道教', '文化融合'],
      combination_potential: 0.95
    },
    {
      word: '数字游民',
      category: 'lifestyle_concept',
      subcategory: 'modern_nomad',
      interest_score: 0.8,
      creativity_boost: 0.85,
      cultural_depth: 0.6,
      usage_context: ['远程工作', '自由', '数字化'],
      combination_potential: 0.75
    },
    {
      word: '元宇宙原住民',
      category: 'tech_identity',
      subcategory: 'virtual_native',
      interest_score: 0.88,
      creativity_boost: 0.9,
      cultural_depth: 0.65,
      usage_context: ['元宇宙', '原生', '虚拟世界'],
      combination_potential: 0.8
    }
  ],

  // 情感共鸣系列 - 现代人的真实状态
  emotional_resonance: [
    {
      word: '精神内耗怪',
      category: 'modern_psychology',
      subcategory: 'mental_state',
      interest_score: 0.9,
      creativity_boost: 0.75,
      cultural_depth: 0.7,
      usage_context: ['精神内耗', '心理状态', '现代焦虑'],
      combination_potential: 0.85
    },
    {
      word: 'emo文学少女',
      category: 'youth_culture',
      subcategory: 'emotional_expression',
      interest_score: 0.85,
      creativity_boost: 0.8,
      cultural_depth: 0.75,
      usage_context: ['emo', '文学', '少女心'],
      combination_potential: 0.8
    },
    {
      word: '治愈系大叔',
      category: 'healing_personality',
      subcategory: 'warm_character',
      interest_score: 0.82,
      creativity_boost: 0.75,
      cultural_depth: 0.6,
      usage_context: ['治愈', '大叔', '温暖'],
      combination_potential: 0.85
    },
    {
      word: '破防专家',
      category: 'emotional_impact',
      subcategory: 'psychological_effect',
      interest_score: 0.88,
      creativity_boost: 0.8,
      cultural_depth: 0.65,
      usage_context: ['破防', '情感冲击', '心理'],
      combination_potential: 0.9
    },
    {
      word: '松弛感大师',
      category: 'lifestyle_guru',
      subcategory: 'relaxed_vibe',
      interest_score: 0.85,
      creativity_boost: 0.78,
      cultural_depth: 0.7,
      usage_context: ['松弛感', '生活态度', '从容'],
      combination_potential: 0.82
    }
  ],

  // 创意职业系列 - 新时代的有趣职业
  creative_professions: [
    {
      word: '氛围感制造师',
      category: 'creative_job',
      subcategory: 'atmosphere_creator',
      interest_score: 0.9,
      creativity_boost: 0.95,
      cultural_depth: 0.6,
      usage_context: ['氛围', '制造', '创意'],
      combination_potential: 0.88
    },
    {
      word: '仪式感设计师',
      category: 'creative_job',
      subcategory: 'ritual_designer',
      interest_score: 0.88,
      creativity_boost: 0.9,
      cultural_depth: 0.75,
      usage_context: ['仪式感', '设计', '生活美学'],
      combination_potential: 0.85
    },
    {
      word: '情绪价值输出者',
      category: 'emotional_service',
      subcategory: 'value_provider',
      interest_score: 0.85,
      creativity_boost: 0.8,
      cultural_depth: 0.7,
      usage_context: ['情绪价值', '输出', '服务'],
      combination_potential: 0.9
    },
    {
      word: '多巴胺调色师',
      category: 'creative_job',
      subcategory: 'happiness_creator',
      interest_score: 0.92,
      creativity_boost: 0.88,
      cultural_depth: 0.65,
      usage_context: ['多巴胺', '调色', '快乐'],
      combination_potential: 0.85
    },
    {
      word: '灵魂摆渡人',
      category: 'spiritual_guide',
      subcategory: 'soul_helper',
      interest_score: 0.9,
      creativity_boost: 0.85,
      cultural_depth: 0.9,
      usage_context: ['灵魂', '摆渡', '精神引导'],
      combination_potential: 0.88
    }
  ],

  // 生活美学系列 - 精致生活的有趣表达
  lifestyle_aesthetics: [
    {
      word: '慢生活艺术家',
      category: 'lifestyle_artist',
      subcategory: 'slow_living',
      interest_score: 0.85,
      creativity_boost: 0.8,
      cultural_depth: 0.8,
      usage_context: ['慢生活', '艺术', '生活方式'],
      combination_potential: 0.85
    },
    {
      word: '极简主义者',
      category: 'lifestyle_philosophy',
      subcategory: 'minimalism',
      interest_score: 0.8,
      creativity_boost: 0.75,
      cultural_depth: 0.75,
      usage_context: ['极简', '主义', '生活哲学'],
      combination_potential: 0.8
    },
    {
      word: '烟火气收集者',
      category: 'life_collector',
      subcategory: 'human_warmth',
      interest_score: 0.88,
      creativity_boost: 0.85,
      cultural_depth: 0.9,
      usage_context: ['烟火气', '收集', '人间温暖'],
      combination_potential: 0.9
    },
    {
      word: '小确幸猎人',
      category: 'happiness_hunter',
      subcategory: 'small_joy',
      interest_score: 0.85,
      creativity_boost: 0.8,
      cultural_depth: 0.7,
      usage_context: ['小确幸', '猎人', '微小幸福'],
      combination_potential: 0.85
    },
    {
      word: '质感生活家',
      category: 'quality_lifestyle',
      subcategory: 'texture_life',
      interest_score: 0.82,
      creativity_boost: 0.78,
      cultural_depth: 0.75,
      usage_context: ['质感', '生活', '品质'],
      combination_potential: 0.8
    }
  ],

  // 网络文化系列 - 互联网原生的有趣表达
  internet_culture: [
    {
      word: '表情包大师',
      category: 'meme_creator',
      subcategory: 'emoji_master',
      interest_score: 0.9,
      creativity_boost: 0.85,
      cultural_depth: 0.6,
      usage_context: ['表情包', '大师', '网络文化'],
      combination_potential: 0.88
    },
    {
      word: '弹幕诗人',
      category: 'digital_poet',
      subcategory: 'comment_artist',
      interest_score: 0.88,
      creativity_boost: 0.9,
      cultural_depth: 0.75,
      usage_context: ['弹幕', '诗人', '数字文学'],
      combination_potential: 0.85
    },
    {
      word: '热梗考古学家',
      category: 'meme_researcher',
      subcategory: 'trend_archaeologist',
      interest_score: 0.92,
      creativity_boost: 0.88,
      cultural_depth: 0.7,
      usage_context: ['热梗', '考古', '网络文化'],
      combination_potential: 0.9
    },
    {
      word: '流量密码破译师',
      category: 'traffic_analyst',
      subcategory: 'viral_decoder',
      interest_score: 0.85,
      creativity_boost: 0.82,
      cultural_depth: 0.6,
      usage_context: ['流量密码', '破译', '网络营销'],
      combination_potential: 0.85
    },
    {
      word: '算法驯兽师',
      category: 'tech_tamer',
      subcategory: 'algorithm_master',
      interest_score: 0.9,
      creativity_boost: 0.88,
      cultural_depth: 0.65,
      usage_context: ['算法', '驯兽', '技术控制'],
      combination_potential: 0.9
    }
  ],

  // 二次元文化系列 - 动漫文化的有趣融合
  anime_culture: [
    {
      word: '现充终结者',
      category: 'otaku_identity',
      subcategory: 'reality_fighter',
      interest_score: 0.88,
      creativity_boost: 0.85,
      cultural_depth: 0.7,
      usage_context: ['现充', '终结者', '宅文化'],
      combination_potential: 0.85
    },
    {
      word: '二次元考古家',
      category: 'anime_researcher',
      subcategory: 'culture_explorer',
      interest_score: 0.85,
      creativity_boost: 0.8,
      cultural_depth: 0.75,
      usage_context: ['二次元', '考古', '文化研究'],
      combination_potential: 0.8
    },
    {
      word: '三坑少女',
      category: 'hobby_enthusiast',
      subcategory: 'fashion_otaku',
      interest_score: 0.82,
      creativity_boost: 0.78,
      cultural_depth: 0.7,
      usage_context: ['三坑', '少女', 'JK制服'],
      combination_potential: 0.85
    },
    {
      word: '中二病晚期患者',
      category: 'personality_type',
      subcategory: 'chuunibyou',
      interest_score: 0.9,
      creativity_boost: 0.82,
      cultural_depth: 0.75,
      usage_context: ['中二病', '晚期', '个性表达'],
      combination_potential: 0.88
    },
    {
      word: '元气充电宝',
      category: 'energy_source',
      subcategory: 'positive_battery',
      interest_score: 0.88,
      creativity_boost: 0.85,
      cultural_depth: 0.6,
      usage_context: ['元气', '充电宝', '正能量'],
      combination_potential: 0.9
    }
  ],

  // 美食文化系列 - 吃货的有趣世界
  food_culture: [
    {
      word: '深夜食堂老板',
      category: 'food_provider',
      subcategory: 'night_chef',
      interest_score: 0.85,
      creativity_boost: 0.8,
      cultural_depth: 0.85,
      usage_context: ['深夜食堂', '老板', '温暖'],
      combination_potential: 0.88
    },
    {
      word: '卡路里计算器',
      category: 'health_monitor',
      subcategory: 'calorie_tracker',
      interest_score: 0.8,
      creativity_boost: 0.75,
      cultural_depth: 0.5,
      usage_context: ['卡路里', '计算', '健康'],
      combination_potential: 0.8
    },
    {
      word: '奶茶续命师',
      category: 'beverage_expert',
      subcategory: 'tea_lifesaver',
      interest_score: 0.88,
      creativity_boost: 0.82,
      cultural_depth: 0.6,
      usage_context: ['奶茶', '续命', '生活必需'],
      combination_potential: 0.85
    },
    {
      word: '火锅外交官',
      category: 'social_connector',
      subcategory: 'hotpot_diplomat',
      interest_score: 0.9,
      creativity_boost: 0.85,
      cultural_depth: 0.8,
      usage_context: ['火锅', '外交', '社交'],
      combination_potential: 0.9
    },
    {
      word: '美食地图绘制者',
      category: 'food_explorer',
      subcategory: 'culinary_mapper',
      interest_score: 0.85,
      creativity_boost: 0.8,
      cultural_depth: 0.7,
      usage_context: ['美食地图', '绘制', '探索'],
      combination_potential: 0.85
    }
  ]
}

/**
 * 有趣词汇的组合规则
 */
export const INTERESTING_COMBINATION_RULES = {
  // 反差萌组合：传统 + 现代
  contrast_combinations: [
    { traditional: ['诗仙', '词圣', '书生', '侠客', '道士'], modern: ['程序猿', '设计师', '主播', '博主', 'UP主'] },
    { ancient: ['古风', '雅致', '清雅', '文雅'], contemporary: ['社恐', '内卷', '躺平', '摆烂', 'emo'] }
  ],
  
  // 职业升级：基础职业 + 有趣修饰
  profession_upgrades: [
    { base: ['程序员', '设计师', '产品经理'], modifiers: ['佛系', '社恐', '治愈系', '松弛感', '氛围感'] },
    { base: ['老师', '医生', '律师'], modifiers: ['温暖', '治愈', '专业', '靠谱', '贴心'] }
  ],
  
  // 情感状态：复杂情感的有趣表达
  emotional_states: [
    { positive: ['温暖', '治愈', '元气', '阳光'], negative: ['社恐', 'emo', '内耗', '破防'] },
    { calm: ['佛系', '淡定', '松弛', '从容'], energetic: ['元气', '活力', '朝气', '满满'] }
  ]
}

/**
 * 计算词汇的有趣程度综合评分
 */
export function calculateInterestScore(entry: EnhancedVocabularyEntry): number {
  const weights = {
    interest_score: 0.4,
    creativity_boost: 0.3,
    cultural_depth: 0.2,
    combination_potential: 0.1
  }
  
  return (
    entry.interest_score * weights.interest_score +
    entry.creativity_boost * weights.creativity_boost +
    entry.cultural_depth * weights.cultural_depth +
    entry.combination_potential * weights.combination_potential
  )
}

/**
 * 获取最有趣的词汇列表
 */
export function getMostInterestingWords(limit: number = 50): EnhancedVocabularyEntry[] {
  const allWords = Object.values(ENHANCED_INTERESTING_VOCABULARY).flat()
  
  return allWords
    .sort((a, b) => calculateInterestScore(b) - calculateInterestScore(a))
    .slice(0, limit)
}

/**
 * 按类别获取有趣词汇
 */
export function getInterestingWordsByCategory(category: string): EnhancedVocabularyEntry[] {
  return ENHANCED_INTERESTING_VOCABULARY[category] || []
}

/**
 * 导出所有增强词汇
 */
export function exportAllEnhancedVocabulary(): EnhancedVocabularyEntry[] {
  return Object.values(ENHANCED_INTERESTING_VOCABULARY).flat()
}
