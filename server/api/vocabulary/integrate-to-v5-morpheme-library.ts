/**
 * 词汇扩展引擎到V5语素库集成工具
 * 将词汇扩展引擎中的丰富词汇适配到V5引擎的语素库配置中
 * 
 * @version 1.0.0
 * @created 2025-06-19
 */

import { VocabularyExpansionEngine } from './vocabulary-expansion-engine'
import type { VocabularyEntry } from './vocabulary-expansion-engine'

/**
 * V5语素库配置格式
 */
interface V5ElementConfig {
  category: string
  description: string
  elements: string[]
  usage_patterns: string[]
  cultural_weight?: number
  frequency_weight?: number
  authority_weight?: number
  emotional_weight?: number
  formality_weight?: number
  polarity?: 'positive' | 'negative' | 'neutral'
}

/**
 * 集成结果接口
 */
interface IntegrationResult {
  subjects_config: Record<string, V5ElementConfig>
  actions_config: Record<string, V5ElementConfig>
  modifiers_config: Record<string, V5ElementConfig>
  suffixes_config: Record<string, V5ElementConfig>
  traits_config: Record<string, V5ElementConfig>
  statistics: {
    total_categories_added: number
    total_elements_added: number
    subjects_added: number
    actions_added: number
    modifiers_added: number
    suffixes_added: number
    traits_added: number
  }
}

/**
 * 词汇扩展引擎到V5语素库集成器
 */
export class VocabularyToV5MorphemeIntegrator {
  private vocabularyEngine: VocabularyExpansionEngine
  private initialized: boolean = false

  constructor() {
    this.vocabularyEngine = new VocabularyExpansionEngine()
  }

  /**
   * 初始化集成器
   */
  async initialize(): Promise<void> {
    if (this.initialized) return

    console.log('🚀 初始化词汇扩展引擎到V5语素库集成器...')
    await this.vocabularyEngine.initialize()
    this.initialized = true
    console.log('✅ 集成器初始化完成')
  }

  /**
   * 执行完整集成
   */
  async executeFullIntegration(): Promise<IntegrationResult> {
    await this.initialize()

    console.log('🔄 开始执行词汇扩展引擎到V5语素库的完整集成...')

    const result: IntegrationResult = {
      subjects_config: {},
      actions_config: {},
      modifiers_config: {},
      suffixes_config: {},
      traits_config: {},
      statistics: {
        total_categories_added: 0,
        total_elements_added: 0,
        subjects_added: 0,
        actions_added: 0,
        modifiers_added: 0,
        suffixes_added: 0,
        traits_added: 0
      }
    }

    // 1. 集成情感词汇 (作为特质)
    await this.integrateEmotionVocabulary(result)

    // 2. 集成职业词汇 (作为主体)
    await this.integrateProfessionVocabulary(result)

    // 3. 集成特征词汇 (作为修饰词和特质)
    await this.integrateCharacteristicVocabulary(result)

    // 4. 集成传统文化词汇
    await this.integrateTraditionalVocabulary(result)

    // 5. 集成流行词汇
    await this.integratePopularVocabulary(result)

    // 6. 集成时代潮流词汇
    await this.integrateTrendVocabulary(result)

    // 计算统计信息
    this.calculateStatistics(result)

    console.log('✅ 词汇扩展引擎到V5语素库集成完成')
    console.log(`📊 集成统计: 新增${result.statistics.total_categories_added}个类别，${result.statistics.total_elements_added}个语素`)

    return result
  }

  /**
   * 集成情感词汇
   */
  private async integrateEmotionVocabulary(result: IntegrationResult): Promise<void> {
    console.log('💝 集成情感词汇...')

    const emotionWords = await this.vocabularyEngine.expandEmotionVocabulary()
    const groupedEmotions = this.groupVocabularyBySubcategory(emotionWords)

    for (const [subcategory, entries] of Object.entries(groupedEmotions)) {
      const categoryName = this.translateSubcategoryName(subcategory, 'emotion')
      
      result.traits_config[categoryName] = {
        category: categoryName,
        description: this.generateCategoryDescription(subcategory, 'emotion'),
        elements: entries.map(e => e.word),
        usage_patterns: ['contradiction_unity', 'emotion_state'],
        cultural_weight: this.calculateCulturalWeight(entries),
        polarity: this.determinePolarity(entries)
      }

      result.statistics.traits_added += entries.length
    }

    console.log(`   ✅ 情感词汇集成完成: ${Object.keys(groupedEmotions).length}个类别`)
  }

  /**
   * 集成职业词汇
   */
  private async integrateProfessionVocabulary(result: IntegrationResult): Promise<void> {
    console.log('👔 集成职业词汇...')

    const professionWords = await this.vocabularyEngine.expandProfessionVocabulary()
    const groupedProfessions = this.groupVocabularyBySubcategory(professionWords)

    for (const [subcategory, entries] of Object.entries(groupedProfessions)) {
      const categoryName = this.translateSubcategoryName(subcategory, 'profession')
      
      result.subjects_config[categoryName] = {
        category: categoryName,
        description: this.generateCategoryDescription(subcategory, 'profession'),
        elements: entries.map(e => e.word),
        usage_patterns: ['identity_elevation', 'temporal_displacement'],
        cultural_weight: this.calculateCulturalWeight(entries)
      }

      result.statistics.subjects_added += entries.length
    }

    console.log(`   ✅ 职业词汇集成完成: ${Object.keys(groupedProfessions).length}个类别`)
  }

  /**
   * 集成特征词汇
   */
  private async integrateCharacteristicVocabulary(result: IntegrationResult): Promise<void> {
    console.log('⭐ 集成特征词汇...')

    const characteristicWords = await this.vocabularyEngine.expandCharacteristicVocabulary()
    const groupedCharacteristics = this.groupVocabularyBySubcategory(characteristicWords)

    for (const [subcategory, entries] of Object.entries(groupedCharacteristics)) {
      const categoryName = this.translateSubcategoryName(subcategory, 'characteristic')
      
      // 根据特征类型决定放入修饰词还是特质
      if (this.isModifierLike(subcategory)) {
        result.modifiers_config[categoryName] = {
          category: categoryName,
          description: this.generateCategoryDescription(subcategory, 'characteristic'),
          elements: entries.map(e => e.word),
          usage_patterns: ['identity_elevation', 'contradiction_unity'],
          authority_weight: this.calculateAuthorityWeight(entries)
        }
        result.statistics.modifiers_added += entries.length
      } else {
        result.traits_config[categoryName] = {
          category: categoryName,
          description: this.generateCategoryDescription(subcategory, 'characteristic'),
          elements: entries.map(e => e.word),
          usage_patterns: ['contradiction_unity'],
          cultural_weight: this.calculateCulturalWeight(entries),
          polarity: this.determinePolarity(entries)
        }
        result.statistics.traits_added += entries.length
      }
    }

    console.log(`   ✅ 特征词汇集成完成: ${Object.keys(groupedCharacteristics).length}个类别`)
  }

  /**
   * 集成传统文化词汇
   */
  private async integrateTraditionalVocabulary(result: IntegrationResult): Promise<void> {
    console.log('🏮 集成传统文化词汇...')

    const traditionalWords = await this.vocabularyEngine.expandTraditionalCulturalVocabulary()
    const groupedTraditional = this.groupVocabularyBySubcategory(traditionalWords)

    for (const [subcategory, entries] of Object.entries(groupedTraditional)) {
      const categoryName = this.translateSubcategoryName(subcategory, 'traditional')
      
      // 根据词汇特征分类
      if (this.isSubjectLike(entries)) {
        result.subjects_config[categoryName] = {
          category: categoryName,
          description: this.generateCategoryDescription(subcategory, 'traditional'),
          elements: entries.map(e => e.word),
          usage_patterns: ['temporal_displacement', 'service_personification'],
          cultural_weight: 1.2 // 传统文化权重较高
        }
        result.statistics.subjects_added += entries.length
      } else if (this.isSuffixLike(entries)) {
        result.suffixes_config[categoryName] = {
          category: categoryName,
          description: this.generateCategoryDescription(subcategory, 'traditional'),
          elements: entries.map(e => e.word),
          usage_patterns: ['identity_elevation', 'temporal_displacement'],
          formality_weight: 1.0
        }
        result.statistics.suffixes_added += entries.length
      } else {
        result.traits_config[categoryName] = {
          category: categoryName,
          description: this.generateCategoryDescription(subcategory, 'traditional'),
          elements: entries.map(e => e.word),
          usage_patterns: ['contradiction_unity'],
          cultural_weight: 1.2,
          polarity: 'positive'
        }
        result.statistics.traits_added += entries.length
      }
    }

    console.log(`   ✅ 传统文化词汇集成完成: ${Object.keys(groupedTraditional).length}个类别`)
  }

  /**
   * 集成流行词汇
   */
  private async integratePopularVocabulary(result: IntegrationResult): Promise<void> {
    console.log('🌟 集成流行词汇...')

    const popularWords = await this.vocabularyEngine.expandPopularVocabulary()
    const groupedPopular = this.groupVocabularyBySubcategory(popularWords)

    for (const [subcategory, entries] of Object.entries(groupedPopular)) {
      const categoryName = this.translateSubcategoryName(subcategory, 'popular')
      
      result.traits_config[categoryName] = {
        category: categoryName,
        description: this.generateCategoryDescription(subcategory, 'popular'),
        elements: entries.map(e => e.word),
        usage_patterns: ['emotion_state', 'contradiction_unity'],
        cultural_weight: 1.1,
        polarity: this.determinePolarity(entries)
      }

      result.statistics.traits_added += entries.length
    }

    console.log(`   ✅ 流行词汇集成完成: ${Object.keys(groupedPopular).length}个类别`)
  }

  /**
   * 集成时代潮流词汇
   */
  private async integrateTrendVocabulary(result: IntegrationResult): Promise<void> {
    console.log('🚀 集成时代潮流词汇...')

    const trendWords = await this.vocabularyEngine.expandTrendVocabulary()
    const groupedTrend = this.groupVocabularyBySubcategory(trendWords)

    for (const [subcategory, entries] of Object.entries(groupedTrend)) {
      const categoryName = this.translateSubcategoryName(subcategory, 'trend')
      
      if (this.isSubjectLike(entries)) {
        result.subjects_config[categoryName] = {
          category: categoryName,
          description: this.generateCategoryDescription(subcategory, 'trend'),
          elements: entries.map(e => e.word),
          usage_patterns: ['temporal_displacement', 'identity_elevation'],
          cultural_weight: 1.0
        }
        result.statistics.subjects_added += entries.length
      } else {
        result.traits_config[categoryName] = {
          category: categoryName,
          description: this.generateCategoryDescription(subcategory, 'trend'),
          elements: entries.map(e => e.word),
          usage_patterns: ['emotion_state', 'tech_expression'],
          cultural_weight: 1.0,
          polarity: this.determinePolarity(entries)
        }
        result.statistics.traits_added += entries.length
      }
    }

    console.log(`   ✅ 时代潮流词汇集成完成: ${Object.keys(groupedTrend).length}个类别`)
  }

  /**
   * 按子类别分组词汇
   */
  private groupVocabularyBySubcategory(entries: VocabularyEntry[]): Record<string, VocabularyEntry[]> {
    const grouped: Record<string, VocabularyEntry[]> = {}

    for (const entry of entries) {
      if (!grouped[entry.subcategory]) {
        grouped[entry.subcategory] = []
      }
      grouped[entry.subcategory].push(entry)
    }

    return grouped
  }

  /**
   * 翻译子类别名称为中文
   */
  private translateSubcategoryName(subcategory: string, type: string): string {
    const translations: Record<string, string> = {
      // 情感类别
      'basic_emotions': '基础情感',
      'positive_emotions': '积极情感',
      'deep_emotions': '深层情感',
      'artistic_emotions': '文艺情感',
      'modern_emotions': '现代情感',
      'advanced_emotions': '高级情感',

      // 职业类别
      'traditional_professions': '传统职业',
      'modern_professions': '现代职业',
      'creative_professions': '创意职业',
      'emerging_professions': '新兴职业',
      'service_professions': '服务职业',

      // 特征类别
      'personality_traits': '性格特征',
      'ability_traits': '能力特征',
      'quality_traits': '品质特征',
      'style_traits': '风格特征',
      'state_traits': '状态特征',
      'advanced_characteristics': '高级特征',

      // 传统文化类别
      'classical_poetry': '古典诗词',
      'traditional_concepts': '传统概念',
      'classical_expressions': '经典表达',
      'traditional_virtues': '传统美德',
      'scholar_titles': '文人雅士',

      // 流行文化类别
      'daily_life': '日常生活',
      'internet_popular': '网络流行',
      'modern_expressions': '现代表达',
      'emotional_expressions': '情感表达',

      // 时代潮流类别
      'anime_culture': '二次元文化',
      'internet_subculture': '网络亚文化',
      'emerging_concepts': '新兴概念',
      'gen_z_culture': 'Z世代文化',
      'trend_concepts': '潮流概念'
    }

    return translations[subcategory] || `${type}_${subcategory}`
  }

  /**
   * 生成类别描述
   */
  private generateCategoryDescription(subcategory: string, type: string): string {
    const descriptions: Record<string, string> = {
      // 情感类别描述
      'basic_emotions': '基础的情感表达词汇',
      'positive_emotions': '积极正面的情感词汇',
      'deep_emotions': '深层次的情感体验词汇',
      'artistic_emotions': '具有文艺气息的情感词汇',
      'modern_emotions': '现代社会的情感表达',
      'advanced_emotions': '高级复杂的情感词汇',

      // 职业类别描述
      'traditional_professions': '传统行业的职业角色',
      'modern_professions': '现代社会的职业身份',
      'creative_professions': '创意产业的职业角色',
      'emerging_professions': '新兴行业的职业身份',
      'service_professions': '服务行业的职业角色',

      // 特征类别描述
      'personality_traits': '个性和性格相关的特征',
      'ability_traits': '能力和技能相关的特征',
      'quality_traits': '品质和品格相关的特征',
      'style_traits': '风格和审美相关的特征',
      'state_traits': '状态和情况相关的特征',
      'advanced_characteristics': '高级复杂的特征描述',

      // 传统文化描述
      'classical_poetry': '古典诗词文化中的语素',
      'traditional_concepts': '传统文化概念词汇',
      'classical_expressions': '经典文化表达方式',
      'traditional_virtues': '传统道德品德词汇',
      'scholar_titles': '文人雅士的称谓',

      // 流行文化描述
      'daily_life': '日常生活中的流行表达',
      'internet_popular': '网络流行文化词汇',
      'modern_expressions': '现代社会的表达方式',
      'emotional_expressions': '情感表达的流行词汇',

      // 时代潮流描述
      'anime_culture': '二次元动漫文化词汇',
      'internet_subculture': '网络亚文化群体词汇',
      'emerging_concepts': '新兴概念和趋势词汇',
      'gen_z_culture': 'Z世代文化特色词汇',
      'trend_concepts': '时代潮流概念词汇'
    }

    return descriptions[subcategory] || `${type}相关的词汇集合`
  }

  /**
   * 计算文化权重
   */
  private calculateCulturalWeight(entries: VocabularyEntry[]): number {
    const avgCulturalScore = entries.reduce((sum, entry) => {
      // 根据文化语境计算权重
      switch (entry.cultural_context) {
        case 'ancient': return sum + 1.2
        case 'modern': return sum + 1.0
        case 'neutral': return sum + 0.9
        default: return sum + 1.0
      }
    }, 0) / entries.length

    return Math.round(avgCulturalScore * 10) / 10
  }

  /**
   * 计算权威权重
   */
  private calculateAuthorityWeight(entries: VocabularyEntry[]): number {
    // 基于词汇的权威性特征计算权重
    const authorityKeywords = ['专业', '高级', '顶级', '超级', '极致', '完美', '卓越', '杰出']

    const authorityScore = entries.reduce((sum, entry) => {
      const hasAuthority = authorityKeywords.some(keyword => entry.word.includes(keyword))
      return sum + (hasAuthority ? 1.2 : 0.8)
    }, 0) / entries.length

    return Math.round(authorityScore * 10) / 10
  }

  /**
   * 确定极性
   */
  private determinePolarity(entries: VocabularyEntry[]): 'positive' | 'negative' | 'neutral' {
    const avgSentiment = entries.reduce((sum, entry) => sum + entry.sentiment, 0) / entries.length

    if (avgSentiment > 0.3) return 'positive'
    if (avgSentiment < -0.3) return 'negative'
    return 'neutral'
  }

  /**
   * 判断是否为修饰词类型
   */
  private isModifierLike(subcategory: string): boolean {
    const modifierCategories = ['ability_traits', 'quality_traits', 'style_traits', 'advanced_characteristics']
    return modifierCategories.includes(subcategory)
  }

  /**
   * 判断是否为主体词类型
   */
  private isSubjectLike(entries: VocabularyEntry[]): boolean {
    // 检查是否包含职业、角色、身份等主体性词汇
    const subjectKeywords = ['师', '者', '人', '士', '家', '客', '员', '官', '长', '总', '专家', '达人']

    const subjectCount = entries.filter(entry =>
      subjectKeywords.some(keyword => entry.word.includes(keyword)) ||
      entry.word.length >= 3 // 长词汇更可能是主体
    ).length

    return subjectCount / entries.length > 0.5
  }

  /**
   * 判断是否为后缀词类型
   */
  private isSuffixLike(entries: VocabularyEntry[]): boolean {
    // 检查是否包含典型的后缀词汇
    const suffixKeywords = ['子', '儿', '郎', '娘', '翁', '姑', '爷', '叔', '哥', '姐']

    const suffixCount = entries.filter(entry =>
      suffixKeywords.some(keyword => entry.word.includes(keyword)) ||
      entry.word.length === 1 // 单字更可能是后缀
    ).length

    return suffixCount / entries.length > 0.3
  }

  /**
   * 计算统计信息
   */
  private calculateStatistics(result: IntegrationResult): void {
    result.statistics.total_categories_added =
      Object.keys(result.subjects_config).length +
      Object.keys(result.actions_config).length +
      Object.keys(result.modifiers_config).length +
      Object.keys(result.suffixes_config).length +
      Object.keys(result.traits_config).length

    result.statistics.total_elements_added =
      result.statistics.subjects_added +
      result.statistics.actions_added +
      result.statistics.modifiers_added +
      result.statistics.suffixes_added +
      result.statistics.traits_added
  }

  /**
   * 生成V5语素库配置代码
   */
  async generateV5ConfigCode(result: IntegrationResult): Promise<string> {
    let configCode = `/**
 * V5引擎扩展语素库配置
 * 基于词汇扩展引擎自动生成
 *
 * @generated 自动生成于 ${new Date().toISOString()}
 * @source vocabulary-expansion-engine
 */

// ============ 扩展主体词汇配置 ============
export const EXPANDED_SUBJECTS_CONFIG = {\n`

    // 生成主体配置
    for (const [key, config] of Object.entries(result.subjects_config)) {
      configCode += `  ${key}: {\n`
      configCode += `    category: '${config.category}',\n`
      configCode += `    description: '${config.description}',\n`
      configCode += `    elements: ${JSON.stringify(config.elements, null, 6).replace(/\n/g, '\n    ')},\n`
      configCode += `    usage_patterns: ${JSON.stringify(config.usage_patterns)},\n`
      configCode += `    cultural_weight: ${config.cultural_weight}\n`
      configCode += `  },\n`
    }

    configCode += `} as const\n\n`

    // 生成特质配置
    configCode += `// ============ 扩展特质词汇配置 ============\n`
    configCode += `export const EXPANDED_TRAITS_CONFIG = {\n`

    for (const [key, config] of Object.entries(result.traits_config)) {
      configCode += `  ${key}: {\n`
      configCode += `    category: '${config.category}',\n`
      configCode += `    description: '${config.description}',\n`
      configCode += `    elements: ${JSON.stringify(config.elements, null, 6).replace(/\n/g, '\n    ')},\n`
      configCode += `    usage_patterns: ${JSON.stringify(config.usage_patterns)},\n`
      configCode += `    cultural_weight: ${config.cultural_weight},\n`
      configCode += `    polarity: '${config.polarity}'\n`
      configCode += `  },\n`
    }

    configCode += `} as const\n\n`

    // 生成统计信息
    configCode += `// ============ 扩展统计信息 ============\n`
    configCode += `export const EXPANDED_LIBRARY_STATS = ${JSON.stringify(result.statistics, null, 2)}\n`

    return configCode
  }
}
