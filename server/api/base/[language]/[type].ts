import fs from 'fs'
import path from 'path'
import { defineEventHandler, createError } from 'h3'

export default defineEventHandler(async (event) => {
  try {
    // 获取参数
    const language = event.context.params?.language || 'en'
    const type = event.context.params?.type || 'adjectives'
    
    // 验证语言参数
    const supportedLanguages = ['en', 'zh', 'ja', 'ru']
    if (!supportedLanguages.includes(language)) {
      throw createError({
        statusCode: 400,
        message: `不支持的语言: ${language}`
      })
    }
    
    // 验证类型参数
    const supportedTypes = ['adjectives', 'nouns', 'prefixes', 'suffixes']
    if (!supportedTypes.includes(type)) {
      throw createError({
        statusCode: 400,
        message: `不支持的词汇类型: ${type}`
      })
    }
    
    // 构建数据文件路径
    const dataPath = path.resolve(process.cwd(), 'data', 'base', language, `${type}.json`)
    
    // 检查文件是否存在
    if (!fs.existsSync(dataPath)) {
      throw createError({
        statusCode: 404,
        message: `找不到${language}语言的${type}基础词汇数据`
      })
    }
    
    // 读取数据文件
    const fileContent = fs.readFileSync(dataPath, 'utf-8')
    const baseData = JSON.parse(fileContent)
    
    // 添加缓存控制头
    // 设置缓存时间为7天，因为基础词汇数据几乎不会更新
    event.node.res.setHeader('Cache-Control', 'public, max-age=604800')
    
    // 返回数据
    return {
      success: true,
      data: baseData
    }
  } catch (error: any) {
    // 错误处理
    return {
      success: false,
      error: error.message || '获取基础词汇数据失败'
    }
  }
}) 