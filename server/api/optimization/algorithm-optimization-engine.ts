/**
 * 算法优化引擎
 * 修复文化深度和创意分数计算逻辑，提升生成效果
 * 
 * @version 4.0.0
 * @created 2025-06-16
 */

/**
 * 语义相似度计算接口
 */
export interface SemanticSimilarity {
  cultural_similarity: number
  thematic_similarity: number
  contextual_similarity: number
  overall_similarity: number
}

/**
 * 元素兼容性评估接口
 */
export interface ElementCompatibility {
  cultural_compatibility: number
  temporal_compatibility: number
  thematic_compatibility: number
  fusion_potential: number
  overall_compatibility: number
}

/**
 * 优化后的融合结果接口
 */
export interface OptimizedFusionResult {
  username: string
  fusion_quality: number
  cultural_depth: number
  creativity_score: number
  semantic_coherence: number
  innovation_index: number
  user_appeal: number
  overall_score: number
  optimization_notes: string[]
}

/**
 * 算法优化引擎类
 */
export class AlgorithmOptimizationEngine {
  private semanticVectorCache: Map<string, number[]>
  private compatibilityCache: Map<string, ElementCompatibility>
  private optimizationWeights: {
    cultural_depth: {
      ancient_weight: number
      modern_weight: number
      strategy_balance: number
      semantic_bonus: number
    }
    creativity_score: {
      strategy_creativity: number
      element_novelty: number
      fusion_surprise: number
      innovation_bonus: number
    }
    quality_factors: {
      semantic_coherence: number
      cultural_appropriateness: number
      user_appeal: number
      innovation_value: number
    }
  }
  private initialized: boolean

  constructor() {
    this.semanticVectorCache = new Map()
    this.compatibilityCache = new Map()
    this.optimizationWeights = {
      cultural_depth: {
        ancient_weight: 0.5,      // 提升古代元素权重
        modern_weight: 0.3,       // 适度降低现代元素权重
        strategy_balance: 0.15,   // 降低策略权重
        semantic_bonus: 0.05      // 新增语义奖励
      },
      creativity_score: {
        strategy_creativity: 0.6,  // 提升策略创意权重
        element_novelty: 0.25,     // 元素新颖性
        fusion_surprise: 0.1,      // 融合惊喜度
        innovation_bonus: 0.05     // 创新奖励
      },
      quality_factors: {
        semantic_coherence: 0.3,   // 语义连贯性
        cultural_appropriateness: 0.25, // 文化适宜性
        user_appeal: 0.25,         // 用户吸引力
        innovation_value: 0.2      // 创新价值
      }
    }
    this.initialized = false
  }

  /**
   * 初始化算法优化引擎
   */
  async initialize(): Promise<void> {
    if (this.initialized) return

    console.log('🔧 初始化算法优化引擎...')
    
    // 预计算常用语义向量
    await this.precomputeSemanticVectors()
    
    // 初始化兼容性评估缓存
    await this.initializeCompatibilityCache()
    
    this.initialized = true
    console.log('✅ 算法优化引擎初始化完成')
  }

  /**
   * 优化后的文化深度计算
   */
  calculateOptimizedCulturalDepth(
    ancientElement: any,
    modernElement: any,
    strategy: any,
    semanticSimilarity?: SemanticSimilarity
  ): number {
    const weights = this.optimizationWeights.cultural_depth
    
    // 基础文化权重计算
    let culturalDepth = 0
    culturalDepth += ancientElement.cultural_weight * weights.ancient_weight
    culturalDepth += modernElement.cultural_weight * weights.modern_weight
    culturalDepth += strategy.cultural_balance * weights.strategy_balance
    
    // 语义相似度奖励
    if (semanticSimilarity) {
      const semanticBonus = semanticSimilarity.cultural_similarity * weights.semantic_bonus
      culturalDepth += semanticBonus
    }
    
    // 元素兼容性调整
    const compatibility = this.calculateElementCompatibility(ancientElement, modernElement)
    const compatibilityBonus = compatibility.cultural_compatibility * 0.1
    culturalDepth += compatibilityBonus
    
    // 确保结果在合理范围内
    return Math.max(0.3, Math.min(1.0, culturalDepth))
  }

  /**
   * 优化后的创意分数计算
   */
  calculateOptimizedCreativityScore(
    ancientElement: any,
    modernElement: any,
    strategy: any,
    fusionContext?: any
  ): number {
    const weights = this.optimizationWeights.creativity_score
    
    // 基础创意分数
    let creativityScore = 0
    creativityScore += strategy.creativity_index * weights.strategy_creativity
    
    // 元素新颖性评估
    const elementNovelty = this.assessElementNovelty(ancientElement, modernElement)
    creativityScore += elementNovelty * weights.element_novelty
    
    // 融合惊喜度
    const fusionSurprise = this.calculateFusionSurprise(ancientElement, modernElement, strategy)
    creativityScore += fusionSurprise * weights.fusion_surprise
    
    // 创新奖励
    const innovationBonus = this.assessInnovationValue(ancientElement, modernElement, strategy)
    creativityScore += innovationBonus * weights.innovation_bonus
    
    // 确保结果在合理范围内
    return Math.max(0.4, Math.min(1.0, creativityScore))
  }

  /**
   * 计算语义相似度
   */
  calculateSemanticSimilarity(
    ancientElement: any,
    modernElement: any,
    theme: string
  ): SemanticSimilarity {
    // 文化相似度：基于文化标签的匹配
    const culturalSimilarity = this.calculateCulturalSimilarity(ancientElement, modernElement)
    
    // 主题相似度：与给定主题的匹配度
    const thematicSimilarity = this.calculateThematicSimilarity(ancientElement, modernElement, theme)
    
    // 上下文相似度：在使用场景上的相似性
    const contextualSimilarity = this.calculateContextualSimilarity(ancientElement, modernElement)
    
    // 综合相似度
    const overallSimilarity = (culturalSimilarity * 0.4 + thematicSimilarity * 0.4 + contextualSimilarity * 0.2)
    
    return {
      cultural_similarity: culturalSimilarity,
      thematic_similarity: thematicSimilarity,
      contextual_similarity: contextualSimilarity,
      overall_similarity: overallSimilarity
    }
  }

  /**
   * 计算元素兼容性
   */
  calculateElementCompatibility(
    ancientElement: any,
    modernElement: any
  ): ElementCompatibility {
    const cacheKey = `${ancientElement.id}_${modernElement.id}`
    
    if (this.compatibilityCache.has(cacheKey)) {
      return this.compatibilityCache.get(cacheKey)!
    }
    
    // 文化兼容性：文化层面的匹配度
    const culturalCompatibility = this.assessCulturalCompatibility(ancientElement, modernElement)
    
    // 时间兼容性：时代跨越的合理性
    const temporalCompatibility = this.assessTemporalCompatibility(ancientElement, modernElement)
    
    // 主题兼容性：主题层面的协调性
    const thematicCompatibility = this.assessThematicCompatibility(ancientElement, modernElement)
    
    // 融合潜力：两个元素融合的可能性
    const fusionPotential = this.assessFusionPotential(ancientElement, modernElement)
    
    // 综合兼容性
    const overallCompatibility = (
      culturalCompatibility * 0.3 +
      temporalCompatibility * 0.2 +
      thematicCompatibility * 0.3 +
      fusionPotential * 0.2
    )
    
    const compatibility: ElementCompatibility = {
      cultural_compatibility: culturalCompatibility,
      temporal_compatibility: temporalCompatibility,
      thematic_compatibility: thematicCompatibility,
      fusion_potential: fusionPotential,
      overall_compatibility: overallCompatibility
    }
    
    this.compatibilityCache.set(cacheKey, compatibility)
    return compatibility
  }

  /**
   * 智能元素选择
   */
  selectOptimalElements(
    theme: string,
    strategy: any,
    ancientCandidates: any[],
    modernCandidates: any[]
  ): { ancient: any; modern: any; score: number } {
    let bestCombination = { ancient: ancientCandidates[0], modern: modernCandidates[0], score: 0 }
    
    // 评估所有可能的组合
    for (const ancient of ancientCandidates.slice(0, 5)) { // 限制候选数量以提高性能
      for (const modern of modernCandidates.slice(0, 5)) {
        const score = this.evaluateCombinationScore(ancient, modern, strategy, theme)
        
        if (score > bestCombination.score) {
          bestCombination = { ancient, modern, score }
        }
      }
    }
    
    return bestCombination
  }

  /**
   * 评估组合分数
   */
  private evaluateCombinationScore(
    ancientElement: any,
    modernElement: any,
    strategy: any,
    theme: string
  ): number {
    // 基础适配分数
    let score = 0
    
    // 文化权重分数
    score += ancientElement.cultural_weight * 0.25
    score += modernElement.cultural_weight * 0.2
    
    // 使用频率分数
    score += ancientElement.usage_frequency * 0.15
    score += modernElement.usage_frequency * 0.15
    
    // 语义相似度分数
    const semanticSimilarity = this.calculateSemanticSimilarity(ancientElement, modernElement, theme)
    score += semanticSimilarity.overall_similarity * 0.15
    
    // 元素兼容性分数
    const compatibility = this.calculateElementCompatibility(ancientElement, modernElement)
    score += compatibility.overall_compatibility * 0.1
    
    return score
  }

  /**
   * 生成优化后的融合结果
   */
  async generateOptimizedFusion(
    ancientElement: any,
    modernElement: any,
    strategy: any,
    theme: string
  ): Promise<OptimizedFusionResult> {
    // 计算语义相似度
    const semanticSimilarity = this.calculateSemanticSimilarity(ancientElement, modernElement, theme)
    
    // 计算优化后的指标
    const culturalDepth = this.calculateOptimizedCulturalDepth(
      ancientElement, modernElement, strategy, semanticSimilarity
    )
    
    const creativityScore = this.calculateOptimizedCreativityScore(
      ancientElement, modernElement, strategy
    )
    
    // 计算语义连贯性
    const semanticCoherence = this.calculateSemanticCoherence(ancientElement, modernElement, strategy)
    
    // 计算创新指数
    const innovationIndex = this.calculateInnovationIndex(ancientElement, modernElement, strategy)
    
    // 计算用户吸引力
    const userAppeal = this.calculateUserAppeal(ancientElement, modernElement, strategy, theme)
    
    // 生成用户名
    const username = this.generateOptimizedUsername(ancientElement, modernElement, strategy)
    
    // 计算融合质量
    const fusionQuality = this.calculateFusionQuality(
      culturalDepth, creativityScore, semanticCoherence, innovationIndex, userAppeal
    )
    
    // 计算综合分数
    const overallScore = this.calculateOverallScore(
      fusionQuality, culturalDepth, creativityScore, semanticCoherence, innovationIndex, userAppeal
    )
    
    // 生成优化说明
    const optimizationNotes = this.generateOptimizationNotes(
      ancientElement, modernElement, strategy, semanticSimilarity
    )
    
    return {
      username,
      fusion_quality: fusionQuality,
      cultural_depth: culturalDepth,
      creativity_score: creativityScore,
      semantic_coherence: semanticCoherence,
      innovation_index: innovationIndex,
      user_appeal: userAppeal,
      overall_score: overallScore,
      optimization_notes: optimizationNotes
    }
  }

  // 辅助计算方法
  private calculateCulturalSimilarity(ancient: any, modern: any): number {
    // 基于文化标签的相似度计算
    const ancientTags = ancient.semantic_tags || []
    const modernTags = modern.semantic_tags || []
    
    const commonTags = ancientTags.filter(tag => modernTags.includes(tag))
    const totalTags = new Set([...ancientTags, ...modernTags]).size
    
    return totalTags > 0 ? commonTags.length / totalTags : 0.3
  }

  private calculateThematicSimilarity(ancient: any, modern: any, theme: string): number {
    // 基于主题的相似度计算
    const themeRelevance = {
      '情感': ['文学', '艺术', '哲学'],
      '文学': ['文学', '艺术'],
      '科技': ['哲学', '技术'],
      '职场': ['社会', '专业'],
      '生活': ['生活', '艺术'],
      '艺术': ['艺术', '文学']
    }
    
    const relevantCategories = themeRelevance[theme] || []
    const ancientMatch = relevantCategories.includes(ancient.subcategory)
    const modernMatch = relevantCategories.includes(modern.subcategory)
    
    if (ancientMatch && modernMatch) return 0.9
    if (ancientMatch || modernMatch) return 0.6
    return 0.3
  }

  private calculateContextualSimilarity(ancient: any, modern: any): number {
    // 基于使用场景的相似度
    return 0.5 + Math.random() * 0.3 // 简化实现
  }

  private assessCulturalCompatibility(ancient: any, modern: any): number {
    // 文化兼容性评估
    return Math.min(1, (ancient.cultural_weight + modern.cultural_weight) / 2 + 0.2)
  }

  private assessTemporalCompatibility(ancient: any, modern: any): number {
    // 时间兼容性评估
    return 0.7 + Math.random() * 0.2 // 简化实现
  }

  private assessThematicCompatibility(ancient: any, modern: any): number {
    // 主题兼容性评估
    return 0.6 + Math.random() * 0.3 // 简化实现
  }

  private assessFusionPotential(ancient: any, modern: any): number {
    // 融合潜力评估
    const weightDiff = Math.abs(ancient.cultural_weight - modern.cultural_weight)
    return Math.max(0.4, 1 - weightDiff * 0.5)
  }

  private assessElementNovelty(ancient: any, modern: any): number {
    // 元素新颖性评估
    const noveltyScore = (1 - ancient.usage_frequency) * 0.5 + (1 - modern.usage_frequency) * 0.5
    return Math.max(0.3, noveltyScore)
  }

  private calculateFusionSurprise(ancient: any, modern: any, strategy: any): number {
    // 融合惊喜度计算
    const categoryContrast = ancient.subcategory !== modern.subcategory ? 0.3 : 0.1
    const strategyBonus = strategy.creativity_index > 0.9 ? 0.2 : 0.1
    return Math.min(1, 0.4 + categoryContrast + strategyBonus)
  }

  private assessInnovationValue(ancient: any, modern: any, strategy: any): number {
    // 创新价值评估
    return Math.min(1, strategy.creativity_index * 0.6 + 0.3)
  }

  private calculateSemanticCoherence(ancient: any, modern: any, strategy: any): number {
    // 语义连贯性计算
    return 0.8 + Math.random() * 0.15 // 简化实现
  }

  private calculateInnovationIndex(ancient: any, modern: any, strategy: any): number {
    // 创新指数计算
    return Math.min(1, strategy.creativity_index * 0.7 + 0.2)
  }

  private calculateUserAppeal(ancient: any, modern: any, strategy: any, theme: string): number {
    // 用户吸引力计算
    const themeBonus = ['情感', '文学', '艺术'].includes(theme) ? 0.1 : 0.05
    return Math.min(1, (ancient.usage_frequency + modern.usage_frequency) / 2 + themeBonus)
  }

  private generateOptimizedUsername(ancient: any, modern: any, strategy: any): string {
    // 优化后的用户名生成
    const patterns = {
      'contrast': () => `${modern.name}${ancient.name}`,
      'harmony': () => `${ancient.name.slice(0, -1)}${modern.name}`,
      'evolution': () => `数字${ancient.name}`,
      'creative': () => `${ancient.name}${modern.name.slice(-2)}`,
      'temporal_bridge': () => `时空${ancient.name}`,
      'role_transformation': () => `新时代${ancient.name}`,
      'essence_extraction': () => `${ancient.name}精神${modern.name}`,
      'parallel_universe': () => `赛博${ancient.name}`
    }
    
    const pattern = patterns[strategy.name] || patterns['harmony']
    return pattern()
  }

  private calculateFusionQuality(
    culturalDepth: number,
    creativityScore: number,
    semanticCoherence: number,
    innovationIndex: number,
    userAppeal: number
  ): number {
    const weights = this.optimizationWeights.quality_factors
    
    return (
      semanticCoherence * weights.semantic_coherence +
      culturalDepth * weights.cultural_appropriateness +
      userAppeal * weights.user_appeal +
      innovationIndex * weights.innovation_value
    )
  }

  private calculateOverallScore(
    fusionQuality: number,
    culturalDepth: number,
    creativityScore: number,
    semanticCoherence: number,
    innovationIndex: number,
    userAppeal: number
  ): number {
    return (
      fusionQuality * 0.3 +
      culturalDepth * 0.25 +
      creativityScore * 0.25 +
      semanticCoherence * 0.1 +
      innovationIndex * 0.05 +
      userAppeal * 0.05
    )
  }

  private generateOptimizationNotes(
    ancient: any,
    modern: any,
    strategy: any,
    semanticSimilarity: SemanticSimilarity
  ): string[] {
    const notes = []
    
    if (semanticSimilarity.overall_similarity > 0.7) {
      notes.push('语义相似度高，融合效果良好')
    }
    
    if (ancient.cultural_weight > 0.85 && modern.cultural_weight > 0.85) {
      notes.push('双高文化权重，文化深度优秀')
    }
    
    if (strategy.creativity_index > 0.9) {
      notes.push('高创意策略，创新性突出')
    }
    
    return notes
  }

  private async precomputeSemanticVectors(): Promise<void> {
    // 预计算常用语义向量
    console.log('   预计算语义向量...')
  }

  private async initializeCompatibilityCache(): Promise<void> {
    // 初始化兼容性缓存
    console.log('   初始化兼容性缓存...')
  }
}

/**
 * 全局算法优化引擎实例
 */
export const algorithmOptimizationEngine = new AlgorithmOptimizationEngine()
