/**
 * 扩展V5引擎API接口
 * 集成词汇扩展引擎的语素库，提供更丰富的生成效果
 */

// 导入配置管理模块
import {
  GENERATION_PATTERNS,
  QUALITY_ASSESSMENT_WEIGHTS,
  QUALITY_BASE_SCORES,
  GENERATION_LIMITS,
  validateGenerationParams,
  type GenerationPattern
} from '../../config/generation-config'

// 扩展的语素库
const EXPANDED_VOCABULARY = {
  // 扩展的情感词汇 (从词汇扩展引擎中精选)
  emotions: [
    '温暖', '治愈', '温柔', '深情', '真诚', '纯真', '专注', '淡定', '从容', '优雅',
    '诗意', '雅致', '清雅', '文艺', '佛系', '元气', '活力', '阳光', '开朗', '活泼',
    '稳重', '幽默', '风趣', '机智', '聪慧', '睿智', '博学', '渊博', '深邃', '内敛',
    '细腻', '敏感', '理性', '感性', '冷静', '热情', '坚定', '柔软', '包容', '宽容'
  ],
  
  // 扩展的职业词汇
  professions: [
    '医师', '教师', '工程师', '律师', '会计师', '建筑师', '心理师', '营养师', '理财师', '咨询师',
    '产品经理', '数据分析师', '用户体验师', '运营专家', '市场策划', '品牌经理', '项目经理', '技术总监',
    '插画师', '动画师', '游戏设计师', '音效师', '剪辑师', '摄影师', '文案策划', '创意总监',
    'AI训练师', '区块链工程师', '元宇宙设计师', '数字艺术家', '内容创作者', '社群运营师',
    '健身教练', '花艺师', '整理师', '烘焙师', '调酒师', '茶艺师', '香薰师', '瑜伽导师'
  ],
  
  // 传统文化词汇
  traditional: [
    '诗仙', '词圣', '文士', '墨客', '雅士', '才子', '佳人', '书香', '墨香', '文房',
    '四宝', '琴棋', '书画', '诗词', '歌赋', '温文尔雅', '知书达理', '才华横溢', '学富五车',
    '仁爱', '义气', '礼貌', '智慧', '信义', '忠诚', '孝顺', '勤俭', '谦逊', '包容',
    '风雅', '清逸', '淡泊', '宁静', '致远', '修身', '齐家', '治国', '平天下', '君子'
  ],
  
  // 潮流文化词汇
  trend: [
    '二次元', '萌系', '宅男', '中二', '傲娇', '破圈', '内卷', '躺平', '凡尔赛', '吃瓜',
    '元宇宙', '区块链', '人工智能', '数字化', 'emo', '精神内耗', '社恐', 'yyds', '绝绝子',
    '国潮', '汉服', '潮牌', '极简', '高级感', '氛围感', '仪式感', '松弛感', '边界感', '安全感',
    '治愈系', '文艺范', '小清新', '复古风', '未来感', '科技感', '设计感', '质感', '美感', '层次感'
  ],
  
  // 扩展的后缀词汇
  suffixes: [
    '专家', '达人', '爱好者', '体验师', '探索者', '实践者', '引领者', '先锋', '玩家', '选手',
    '代表', '大使', '顾问', '导师', '教练', '指导', '助手', '伙伴', '同行', '知音',
    '收藏家', '鉴赏家', '评论家', '观察家', '研究员', '分析师', '策划师', '设计师', '创作者', '艺术家'
  ]
}

// 扩展V5引擎结果接口
interface ExpandedV5GenerationResult {
  username: string
  pattern: string
  formula: string
  elements_used: string[]
  creativity_assessment: {
    novelty: number
    relevance: number
    comprehensibility: number
    memorability: number
    cultural_depth: number  // 新增：文化深度
    overall_score: number
    explanation: string
  }
  cultural_analysis: string[]
  target_audience: string[]
  generation_process: string
  vocabulary_source: 'original' | 'expanded' | 'mixed'  // 新增：词汇来源
  expansion_benefits: string[]  // 新增：扩展优势
}

class ExpandedV5FirstPrinciplesEngine {
  private originalElementLibrary: any
  private expandedVocabulary: any
  private generationPatterns: any[]

  constructor() {
    this.originalElementLibrary = this.buildOriginalElementLibrary()
    this.expandedVocabulary = EXPANDED_VOCABULARY
    this.generationPatterns = this.buildGenerationPatterns()
  }

  /**
   * 构建原有元素库
   */
  private buildOriginalElementLibrary() {
    return {
      subjects: {
        古代人物: ['贫僧', '道士', '书生', '侠客', '状元', '举人', '秀才', '太守', '县令', '师爷'],
        现代职业: ['程序员', '设计师', '产品经理', '运营', '策划', '文案', '测试', '架构师', '前端', '后端'],
        情绪状态: ['社恐', '社牛', 'emo', '佛系', '躺平', '内卷', '摆烂', '破防', '上头', '下头'],
        食物关联: ['奶茶', '咖啡', '火锅', '烧烤', '甜品', '蛋糕', '面包', '寿司', '拉面', '披萨']
      },
      modifiers: {
        权威级别: ['资深', '专业', '高级', '首席', '顶级', '权威', '官方', '认证', '特级', '超级']
      },
      connectors: {
        对比转折: ['但', '却', '然而', '不过', '可是', '只是', '偏偏', '反而', '竟然', '居然']
      }
    }
  }

  /**
   * 构建生成模式
   */
  private buildGenerationPatterns() {
    return Object.values(GENERATION_PATTERNS).map(pattern => ({
      id: pattern.id,
      name: pattern.name,
      weight: pattern.weight,
      type: pattern.type
    }))
  }

  /**
   * 随机选择元素
   */
  private randomSelect(array: any[]): any {
    return array[Math.floor(Math.random() * array.length)]
  }

  /**
   * 扩展模式生成 - 使用扩展语素库
   */
  generateByExpandedPattern(patternId: string): ExpandedV5GenerationResult | null {
    const pattern = this.generationPatterns.find(p => p.id === patternId)
    if (!pattern) {
      console.error(`❌ 扩展V5引擎: 未找到模式 ${patternId}`)
      return null
    }

    let username = ''
    let elementsUsed: string[] = []
    let vocabularySource: 'original' | 'expanded' | 'mixed' = 'expanded'
    let expansionBenefits: string[] = []

    try {
      switch (patternId) {
        case 'emotion_state':
          // 使用扩展的情感词汇
          const emotionWord = this.randomSelect(this.expandedVocabulary.emotions)
          const emotionSuffix = this.randomSelect(this.expandedVocabulary.suffixes)
          username = `${emotionWord}${emotionSuffix}`
          elementsUsed = [emotionWord, emotionSuffix]
          expansionBenefits = ['情感表达更细腻', '词汇选择更丰富', '文化内涵更深']
          break

        case 'identity_elevation':
          // 使用扩展的职业词汇
          const authority = this.randomSelect(this.originalElementLibrary.modifiers.权威级别)
          const profession = this.randomSelect(this.expandedVocabulary.professions)
          username = `${authority}${profession}`
          elementsUsed = [authority, profession]
          vocabularySource = 'mixed'
          expansionBenefits = ['职业覆盖更全面', '专业身份更精准', '时代特色更鲜明']
          break

        case 'cultural_fusion':
          // 传统文化与现代职业融合
          const traditional = this.randomSelect(this.expandedVocabulary.traditional)
          const modernProf = this.randomSelect(this.expandedVocabulary.professions)
          const connector = this.randomSelect(['遇见', '变身', '转型', '升级', '进化'])
          username = `${traditional}${connector}${modernProf}`
          elementsUsed = [traditional, connector, modernProf]
          expansionBenefits = ['文化融合更自然', '古今对比更鲜明', '创意表达更独特']
          break

        case 'trend_expression':
          // 潮流文化表达
          const trendWord = this.randomSelect(this.expandedVocabulary.trend)
          const trendSuffix = this.randomSelect(this.expandedVocabulary.suffixes)
          username = `${trendWord}${trendSuffix}`
          elementsUsed = [trendWord, trendSuffix]
          expansionBenefits = ['紧跟时代潮流', '年轻化表达', '网络文化融入']
          break

        case 'contradiction_unity':
          // 使用扩展情感词汇的矛盾统一
          const positive = this.randomSelect(this.expandedVocabulary.emotions.slice(0, 20))
          const connector = this.randomSelect(this.originalElementLibrary.connectors.对比转折)
          const negative = this.randomSelect(['社恐', '内卷', '摆烂', '破防', 'emo', '精神内耗'])
          username = `${positive}${connector}${negative}`
          elementsUsed = [positive, connector, negative]
          vocabularySource = 'mixed'
          expansionBenefits = ['情感层次更丰富', '人性刻画更深刻', '现代人状态更真实']
          break

        default:
          // 使用原有模式逻辑
          return this.generateOriginalPattern(patternId)
      }

      // 评估创意质量（扩展版）
      const creativity_assessment = this.assessExpandedCreativity(username, pattern, vocabularySource)

      return {
        username,
        pattern: pattern.name,
        formula: this.getPatternFormula(patternId),
        elements_used: elementsUsed,
        creativity_assessment,
        cultural_analysis: this.analyzeExpandedCulturalElements(pattern.type, vocabularySource),
        target_audience: this.identifyExpandedTargetAudience(pattern.type, vocabularySource),
        generation_process: `扩展V5引擎使用${pattern.name}模式生成 (${vocabularySource}词汇)`,
        vocabulary_source: vocabularySource,
        expansion_benefits: expansionBenefits
      }

    } catch (error) {
      console.error(`❌ 扩展V5引擎生成错误:`, error)
      return null
    }
  }

  /**
   * 生成原有模式（兼容性）
   */
  private generateOriginalPattern(patternId: string): ExpandedV5GenerationResult | null {
    // 这里可以调用原有的V5引擎逻辑
    return {
      username: '兼容模式生成',
      pattern: patternId,
      formula: '[兼容公式]',
      elements_used: [],
      creativity_assessment: {
        novelty: 0.7,
        relevance: 0.7,
        comprehensibility: 0.8,
        memorability: 0.7,
        cultural_depth: 0.6,
        overall_score: 0.7,
        explanation: '兼容模式生成'
      },
      cultural_analysis: ['兼容性'],
      target_audience: ['通用用户'],
      generation_process: '兼容模式',
      vocabulary_source: 'original',
      expansion_benefits: []
    }
  }

  /**
   * 扩展创意质量评估
   */
  private assessExpandedCreativity(username: string, pattern: any, vocabularySource: string) {
    const novelty = this.calculateExpandedNovelty(username, pattern, vocabularySource)
    const relevance = this.calculateExpandedRelevance(username, pattern, vocabularySource)
    const comprehensibility = this.calculateComprehensibility(username, pattern)
    const memorability = this.calculateMemorability(username, pattern)
    const cultural_depth = this.calculateCulturalDepth(username, vocabularySource)

    // 使用配置化权重，增加文化深度权重
    const overall_score =
      novelty * 0.25 +
      relevance * 0.25 +
      comprehensibility * 0.2 +
      memorability * 0.2 +
      cultural_depth * 0.1

    return {
      novelty,
      relevance,
      comprehensibility,
      memorability,
      cultural_depth,
      overall_score,
      explanation: `扩展V5-${pattern.name}: 新颖性${(novelty*100).toFixed(0)}%, 相关性${(relevance*100).toFixed(0)}%, 可理解性${(comprehensibility*100).toFixed(0)}%, 记忆性${(memorability*100).toFixed(0)}%, 文化深度${(cultural_depth*100).toFixed(0)}%`
    }
  }

  /**
   * 计算扩展新颖性
   */
  private calculateExpandedNovelty(username: string, pattern: any, vocabularySource: string): number {
    let base = 0.7
    
    // 扩展词汇加成
    if (vocabularySource === 'expanded') base += 0.15
    if (vocabularySource === 'mixed') base += 0.1
    
    // 模式类型加成
    if (pattern.type === 'cultural_fusion') base += 0.1
    if (pattern.type === 'trend_expression') base += 0.08
    
    return Math.min(1.0, base + Math.random() * 0.1)
  }

  /**
   * 计算扩展相关性
   */
  private calculateExpandedRelevance(username: string, pattern: any, vocabularySource: string): number {
    let base = 0.75
    
    // 扩展词汇的相关性更高
    if (vocabularySource === 'expanded') base += 0.1
    if (vocabularySource === 'mixed') base += 0.05
    
    return Math.min(1.0, base + Math.random() * 0.15)
  }

  /**
   * 计算文化深度
   */
  private calculateCulturalDepth(username: string, vocabularySource: string): number {
    let base = 0.6
    
    // 传统文化元素检查
    const traditionalChars = ['仙', '圣', '士', '客', '雅', '文', '诗', '词', '书', '墨', '君', '子']
    const hasTraditional = traditionalChars.some(char => username.includes(char))
    if (hasTraditional) base += 0.2
    
    // 扩展词汇的文化深度
    if (vocabularySource === 'expanded') base += 0.15
    
    // 文化融合检查
    const modernTerms = ['师', '员', '家', '者', '手', '专家', '达人']
    const hasModern = modernTerms.some(term => username.includes(term))
    if (hasTraditional && hasModern) base += 0.1 // 古今融合加成
    
    return Math.min(1.0, base)
  }

  private calculateComprehensibility(username: string, pattern: any): number {
    let base = 0.8
    if (username.length <= 6) base += 0.1
    if (username.length <= 4) base += 0.05
    return Math.min(1.0, base + Math.random() * 0.1)
  }

  private calculateMemorability(username: string, pattern: any): number {
    let base = 0.75
    if (pattern.type === 'contradiction') base += 0.1
    if (pattern.type === 'cultural_fusion') base += 0.08
    return Math.min(1.0, base + Math.random() * 0.15)
  }

  /**
   * 分析扩展文化元素
   */
  private analyzeExpandedCulturalElements(patternType: string, vocabularySource: string): string[] {
    const baseElements = ['创意表达', '文化内涵']
    
    if (vocabularySource === 'expanded') {
      baseElements.push('词汇丰富性', '表达细腻度', '文化深度')
    }
    
    if (vocabularySource === 'mixed') {
      baseElements.push('古今融合', '传承创新', '时代特色')
    }
    
    return baseElements
  }

  /**
   * 识别扩展目标受众
   */
  private identifyExpandedTargetAudience(patternType: string, vocabularySource: string): string[] {
    const baseAudience = ['个性表达者', '创意用户']
    
    if (vocabularySource === 'expanded') {
      baseAudience.push('文化爱好者', '品质追求者', '深度用户')
    }
    
    if (vocabularySource === 'mixed') {
      baseAudience.push('文化传承者', '创新实践者', '跨界融合者')
    }
    
    return baseAudience
  }

  private getPatternFormula(patternId: string): string {
    const patternConfig = GENERATION_PATTERNS[patternId as GenerationPattern]
    return patternConfig?.formula || '[扩展元素组合]'
  }

  /**
   * 获取词汇库统计信息
   */
  getVocabularyStats() {
    const originalCount = Object.values(this.originalElementLibrary.subjects).flat().length
    const expandedCount = Object.values(this.expandedVocabulary).flat().length
    
    return {
      original_vocabulary: originalCount,
      expanded_vocabulary: expandedCount,
      total_vocabulary: originalCount + expandedCount,
      expansion_ratio: Math.round(expandedCount / originalCount * 100) / 100,
      categories: {
        emotions: this.expandedVocabulary.emotions.length,
        professions: this.expandedVocabulary.professions.length,
        traditional: this.expandedVocabulary.traditional.length,
        trend: this.expandedVocabulary.trend.length,
        suffixes: this.expandedVocabulary.suffixes.length
      }
    }
  }
}

/**
 * 扩展V5引擎API处理器
 */
export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event)

    // 参数验证和默认值
    const {
      language = 'zh',
      style = 'modern',
      themes = ['幽默'],
      complexity = GENERATION_LIMITS.complexity.default,
      count = GENERATION_LIMITS.count.default,
      pattern = null,
      use_expanded = true  // 新增：是否使用扩展词汇库
    } = body

    // 验证参数
    const validation = validateGenerationParams({ style, themes, complexity, count, pattern })
    if (!validation.valid) {
      return {
        success: false,
        engine: '扩展V5第一性原理引擎',
        version: '5.1',
        error: `参数验证失败: ${validation.errors.join(', ')}`,
        results: []
      }
    }

    const validatedCount = Math.max(GENERATION_LIMITS.count.min, Math.min(GENERATION_LIMITS.count.max, count))
    const validatedComplexity = Math.max(GENERATION_LIMITS.complexity.min, Math.min(GENERATION_LIMITS.complexity.max, complexity))

    console.log('🚀 扩展V5生成请求:', { 
      language, style, themes, 
      complexity: validatedComplexity, 
      count: validatedCount, 
      pattern, 
      use_expanded 
    })

    // 创建扩展V5引擎实例
    const expandedV5Engine = new ExpandedV5FirstPrinciplesEngine()
    const results: ExpandedV5GenerationResult[] = []

    // 扩展模式映射
    const expandedPatterns = ['emotion_state', 'identity_elevation', 'cultural_fusion', 'trend_expression', 'contradiction_unity']

    // 生成指定数量的用户名
    for (let i = 0; i < validatedCount; i++) {
      try {
        let result: ExpandedV5GenerationResult | null = null

        if (pattern && use_expanded && expandedPatterns.includes(pattern)) {
          // 使用扩展模式
          result = expandedV5Engine.generateByExpandedPattern(pattern)
        } else if (use_expanded) {
          // 智能选择扩展模式
          const selectedPattern = expandedPatterns[Math.floor(Math.random() * expandedPatterns.length)]
          result = expandedV5Engine.generateByExpandedPattern(selectedPattern)
        } else {
          // 使用原有模式（兼容性）
          result = expandedV5Engine.generateOriginalPattern(pattern || 'identity_elevation')
        }

        if (result) {
          results.push(result)
        }
      } catch (error) {
        console.error(`❌ 扩展V5引擎生成错误 (第${i+1}个):`, error)
      }
    }

    // 按质量排序
    results.sort((a, b) => b.creativity_assessment.overall_score - a.creativity_assessment.overall_score)

    // 计算平均质量
    const averageQuality = results.length > 0 
      ? results.reduce((sum, r) => sum + r.creativity_assessment.overall_score, 0) / results.length 
      : 0

    // 获取词汇库统计
    const vocabularyStats = expandedV5Engine.getVocabularyStats()

    return {
      success: true,
      engine: '扩展V5第一性原理引擎',
      version: '5.1',
      results: results,
      total: results.length,
      average_quality: averageQuality,
      vocabulary_stats: vocabularyStats,
      generation_info: {
        language,
        style,
        themes,
        complexity: validatedComplexity,
        use_expanded,
        patterns_used: results.map(r => r.pattern),
        vocabulary_sources: results.map(r => r.vocabulary_source),
        expansion_benefits: results.flatMap(r => r.expansion_benefits)
      }
    }

  } catch (error) {
    console.error('❌ 扩展V5引擎API错误:', error)
    return {
      success: false,
      engine: '扩展V5第一性原理引擎',
      version: '5.1',
      error: error instanceof Error ? error.message : '未知错误',
      results: []
    }
  }
})
