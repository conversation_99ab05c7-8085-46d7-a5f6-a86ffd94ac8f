import fs from 'fs'
import path from 'path'
import { defineEventHandler, createError } from 'h3'

export default defineEventHandler(async (event) => {
  try {
    // 获取语言参数
    const language = event.context.params?.language || 'en'
    
    // 验证语言参数
    const supportedLanguages = ['en', 'zh', 'ja', 'ru']
    if (!supportedLanguages.includes(language)) {
      throw createError({
        statusCode: 400,
        message: `不支持的语言: ${language}`
      })
    }
    
    // 构建数据文件路径
    const dataPath = path.resolve(process.cwd(), 'data', 'trends', 'current', `${language}.json`)
    
    // 检查文件是否存在
    if (!fs.existsSync(dataPath)) {
      throw createError({
        statusCode: 404,
        message: `找不到${language}语言的趋势数据`
      })
    }
    
    // 读取数据文件
    const fileContent = fs.readFileSync(dataPath, 'utf-8')
    const trendData = JSON.parse(fileContent)
    
    // 添加缓存控制头
    // 设置缓存时间为1天，因为趋势数据更新不频繁
    event.node.res.setHeader('Cache-Control', 'public, max-age=86400')
    
    // 返回数据
    return {
      success: true,
      data: trendData
    }
  } catch (error: any) {
    // 错误处理
    return {
      success: false,
      error: error.message || '获取趋势数据失败'
    }
  }
}) 