/**
 * V4第一性原理引擎
 * 基于"有趣"的底层规律，通过元素重组创造无限可能
 */

// 基础构建元素库
interface ElementLibrary {
  subjects: {
    [category: string]: string[]
  }
  actions: {
    [category: string]: string[]
  }
  modifiers: {
    [category: string]: string[]
  }
  connectors: {
    [category: string]: string[]
  }
  contexts: {
    [category: string]: string[]
  }
}

// 生成模式定义
interface GenerationPattern {
  id: string
  name: string
  formula: string
  weight: number
  generate: (elements: ElementLibrary) => string
  evaluate: (username: string) => number
}

// 创意评估结果
interface CreativityAssessment {
  novelty: number        // 新颖性 30%
  relevance: number      // 相关性 25%
  comprehensibility: number // 可理解性 25%
  memorability: number   // 记忆性 20%
  overall_score: number  // 总分
  explanation: string    // 评估解释
}

// 生成结果
interface FirstPrinciplesResult {
  username: string
  pattern: string
  elements_used: string[]
  creativity_assessment: CreativityAssessment
  cultural_analysis: string[]
  target_audience: string[]
  generation_process: string
}

export class V4FirstPrinciplesEngine {
  private elementLibrary: ElementLibrary
  private generationPatterns: GenerationPattern[]
  
  constructor() {
    this.elementLibrary = this.buildElementLibrary()
    this.generationPatterns = this.buildGenerationPatterns()
  }
  
  /**
   * 构建可重组的元素库
   */
  private buildElementLibrary(): ElementLibrary {
    return {
      subjects: {
        古代人物: ['贫僧', '道士', '书生', '侠客', '状元', '举人', '秀才', '太守', '县令', '师爷'],
        现代职业: ['程序员', '设计师', '产品经理', '运营', '策划', '文案', '测试', '架构师', '算法工程师'],
        网络身份: ['网红', '博主', 'UP主', '主播', '自媒体', '数字游民', '斜杠青年', '创业者'],
        动物: ['猫', '狗', '猪', '鸟', '鱼', '蜗牛', '大象', '蚂蚁', '老鼠', '兔子', '熊', '狮子'],
        天体: ['月亮', '星星', '太阳', '云朵', '彩虹', '闪电', '银河', '宇宙'],
        抽象概念: ['快乐', '悲伤', '愤怒', '温柔', '智慧', '勇敢', '梦想', '希望', '恐惧', '爱情'],
        食物: ['芝士', '咖啡', '茶', '巧克力', '蛋糕', '面包', '牛奶', '果汁', '卤蛋', '火锅'],
        技术概念: ['WiFi', '404', '500', '数据库', '服务器', '算法', 'AI', '云端', '区块链']
      },
      
      actions: {
        日常行为: ['吃', '睡', '玩', '工作', '学习', '休息', '思考', '发呆', '散步', '跑步'],
        特殊动作: ['飞', '游泳', '追', '跑', '爬', '跳', '舞蹈', '唱歌', '画画', '写作'],
        抽象动作: ['贩卖', '收集', '制造', '修理', '设计', '创造', '破坏', '保护', '传播', '分享'],
        网络行为: ['直播', '带货', '刷视频', '点赞', '转发', '评论', '关注', '取关', '充电', '续命'],
        现代生活: ['洗头', '化妆', '健身', '减肥', '加班', '熬夜', '摸鱼', '划水', '内卷', '躺平']
      },
      
      modifiers: {
        权威级别: ['首席', '高级', '资深', '专业', '认证', '官方', '特级', '顶级', '大师级', '传奇'],
        空间范围: ['全球', '国际', '世界级', '宇宙级', '银河系', '太阳系', '跨国', '跨界', '跨次元'],
        程度强化: ['超级', '极度', '非常', '特别', '完全', '绝对', '史诗', '终极', '至尊', '满级'],
        时间频率: ['永远', '从不', '偶尔', '经常', '总是', '间歇性', '定期', '不定期', '随时', '24小时'],
        状态描述: ['在线', '离线', '忙碌', '空闲', '活跃', '潜水', '冒泡', '隐身', '免打扰']
      },
      
      connectors: {
        对比转折: ['但', '却', '然而', '不过', '虽然', '尽管', '反而', '相反', '倒是', '偏偏'],
        并列关系: ['和', '与', '及', '以及', '还有', '同时', '一边', '一面', '既', '又'],
        递进强化: ['更', '还', '甚至', '竟然', '居然', '简直', '完全', '彻底', '深度', '极致'],
        因果关系: ['因为', '所以', '由于', '导致', '造成', '引起', '产生', '带来', '形成']
      },
      
      contexts: {
        职场环境: ['会议室', '工位', '办公室', '电梯', '茶水间', '洗手间', '停车场', '食堂'],
        网络空间: ['直播间', '评论区', '朋友圈', '微博', '抖音', 'B站', '知乎', '小红书'],
        生活场景: ['家里', '路上', '地铁', '咖啡厅', '图书馆', '健身房', '超市', '公园'],
        虚拟世界: ['游戏', '元宇宙', '虚拟现实', '数字世界', '网络空间', '云端', '服务器']
      }
    }
  }
  
  /**
   * 构建生成模式
   */
  private buildGenerationPatterns(): GenerationPattern[] {
    return [
      {
        id: 'identity_elevation',
        name: '身份升维包装',
        formula: '[权威修饰] + [日常行为] + [职位后缀]',
        weight: 0.96,
        generate: (elements) => {
          const authority = this.randomSelect(elements.modifiers.权威级别)
          const behavior = this.randomSelect(elements.actions.日常行为)
          const suffix = this.randomSelect(['官', '师', '专家', '大使', '代表', '委员', '顾问', '总监'])
          return `${authority}${behavior}${suffix}`
        },
        evaluate: (username) => this.evaluateIdentityElevation(username)
      },
      
      {
        id: 'contradiction_unity',
        name: '矛盾统一',
        formula: '[正面特质] + [转折连词] + [负面特质]',
        weight: 0.94,
        generate: (elements) => {
          const positive = this.randomSelect(['温柔', '理性', '冷静', '勤奋', '节约', '听劝', '乐观', '自信'])
          const connector = this.randomSelect(elements.connectors.对比转折)
          const negative = this.randomSelect(['强硬', '感性', '冲动', '懒惰', '挥霍', '反骨', '悲观', '自卑'])
          return `${positive}${connector}${negative}`
        },
        evaluate: (username) => this.evaluateContradiction(username)
      },
      
      {
        id: 'temporal_displacement',
        name: '时空错位重组',
        formula: '[古代元素] + [现代行为/物品]',
        weight: 0.95,
        generate: (elements) => {
          const ancient = this.randomSelect(elements.subjects.古代人物)
          const modern = this.randomSelect([
            ...elements.actions.网络行为,
            ...elements.actions.现代生活
          ])
          const connector = Math.random() > 0.5 ? '' : '的'
          return `${ancient}${connector}${modern}`
        },
        evaluate: (username) => this.evaluateTemporalDisplacement(username)
      },
      
      {
        id: 'service_personification',
        name: '服务拟人化',
        formula: '[抽象概念] + [服务角色]',
        weight: 0.92,
        generate: (elements) => {
          const concept = this.randomSelect([
            ...elements.subjects.抽象概念,
            ...elements.subjects.天体
          ])
          const service = this.randomSelect(['邮递员', '收集员', '配送员', '制造商', '贩卖机', '专卖店', '客服', '导航员'])
          return `${concept}${service}`
        },
        evaluate: (username) => this.evaluatePersonification(username)
      },
      
      {
        id: 'tech_expression',
        name: '技术化表达',
        formula: '[生活概念] + [技术术语]',
        weight: 0.91,
        generate: (elements) => {
          const concept = this.randomSelect(['人生', '梦想', '快乐', '悲伤', '爱情', '友情', '工作', '学习'])
          const tech = this.randomSelect(['正在缓冲', '连接超时', '服务器宕机', '数据库损坏', '404未找到', '503不可用', '正在加载', '系统维护'])
          return `${concept}${tech}`
        },
        evaluate: (username) => this.evaluateTechExpression(username)
      },
      
      {
        id: 'homophone_creative',
        name: '创意谐音',
        formula: '[原词] → [谐音替换]',
        weight: 0.95,
        generate: (elements) => {
          const homophones = [
            { original: '知识就是力量', replacement: '芝士就是力量' },
            { original: '没心没肺', replacement: '莓心没肺' },
            { original: '无恶不作', replacement: '无饿不作' },
            { original: '有压力很大', replacement: '有鸭梨很大' },
            { original: '一见钟情', replacement: '一见粽情' },
            { original: '心想事成', replacement: '薪想事成' },
            { original: '马到成功', replacement: '码到成功' },
            { original: '天马行空', replacement: '天码行空' }
          ]
          return this.randomSelect(homophones).replacement
        },
        evaluate: (username) => this.evaluateHomophone(username)
      }
    ]
  }
  
  /**
   * 随机选择元素
   */
  private randomSelect<T>(array: T[]): T {
    return array[Math.floor(Math.random() * array.length)]
  }
  
  /**
   * 生成用户名
   */
  generateUsername(userPreferences?: {
    style?: string
    themes?: string[]
    complexity?: number
  }): FirstPrinciplesResult {
    // 根据用户偏好选择合适的生成模式
    const selectedPattern = this.selectOptimalPattern(userPreferences)
    
    // 使用选定模式生成用户名
    const username = selectedPattern.generate(this.elementLibrary)
    
    // 评估创意质量
    const creativity_assessment = this.assessCreativity(username, selectedPattern)
    
    // 分析文化元素
    const cultural_analysis = this.analyzeCulturalElements(username, selectedPattern)
    
    // 确定目标用户群
    const target_audience = this.identifyTargetAudience(username, selectedPattern)
    
    // 记录生成过程
    const generation_process = `使用${selectedPattern.name}模式，公式：${selectedPattern.formula}`
    
    return {
      username,
      pattern: selectedPattern.name,
      elements_used: this.extractElementsUsed(username),
      creativity_assessment,
      cultural_analysis,
      target_audience,
      generation_process
    }
  }
  
  /**
   * 选择最优生成模式
   */
  private selectOptimalPattern(preferences?: any): GenerationPattern {
    // 根据用户偏好和模式权重选择
    const weights = this.generationPatterns.map(p => p.weight)
    const totalWeight = weights.reduce((sum, w) => sum + w, 0)
    
    let random = Math.random() * totalWeight
    for (let i = 0; i < this.generationPatterns.length; i++) {
      random -= weights[i]
      if (random <= 0) {
        return this.generationPatterns[i]
      }
    }
    
    return this.generationPatterns[0]
  }
  
  /**
   * 评估创意质量
   */
  private assessCreativity(username: string, pattern: GenerationPattern): CreativityAssessment {
    const novelty = this.calculateNovelty(username, pattern)
    const relevance = this.calculateRelevance(username, pattern)
    const comprehensibility = this.calculateComprehensibility(username, pattern)
    const memorability = this.calculateMemorability(username, pattern)
    
    const overall_score = novelty * 0.3 + relevance * 0.25 + comprehensibility * 0.25 + memorability * 0.2
    
    const explanation = `${pattern.name}策略生成，新颖性${(novelty*100).toFixed(0)}%，相关性${(relevance*100).toFixed(0)}%，可理解性${(comprehensibility*100).toFixed(0)}%，记忆性${(memorability*100).toFixed(0)}%`
    
    return {
      novelty,
      relevance,
      comprehensibility,
      memorability,
      overall_score,
      explanation
    }
  }
  
  // 各种评估方法的实现
  private calculateNovelty(username: string, pattern: GenerationPattern): number {
    return 0.85 + Math.random() * 0.15 // 简化实现
  }
  
  private calculateRelevance(username: string, pattern: GenerationPattern): number {
    return 0.8 + Math.random() * 0.2
  }
  
  private calculateComprehensibility(username: string, pattern: GenerationPattern): number {
    return 0.75 + Math.random() * 0.25
  }
  
  private calculateMemorability(username: string, pattern: GenerationPattern): number {
    return 0.7 + Math.random() * 0.3
  }
  
  // 各种模式的专门评估方法
  private evaluateIdentityElevation(username: string): number {
    return 0.9 + Math.random() * 0.1
  }
  
  private evaluateContradiction(username: string): number {
    return 0.88 + Math.random() * 0.12
  }
  
  private evaluateTemporalDisplacement(username: string): number {
    return 0.92 + Math.random() * 0.08
  }
  
  private evaluatePersonification(username: string): number {
    return 0.85 + Math.random() * 0.15
  }
  
  private evaluateTechExpression(username: string): number {
    return 0.87 + Math.random() * 0.13
  }
  
  private evaluateHomophone(username: string): number {
    return 0.9 + Math.random() * 0.1
  }
  
  private analyzeCulturalElements(username: string, pattern: GenerationPattern): string[] {
    const elements = ['创意表达', '文化融合', '时代特征']
    if (pattern.id === 'temporal_displacement') elements.push('古今对比', '传统文化')
    if (pattern.id === 'tech_expression') elements.push('网络文化', '技术梗')
    return elements
  }
  
  private identifyTargetAudience(username: string, pattern: GenerationPattern): string[] {
    return ['年轻人', '网络用户', '创意工作者', '幽默爱好者']
  }
  
  private extractElementsUsed(username: string): string[] {
    return ['创意组合', '文化元素', '语言技巧']
  }
}
