import { useI18n } from 'vue-i18n'
import { useDataFetcher } from '~/composables/useDataFetcher'
import { generateCulturalUsername as generateCulturalUsernameCore } from '../core/UsernameGenerator'

interface GeneratorOptions {
  slot_count?: number;  // 替代 length，使用槽位数
  includeNumbers?: boolean;
  includeSymbols?: boolean;
  includeUppercase?: boolean;
  includeLowercase?: boolean;
  excludeSimilarCharacters?: boolean;
  exclude?: string;
  startWith?: string;
  endWith?: string;
}

interface WordItem {
  word: string
  weight: number
  tags?: string[]
}

interface PatternTemplate {
  template: string
  weight: number
}

interface PatternComponents {
  [key: string]: WordItem[]
}

interface Pattern {
  name: string
  weight: number
  templates: PatternTemplate[]
  components: PatternComponents
}

interface CulturalData {
  meta: {
    language: string
    category: string
    version: string
    updated_at: string
  }
  patterns: Pattern[]
}

interface TrendingWord {
  word: string
  weight: number
  tags: string[]
  category: string
}

interface TrendingData {
  meta: {
    language: string
    period: string
    version: string
    updated_at: string
  }
  trending_words: TrendingWord[]
  trending_phrases: {
    phrase: string
    weight: number
    tags: string[]
    components: string[]
  }[]
  cultural_references: {
    reference: string
    weight: number
    tags: string[]
    category: string
  }[]
}

interface BaseWordData {
  [key: string]: WordItem[]
}

// 语言特定的用户名槽位数配置
interface SlotConfig {
  default: number
  min: number
  max: number
}

interface LanguageSlotConfig {
  [key: string]: SlotConfig
}

const languageSlotConfig: LanguageSlotConfig = {
  en: { default: 3, min: 2, max: 6 },   // 英文：2-6个语义槽位
  zh: { default: 3, min: 2, max: 8 },   // 中文：2-8个语义槽位
  ja: { default: 3, min: 2, max: 6 },   // 日文：2-6个语义槽位
  ru: { default: 3, min: 2, max: 6 }    // 俄文：2-6个语义槽位
}

// 获取随机字符
function getRandomChar(chars: string): string {
  return chars.charAt(Math.floor(Math.random() * chars.length));
}

// 简单用户名生成（基于槽位数）
export function generateSimpleUsername(slotCount: number = 3, language: string = 'en'): string {
  try {
    // 使用英文文化生成器生成有意义的用户名
    // 这里我们异步调用generateCulturalUsername，但在同步函数中使用同步方式处理
    // 由于这是同步函数，我们无法直接使用await，所以返回一个临时用户名
    // 实际使用时，应该使用generateMeaningfulUsername替代此函数
    
    // 为防止在同步上下文中出错，返回一个格式化的临时用户名
    const { t, locale } = useI18n()
    const currentLanguage = language || locale.value
    
    // 根据语言选择前缀
    let prefix = ''
    switch (currentLanguage) {
      case 'zh':
        prefix = '用户'
        break
      case 'ja':
        prefix = 'ユーザー'
        break
      case 'ru':
        prefix = 'польз'
        break
      default:
        prefix = 'user'
    }
    
    // 生成随机数字后缀
    const randomSuffix = Math.floor(Math.random() * 10000).toString().padStart(4, '0')
    
    // 组合成临时用户名
    return `${prefix}${randomSuffix}`
  } catch (error) {
    console.error('Error in generateSimpleUsername:', error)
    // 最后的回退方案：生成基于槽位数的简单组合
    const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
    const segments = []
    for (let i = 0; i < slotCount; i++) {
      let segment = ''
      const segmentLength = Math.floor(Math.random() * 3) + 2 // 2-4字符每段
      for (let j = 0; j < segmentLength; j++) {
        segment += chars.charAt(Math.floor(Math.random() * chars.length))
      }
      segments.push(segment)
    }
    return segments.join('')
  }
}

// 有意义的用户名生成（异步）- 推荐使用此函数替代generateSimpleUsername
export async function generateMeaningfulUsername(slotCount: number = 3, language: string = 'en'): Promise<string> {
  try {
    // 使用指定语言的文化生成器
    const username = await generateCulturalUsername(
      language, // 使用用户选择的语言
      'internet', // 使用互联网文化类别
      {
        slot_count: slotCount,
        includeTrends: true
      }
    )

    // 如果生成成功，返回结果
    if (username) return username

    // 如果生成失败，使用简单的回退方案
    throw new Error('Failed to generate cultural username')
  } catch (error) {
    console.error('Error in generateMeaningfulUsername:', error)
    // 回退到简单生成器
    return generateSimpleUsername(slotCount, language)
  }
}

// 加载基础词汇数据
async function loadBaseWordData(language: string): Promise<Record<string, BaseWordData>> {
  try {
    const dataFetcher = useDataFetcher()
    
    // 并行获取所有基础词汇数据
    const [adjectives, nouns, prefixes, suffixes] = await Promise.all([
      dataFetcher.fetchBaseData(language, 'adjectives'),
      dataFetcher.fetchBaseData(language, 'nouns'),
      dataFetcher.fetchBaseData(language, 'prefixes'),
      dataFetcher.fetchBaseData(language, 'suffixes')
    ])
    
    return {
      adjectives: adjectives || {},
      nouns: nouns || {},
      prefixes: prefixes || {},
      suffixes: suffixes || {}
    }
  } catch (error) {
    console.error(`Failed to load base word data for ${language}:`, error)
    return {
      adjectives: {},
      nouns: {},
      prefixes: {},
      suffixes: {}
    }
  }
}

// 加载趋势数据
async function loadTrendingData(language: string): Promise<TrendingData | null> {
  try {
    const dataFetcher = useDataFetcher()
    return await dataFetcher.fetchTrendData(language)
  } catch (error) {
    console.error(`Failed to load trending data for ${language}:`, error)
    return null
  }
}

// 加载文化数据
async function loadCulturalData(language: string, category: string): Promise<CulturalData | null> {
  try {
    const dataFetcher = useDataFetcher()
    return await dataFetcher.fetchCulturalData(language, category)
  } catch (error) {
    console.error(`Failed to load cultural data for ${language}/${category}:`, error)
    return null
  }
}

// 从模板生成用户名
function generateFromTemplate(template: string, components: PatternComponents): string {
  // 替换模板中的所有占位符 {component_name}
  return template.replace(/{([^}]+)}/g, (match, componentName) => {
    const componentItems = components[componentName]
    if (!componentItems || componentItems.length === 0) {
      return match // 如果没有找到组件，保留原始占位符
    }
    
    // 根据权重选择项
    const totalWeight = componentItems.reduce((sum, item) => sum + item.weight, 0)
    let randomWeight = Math.random() * totalWeight
    
    for (const item of componentItems) {
      randomWeight -= item.weight
      if (randomWeight <= 0) {
        return item.word
      }
    }
    
    // 默认情况，返回第一项
    return componentItems[0].word
  })
}

// 选择一个模式
function selectPattern(patterns: Pattern[]): Pattern {
  const totalWeight = patterns.reduce((sum, pattern) => sum + pattern.weight, 0)
  let randomWeight = Math.random() * totalWeight
  
  for (const pattern of patterns) {
    randomWeight -= pattern.weight
    if (randomWeight <= 0) {
      return pattern
    }
  }
  
  // 默认情况，返回第一个模式
  return patterns[0]
}

// 选择一个模板
function selectTemplate(templates: PatternTemplate[]): string {
  const totalWeight = templates.reduce((sum, template) => sum + template.weight, 0)
  let randomWeight = Math.random() * totalWeight
  
  for (const template of templates) {
    randomWeight -= template.weight
    if (randomWeight <= 0) {
      return template.template
    }
  }
  
  // 默认情况，返回第一个模板
  return templates[0].template
}

// 文化风格用户名生成
export async function generateCulturalUsername(
  language: string = 'en',
  category: string = 'internet',
  options: { includeTrends?: boolean; slot_count?: number } = {}
): Promise<string> {
  return generateCulturalUsernameCore({
    language,
    category,
    includeTrends: options.includeTrends,
    slot_count: options.slot_count
  })
}

// 获取当前语言的槽位数配置
export function getLanguageSlotConfig(language: string): SlotConfig {
  return languageSlotConfig[language as keyof typeof languageSlotConfig] || languageSlotConfig.en
}