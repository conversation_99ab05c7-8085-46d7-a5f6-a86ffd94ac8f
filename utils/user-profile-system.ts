/**
 * 用户画像系统
 * 基于第一性原理，实现精准的个性化用户名推荐
 */

// 用户画像接口
interface UserProfile {
  id: string
  demographics: {
    age_group: string      // 年龄段：'18-25', '26-35', '36-45', '45+'
    occupation: string     // 职业类型：'tech', 'creative', 'business', 'student', 'other'
    location: string       // 地区：'north', 'south', 'east', 'west', 'international'
  }
  preferences: {
    style: string          // 风格偏好：'modern', 'cool', 'playful', 'traditional', 'elegant'
    themes: string[]       // 主题偏好：['tech', 'humor', 'creative', 'workplace', 'culture']
    complexity: number     // 复杂度偏好：1-5
    length: string         // 长度偏好：'short', 'medium', 'long'
  }
  behavior: {
    usage_frequency: string    // 使用频率：'daily', 'weekly', 'monthly', 'occasional'
    generation_count: number   // 历史生成次数
    favorite_patterns: string[] // 喜欢的模式
    rejected_patterns: string[] // 不喜欢的模式
  }
  feedback: {
    ratings: { [username: string]: number }  // 用户名评分 1-5
    shares: string[]                          // 分享过的用户名
    bookmarks: string[]                       // 收藏的用户名
  }
}

// 模式适配度配置
interface PatternAffinityConfig {
  [patternId: string]: {
    age_groups: { [key: string]: number }
    occupations: { [key: string]: number }
    styles: { [key: string]: number }
    themes: { [key: string]: number }
    complexity_range: [number, number]
  }
}

export class UserProfileSystem {
  private patternAffinityConfig: PatternAffinityConfig
  
  constructor() {
    this.patternAffinityConfig = this.buildPatternAffinityConfig()
  }
  
  /**
   * 构建模式适配度配置
   */
  private buildPatternAffinityConfig(): PatternAffinityConfig {
    return {
      'identity_elevation': {
        age_groups: { '18-25': 0.9, '26-35': 0.95, '36-45': 0.8, '45+': 0.6 },
        occupations: { 'tech': 0.9, 'business': 0.95, 'creative': 0.8, 'student': 0.85 },
        styles: { 'modern': 0.9, 'cool': 0.7, 'playful': 0.8, 'traditional': 0.6 },
        themes: { 'workplace': 0.95, 'humor': 0.8, 'tech': 0.7, 'creative': 0.6 },
        complexity_range: [2, 4]
      },
      
      'contradiction_unity': {
        age_groups: { '18-25': 0.95, '26-35': 0.9, '36-45': 0.85, '45+': 0.7 },
        occupations: { 'creative': 0.95, 'tech': 0.8, 'student': 0.9, 'business': 0.7 },
        styles: { 'cool': 0.95, 'modern': 0.8, 'elegant': 0.85, 'playful': 0.7 },
        themes: { 'culture': 0.9, 'creative': 0.95, 'humor': 0.8, 'workplace': 0.6 },
        complexity_range: [3, 5]
      },
      
      'temporal_displacement': {
        age_groups: { '18-25': 0.85, '26-35': 0.9, '36-45': 0.95, '45+': 0.9 },
        occupations: { 'creative': 0.9, 'tech': 0.95, 'student': 0.8, 'business': 0.7 },
        styles: { 'modern': 0.95, 'cool': 0.9, 'traditional': 0.8, 'playful': 0.7 },
        themes: { 'tech': 0.95, 'culture': 0.9, 'creative': 0.8, 'humor': 0.85 },
        complexity_range: [3, 5]
      },
      
      'service_personification': {
        age_groups: { '18-25': 0.9, '26-35': 0.8, '36-45': 0.7, '45+': 0.6 },
        occupations: { 'creative': 0.95, 'student': 0.9, 'tech': 0.7, 'business': 0.6 },
        styles: { 'playful': 0.95, 'elegant': 0.8, 'modern': 0.7, 'cool': 0.6 },
        themes: { 'creative': 0.95, 'culture': 0.8, 'humor': 0.9, 'tech': 0.6 },
        complexity_range: [2, 4]
      },
      
      'tech_expression': {
        age_groups: { '18-25': 0.95, '26-35': 0.9, '36-45': 0.7, '45+': 0.5 },
        occupations: { 'tech': 0.95, 'student': 0.9, 'creative': 0.7, 'business': 0.6 },
        styles: { 'modern': 0.95, 'cool': 0.9, 'playful': 0.7, 'traditional': 0.4 },
        themes: { 'tech': 0.95, 'humor': 0.8, 'workplace': 0.7, 'creative': 0.6 },
        complexity_range: [2, 4]
      },
      
      'homophone_creative': {
        age_groups: { '18-25': 0.9, '26-35': 0.85, '36-45': 0.8, '45+': 0.9 },
        occupations: { 'creative': 0.9, 'student': 0.85, 'tech': 0.8, 'business': 0.7 },
        styles: { 'playful': 0.95, 'modern': 0.8, 'cool': 0.7, 'traditional': 0.85 },
        themes: { 'humor': 0.95, 'creative': 0.8, 'culture': 0.85, 'tech': 0.6 },
        complexity_range: [1, 3]
      },
      
      'context_misplacement': {
        age_groups: { '18-25': 0.85, '26-35': 0.9, '36-45': 0.8, '45+': 0.6 },
        occupations: { 'creative': 0.9, 'tech': 0.85, 'student': 0.8, 'business': 0.7 },
        styles: { 'cool': 0.9, 'modern': 0.85, 'playful': 0.8, 'elegant': 0.7 },
        themes: { 'creative': 0.9, 'humor': 0.85, 'workplace': 0.8, 'culture': 0.7 },
        complexity_range: [3, 5]
      },
      
      'emotion_concrete': {
        age_groups: { '18-25': 0.9, '26-35': 0.85, '36-45': 0.8, '45+': 0.75 },
        occupations: { 'creative': 0.95, 'student': 0.85, 'tech': 0.7, 'business': 0.6 },
        styles: { 'elegant': 0.9, 'playful': 0.85, 'modern': 0.8, 'cool': 0.7 },
        themes: { 'culture': 0.9, 'creative': 0.95, 'humor': 0.7, 'tech': 0.5 },
        complexity_range: [2, 4]
      },
      
      'absurd_logic': {
        age_groups: { '18-25': 0.95, '26-35': 0.8, '36-45': 0.6, '45+': 0.4 },
        occupations: { 'creative': 0.9, 'student': 0.95, 'tech': 0.8, 'business': 0.5 },
        styles: { 'playful': 0.95, 'cool': 0.9, 'modern': 0.7, 'traditional': 0.4 },
        themes: { 'humor': 0.95, 'creative': 0.9, 'tech': 0.7, 'workplace': 0.5 },
        complexity_range: [3, 5]
      },
      
      'status_announcement': {
        age_groups: { '18-25': 0.8, '26-35': 0.9, '36-45': 0.85, '45+': 0.7 },
        occupations: { 'tech': 0.8, 'business': 0.85, 'creative': 0.9, 'student': 0.75 },
        styles: { 'cool': 0.95, 'modern': 0.8, 'elegant': 0.7, 'playful': 0.6 },
        themes: { 'workplace': 0.8, 'tech': 0.85, 'humor': 0.7, 'creative': 0.75 },
        complexity_range: [1, 3]
      }
    }
  }
  
  /**
   * 为用户计算模式适配度
   */
  calculatePatternAffinity(userProfile: UserProfile, patternId: string): number {
    const config = this.patternAffinityConfig[patternId]
    if (!config) return 0.5 // 默认适配度
    
    let totalScore = 0
    let weightSum = 0
    
    // 年龄段适配度 (权重: 0.2)
    const ageScore = config.age_groups[userProfile.demographics.age_group] || 0.5
    totalScore += ageScore * 0.2
    weightSum += 0.2
    
    // 职业适配度 (权重: 0.25)
    const occupationScore = config.occupations[userProfile.demographics.occupation] || 0.5
    totalScore += occupationScore * 0.25
    weightSum += 0.25
    
    // 风格适配度 (权重: 0.3)
    const styleScore = config.styles[userProfile.preferences.style] || 0.5
    totalScore += styleScore * 0.3
    weightSum += 0.3
    
    // 主题适配度 (权重: 0.15)
    let themeScore = 0
    userProfile.preferences.themes.forEach(theme => {
      themeScore += config.themes[theme] || 0.5
    })
    themeScore = themeScore / userProfile.preferences.themes.length
    totalScore += themeScore * 0.15
    weightSum += 0.15
    
    // 复杂度适配度 (权重: 0.1)
    const [minComplexity, maxComplexity] = config.complexity_range
    const complexityScore = userProfile.preferences.complexity >= minComplexity && 
                           userProfile.preferences.complexity <= maxComplexity ? 1.0 : 0.3
    totalScore += complexityScore * 0.1
    weightSum += 0.1
    
    return totalScore / weightSum
  }
  
  /**
   * 为用户推荐最佳模式
   */
  recommendPatternsForUser(userProfile: UserProfile, count: number = 3): string[] {
    const patternScores: { [patternId: string]: number } = {}
    
    // 计算所有模式的适配度
    Object.keys(this.patternAffinityConfig).forEach(patternId => {
      let baseScore = this.calculatePatternAffinity(userProfile, patternId)
      
      // 根据用户历史行为调整分数
      if (userProfile.behavior.favorite_patterns.includes(patternId)) {
        baseScore *= 1.3 // 喜欢的模式加权
      }
      if (userProfile.behavior.rejected_patterns.includes(patternId)) {
        baseScore *= 0.5 // 不喜欢的模式降权
      }
      
      // 根据使用频率调整多样性
      const usageCount = userProfile.behavior.favorite_patterns.filter(p => p === patternId).length
      if (usageCount > 3) {
        baseScore *= 0.8 // 避免过度重复
      }
      
      patternScores[patternId] = baseScore
    })
    
    // 按分数排序并返回前N个
    return Object.entries(patternScores)
      .sort(([, a], [, b]) => b - a)
      .slice(0, count)
      .map(([patternId]) => patternId)
  }
  
  /**
   * 更新用户画像
   */
  updateUserProfile(userProfile: UserProfile, feedback: {
    username: string
    pattern: string
    rating?: number
    action: 'like' | 'dislike' | 'share' | 'bookmark'
  }): UserProfile {
    const updatedProfile = { ...userProfile }
    
    // 更新反馈数据
    if (feedback.rating) {
      updatedProfile.feedback.ratings[feedback.username] = feedback.rating
    }
    
    // 更新行为偏好
    switch (feedback.action) {
      case 'like':
        if (!updatedProfile.behavior.favorite_patterns.includes(feedback.pattern)) {
          updatedProfile.behavior.favorite_patterns.push(feedback.pattern)
        }
        break
      case 'dislike':
        if (!updatedProfile.behavior.rejected_patterns.includes(feedback.pattern)) {
          updatedProfile.behavior.rejected_patterns.push(feedback.pattern)
        }
        break
      case 'share':
        updatedProfile.feedback.shares.push(feedback.username)
        break
      case 'bookmark':
        updatedProfile.feedback.bookmarks.push(feedback.username)
        break
    }
    
    return updatedProfile
  }
  
  /**
   * 创建默认用户画像
   */
  createDefaultProfile(userId: string): UserProfile {
    return {
      id: userId,
      demographics: {
        age_group: '18-25',
        occupation: 'student',
        location: 'east'
      },
      preferences: {
        style: 'modern',
        themes: ['humor', 'creative'],
        complexity: 3,
        length: 'medium'
      },
      behavior: {
        usage_frequency: 'weekly',
        generation_count: 0,
        favorite_patterns: [],
        rejected_patterns: []
      },
      feedback: {
        ratings: {},
        shares: [],
        bookmarks: []
      }
    }
  }
  
  /**
   * 分析用户偏好趋势
   */
  analyzeUserTrends(userProfile: UserProfile): {
    preferred_complexity: number
    preferred_themes: string[]
    pattern_diversity: number
    engagement_level: string
  } {
    // 分析复杂度偏好
    const ratings = Object.values(userProfile.feedback.ratings)
    const avgRating = ratings.length > 0 ? ratings.reduce((a, b) => a + b, 0) / ratings.length : 3
    
    // 分析主题偏好
    const themeFrequency: { [theme: string]: number } = {}
    userProfile.preferences.themes.forEach(theme => {
      themeFrequency[theme] = (themeFrequency[theme] || 0) + 1
    })
    
    // 分析模式多样性
    const uniquePatterns = new Set(userProfile.behavior.favorite_patterns).size
    const patternDiversity = uniquePatterns / Math.max(userProfile.behavior.favorite_patterns.length, 1)
    
    // 分析参与度
    const totalInteractions = userProfile.feedback.shares.length + 
                             userProfile.feedback.bookmarks.length + 
                             Object.keys(userProfile.feedback.ratings).length
    
    let engagementLevel = 'low'
    if (totalInteractions > 20) engagementLevel = 'high'
    else if (totalInteractions > 5) engagementLevel = 'medium'
    
    return {
      preferred_complexity: userProfile.preferences.complexity,
      preferred_themes: Object.entries(themeFrequency)
        .sort(([, a], [, b]) => b - a)
        .slice(0, 3)
        .map(([theme]) => theme),
      pattern_diversity: patternDiversity,
      engagement_level: engagementLevel
    }
  }
}
