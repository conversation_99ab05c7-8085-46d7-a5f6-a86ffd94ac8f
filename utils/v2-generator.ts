/**
 * V2用户名生成器接口
 *
 * 为Vue组件提供V2系统的接口，集成V3智能模式引擎
 */

import { V3PatternEngine, createDefaultUserProfile, type UserProfile, type GenerationResult as V3GenerationResult } from './v3-pattern-engine'
import { V4UltimateEngine, type UltimateGenerationResult, type CreativeStrategy } from './v4-ultimate-engine'
import { globalQualityMonitor, type QualityMetrics } from './quality-monitor'

// V2生成器选项接口
export interface V2GenerationOptions {
  language?: string
  style?: 'traditional' | 'modern' | 'cute' | 'cool' | 'elegant' | 'playful' | 'mysterious' | 'powerful'
  themes?: string[]
  count?: number
  minLength?: number
  maxLength?: number
  complexity?: number // 1-5的复杂度等级
}

// V2生成结果接口
export interface V2GenerationResult {
  username: string
  quality: number
  explanation: string
  components: Array<{
    word: string
    pos: string
    domains: string[]
    cultural_scores: { [style: string]: number }
    interest_metrics?: {
      surprise: number
      cleverness: number
      relatability: number
      memorability: number
      shareability: number
    }
    meaning?: string
    emotional_tone?: string
  }>
  pattern: {
    id: string
    name: string
    structure: string[]
  }
  interest_analysis: {
    overall_interest_score: number
    surprise_factor: number
    cleverness_factor: number
    relatability_factor: number
    memorability_factor: number
    shareability_factor: number
    interesting_elements: string[]
    humor_type?: string
    cultural_depth: number
  }
}

/**
 * 将UI的slotCount转换为V2的复杂度参数
 */
function convertSlotCountToV2Options(slotCount: number, language: string): V2GenerationOptions {
  const options: V2GenerationOptions = {
    language: language === 'zh' ? 'zh' : language,
    count: 1
  }
  
  // 根据槽位数设置复杂度和长度
  if (slotCount <= 2) {
    options.complexity = 1
    options.minLength = 1
    options.maxLength = 2
    options.style = 'modern'
  } else if (slotCount <= 3) {
    options.complexity = 2
    options.minLength = 2
    options.maxLength = 3
    options.style = 'modern'
  } else if (slotCount <= 4) {
    options.complexity = 3
    options.minLength = 2
    options.maxLength = 4
    options.style = 'elegant'
  } else if (slotCount <= 5) {
    options.complexity = 4
    options.minLength = 3
    options.maxLength = 5
    options.style = 'traditional'
  } else {
    options.complexity = 5
    options.minLength = 3
    options.maxLength = 6
    options.style = 'traditional'
  }
  
  return options
}

/**
 * 将生成类型转换为V2风格
 */
function convertGeneratorTypeToStyle(generatorType: string): string {
  switch (generatorType) {
    case 'cultural':
      return 'traditional'
    case 'random':
      return 'modern'
    default:
      return 'modern'
  }
}

/**
 * V2中文用户名生成器 - 主接口函数
 */
export async function generateV2ChineseUsername(
  slotCount: number,
  language: string,
  generatorType: string = 'random',
  includeTrends: boolean = true
): Promise<string> {
  
  // 如果不是中文，回退到原有生成器
  if (language !== 'zh') {
    console.log('V2系统目前只支持中文，回退到原有生成器')
    // 这里可以调用原有的生成器
    return `fallback-${Math.random().toString(36).substr(2, slotCount + 2)}`
  }
  
  try {
    // 转换参数
    const v2Options = convertSlotCountToV2Options(slotCount, language)
    v2Options.style = convertGeneratorTypeToStyle(generatorType) as any
    
    // 根据includeTrends调整主题
    if (includeTrends) {
      v2Options.themes = ['modern', 'tech']
    }
    
    // 调用V2生成器
    const result = await callV2Generator(v2Options)
    
    return result.username
    
  } catch (error) {
    console.error('V2生成器错误:', error)
    // 回退到简单生成
    return `v2-fallback-${Math.random().toString(36).substr(2, slotCount)}`
  }
}

/**
 * V2中文用户名生成器 - 详细结果版本
 */
export async function generateV2ChineseUsernameWithDetails(
  slotCount: number,
  language: string,
  generatorType: string = 'random',
  includeTrends: boolean = true
): Promise<V2GenerationResult | null> {
  
  if (language !== 'zh') {
    return null
  }
  
  try {
    const v2Options = convertSlotCountToV2Options(slotCount, language)
    v2Options.style = convertGeneratorTypeToStyle(generatorType) as any
    
    if (includeTrends) {
      v2Options.themes = ['modern', 'tech']
    }
    
    return await callV2Generator(v2Options)
    
  } catch (error) {
    console.error('V2生成器详细结果错误:', error)
    return null
  }
}

/**
 * 调用V2生成器核心逻辑
 */
async function callV2Generator(options: V2GenerationOptions): Promise<V2GenerationResult> {
  // 检查是否启用V4终极模式
  const useV4Engine = shouldUseV4Engine(options)

  if (useV4Engine) {
    console.log('🎭 使用V4终极有趣引擎')
    return await generateWithV4Engine(options)
  }

  // 检查是否启用V3智能模式
  const useV3Engine = shouldUseV3Engine(options)

  if (useV3Engine) {
    console.log('🚀 使用V3智能模式引擎')
    return await generateWithV3Engine(options)
  } else {
    console.log('📱 使用V2模拟生成器')
    return await simulateV2Generator(options)
  }
}

/**
 * 判断是否使用V4终极引擎
 */
function shouldUseV4Engine(options: V2GenerationOptions): boolean {
  // V4引擎适用条件：
  // 1. 中文语言
  // 2. 追求极致有趣度
  // 3. 现代风格且复杂度较高
  // 4. 包含创意或幽默主题

  if (options.language !== 'zh') return false

  const ultimateStyles = ['modern', 'cool', 'playful']
  const hasUltimateStyle = ultimateStyles.includes(options.style || '')

  const hasCreativeThemes = options.themes?.some(theme =>
    ['humor', 'creative', 'modern', 'workplace', 'tech'].includes(theme)
  )

  const highComplexity = (options.complexity || 2) >= 3

  // V4引擎需要更高的条件
  return hasUltimateStyle && hasCreativeThemes && highComplexity
}

/**
 * 判断是否使用V3引擎
 */
function shouldUseV3Engine(options: V2GenerationOptions): boolean {
  // V3引擎适用条件：
  // 1. 中文语言
  // 2. 现代风格或包含现代主题
  // 3. 复杂度适中（2-4级）

  if (options.language !== 'zh') return false

  const modernStyles = ['modern', 'cool', 'playful']
  const hasModernStyle = modernStyles.includes(options.style || '')

  const hasModernThemes = options.themes?.some(theme =>
    ['modern', 'tech', 'humor', 'workplace'].includes(theme)
  )

  const appropriateComplexity = (options.complexity || 2) >= 2 && (options.complexity || 2) <= 4

  return hasModernStyle || hasModernThemes || appropriateComplexity
}

/**
 * 使用V4终极引擎生成用户名
 */
async function generateWithV4Engine(options: V2GenerationOptions): Promise<V2GenerationResult> {
  const v4Engine = new V4UltimateEngine()

  // 选择最优策略
  const strategies = v4Engine['strategies'] // 访问私有属性用于演示
  const selectedStrategy = selectOptimalStrategy(strategies, options)

  if (!selectedStrategy) {
    console.log('⚠️ V4引擎未找到合适策略，回退到V3')
    return await generateWithV3Engine(options)
  }

  // 使用选定策略生成
  const v4Result = v4Engine.generateByStrategy(selectedStrategy)

  // 转换V4结果为V2格式
  return convertV4ToV2Result(v4Result, options)
}

/**
 * 使用V3引擎生成用户名
 */
async function generateWithV3Engine(options: V2GenerationOptions): Promise<V2GenerationResult> {
  const v3Engine = new V3PatternEngine()

  // 创建用户画像
  const userProfile = createUserProfileFromOptions(options)

  // 选择最优模式
  const patterns = v3Engine.selectOptimalPatterns(userProfile)

  if (patterns.length === 0) {
    console.log('⚠️ V3引擎未找到合适模式，回退到V2')
    return await simulateV2Generator(options)
  }

  // 使用第一个最优模式生成
  const selectedPattern = patterns[0]
  const v3Result = v3Engine.generateByPattern(selectedPattern, userProfile)

  // 转换V3结果为V2格式
  return convertV3ToV2Result(v3Result, options)
}

/**
 * 从V2选项创建用户画像
 */
function createUserProfileFromOptions(options: V2GenerationOptions): UserProfile {
  const profile = createDefaultUserProfile()

  // 根据风格调整个性特征
  if (options.style) {
    switch (options.style) {
      case 'modern':
        profile.personality_traits.traditional_modern = 0.8
        profile.humor_preferences.wordplay = 0.9
        profile.humor_preferences.professional = 0.8
        break
      case 'cool':
        profile.personality_traits.conservative_bold = 0.7
        profile.humor_preferences.absurd = 0.8
        profile.humor_preferences.contrast = 0.9
        break
      case 'playful':
        profile.personality_traits.serious_playful = 0.8
        profile.humor_preferences.absurd = 0.9
        profile.humor_preferences.contrast = 0.8
        break
      case 'traditional':
        profile.personality_traits.traditional_modern = -0.5
        profile.humor_preferences.poetic = 0.9
        break
    }
  }

  // 根据主题调整兴趣
  if (options.themes) {
    profile.interests = options.themes

    if (options.themes.includes('tech')) {
      profile.profession = 'programmer'
      profile.humor_preferences.professional = 0.9
    }

    if (options.themes.includes('workplace')) {
      profile.humor_preferences.self_deprecating = 0.9
      profile.humor_preferences.professional = 0.8
    }
  }

  // 根据复杂度调整偏好
  if (options.complexity) {
    if (options.complexity >= 4) {
      profile.humor_preferences.poetic = 0.8
      profile.personality_traits.traditional_modern = 0.3
    } else if (options.complexity <= 2) {
      profile.humor_preferences.absurd = 0.8
      profile.personality_traits.serious_playful = 0.7
    }
  }

  return profile
}

/**
 * 选择最优V4策略
 */
function selectOptimalStrategy(strategies: CreativeStrategy[], options: V2GenerationOptions): CreativeStrategy | null {
  // 根据选项筛选合适的策略
  const suitableStrategies = strategies.filter(strategy => {
    // 根据风格匹配策略
    if (options.style === 'modern') {
      return ['misplacement', 'elevation', 'homophone', 'fusion'].includes(strategy.type)
    } else if (options.style === 'cool') {
      return ['contradiction', 'absurd', 'announcement', 'identity'].includes(strategy.type)
    } else if (options.style === 'playful') {
      return ['personification', 'emotion', 'homophone', 'absurd'].includes(strategy.type)
    }
    return true
  })

  if (suitableStrategies.length === 0) return null

  // 按优先级和效果排序
  suitableStrategies.sort((a, b) => (b.priority * b.effectiveness) - (a.priority * a.effectiveness))

  return suitableStrategies[0]
}

/**
 * 转换V4结果为V2格式
 */
function convertV4ToV2Result(v4Result: UltimateGenerationResult, options: V2GenerationOptions): V2GenerationResult {
  // 转换组件格式
  const v2Components = [{
    word: v4Result.username,
    pos: 'creative',
    domains: v4Result.cultural_elements,
    cultural_scores: { [options.style || 'modern']: 0.9 },
    interest_metrics: {
      surprise: v4Result.interest_analysis.breakdown.surprise,
      cleverness: v4Result.interest_analysis.breakdown.cleverness,
      relatability: v4Result.interest_analysis.breakdown.relatability,
      memorability: v4Result.interest_analysis.breakdown.memorability,
      shareability: v4Result.interest_analysis.breakdown.shareability
    },
    meaning: v4Result.explanation,
    emotional_tone: v4Result.psychological_appeal.join(', ')
  }]

  return {
    username: v4Result.username,
    quality: v4Result.interest_analysis.overall_score,
    explanation: v4Result.explanation,
    components: v2Components,
    pattern: {
      id: v4Result.strategy.id,
      name: v4Result.strategy.name,
      structure: ['v4_ultimate'] // V4终极模式的标识
    },
    interest_analysis: {
      overall_interest_score: v4Result.interest_analysis.overall_score,
      surprise_factor: v4Result.interest_analysis.breakdown.surprise,
      cleverness_factor: v4Result.interest_analysis.breakdown.cleverness,
      relatability_factor: v4Result.interest_analysis.breakdown.relatability,
      memorability_factor: v4Result.interest_analysis.breakdown.memorability,
      shareability_factor: v4Result.interest_analysis.breakdown.shareability,
      interesting_elements: v4Result.cultural_elements,
      humor_type: v4Result.strategy.name,
      cultural_depth: v4Result.interest_analysis.cultural_consensus
    }
  }
}

/**
 * 转换V3结果为V2格式
 */
function convertV3ToV2Result(v3Result: V3GenerationResult, options: V2GenerationOptions): V2GenerationResult {
  // 转换组件格式
  const v2Components = v3Result.components.map((comp: any) => ({
    word: comp.word || comp.natural || comp.positive || comp.prefix || comp.modifier || comp.slow || '未知',
    pos: comp.pos || 'unknown',
    domains: comp.domains || [v3Result.pattern.type],
    cultural_scores: comp.cultural_scores || { [options.style || 'modern']: 0.8 },
    interest_metrics: {
      surprise: v3Result.interest_analysis.surprise_factor,
      cleverness: v3Result.interest_analysis.cleverness_factor,
      relatability: v3Result.interest_analysis.relatability_factor,
      memorability: v3Result.interest_analysis.memorability_factor,
      shareability: v3Result.interest_analysis.shareability_factor
    },
    meaning: comp.meaning,
    emotional_tone: comp.emotional_tone
  }))

  return {
    username: v3Result.username,
    quality: v3Result.quality,
    explanation: v3Result.explanation,
    components: v2Components,
    pattern: {
      id: v3Result.pattern.id,
      name: v3Result.pattern.name,
      structure: ['v3_pattern'] // V3模式的简化结构
    },
    interest_analysis: {
      overall_interest_score: v3Result.interest_analysis.overall_interest_score,
      surprise_factor: v3Result.interest_analysis.surprise_factor,
      cleverness_factor: v3Result.interest_analysis.cleverness_factor,
      relatability_factor: v3Result.interest_analysis.relatability_factor,
      memorability_factor: v3Result.interest_analysis.memorability_factor,
      shareability_factor: v3Result.interest_analysis.shareability_factor,
      interesting_elements: v3Result.interest_analysis.interesting_elements,
      humor_type: v3Result.interest_analysis.pattern_type,
      cultural_depth: 0.8 // V3引擎默认具有较高文化深度
    }
  }
}

/**
 * 模拟V2生成器 - 在浏览器环境中运行
 */
// 防重复缓存
const generationCache = new Set<string>()

async function simulateV2Generator(options: V2GenerationOptions): Promise<V2GenerationResult> {
  // 最多尝试10次生成不重复的结果
  for (let attempt = 0; attempt < 10; attempt++) {
    const result = await generateSingleResult(options)

    // 检查是否重复
    if (!generationCache.has(result.username)) {
      generationCache.add(result.username)

      // 清理缓存，保持最近100个结果
      if (generationCache.size > 100) {
        const firstItem = generationCache.values().next().value
        generationCache.delete(firstItem)
      }

      // 质量监控
      const qualityMetrics = globalQualityMonitor.evaluateResult(result, options)
      if (globalQualityMonitor.passesQualityGate(qualityMetrics)) {
        return result
      } else {
        console.log(`🔄 第${attempt + 1}次生成未通过质量门槛，重新生成...`)
      }
    }
  }

  // 如果10次都重复，返回最后一次结果但标记为重复
  const finalResult = await generateSingleResult(options)
  finalResult.explanation += ' (注意：可能存在重复)'

  // 质量监控
  const qualityMetrics = globalQualityMonitor.evaluateResult(finalResult, options)
  if (!globalQualityMonitor.passesQualityGate(qualityMetrics)) {
    console.warn('⚠️ 生成结果未通过质量门槛:', qualityMetrics.issues)
  }

  return finalResult
}

async function generateSingleResult(options: V2GenerationOptions): Promise<V2GenerationResult> {
  // 模拟V2词汇库（包含有趣词汇）
  const v2Vocabulary = {
    nouns: [
      // 传统词汇
      { word: '星', domains: ['nature'], cultural_scores: { modern: 0.8, traditional: 0.9, elegant: 0.9 }, interest_metrics: { surprise: 0.3, cleverness: 0.4, relatability: 0.7, memorability: 0.8, shareability: 0.6 } },
      { word: '月', domains: ['nature'], cultural_scores: { modern: 0.8, traditional: 0.9, elegant: 0.9 }, interest_metrics: { surprise: 0.3, cleverness: 0.4, relatability: 0.7, memorability: 0.8, shareability: 0.6 } },
      { word: '云', domains: ['nature', 'tech'], cultural_scores: { modern: 0.9, traditional: 0.8, elegant: 0.9 }, interest_metrics: { surprise: 0.5, cleverness: 0.6, relatability: 0.7, memorability: 0.8, shareability: 0.7 } },
      { word: '心', domains: ['emotion', 'social'], cultural_scores: { modern: 0.8, traditional: 0.9, elegant: 0.8 }, interest_metrics: { surprise: 0.4, cleverness: 0.5, relatability: 0.8, memorability: 0.7, shareability: 0.6 } },
      { word: '梦', domains: ['emotion', 'fantasy'], cultural_scores: { modern: 0.8, traditional: 0.8, cute: 0.9 }, interest_metrics: { surprise: 0.6, cleverness: 0.6, relatability: 0.8, memorability: 0.8, shareability: 0.7 } },

      // 有趣词汇 - 职业梗
      { word: '社畜', domains: ['workplace', 'humor'], cultural_scores: { modern: 0.9, traditional: 0.1, cool: 0.7 }, interest_metrics: { surprise: 0.6, cleverness: 0.8, relatability: 0.95, memorability: 0.9, shareability: 0.8 }, meaning: '公司员工的自嘲称呼', emotional_tone: 'self_deprecating_humor' },
      { word: '打工人', domains: ['workplace', 'humor'], cultural_scores: { modern: 0.95, traditional: 0.3, cool: 0.8 }, interest_metrics: { surprise: 0.7, cleverness: 0.6, relatability: 0.95, memorability: 0.8, shareability: 0.9 }, meaning: '工作者的团结称呼', emotional_tone: 'solidarity_humor' },
      { word: '代码农', domains: ['tech', 'humor'], cultural_scores: { modern: 0.9, traditional: 0.2, cool: 0.8 }, interest_metrics: { surprise: 0.7, cleverness: 0.8, relatability: 0.9, memorability: 0.8, shareability: 0.8 }, meaning: '程序员的幽默称呼' },

      // 扩展职业梗词汇
      { word: '设计狮', domains: ['creative', 'humor'], cultural_scores: { modern: 0.9, traditional: 0.2, cool: 0.9 }, interest_metrics: { surprise: 0.8, cleverness: 0.9, relatability: 0.8, memorability: 0.9, shareability: 0.8 }, meaning: '设计师的幽默称呼' },
      { word: '产品汪', domains: ['workplace', 'humor'], cultural_scores: { modern: 0.9, traditional: 0.1, cool: 0.8 }, interest_metrics: { surprise: 0.8, cleverness: 0.8, relatability: 0.8, memorability: 0.9, shareability: 0.8 }, meaning: '产品经理的幽默称呼' },
      { word: '运营喵', domains: ['workplace', 'humor'], cultural_scores: { modern: 0.9, traditional: 0.1, cute: 0.9 }, interest_metrics: { surprise: 0.8, cleverness: 0.8, relatability: 0.8, memorability: 0.9, shareability: 0.9 }, meaning: '运营人员的可爱称呼' },

      // 网络文化词汇
      { word: '网友', domains: ['social', 'modern'], cultural_scores: { modern: 0.9, traditional: 0.3, cool: 0.7 }, interest_metrics: { surprise: 0.5, cleverness: 0.6, relatability: 0.8, memorability: 0.7, shareability: 0.7 } },
      { word: '博主', domains: ['social', 'modern'], cultural_scores: { modern: 0.95, traditional: 0.2, cool: 0.8 }, interest_metrics: { surprise: 0.6, cleverness: 0.7, relatability: 0.8, memorability: 0.8, shareability: 0.8 } },
      { word: '主播', domains: ['entertainment', 'modern'], cultural_scores: { modern: 0.95, traditional: 0.1, cool: 0.9 }, interest_metrics: { surprise: 0.7, cleverness: 0.6, relatability: 0.7, memorability: 0.8, shareability: 0.8 } },

      // 情感状态词汇
      { word: '小透明', domains: ['emotion', 'humor'], cultural_scores: { modern: 0.9, traditional: 0.2, cute: 0.8 }, interest_metrics: { surprise: 0.7, cleverness: 0.7, relatability: 0.9, memorability: 0.8, shareability: 0.8 }, meaning: '存在感很低的人' },
      { word: '柠檬精', domains: ['emotion', 'humor'], cultural_scores: { modern: 0.95, traditional: 0.1, cute: 0.9 }, interest_metrics: { surprise: 0.8, cleverness: 0.8, relatability: 0.9, memorability: 0.9, shareability: 0.9 }, meaning: '容易嫉妒的人' },
      { word: '吃瓜群众', domains: ['social', 'humor'], cultural_scores: { modern: 0.9, traditional: 0.2, playful: 0.9 }, interest_metrics: { surprise: 0.7, cleverness: 0.8, relatability: 0.9, memorability: 0.8, shareability: 0.9 }, meaning: '看热闹的人' },

      // 科技词汇扩展
      { word: '码', domains: ['tech', 'modern'], cultural_scores: { modern: 0.95, traditional: 0.2, cool: 0.9 }, interest_metrics: { surprise: 0.7, cleverness: 0.7, relatability: 0.6, memorability: 0.7, shareability: 0.6 } },
      { word: '网', domains: ['tech', 'social'], cultural_scores: { modern: 0.9, traditional: 0.3, cool: 0.8 }, interest_metrics: { surprise: 0.5, cleverness: 0.6, relatability: 0.7, memorability: 0.7, shareability: 0.6 } },
      { word: '云', domains: ['tech', 'nature'], cultural_scores: { modern: 0.9, traditional: 0.8, elegant: 0.9 }, interest_metrics: { surprise: 0.6, cleverness: 0.7, relatability: 0.7, memorability: 0.8, shareability: 0.7 } },
      { word: '端', domains: ['tech', 'modern'], cultural_scores: { modern: 0.9, traditional: 0.3, cool: 0.8 }, interest_metrics: { surprise: 0.6, cleverness: 0.7, relatability: 0.6, memorability: 0.7, shareability: 0.6 } },

      // 创意词汇
      { word: '收藏家', domains: ['hobby', 'elegant'], cultural_scores: { modern: 0.8, traditional: 0.7, elegant: 0.9 }, interest_metrics: { surprise: 0.6, cleverness: 0.7, relatability: 0.7, memorability: 0.8, shareability: 0.7 } },
      { word: '观察员', domains: ['social', 'intellectual'], cultural_scores: { modern: 0.8, traditional: 0.6, cool: 0.8 }, interest_metrics: { surprise: 0.6, cleverness: 0.8, relatability: 0.7, memorability: 0.8, shareability: 0.7 } },
      { word: '研究生', domains: ['academic', 'modern'], cultural_scores: { modern: 0.8, traditional: 0.7, elegant: 0.8 }, interest_metrics: { surprise: 0.5, cleverness: 0.7, relatability: 0.8, memorability: 0.7, shareability: 0.6 } }
    ],
    adjectives: [
      // 传统形容词
      { word: '美', domains: ['quality'], cultural_scores: { modern: 0.8, traditional: 0.9, elegant: 0.9 }, interest_metrics: { surprise: 0.2, cleverness: 0.3, relatability: 0.7, memorability: 0.6, shareability: 0.5 } },
      { word: '好', domains: ['quality'], cultural_scores: { modern: 0.9, traditional: 0.8, elegant: 0.7 }, interest_metrics: { surprise: 0.2, cleverness: 0.3, relatability: 0.8, memorability: 0.6, shareability: 0.5 } },
      { word: '新', domains: ['quality', 'modern'], cultural_scores: { modern: 0.95, traditional: 0.6, cool: 0.8 }, interest_metrics: { surprise: 0.4, cleverness: 0.4, relatability: 0.7, memorability: 0.6, shareability: 0.6 } },
      { word: '静', domains: ['quality', 'emotion'], cultural_scores: { modern: 0.7, traditional: 0.9, elegant: 0.9 }, interest_metrics: { surprise: 0.3, cleverness: 0.5, relatability: 0.6, memorability: 0.7, shareability: 0.5 } },
      { word: '雅', domains: ['quality', 'culture'], cultural_scores: { modern: 0.7, traditional: 0.95, elegant: 0.98 }, interest_metrics: { surprise: 0.4, cleverness: 0.6, relatability: 0.5, memorability: 0.8, shareability: 0.6 } },

      // 有趣形容词
      { word: '酷', domains: ['quality', 'modern'], cultural_scores: { modern: 0.95, traditional: 0.3, cool: 0.98 }, interest_metrics: { surprise: 0.6, cleverness: 0.5, relatability: 0.8, memorability: 0.8, shareability: 0.8 } },
      { word: '萌', domains: ['quality', 'emotion'], cultural_scores: { modern: 0.9, traditional: 0.2, cute: 0.98 }, interest_metrics: { surprise: 0.7, cleverness: 0.6, relatability: 0.8, memorability: 0.9, shareability: 0.9 } },
      { word: '潮', domains: ['quality', 'modern'], cultural_scores: { modern: 0.95, traditional: 0.2, cool: 0.9 }, interest_metrics: { surprise: 0.8, cleverness: 0.7, relatability: 0.7, memorability: 0.8, shareability: 0.8 } },
      { word: '炫', domains: ['quality', 'modern'], cultural_scores: { modern: 0.9, traditional: 0.2, cool: 0.95 }, interest_metrics: { surprise: 0.8, cleverness: 0.7, relatability: 0.6, memorability: 0.8, shareability: 0.7 } },

      // 扩展有趣形容词
      { word: '佛系', domains: ['lifestyle', 'philosophy'], cultural_scores: { modern: 0.9, traditional: 0.3, cool: 0.8 }, interest_metrics: { surprise: 0.8, cleverness: 0.8, relatability: 0.9, memorability: 0.8, shareability: 0.8 } },
      { word: '硬核', domains: ['quality', 'modern'], cultural_scores: { modern: 0.95, traditional: 0.1, cool: 0.95 }, interest_metrics: { surprise: 0.8, cleverness: 0.7, relatability: 0.7, memorability: 0.9, shareability: 0.8 } },
      { word: '沙雕', domains: ['humor', 'modern'], cultural_scores: { modern: 0.95, traditional: 0.1, playful: 0.95 }, interest_metrics: { surprise: 0.9, cleverness: 0.7, relatability: 0.8, memorability: 0.9, shareability: 0.9 } },
      { word: '神仙', domains: ['quality', 'fantasy'], cultural_scores: { modern: 0.8, traditional: 0.9, elegant: 0.9 }, interest_metrics: { surprise: 0.7, cleverness: 0.8, relatability: 0.7, memorability: 0.9, shareability: 0.8 } },
      { word: '宝藏', domains: ['quality', 'emotion'], cultural_scores: { modern: 0.9, traditional: 0.6, cute: 0.9 }, interest_metrics: { surprise: 0.7, cleverness: 0.7, relatability: 0.8, memorability: 0.8, shareability: 0.8 } },
      { word: '治愈', domains: ['emotion', 'quality'], cultural_scores: { modern: 0.9, traditional: 0.5, cute: 0.9 }, interest_metrics: { surprise: 0.6, cleverness: 0.7, relatability: 0.9, memorability: 0.8, shareability: 0.8 } },
      { word: '暖心', domains: ['emotion', 'quality'], cultural_scores: { modern: 0.8, traditional: 0.7, cute: 0.9 }, interest_metrics: { surprise: 0.5, cleverness: 0.6, relatability: 0.9, memorability: 0.8, shareability: 0.8 } },
      { word: '逗比', domains: ['humor', 'modern'], cultural_scores: { modern: 0.9, traditional: 0.1, playful: 0.95 }, interest_metrics: { surprise: 0.8, cleverness: 0.6, relatability: 0.8, memorability: 0.8, shareability: 0.9 } }
    ],
    verbs: [
      // 传统动词
      { word: '飞', domains: ['action', 'nature'], cultural_scores: { modern: 0.8, traditional: 0.8, cool: 0.9 }, interest_metrics: { surprise: 0.5, cleverness: 0.5, relatability: 0.6, memorability: 0.7, shareability: 0.6 } },
      { word: '舞', domains: ['action', 'culture'], cultural_scores: { modern: 0.7, traditional: 0.9, elegant: 0.9 }, interest_metrics: { surprise: 0.4, cleverness: 0.6, relatability: 0.6, memorability: 0.7, shareability: 0.6 } },
      { word: '笑', domains: ['action', 'emotion'], cultural_scores: { modern: 0.9, traditional: 0.8, cute: 0.9 }, interest_metrics: { surprise: 0.3, cleverness: 0.4, relatability: 0.9, memorability: 0.8, shareability: 0.8 } },

      // 有趣动词 - 网络流行语
      { word: '摸鱼', domains: ['workplace', 'humor'], cultural_scores: { modern: 0.95, traditional: 0.1, playful: 0.9 }, interest_metrics: { surprise: 0.8, cleverness: 0.7, relatability: 0.9, memorability: 0.8, shareability: 0.9 }, meaning: '上班时偷懒，不认真工作' },
      { word: '内卷', domains: ['social', 'workplace'], cultural_scores: { modern: 0.9, traditional: 0.2, powerful: 0.8 }, interest_metrics: { surprise: 0.7, cleverness: 0.8, relatability: 0.95, memorability: 0.7, shareability: 0.8 }, meaning: '过度竞争，恶性循环' },
      { word: '躺平', domains: ['lifestyle', 'philosophy'], cultural_scores: { modern: 0.9, traditional: 0.1, cool: 0.8 }, interest_metrics: { surprise: 0.8, cleverness: 0.7, relatability: 0.9, memorability: 0.8, shareability: 0.9 }, meaning: '不再努力，接受现状' },

      // 扩展有趣动词
      { word: '划水', domains: ['workplace', 'humor'], cultural_scores: { modern: 0.9, traditional: 0.1, playful: 0.9 }, interest_metrics: { surprise: 0.8, cleverness: 0.7, relatability: 0.9, memorability: 0.8, shareability: 0.8 }, meaning: '工作不认真，敷衍了事' },
      { word: '吃瓜', domains: ['social', 'humor'], cultural_scores: { modern: 0.95, traditional: 0.1, playful: 0.9 }, interest_metrics: { surprise: 0.8, cleverness: 0.8, relatability: 0.9, memorability: 0.9, shareability: 0.9 }, meaning: '看热闹，围观' },
      { word: '种草', domains: ['social', 'modern'], cultural_scores: { modern: 0.95, traditional: 0.1, cute: 0.8 }, interest_metrics: { surprise: 0.7, cleverness: 0.8, relatability: 0.8, memorability: 0.8, shareability: 0.8 }, meaning: '推荐好物，引起购买欲' },
      { word: '拔草', domains: ['social', 'modern'], cultural_scores: { modern: 0.9, traditional: 0.1, cool: 0.7 }, interest_metrics: { surprise: 0.7, cleverness: 0.8, relatability: 0.8, memorability: 0.8, shareability: 0.7 }, meaning: '购买心仪物品' },
      { word: '冲浪', domains: ['tech', 'modern'], cultural_scores: { modern: 0.9, traditional: 0.2, cool: 0.9 }, interest_metrics: { surprise: 0.7, cleverness: 0.7, relatability: 0.7, memorability: 0.8, shareability: 0.7 }, meaning: '网上浏览，上网' },
      { word: '刷屏', domains: ['social', 'tech'], cultural_scores: { modern: 0.95, traditional: 0.1, cool: 0.8 }, interest_metrics: { surprise: 0.6, cleverness: 0.7, relatability: 0.8, memorability: 0.7, shareability: 0.7 }, meaning: '大量发布信息' },
      { word: '打卡', domains: ['lifestyle', 'social'], cultural_scores: { modern: 0.9, traditional: 0.3, cool: 0.7 }, interest_metrics: { surprise: 0.6, cleverness: 0.6, relatability: 0.8, memorability: 0.7, shareability: 0.7 }, meaning: '记录生活，分享动态' },
      { word: '熬夜', domains: ['lifestyle', 'modern'], cultural_scores: { modern: 0.9, traditional: 0.3, cool: 0.6 }, interest_metrics: { surprise: 0.5, cleverness: 0.5, relatability: 0.95, memorability: 0.7, shareability: 0.7 }, meaning: '晚睡，不按时休息' },
      { word: '追剧', domains: ['entertainment', 'modern'], cultural_scores: { modern: 0.9, traditional: 0.2, cute: 0.8 }, interest_metrics: { surprise: 0.5, cleverness: 0.5, relatability: 0.9, memorability: 0.7, shareability: 0.8 }, meaning: '连续观看电视剧' },
      { word: '刷题', domains: ['academic', 'modern'], cultural_scores: { modern: 0.8, traditional: 0.4, cool: 0.6 }, interest_metrics: { surprise: 0.5, cleverness: 0.6, relatability: 0.8, memorability: 0.7, shareability: 0.6 }, meaning: '大量做练习题' }
    ]
  }
  
  // 扩展语法模式，增加更多组合
  const patterns = [
    { id: 'single_noun', name: '单一名词', structure: ['n'], complexity: 1 },
    { id: 'adj_noun', name: '形容词+名词', structure: ['adj', 'n'], complexity: 2 },
    { id: 'noun_adj', name: '名词+形容词', structure: ['n', 'adj'], complexity: 2 },
    { id: 'noun_noun', name: '名词+名词', structure: ['n', 'n'], complexity: 2 },
    { id: 'verb_noun', name: '动词+名词', structure: ['v', 'n'], complexity: 2 },
    { id: 'adj_noun_noun', name: '形容词+名词+名词', structure: ['adj', 'n', 'n'], complexity: 3 },
    { id: 'noun_verb_noun', name: '名词+动词+名词', structure: ['n', 'v', 'n'], complexity: 3 },
    { id: 'adj_verb_noun', name: '形容词+动词+名词', structure: ['adj', 'v', 'n'], complexity: 3 },
    { id: 'verb_adj_noun', name: '动词+形容词+名词', structure: ['v', 'adj', 'n'], complexity: 3 },
    { id: 'noun_adj_noun', name: '名词+形容词+名词', structure: ['n', 'adj', 'n'], complexity: 3 },
    // 复杂度4+的模式
    { id: 'adj_noun_verb_noun', name: '形容词+名词+动词+名词', structure: ['adj', 'n', 'v', 'n'], complexity: 4 },
    { id: 'verb_adj_noun_noun', name: '动词+形容词+名词+名词', structure: ['v', 'adj', 'n', 'n'], complexity: 4 }
  ]
  
  // 根据复杂度智能选择模式
  let availablePatterns = patterns.filter(p => p.complexity <= (options.complexity || 3))

  // 如果复杂度较高，优先选择更复杂的模式
  if (options.complexity && options.complexity >= 3) {
    const complexPatterns = patterns.filter(p => p.complexity >= 3)
    if (complexPatterns.length > 0) {
      // 70%概率选择复杂模式，30%概率选择简单模式
      availablePatterns = Math.random() < 0.7 ? complexPatterns : availablePatterns
    }
  }

  // 确保有可用模式
  if (availablePatterns.length === 0) {
    availablePatterns = patterns.filter(p => p.complexity <= 3)
  }

  // 智能选择模式（偏向有趣的组合）
  const pattern = selectBestPattern(availablePatterns, options)
  
  // 为每个位置选择词汇
  const components = []
  for (const pos of pattern.structure) {
    let candidates = []
    
    if (pos === 'n') candidates = v2Vocabulary.nouns
    else if (pos === 'adj') candidates = v2Vocabulary.adjectives
    else if (pos === 'v') candidates = v2Vocabulary.verbs
    
    // 根据风格过滤
    if (options.style) {
      candidates = candidates.filter(word => 
        (word.cultural_scores[options.style!] || 0) > 0.5
      )
    }
    
    // 根据主题过滤
    if (options.themes && options.themes.length > 0) {
      const themeFiltered = candidates.filter(word =>
        options.themes!.some(theme => word.domains.includes(theme))
      )
      if (themeFiltered.length > 0) {
        candidates = themeFiltered
      }
    }
    
    // 避免重复
    candidates = candidates.filter(word => 
      !components.some(c => c.word === word.word)
    )
    
    if (candidates.length === 0) {
      // 如果没有候选词，回退到所有词汇
      if (pos === 'n') candidates = v2Vocabulary.nouns
      else if (pos === 'adj') candidates = v2Vocabulary.adjectives
      else if (pos === 'v') candidates = v2Vocabulary.verbs
    }
    
    // 随机选择
    const selected = candidates[Math.floor(Math.random() * candidates.length)]
    components.push({
      word: selected.word,
      pos: pos,
      domains: selected.domains,
      cultural_scores: selected.cultural_scores
    })
  }
  
  // 构造用户名
  const username = components.map(c => c.word).join('')
  
  // 检查长度约束
  if (options.minLength && username.length < options.minLength) {
    // 如果太短，尝试添加一个词
    const extraCandidates = v2Vocabulary.nouns.filter(w => 
      !components.some(c => c.word === w.word)
    )
    if (extraCandidates.length > 0) {
      const extra = extraCandidates[Math.floor(Math.random() * extraCandidates.length)]
      components.push({
        word: extra.word,
        pos: 'n',
        domains: extra.domains,
        cultural_scores: extra.cultural_scores
      })
    }
  }
  
  if (options.maxLength && username.length > options.maxLength) {
    // 如果太长，移除最后一个词
    components.pop()
  }
  
  // 重新构造用户名
  const finalUsername = components.map(c => c.word).join('')
  
  // 计算有趣度分析
  const interestAnalysis = calculateInterestAnalysis(components, pattern, finalUsername, options.style || 'modern')

  // 计算质量分数（基于有趣度）
  const quality = Math.min(1.0, 0.5 + interestAnalysis.overall_interest_score * 0.5)

  // 生成智能解释
  const explanation = generateSmartExplanation(components, pattern, interestAnalysis, options.style || 'modern')

  return {
    username: finalUsername,
    quality,
    explanation,
    components,
    pattern: {
      id: pattern.id,
      name: pattern.name,
      structure: pattern.structure
    },
    interest_analysis: interestAnalysis
  }
}

/**
 * 智能选择最佳模式
 */
function selectBestPattern(availablePatterns: any[], options: V2GenerationOptions): any {
  // 根据风格和主题评分模式
  const scoredPatterns = availablePatterns.map(pattern => {
    let score = 0.5 // 基础分

    // 根据风格调整分数
    if (options.style === 'modern' || options.style === 'cool') {
      // 现代风格偏好动词开头的模式
      if (pattern.structure[0] === 'v') score += 0.3
      if (pattern.structure.includes('v')) score += 0.2
    }

    if (options.style === 'cute' || options.style === 'playful') {
      // 可爱风格偏好形容词+名词
      if (pattern.structure.includes('adj') && pattern.structure.includes('n')) score += 0.3
    }

    if (options.style === 'elegant' || options.style === 'traditional') {
      // 优雅风格偏好名词组合
      if (pattern.structure.filter(s => s === 'n').length >= 2) score += 0.3
    }

    // 根据主题调整分数
    if (options.themes?.includes('humor') || options.themes?.includes('workplace')) {
      // 幽默主题偏好包含动词的模式
      if (pattern.structure.includes('v')) score += 0.2
    }

    // 复杂度奖励
    if (pattern.complexity === (options.complexity || 3)) {
      score += 0.2
    }

    return { pattern, score }
  })

  // 按分数排序
  scoredPatterns.sort((a, b) => b.score - a.score)

  // 从前50%中随机选择，增加多样性
  const topHalf = scoredPatterns.slice(0, Math.max(1, Math.ceil(scoredPatterns.length / 2)))
  return topHalf[Math.floor(Math.random() * topHalf.length)].pattern
}

/**
 * 计算有趣度分析
 */
function calculateInterestAnalysis(
  components: any[],
  pattern: any,
  username: string,
  style: string
): any {
  // 计算各维度有趣度
  const surprise = calculateSurpriseFactor(components, pattern)
  const cleverness = calculateClevernessFactor(components, pattern, username)
  const relatability = calculateRelatabilityFactor(components, style)
  const memorability = calculateMemorabilityFactor(components, username)
  const shareability = calculateShareabilityFactor(components, pattern)

  // 综合有趣度评分
  const overallScore = (
    surprise * 0.2 +
    cleverness * 0.25 +
    relatability * 0.3 +
    memorability * 0.15 +
    shareability * 0.1
  )

  // 识别有趣元素
  const interestingElements = identifyInterestingElements(components, pattern)

  // 判断幽默类型
  const humorType = identifyHumorType(components)

  // 计算文化深度
  const culturalDepth = calculateCulturalDepth(components, pattern)

  return {
    overall_interest_score: overallScore,
    surprise_factor: surprise,
    cleverness_factor: cleverness,
    relatability_factor: relatability,
    memorability_factor: memorability,
    shareability_factor: shareability,
    interesting_elements: interestingElements,
    humor_type: humorType,
    cultural_depth: culturalDepth
  }
}

/**
 * 计算惊喜因子
 */
function calculateSurpriseFactor(components: any[], pattern: any): number {
  // 基于词汇的意外性
  const wordSurprise = components.reduce((sum, comp) => {
    return sum + (comp.interest_metrics?.surprise || 0.3)
  }, 0) / components.length

  // 组合的意外性
  const combinationSurprise = calculateCombinationSurprise(components)

  return Math.min(1.0, (wordSurprise * 0.7 + combinationSurprise * 0.3))
}

/**
 * 计算巧妙因子
 */
function calculateClevernessFactor(components: any[], pattern: any, username: string): number {
  // 词汇巧妙度
  const wordCleverness = components.reduce((sum, comp) => {
    return sum + (comp.interest_metrics?.cleverness || 0.3)
  }, 0) / components.length

  // 语法巧妙度
  const grammarCleverness = pattern.structure.length > 2 ? 0.8 : 0.5

  // 谐音或双关检测
  const wordplayBonus = detectWordplay(components, username) ? 0.3 : 0

  return Math.min(1.0, wordCleverness * 0.6 + grammarCleverness * 0.3 + wordplayBonus)
}

/**
 * 计算共鸣因子
 */
function calculateRelatabilityFactor(components: any[], style: string): number {
  // 基于词汇的共鸣度
  const wordRelatability = components.reduce((sum, comp) => {
    return sum + (comp.interest_metrics?.relatability || 0.5)
  }, 0) / components.length

  // 风格匹配度
  const styleMatch = components.reduce((sum, comp) => {
    return sum + (comp.cultural_scores[style] || 0.5)
  }, 0) / components.length

  return Math.min(1.0, wordRelatability * 0.7 + styleMatch * 0.3)
}

/**
 * 计算记忆性因子
 */
function calculateMemorabilityFactor(components: any[], username: string): number {
  // 基于词汇的记忆性
  const wordMemorability = components.reduce((sum, comp) => {
    return sum + (comp.interest_metrics?.memorability || 0.5)
  }, 0) / components.length

  // 长度记忆性（2-3字最佳）
  const lengthScore = username.length === 2 ? 1.0 :
                     username.length === 3 ? 0.9 :
                     username.length === 4 ? 0.7 : 0.5

  // 音韵记忆性（简化）
  const phoneticScore = components.every(c => c.word.length === 1) ? 0.8 : 0.6

  return Math.min(1.0, wordMemorability * 0.5 + lengthScore * 0.3 + phoneticScore * 0.2)
}

/**
 * 计算分享性因子
 */
function calculateShareabilityFactor(components: any[], pattern: any): number {
  // 基于词汇的分享性
  const wordShareability = components.reduce((sum, comp) => {
    return sum + (comp.interest_metrics?.shareability || 0.5)
  }, 0) / components.length

  // 话题性检测
  const topicalBonus = components.some(comp =>
    comp.domains.includes('humor') ||
    comp.domains.includes('workplace') ||
    comp.domains.includes('social')
  ) ? 0.2 : 0

  return Math.min(1.0, wordShareability * 0.8 + topicalBonus)
}

/**
 * 识别有趣元素
 */
function identifyInterestingElements(components: any[], pattern: any): string[] {
  const elements: string[] = []

  // 检测网络流行语
  const internetSlang = components.filter(c =>
    ['摸鱼', '内卷', '躺平', '社畜', '打工人'].includes(c.word)
  )
  if (internetSlang.length > 0) {
    elements.push('网络流行语')
  }

  // 检测职业梗
  const professionalMemes = components.filter(c =>
    ['代码农', '社畜', '打工人'].includes(c.word)
  )
  if (professionalMemes.length > 0) {
    elements.push('职业梗')
  }

  // 检测反差萌
  const hasCute = components.some(c => (c.cultural_scores.cute || 0) > 0.8)
  const hasTech = components.some(c => c.domains.includes('tech'))
  if (hasCute && hasTech) {
    elements.push('反差萌')
  }

  // 检测文化融合
  const hasTraditional = components.some(c => (c.cultural_scores.traditional || 0) > 0.8)
  const hasModern = components.some(c => (c.cultural_scores.modern || 0) > 0.8)
  if (hasTraditional && hasModern) {
    elements.push('文化融合')
  }

  return elements
}

/**
 * 识别幽默类型
 */
function identifyHumorType(components: any[]): string | undefined {
  // 自嘲型幽默
  if (components.some(c => c.emotional_tone === 'self_deprecating_humor')) {
    return '自嘲型幽默'
  }

  // 团结型幽默
  if (components.some(c => c.emotional_tone === 'solidarity_humor')) {
    return '团结型幽默'
  }

  // 反差型幽默
  const styles = components.flatMap(c => Object.keys(c.cultural_scores))
  if (styles.includes('cute') && styles.includes('cool')) {
    return '反差型幽默'
  }

  return undefined
}

/**
 * 计算文化深度
 */
function calculateCulturalDepth(components: any[], pattern: any): number {
  // 文化域多样性
  const culturalDomains = new Set(components.flatMap(c => c.domains))
  const diversityScore = Math.min(1.0, culturalDomains.size / 5)

  // 传统文化元素
  const traditionalScore = components.reduce((sum, comp) => {
    return sum + (comp.cultural_scores.traditional || 0)
  }, 0) / components.length

  // 现代文化元素
  const modernScore = components.reduce((sum, comp) => {
    return sum + (comp.cultural_scores.modern || 0)
  }, 0) / components.length

  return diversityScore * 0.4 + traditionalScore * 0.3 + modernScore * 0.3
}

/**
 * 生成智能解释
 */
function generateSmartExplanation(
  components: any[],
  pattern: any,
  interestAnalysis: any,
  style: string
): string {
  let explanation = `采用${pattern.name}模式，组合${components.map(c => `"${c.word}"`).join(' + ')}`

  // 添加有趣元素说明
  if (interestAnalysis.interesting_elements.length > 0) {
    explanation += `，融入${interestAnalysis.interesting_elements.join('、')}元素`
  }

  // 添加幽默类型说明
  if (interestAnalysis.humor_type) {
    explanation += `，体现${interestAnalysis.humor_type}`
  }

  // 添加文化特色说明
  explanation += `，展现${getStyleDescription(style)}风格`

  // 添加有趣度评价
  if (interestAnalysis.overall_interest_score > 0.8) {
    explanation += '，极具创意和趣味性'
  } else if (interestAnalysis.overall_interest_score > 0.6) {
    explanation += '，富有趣味和个性'
  }

  // 添加特殊含义说明
  const meaningfulComponents = components.filter(c => c.meaning)
  if (meaningfulComponents.length > 0) {
    explanation += `。其中"${meaningfulComponents[0].word}"寓意${meaningfulComponents[0].meaning}`
  }

  explanation += '。'

  return explanation
}

/**
 * 计算组合意外性
 */
function calculateCombinationSurprise(components: any[]): number {
  if (components.length < 2) return 0.3

  // 检测跨域组合
  const allDomains = components.flatMap(c => c.domains)
  const uniqueDomains = new Set(allDomains)

  // 域越多样，意外性越高
  const domainDiversity = uniqueDomains.size / allDomains.length

  // 检测风格反差
  const styles = ['traditional', 'modern', 'cute', 'cool']
  let maxStyleDiff = 0

  for (let i = 0; i < components.length - 1; i++) {
    for (let j = i + 1; j < components.length; j++) {
      for (const style of styles) {
        const score1 = components[i].cultural_scores[style] || 0
        const score2 = components[j].cultural_scores[style] || 0
        const diff = Math.abs(score1 - score2)
        maxStyleDiff = Math.max(maxStyleDiff, diff)
      }
    }
  }

  return Math.min(1.0, domainDiversity * 0.6 + maxStyleDiff * 0.4)
}

/**
 * 检测文字游戏（谐音、双关等）
 */
function detectWordplay(components: any[], username: string): boolean {
  // 简化实现：检测是否包含已知的文字游戏词汇
  const wordplayWords = ['代码农', '设计狮', '产品汪']
  return components.some(c => wordplayWords.includes(c.word))
}

/**
 * 获取风格描述
 */
function getStyleDescription(style: string): string {
  const descriptions: { [key: string]: string } = {
    'traditional': '传统典雅',
    'modern': '现代时尚',
    'cute': '可爱甜美',
    'cool': '酷炫个性',
    'elegant': '优雅精致',
    'playful': '活泼俏皮',
    'mysterious': '神秘深邃',
    'powerful': '强劲有力'
  }

  return descriptions[style] || style
}

/**
 * 检查V2系统是否支持指定语言
 */
export function isV2Supported(language: string): boolean {
  return language === 'zh'
}

/**
 * 获取V2系统支持的复杂度范围
 */
export function getV2ComplexityRange(language: string): { min: number, max: number, default: number } {
  if (language === 'zh') {
    return { min: 1, max: 6, default: 3 }
  }
  return { min: 1, max: 5, default: 3 }
}
