/**
 * 质量监控系统
 * 实时监控生成质量，防止质量下降
 */

export interface QualityMetrics {
  timestamp: number
  username: string
  quality_score: number
  uniqueness_score: number
  complexity_match: number
  style_match: number
  user_satisfaction_predicted: number
  issues: string[]
}

export interface QualityThresholds {
  min_quality: number
  min_uniqueness: number
  min_complexity_match: number
  min_style_match: number
  max_issues: number
}

export class QualityMonitor {
  private metrics: QualityMetrics[] = []
  private recentUsernames = new Set<string>()
  private maxCacheSize = 1000
  
  private thresholds: QualityThresholds = {
    min_quality: 0.65,
    min_uniqueness: 0.9,
    min_complexity_match: 0.7,
    min_style_match: 0.6,
    max_issues: 2
  }
  
  /**
   * 评估生成结果质量
   */
  evaluateResult(result: any, options: any): QualityMetrics {
    const metrics: QualityMetrics = {
      timestamp: Date.now(),
      username: result.username,
      quality_score: result.quality || 0,
      uniqueness_score: this.calculateUniqueness(result.username),
      complexity_match: this.calculateComplexityMatch(result, options),
      style_match: this.calculateStyleMatch(result, options),
      user_satisfaction_predicted: this.predictUserSatisfaction(result, options),
      issues: this.identifyIssues(result, options)
    }
    
    // 添加到监控记录
    this.addMetrics(metrics)
    
    return metrics
  }
  
  /**
   * 计算唯一性分数
   */
  private calculateUniqueness(username: string): number {
    if (this.recentUsernames.has(username)) {
      return 0 // 完全重复
    }
    
    // 检查相似度
    let similarityCount = 0
    for (const existing of this.recentUsernames) {
      if (this.calculateSimilarity(username, existing) > 0.8) {
        similarityCount++
      }
    }
    
    // 相似度惩罚
    const similarityPenalty = Math.min(similarityCount * 0.2, 0.8)
    return Math.max(0, 1 - similarityPenalty)
  }
  
  /**
   * 计算字符串相似度
   */
  private calculateSimilarity(str1: string, str2: string): number {
    if (str1 === str2) return 1
    if (str1.length === 0 || str2.length === 0) return 0
    
    // 简化的编辑距离算法
    const maxLen = Math.max(str1.length, str2.length)
    const minLen = Math.min(str1.length, str2.length)
    
    let matches = 0
    for (let i = 0; i < minLen; i++) {
      if (str1[i] === str2[i]) matches++
    }
    
    return matches / maxLen
  }
  
  /**
   * 计算复杂度匹配度
   */
  private calculateComplexityMatch(result: any, options: any): number {
    const targetComplexity = options.complexity || 3
    const actualComplexity = this.estimateComplexity(result)
    
    // 复杂度差异惩罚
    const diff = Math.abs(targetComplexity - actualComplexity)
    return Math.max(0, 1 - diff * 0.2)
  }
  
  /**
   * 估算实际复杂度
   */
  private estimateComplexity(result: any): number {
    const username = result.username
    const components = result.components || []
    
    let complexity = 1
    
    // 基于长度
    if (username.length > 6) complexity += 1
    if (username.length > 9) complexity += 1
    
    // 基于组件数量
    complexity += Math.max(0, components.length - 1)
    
    // 基于词汇复杂度
    const hasComplexWords = components.some((comp: any) => 
      comp.word && comp.word.length > 2
    )
    if (hasComplexWords) complexity += 1
    
    return Math.min(5, complexity)
  }
  
  /**
   * 计算风格匹配度
   */
  private calculateStyleMatch(result: any, options: any): number {
    const targetStyle = options.style || 'modern'
    const components = result.components || []
    
    let styleScore = 0.5 // 基础分
    
    // 检查文化分数匹配
    for (const comp of components) {
      if (comp.cultural_scores && comp.cultural_scores[targetStyle]) {
        styleScore += comp.cultural_scores[targetStyle] * 0.1
      }
    }
    
    // 检查主题匹配
    if (options.themes) {
      const themeMatch = options.themes.some((theme: string) =>
        components.some((comp: any) => 
          comp.domains && comp.domains.includes(theme)
        )
      )
      if (themeMatch) styleScore += 0.2
    }
    
    return Math.min(1, styleScore)
  }
  
  /**
   * 预测用户满意度
   */
  private predictUserSatisfaction(result: any, options: any): number {
    let satisfaction = 0.5
    
    // 质量因子
    satisfaction += (result.quality - 0.5) * 0.6
    
    // 唯一性因子
    const uniqueness = this.calculateUniqueness(result.username)
    satisfaction += (uniqueness - 0.5) * 0.3
    
    // 风格匹配因子
    const styleMatch = this.calculateStyleMatch(result, options)
    satisfaction += (styleMatch - 0.5) * 0.2
    
    // 长度适中奖励
    const length = result.username.length
    if (length >= 3 && length <= 6) {
      satisfaction += 0.1
    }
    
    return Math.max(0, Math.min(1, satisfaction))
  }
  
  /**
   * 识别问题
   */
  private identifyIssues(result: any, options: any): string[] {
    const issues: string[] = []
    
    // 质量问题
    if (result.quality < this.thresholds.min_quality) {
      issues.push(`质量过低 (${(result.quality * 100).toFixed(1)}%)`)
    }
    
    // 唯一性问题
    const uniqueness = this.calculateUniqueness(result.username)
    if (uniqueness < this.thresholds.min_uniqueness) {
      issues.push(`重复或相似度过高 (唯一性${(uniqueness * 100).toFixed(1)}%)`)
    }
    
    // 长度问题
    const length = result.username.length
    if (length < 2) {
      issues.push('用户名过短')
    } else if (length > 10) {
      issues.push('用户名过长')
    }
    
    // 复杂度匹配问题
    const complexityMatch = this.calculateComplexityMatch(result, options)
    if (complexityMatch < this.thresholds.min_complexity_match) {
      issues.push(`复杂度不匹配 (匹配度${(complexityMatch * 100).toFixed(1)}%)`)
    }
    
    // 风格匹配问题
    const styleMatch = this.calculateStyleMatch(result, options)
    if (styleMatch < this.thresholds.min_style_match) {
      issues.push(`风格不匹配 (匹配度${(styleMatch * 100).toFixed(1)}%)`)
    }
    
    // 字符问题
    if (!/^[\u4e00-\u9fa5a-zA-Z0-9]+$/.test(result.username)) {
      issues.push('包含不支持的字符')
    }
    
    return issues
  }
  
  /**
   * 添加监控指标
   */
  private addMetrics(metrics: QualityMetrics): void {
    this.metrics.push(metrics)
    this.recentUsernames.add(metrics.username)
    
    // 清理缓存
    if (this.metrics.length > this.maxCacheSize) {
      const removed = this.metrics.shift()
      if (removed) {
        this.recentUsernames.delete(removed.username)
      }
    }
    
    if (this.recentUsernames.size > this.maxCacheSize) {
      const firstUsername = this.recentUsernames.values().next().value
      this.recentUsernames.delete(firstUsername)
    }
  }
  
  /**
   * 检查是否通过质量门槛
   */
  passesQualityGate(metrics: QualityMetrics): boolean {
    return (
      metrics.quality_score >= this.thresholds.min_quality &&
      metrics.uniqueness_score >= this.thresholds.min_uniqueness &&
      metrics.complexity_match >= this.thresholds.min_complexity_match &&
      metrics.style_match >= this.thresholds.min_style_match &&
      metrics.issues.length <= this.thresholds.max_issues
    )
  }
  
  /**
   * 获取质量报告
   */
  getQualityReport(timeRange: number = 3600000): any { // 默认1小时
    const cutoff = Date.now() - timeRange
    const recentMetrics = this.metrics.filter(m => m.timestamp >= cutoff)
    
    if (recentMetrics.length === 0) {
      return {
        period: '无数据',
        total_generations: 0,
        avg_quality: 0,
        avg_uniqueness: 0,
        pass_rate: 0,
        common_issues: []
      }
    }
    
    const avgQuality = recentMetrics.reduce((sum, m) => sum + m.quality_score, 0) / recentMetrics.length
    const avgUniqueness = recentMetrics.reduce((sum, m) => sum + m.uniqueness_score, 0) / recentMetrics.length
    const passCount = recentMetrics.filter(m => this.passesQualityGate(m)).length
    const passRate = passCount / recentMetrics.length
    
    // 统计常见问题
    const issueCount: { [key: string]: number } = {}
    recentMetrics.forEach(m => {
      m.issues.forEach(issue => {
        issueCount[issue] = (issueCount[issue] || 0) + 1
      })
    })
    
    const commonIssues = Object.entries(issueCount)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5)
      .map(([issue, count]) => ({ issue, count, rate: count / recentMetrics.length }))
    
    return {
      period: `最近${Math.round(timeRange / 60000)}分钟`,
      total_generations: recentMetrics.length,
      avg_quality: avgQuality,
      avg_uniqueness: avgUniqueness,
      avg_satisfaction: recentMetrics.reduce((sum, m) => sum + m.user_satisfaction_predicted, 0) / recentMetrics.length,
      pass_rate: passRate,
      common_issues: commonIssues
    }
  }
  
  /**
   * 更新质量阈值
   */
  updateThresholds(newThresholds: Partial<QualityThresholds>): void {
    this.thresholds = { ...this.thresholds, ...newThresholds }
  }
  
  /**
   * 清理历史数据
   */
  cleanup(): void {
    this.metrics = []
    this.recentUsernames.clear()
  }
}

// 全局质量监控实例
export const globalQualityMonitor = new QualityMonitor()
