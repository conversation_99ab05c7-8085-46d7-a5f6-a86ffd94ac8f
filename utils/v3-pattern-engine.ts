/**
 * V3智能模式引擎
 * 基于用户名模式分析的智能生成系统
 */

// 核心接口定义
export interface UserProfile {
  age_group: 'gen_z' | 'millennial' | 'gen_x'
  profession?: string
  interests: string[]
  personality_traits: {
    introvert_extrovert: number    // -1到1
    serious_playful: number        // -1到1
    traditional_modern: number     // -1到1
    conservative_bold: number      // -1到1
  }
  humor_preferences: {
    self_deprecating: number       // 0-1
    wordplay: number              // 0-1
    absurd: number                // 0-1
    poetic: number                // 0-1
    professional: number          // 0-1
    contrast: number              // 0-1
  }
}

export interface InterestingPattern {
  id: string
  name: string
  type: 'homophone' | 'contrast' | 'professional_self_deprecating' | 
        'poetic_personification' | 'absurd_combination' | 'state_description'
  template: string
  base_interest_score: number
  target_demographics: string[]
  examples: string[]
}

export interface GenerationResult {
  username: string
  quality: number
  pattern: InterestingPattern
  explanation: string
  components: any[]
  interest_analysis: {
    overall_interest_score: number
    surprise_factor: number
    cleverness_factor: number
    relatability_factor: number
    memorability_factor: number
    shareability_factor: number
    pattern_type: string
    interesting_elements: string[]
  }
}

// V3模式库 - 扩展到11种模式
const V3_PATTERNS: InterestingPattern[] = [
  // 1. 谐音梗模式
  {
    id: 'homophone_classic',
    name: '经典谐音梗',
    type: 'homophone',
    template: '{original_phrase} → {homophone_replacement}',
    base_interest_score: 0.95,
    target_demographics: ['gen_z', 'millennial'],
    examples: ['芝士就是力量', '莓心没肺', '无饿不作']
  },

  // 2. 反差萌模式
  {
    id: 'contrast_cute',
    name: '反差萌组合',
    type: 'contrast',
    template: '{positive_trait} + {negative_trait}',
    base_interest_score: 0.90,
    target_demographics: ['gen_z', 'millennial'],
    examples: ['小可爱的大魔王', '温柔的杀手', '社恐但话多']
  },

  // 3. 职业自嘲模式
  {
    id: 'professional_self_deprecating',
    name: '职业自嘲',
    type: 'professional_self_deprecating',
    template: '{professional_prefix} + {negative_behavior} + {formal_suffix}',
    base_interest_score: 0.92,
    target_demographics: ['millennial', 'gen_x'],
    examples: ['专业退堂鼓选手', '八级抬杠运动员', '甲方闭嘴']
  },

  // 4. 诗意拟人模式
  {
    id: 'poetic_personification',
    name: '诗意拟人',
    type: 'poetic_personification',
    template: '{natural_element} + {human_action}',
    base_interest_score: 0.75,
    target_demographics: ['millennial', 'gen_x'],
    examples: ['晚风有信', '贩卖人间黄昏', '月亮邮递员']
  },

  // 5. 荒诞组合模式
  {
    id: 'absurd_combination',
    name: '荒诞组合',
    type: 'absurd_combination',
    template: '{mismatched_element_a} + {action} + {mismatched_element_b}',
    base_interest_score: 0.88,
    target_demographics: ['gen_z', 'millennial'],
    examples: ['骑着蜗牛追火箭', '骑着扫帚去上班', '一棵很闲的树']
  },

  // 6. 状态描述模式
  {
    id: 'state_description',
    name: '状态描述',
    type: 'state_description',
    template: '{state_modifier} + {real_feeling} + {self_positioning}',
    base_interest_score: 0.85,
    target_demographics: ['gen_z', 'millennial'],
    examples: ['间歇性想努力', '情绪稳定但易怒', '平平无奇小天才']
  },

  // 7. 职业化包装模式 (新增)
  {
    id: 'professional_packaging',
    name: '职业化包装',
    type: 'professional_packaging',
    template: '{authority_prefix} + {daily_behavior} + {formal_position}',
    base_interest_score: 0.96,
    target_demographics: ['gen_z', 'millennial'],
    examples: ['国家一级抬杠运动员', '拖延症全球推广大使', '首席干饭官']
  },

  // 8. 矛盾状态模式 (新增)
  {
    id: 'contradictory_state',
    name: '矛盾状态',
    type: 'contradictory_state',
    template: '{positive_trait} + {conjunction} + {opposite_trait}',
    base_interest_score: 0.92,
    target_demographics: ['millennial', 'gen_z'],
    examples: ['听劝但反骨', '温柔且强硬', '礼貌的恶棍']
  },

  // 9. 拟人化服务模式 (新增)
  {
    id: 'personified_service',
    name: '拟人化服务',
    type: 'personified_service',
    template: '{abstract_concept} + {service_role}',
    base_interest_score: 0.88,
    target_demographics: ['gen_z', 'millennial'],
    examples: ['月亮邮递员', '云朵收藏家', '废话输出机']
  },

  // 10. 状态公告模式 (新增)
  {
    id: 'status_announcement',
    name: '状态公告',
    type: 'status_announcement',
    template: '{announcement_style} + {personal_state}',
    base_interest_score: 0.90,
    target_demographics: ['millennial', 'gen_z'],
    examples: ['暂停营业', '禁止访问', '免谈']
  },

  // 11. 赛博传统融合模式 (新增)
  {
    id: 'cyber_traditional_fusion',
    name: '赛博传统融合',
    type: 'cyber_traditional_fusion',
    template: '{traditional_element} + {cyber_element}',
    base_interest_score: 0.85,
    target_demographics: ['gen_z', 'millennial'],
    examples: ['电子木鱼功德+1', '8G冲浪选手', '发疯文学家']
  }
]

// V3词汇库
const V3_VOCABULARY = {
  // 谐音梗源词库
  homophone_sources: [
    { original: '知识就是力量', replacement: '芝士', domain: 'food', cultural_ref: '经典名言' },
    { original: '贫僧', replacement: '飘柔', domain: 'modern_brand', cultural_ref: '古代称谓' },
    { original: '程序员', replacement: '程序猿', domain: 'profession', cultural_ref: '职业称谓' }
  ],
  
  // 反差对比词库
  contrast_pairs: [
    { positive: '小可爱', negative: '大魔王', connector: '的' },
    { positive: '温柔', negative: '杀手', connector: '的' },
    { trait_a: '社恐', trait_b: '话多', connector: '但' },
    { trait_a: '佛系', trait_b: '暴躁', connector: '又' }
  ],
  
  // 职业自嘲模板
  professional_templates: [
    { prefix: '专业', behavior: '退堂鼓', suffix: '选手' },
    { prefix: '八级', behavior: '抬杠', suffix: '运动员' },
    { prefix: '资深', behavior: '摸鱼', suffix: '专家' },
    { prefix: '高级', behavior: '划水', suffix: '工程师' }
  ],
  
  // 诗意元素库
  poetic_elements: [
    { natural: '晚风', action: '有信', mood: '温柔' },
    { abstract: '黄昏', commercial: '贩卖', setting: '人间' },
    { celestial: '月亮', mundane: '卖零食', contrast: '天地' },
    { weather: '雨', emotion: '听', time: '夜阑' }
  ],
  
  // 荒诞元素库
  absurd_elements: [
    { slow: '蜗牛', fast: '火箭', action: '追' },
    { tech: 'WiFi密码', state: '忘记了', context: '现代困扰' },
    { error: '404', object: '用户', state: '未找到' },
    { animal: '猪', ability: '飞', impossibility: '物理定律' }
  ],
  
  // 状态描述库
  state_templates: [
    { modifier: '间歇性', feeling: '想努力', positioning: '现实主义者' },
    { condition: '吃饱了', result: '就困', type: '生理现象' },
    { contradiction: '平平无奇', claim: '小天才', humor: '自相矛盾' },
    { frequency: '偶尔', action: '清醒', identity: '人间观察者' }
  ],

  // 职业化包装库 (新增)
  professional_packaging_templates: [
    { authority: '国家一级', behavior: '抬杠', position: '运动员' },
    { authority: '全球推广', behavior: '拖延症', position: '大使' },
    { authority: '常务', behavior: '熬夜', position: '委员' },
    { authority: '首席', behavior: '干饭', position: '官' },
    { authority: '资深', behavior: '摸鱼', position: '专家' },
    { authority: '高级', behavior: '划水', position: '工程师' }
  ],

  // 矛盾状态库 (新增)
  contradictory_state_templates: [
    { positive: '听劝', conjunction: '但', negative: '反骨' },
    { positive: '情绪稳定', conjunction: '但', negative: '易怒' },
    { positive: '温柔', conjunction: '且', negative: '强硬' },
    { positive: '礼貌', conjunction: '的', negative: '恶棍' },
    { positive: '佛系', conjunction: '又', negative: '暴躁' }
  ],

  // 拟人化服务库 (新增)
  personified_service_templates: [
    { concept: '月亮', service: '邮递员' },
    { concept: '云朵', service: '收藏家' },
    { concept: '废话', service: '输出机' },
    { concept: '胡言乱语', service: '生成器' },
    { concept: '星星', service: '充电宝' },
    { concept: '晚安', service: '配送员' }
  ],

  // 状态公告库 (新增)
  status_announcement_templates: [
    { style: '暂停', state: '营业' },
    { style: '禁止', state: '访问' },
    { style: '免', state: '谈' },
    { style: '别来', state: '无恙' },
    { style: '停止', state: '内耗' },
    { style: '拒绝', state: 'emo' }
  ],

  // 赛博传统融合库 (新增)
  cyber_traditional_templates: [
    { traditional: '木鱼', cyber: '电子', suffix: '功德+1' },
    { traditional: '文学家', cyber: '发疯', context: '网络行为' },
    { traditional: '冲浪', cyber: '8G', context: '网络浏览' },
    { traditional: '修仙', cyber: '赛博', context: '网络文化' },
    { traditional: '打坐', cyber: '在线', context: '现代禅修' }
  ]
}

/**
 * V3模式引擎主类
 */
export class V3PatternEngine {
  private patterns: InterestingPattern[]
  private vocabulary: any
  
  constructor() {
    this.patterns = V3_PATTERNS
    this.vocabulary = V3_VOCABULARY
  }
  
  /**
   * 根据用户画像选择最优模式
   */
  selectOptimalPatterns(profile: UserProfile): InterestingPattern[] {
    const scored_patterns = this.patterns.map(pattern => ({
      pattern,
      score: this.calculatePatternFitScore(pattern, profile)
    }))
    
    // 按适配度排序
    scored_patterns.sort((a, b) => b.score - a.score)
    
    // 返回前3个最适合的模式
    return scored_patterns.slice(0, 3).map(item => item.pattern)
  }
  
  /**
   * 计算模式适配度评分
   */
  private calculatePatternFitScore(pattern: InterestingPattern, profile: UserProfile): number {
    let score = pattern.base_interest_score * 0.4 // 基础分40%
    
    // 年龄群体匹配 (30%)
    if (pattern.target_demographics.includes(profile.age_group)) {
      score += 0.3
    }
    
    // 幽默偏好匹配 (30%)
    const humor_match = this.calculateHumorMatch(pattern, profile)
    score += humor_match * 0.3
    
    return Math.min(1.0, score)
  }
  
  /**
   * 计算幽默偏好匹配度
   */
  private calculateHumorMatch(pattern: InterestingPattern, profile: UserProfile): number {
    const type_mapping: { [key: string]: keyof UserProfile['humor_preferences'] } = {
      'homophone': 'wordplay',
      'contrast': 'contrast',
      'professional_self_deprecating': 'professional',
      'poetic_personification': 'poetic',
      'absurd_combination': 'absurd',
      'state_description': 'self_deprecating'
    }
    
    const humor_type = type_mapping[pattern.type]
    return humor_type ? profile.humor_preferences[humor_type] : 0.5
  }
  
  /**
   * 根据模式生成用户名
   */
  generateByPattern(pattern: InterestingPattern, profile: UserProfile): GenerationResult {
    switch (pattern.type) {
      case 'homophone':
        return this.generateHomophoneUsername(pattern, profile)
      case 'contrast':
        return this.generateContrastUsername(pattern, profile)
      case 'professional_self_deprecating':
        return this.generateProfessionalSelfDeprecatingUsername(pattern, profile)
      case 'poetic_personification':
        return this.generatePoeticPersonificationUsername(pattern, profile)
      case 'absurd_combination':
        return this.generateAbsurdCombinationUsername(pattern, profile)
      case 'state_description':
        return this.generateStateDescriptionUsername(pattern, profile)
      case 'professional_packaging':
        return this.generateProfessionalPackagingUsername(pattern, profile)
      case 'contradictory_state':
        return this.generateContradictoryStateUsername(pattern, profile)
      case 'personified_service':
        return this.generatePersonifiedServiceUsername(pattern, profile)
      case 'status_announcement':
        return this.generateStatusAnnouncementUsername(pattern, profile)
      case 'cyber_traditional_fusion':
        return this.generateCyberTraditionalFusionUsername(pattern, profile)
      default:
        throw new Error(`Unknown pattern type: ${pattern.type}`)
    }
  }
  
  /**
   * 生成谐音梗用户名
   */
  private generateHomophoneUsername(pattern: InterestingPattern, profile: UserProfile): GenerationResult {
    const sources = this.vocabulary.homophone_sources
    const source = sources[Math.floor(Math.random() * sources.length)]
    
    const username = source.replacement + source.original.slice(2) // 简化实现
    
    return this.createGenerationResult(
      username,
      pattern,
      [source],
      `采用谐音梗模式，将"${source.original}"巧妙替换为"${source.replacement}"，创造出意想不到的幽默效果。`,
      {
        surprise_factor: 0.9,
        cleverness_factor: 0.95,
        relatability_factor: 0.8,
        memorability_factor: 0.9,
        shareability_factor: 0.95,
        pattern_type: '谐音梗',
        interesting_elements: ['文字游戏', '创意替换', '文化梗']
      }
    )
  }
  
  /**
   * 生成反差萌用户名
   */
  private generateContrastUsername(pattern: InterestingPattern, profile: UserProfile): GenerationResult {
    const pairs = this.vocabulary.contrast_pairs
    const pair = pairs[Math.floor(Math.random() * pairs.length)]
    
    const username = pair.positive + pair.connector + pair.negative
    
    return this.createGenerationResult(
      username,
      pattern,
      [pair],
      `采用反差萌模式，将"${pair.positive}"与"${pair.negative}"巧妙组合，形成强烈的性格反差，既可爱又有个性。`,
      {
        surprise_factor: 0.8,
        cleverness_factor: 0.7,
        relatability_factor: 0.9,
        memorability_factor: 0.85,
        shareability_factor: 0.8,
        pattern_type: '反差萌',
        interesting_elements: ['性格反差', '萌系幽默', '个性表达']
      }
    )
  }
  
  /**
   * 生成职业自嘲用户名
   */
  private generateProfessionalSelfDeprecatingUsername(pattern: InterestingPattern, profile: UserProfile): GenerationResult {
    const templates = this.vocabulary.professional_templates
    const template = templates[Math.floor(Math.random() * templates.length)]
    
    const username = template.prefix + template.behavior + template.suffix
    
    return this.createGenerationResult(
      username,
      pattern,
      [template],
      `采用职业自嘲模式，用"${template.prefix}"和"${template.suffix}"的正式表述来描述"${template.behavior}"行为，形成幽默的专业反差。`,
      {
        surprise_factor: 0.7,
        cleverness_factor: 0.9,
        relatability_factor: 0.95,
        memorability_factor: 0.8,
        shareability_factor: 0.85,
        pattern_type: '职业自嘲',
        interesting_elements: ['职场幽默', '自我调侃', '专业反差']
      }
    )
  }
  
  // 其他生成方法的简化实现...
  private generatePoeticPersonificationUsername(pattern: InterestingPattern, profile: UserProfile): GenerationResult {
    const elements = this.vocabulary.poetic_elements
    const element = elements[Math.floor(Math.random() * elements.length)]
    
    const username = element.natural + element.action
    
    return this.createGenerationResult(username, pattern, [element], 
      `采用诗意拟人模式，赋予"${element.natural}"以"${element.action}"的人类行为，营造温柔的诗意氛围。`,
      {
        surprise_factor: 0.6,
        cleverness_factor: 0.8,
        relatability_factor: 0.6,
        memorability_factor: 0.9,
        shareability_factor: 0.7,
        pattern_type: '诗意拟人',
        interesting_elements: ['诗意美感', '拟人手法', '文艺气息']
      }
    )
  }
  
  private generateAbsurdCombinationUsername(pattern: InterestingPattern, profile: UserProfile): GenerationResult {
    const elements = this.vocabulary.absurd_elements
    const element = elements[Math.floor(Math.random() * elements.length)]
    
    const username = `${element.slow}${element.action}${element.fast}`
    
    return this.createGenerationResult(username, pattern, [element],
      `采用荒诞组合模式，将"${element.slow}"与"${element.fast}"进行不可能的组合，创造出超现实的幽默效果。`,
      {
        surprise_factor: 0.95,
        cleverness_factor: 0.8,
        relatability_factor: 0.7,
        memorability_factor: 0.9,
        shareability_factor: 0.9,
        pattern_type: '荒诞组合',
        interesting_elements: ['超现实', '想象力', '荒诞幽默']
      }
    )
  }
  
  private generateStateDescriptionUsername(pattern: InterestingPattern, profile: UserProfile): GenerationResult {
    const templates = this.vocabulary.state_templates
    const template = templates[Math.floor(Math.random() * templates.length)]
    
    const username = template.modifier + template.feeling
    
    return this.createGenerationResult(username, pattern, [template],
      `采用状态描述模式，用"${template.modifier}"来修饰"${template.feeling}"，精准描述现代人的真实状态。`,
      {
        surprise_factor: 0.6,
        cleverness_factor: 0.7,
        relatability_factor: 0.95,
        memorability_factor: 0.8,
        shareability_factor: 0.85,
        pattern_type: '状态描述',
        interesting_elements: ['真实共鸣', '状态描述', '自我认知']
      }
    )
  }

  /**
   * 生成职业化包装用户名 (新增)
   */
  private generateProfessionalPackagingUsername(pattern: InterestingPattern, profile: UserProfile): GenerationResult {
    const templates = this.vocabulary.professional_packaging_templates
    const template = templates[Math.floor(Math.random() * templates.length)]

    const username = template.authority + template.behavior + template.position

    return this.createGenerationResult(username, pattern, [template],
      `采用职业化包装模式，将"${template.behavior}"包装成"${template.authority}${template.position}"，形成权威感与日常行为的幽默反差。`,
      {
        surprise_factor: 0.95,
        cleverness_factor: 0.9,
        relatability_factor: 0.9,
        memorability_factor: 0.85,
        shareability_factor: 0.95,
        pattern_type: '职业化包装',
        interesting_elements: ['权威包装', '行为升级', '职场幽默']
      }
    )
  }

  /**
   * 生成矛盾状态用户名 (新增)
   */
  private generateContradictoryStateUsername(pattern: InterestingPattern, profile: UserProfile): GenerationResult {
    const templates = this.vocabulary.contradictory_state_templates
    const template = templates[Math.floor(Math.random() * templates.length)]

    const username = template.positive + template.conjunction + template.negative

    return this.createGenerationResult(username, pattern, [template],
      `采用矛盾状态模式，将"${template.positive}"与"${template.negative}"组合，精准描述现代人复杂的内心状态。`,
      {
        surprise_factor: 0.8,
        cleverness_factor: 0.85,
        relatability_factor: 0.95,
        memorability_factor: 0.8,
        shareability_factor: 0.85,
        pattern_type: '矛盾状态',
        interesting_elements: ['内心矛盾', '真实写照', '复杂人性']
      }
    )
  }

  /**
   * 生成拟人化服务用户名 (新增)
   */
  private generatePersonifiedServiceUsername(pattern: InterestingPattern, profile: UserProfile): GenerationResult {
    const templates = this.vocabulary.personified_service_templates
    const template = templates[Math.floor(Math.random() * templates.length)]

    const username = template.concept + template.service

    return this.createGenerationResult(username, pattern, [template],
      `采用拟人化服务模式，将"${template.concept}"设定为"${template.service}"，创造出富有想象力的服务角色。`,
      {
        surprise_factor: 0.85,
        cleverness_factor: 0.8,
        relatability_factor: 0.7,
        memorability_factor: 0.9,
        shareability_factor: 0.8,
        pattern_type: '拟人化服务',
        interesting_elements: ['想象力', '服务设定', '创意角色']
      }
    )
  }

  /**
   * 生成状态公告用户名 (新增)
   */
  private generateStatusAnnouncementUsername(pattern: InterestingPattern, profile: UserProfile): GenerationResult {
    const templates = this.vocabulary.status_announcement_templates
    const template = templates[Math.floor(Math.random() * templates.length)]

    const username = template.style + template.state

    return this.createGenerationResult(username, pattern, [template],
      `采用状态公告模式，用"${template.style}${template.state}"的公告形式表达个人状态，简洁而有力。`,
      {
        surprise_factor: 0.7,
        cleverness_factor: 0.75,
        relatability_factor: 0.85,
        memorability_factor: 0.9,
        shareability_factor: 0.8,
        pattern_type: '状态公告',
        interesting_elements: ['公告形式', '状态表达', '简洁有力']
      }
    )
  }

  /**
   * 生成赛博传统融合用户名 (新增)
   */
  private generateCyberTraditionalFusionUsername(pattern: InterestingPattern, profile: UserProfile): GenerationResult {
    const templates = this.vocabulary.cyber_traditional_templates
    const template = templates[Math.floor(Math.random() * templates.length)]

    const username = template.cyber + template.traditional + (template.suffix || '')

    return this.createGenerationResult(username, pattern, [template],
      `采用赛博传统融合模式，将"${template.traditional}"与"${template.cyber}"结合，体现传统文化与现代科技的创意融合。`,
      {
        surprise_factor: 0.8,
        cleverness_factor: 0.9,
        relatability_factor: 0.75,
        memorability_factor: 0.85,
        shareability_factor: 0.8,
        pattern_type: '赛博传统融合',
        interesting_elements: ['文化融合', '时代碰撞', '创意组合']
      }
    )
  }

  /**
   * 创建生成结果
   */
  private createGenerationResult(
    username: string,
    pattern: InterestingPattern,
    components: any[],
    explanation: string,
    interest_metrics: any
  ): GenerationResult {
    const overall_score = (
      interest_metrics.surprise_factor * 0.25 +
      interest_metrics.cleverness_factor * 0.25 +
      interest_metrics.relatability_factor * 0.25 +
      interest_metrics.memorability_factor * 0.15 +
      interest_metrics.shareability_factor * 0.10
    )
    
    return {
      username,
      quality: overall_score,
      pattern,
      explanation,
      components,
      interest_analysis: {
        overall_interest_score: overall_score,
        ...interest_metrics
      }
    }
  }
}

/**
 * 创建默认用户画像
 */
export function createDefaultUserProfile(): UserProfile {
  return {
    age_group: 'millennial',
    interests: [],
    personality_traits: {
      introvert_extrovert: 0,
      serious_playful: 0.3,
      traditional_modern: 0.5,
      conservative_bold: 0.2
    },
    humor_preferences: {
      self_deprecating: 0.7,
      wordplay: 0.8,
      absurd: 0.6,
      poetic: 0.5,
      professional: 0.8,
      contrast: 0.7
    }
  }
}
