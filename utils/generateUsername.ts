import fs from 'fs/promises'
import path from 'path'

export type UsernameType = 'Random' | 'Cultural' | 'Trends'

export interface GenerateUsernameOptions {
  length?: number
  lang?: string
  type?: UsernameType
  allowNumber?: boolean
  allowSymbol?: boolean
}

const ROOT_DIR = path.join(path.dirname(new URL(import.meta.url).pathname), '..')

/**
 * 尝试加载 data 目录中的 json 数据
 */
async function loadJsonSafe(p: string): Promise<any | null> {
  try {
    const data = await fs.readFile(p, 'utf8')
    return JSON.parse(data)
  } catch {
    return null
  }
}

function getFlattenWords(obj: Record<string, any>): string[] {
  const output: string[] = []
  for (const v of Object.values(obj)) {
    if (Array.isArray(v)) {
      // zh base 结构 [{word,weight}]
      v.forEach((item: any) => {
        if (typeof item === 'string') output.push(item)
        else if (item?.word) output.push(item.word as string)
      })
    } else if (typeof v === 'object') {
      // deeper
      output.push(...getFlattenWords(v as Record<string, any>))
    }
  }
  return output
}

function randomChoice<T>(arr: T[]): T {
  return arr[Math.floor(Math.random() * arr.length)]
}

// 缓存敏感词列表，避免重复读取
const sensitiveCache: Record<string, Set<string>> = {}

async function loadSensitiveSet(lang: string): Promise<Set<string>> {
  if (sensitiveCache[lang]) return sensitiveCache[lang]
  const filePath = path.join(ROOT_DIR, 'data', 'sensitive', `${lang}.txt`)
  try {
    const txt = await fs.readFile(filePath, 'utf8')
    const set = new Set(txt.split(/\r?\n/).map((s) => s.trim()).filter(Boolean))
    sensitiveCache[lang] = set
    return set
  } catch {
    const empty = new Set<string>()
    sensitiveCache[lang] = empty
    return empty
  }
}

async function isSensitive(username: string, lang: string): Promise<boolean> {
  const banned = await loadSensitiveSet(lang)
  for (const bad of banned) {
    if (!bad) continue
    if (username.includes(bad)) return true
  }
  return false
}

/**
 * 生成用户名主函数（同步包装）
 */
export async function generateUsername({
  length = 8,
  lang = 'en',
  type = 'Random',
  allowNumber = true,
  allowSymbol = false
}: GenerateUsernameOptions = {}): Promise<string> {
  // Basic Latin chars fallback
  const fallbackChars = 'abcdefghijklmnopqrstuvwxyz0123456789'

  // For Random, try adjective + noun for en/zh if available
  if (type === 'Random') {
    const baseDir = path.join(ROOT_DIR, 'data', 'base', lang)
    const adjPath = path.join(baseDir, 'adjectives.json')
    const nounPath = path.join(baseDir, 'nouns.json')

    const adjJson = await loadJsonSafe(adjPath)
    const nounJson = await loadJsonSafe(nounPath)

    if (adjJson && nounJson) {
      const adjs = getFlattenWords(adjJson)
      const nouns = getFlattenWords(nounJson)
      if (adjs.length && nouns.length) {
        let username = randomChoice(adjs) + randomChoice(nouns)
        // pad with random digits/letters until满足长度
        const padChars = allowNumber ? '0123456789abcdefghijklmnopqrstuvwxyz' : 'abcdefghijklmnopqrstuvwxyz'
        while (username.length < length) {
          username += randomChoice(padChars.split(''))
        }
        username = username.slice(0, length)
        if (!(await isSensitive(username, lang))) return username
        // fallback if sensitive
      }
    }
  }

  // fallback: random characters
  let chars = fallbackChars
  if (!allowNumber) chars = chars.replace(/[0-9]/g, '')
  if (!allowSymbol) {
    // only basic letters+digits already
  }
  let result = ''
  for (let i = 0; i < length; i++) result += randomChoice(chars.split(''))
  if (await isSensitive(result, lang)) {
    // simple strategy: append random digit to bypass sensitive
    result = result + Math.floor(Math.random() * 10).toString()
  }
  return result
}
