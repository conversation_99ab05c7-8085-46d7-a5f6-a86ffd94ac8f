<script setup lang="ts">
// 自定义布局 - 包含导航栏和页脚
defineOptions({
  name: 'CustomLayout'
});

// 使用颜色模式
const colorMode = useColorMode();
const isDark = computed(() => colorMode.value === 'dark');

// 切换暗黑模式
const toggleDarkMode = () => {
  colorMode.preference = isDark.value ? 'light' : 'dark';
};
</script>

<template>
  <div class="min-h-screen flex flex-col bg-gray-50 dark:bg-gray-900">
    <!-- 导航栏 -->
    <header class="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
      <div class="container mx-auto px-4 py-3 flex justify-between items-center">
        <NuxtLink to="/" class="text-xl font-bold text-primary-600 dark:text-primary-400">
          {{ $t('app.title') }}
        </NuxtLink>
        <div class="flex items-center space-x-4">
          <!-- 暗黑模式切换 - 使用ClientOnly确保水合匹配 -->
          <ClientOnly>
            <button @click="toggleDarkMode" class="p-1.5 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700">
              <div v-if="isDark" class="i-heroicons-sun w-5 h-5 text-yellow-400" />
              <div v-else class="i-heroicons-moon w-5 h-5 text-gray-500" />
            </button>
            <template #fallback>
              <!-- 服务端渲染占位 -->
              <button class="p-1.5 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700">
                <div class="w-5 h-5" />
              </button>
            </template>
          </ClientOnly>
          
          <!-- 导航链接 -->
          <nav class="flex space-x-4">
            <NuxtLink to="/" class="text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400">
              {{ $t('app.nav.home') || '首页' }}
            </NuxtLink>
            <NuxtLink to="/about" class="text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400">
              {{ $t('app.nav.about') || '关于' }}
            </NuxtLink>
          </nav>
        </div>
      </div>
    </header>

    <!-- 主要内容 -->
    <main class="flex-grow">
      <slot />
    </main>

    <!-- 页脚 -->
    <footer class="bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700">
      <div class="container mx-auto px-4 py-4">
        <div class="text-center text-sm text-gray-500 dark:text-gray-400">
          <p>{{ $t('app.footer') }}</p>
        </div>
      </div>
    </footer>
  </div>
</template>
