/**
 * 完整的手动AI交互演示模拟
 * 展示从开始到结束的完整流程，包括模拟的AI响应
 */

// 重用之前的ManualAIDemo类
class ManualAIDemo {
  constructor(language = 'zh') {
    this.language = language
    this.iterationHistory = []
    this.currentIteration = 0
    this.currentBatchId = ''
    this.currentSamples = []
    this.currentResults = []
  }

  generateTestSamples(count = 5) {
    const prefixes = ['超级', '究极', '无敌', '神级', '霸道', '传说', '绝世', '王者']
    const cores = ['大神', '高手', '玩家', '战士', '法师', '刺客', '射手', '勇者', '剑客', '忍者']
    const suffixes = ['王', '君', '者', '师', '神', '圣', 'yyds', '绝子', '小可爱', '本尊']

    const samples = []
    for (let i = 0; i < count; i++) {
      const prefix = prefixes[Math.floor(Math.random() * prefixes.length)]
      const core = cores[Math.floor(Math.random() * cores.length)]
      const suffix = suffixes[Math.floor(Math.random() * suffixes.length)]
      
      const templates = [
        `${prefix}${core}${suffix}`,
        `${core}${suffix}`,
        `${prefix}${core}`,
        `${core}`
      ]
      
      const username = templates[Math.floor(Math.random() * templates.length)]
      samples.push(username)
    }

    this.currentSamples = samples
    return samples
  }

  startIteration(samples) {
    this.currentIteration++
    this.currentBatchId = `batch_${this.currentIteration}_${Date.now()}`
    this.currentSamples = samples || this.currentSamples
    this.currentResults = []
    
    console.log(`\n🚀 开始第 ${this.currentIteration} 轮迭代`)
    console.log(`📊 批次ID: ${this.currentBatchId}`)
    console.log(`🎯 样本数量: ${this.currentSamples.length}`)
    console.log(`📝 样本列表: ${this.currentSamples.join(', ')}`)
    
    return this.currentBatchId
  }

  processAIResponse(username, aiResponse) {
    console.log(`\n📝 处理用户名: ${username}`)
    
    const result = {
      username,
      ai_response: aiResponse,
      timestamp: new Date().toISOString(),
      parsed_metrics: null
    }

    try {
      const jsonMatch = aiResponse.match(/```json\s*([\s\S]*?)\s*```/) || 
                       aiResponse.match(/\{[\s\S]*\}/)
      
      if (jsonMatch) {
        const jsonStr = jsonMatch[1] || jsonMatch[0]
        const parsed = JSON.parse(jsonStr)
        
        result.parsed_metrics = {
          creativity: this.validateScore(parsed.creativity),
          unexpectedness: this.validateScore(parsed.unexpectedness),
          coherence: this.validateScore(parsed.coherence),
          cultural_resonance: this.validateScore(parsed.cultural_resonance),
          reasoning: parsed.reasoning || '未提供分析理由'
        }

        const m = result.parsed_metrics
        console.log(`✅ 解析成功!`)
        console.log(`📊 评分: 创意${m.creativity.toFixed(2)} | 意外${m.unexpectedness.toFixed(2)} | 连贯${m.coherence.toFixed(2)} | 共鸣${m.cultural_resonance.toFixed(2)}`)
        console.log(`💡 分析: ${m.reasoning}`)
      } else {
        console.log(`⚠️ 未找到JSON格式，请检查AI响应`)
      }
    } catch (error) {
      console.log(`❌ 解析失败: ${error.message}`)
    }

    this.currentResults.push(result)
    
    const remaining = this.currentSamples.length - this.currentResults.length
    if (remaining > 0) {
      console.log(`\n⏳ 还需要分析 ${remaining} 个用户名`)
    } else {
      console.log(`\n🎉 所有用户名分析完成！`)
    }

    return result
  }

  validateScore(score) {
    const num = parseFloat(score)
    if (isNaN(num)) return 0.5
    return Math.max(0, Math.min(1, num))
  }

  completeIteration() {
    const validResults = this.currentResults.filter(r => r.parsed_metrics)
    
    if (validResults.length === 0) {
      console.error('❌ 没有有效的分析结果')
      return null
    }

    const avgScores = {
      creativity: 0,
      unexpectedness: 0,
      coherence: 0,
      cultural_resonance: 0,
      overall: 0
    }

    validResults.forEach(result => {
      const m = result.parsed_metrics
      avgScores.creativity += m.creativity
      avgScores.unexpectedness += m.unexpectedness
      avgScores.coherence += m.coherence
      avgScores.cultural_resonance += m.cultural_resonance
    })

    Object.keys(avgScores).forEach(key => {
      if (key !== 'overall') {
        avgScores[key] /= validResults.length
      }
    })

    avgScores.overall = (avgScores.creativity * 0.25 + 
                        avgScores.unexpectedness * 0.25 + 
                        avgScores.coherence * 0.25 + 
                        avgScores.cultural_resonance * 0.25)

    const scoredResults = validResults.map(r => ({
      username: r.username,
      score: (r.parsed_metrics.creativity + r.parsed_metrics.unexpectedness + 
              r.parsed_metrics.coherence + r.parsed_metrics.cultural_resonance) / 4
    })).sort((a, b) => b.score - a.score)

    const bestPerformers = scoredResults.slice(0, Math.ceil(scoredResults.length * 0.4)).map(r => r.username)
    const worstPerformers = scoredResults.slice(-Math.ceil(scoredResults.length * 0.4)).map(r => r.username)

    const report = {
      iteration: this.currentIteration,
      batch_id: this.currentBatchId,
      samples: this.currentSamples,
      valid_results: validResults.length,
      total_results: this.currentResults.length,
      avg_scores: avgScores,
      best_performers: bestPerformers,
      worst_performers: worstPerformers,
      timestamp: new Date().toISOString()
    }

    this.iterationHistory.push(report)
    this.displayReport(report)
    
    return report
  }

  displayReport(report) {
    console.log(`\n📊 第 ${report.iteration} 轮迭代报告`)
    console.log('=' .repeat(60))
    
    console.log(`\n📈 平均评分:`)
    console.log(`  🎨 创意性: ${report.avg_scores.creativity.toFixed(3)}`)
    console.log(`  🎲 意外性: ${report.avg_scores.unexpectedness.toFixed(3)}`)
    console.log(`  🔗 连贯性: ${report.avg_scores.coherence.toFixed(3)}`)
    console.log(`  🌍 文化共鸣: ${report.avg_scores.cultural_resonance.toFixed(3)}`)
    console.log(`  ⭐ 综合评分: ${report.avg_scores.overall.toFixed(3)}`)
    
    console.log(`\n🏆 最佳表现: ${report.best_performers.join(', ')}`)
    console.log(`⚠️ 待改进: ${report.worst_performers.join(', ')}`)
    
    const insights = this.generateInsights(report.avg_scores)
    console.log(`\n💡 关键洞察:`)
    insights.forEach(insight => console.log(`  • ${insight}`))
    
    const suggestions = this.generateSuggestions(report.avg_scores)
    console.log(`\n🔧 优化建议:`)
    suggestions.forEach(suggestion => console.log(`  • ${suggestion}`))
  }

  generateInsights(avgScores) {
    const insights = []
    const dimensions = ['creativity', 'unexpectedness', 'coherence', 'cultural_resonance']
    const sortedDims = dimensions.sort((a, b) => avgScores[b] - avgScores[a])
    
    insights.push(`🏆 最强维度: ${this.getDimensionName(sortedDims[0])} (${avgScores[sortedDims[0]].toFixed(3)})`)
    insights.push(`⚠️ 最弱维度: ${this.getDimensionName(sortedDims[3])} (${avgScores[sortedDims[3]].toFixed(3)})`)
    
    if (avgScores.overall >= 0.7) {
      insights.push(`✨ 整体表现优秀，已达到较高水准`)
    } else if (avgScores.overall >= 0.5) {
      insights.push(`📈 整体表现中等，有明显提升空间`)
    } else {
      insights.push(`🔧 整体表现需要大幅改进`)
    }
    
    return insights
  }

  generateSuggestions(avgScores) {
    const suggestions = []
    
    if (avgScores.creativity < 0.6) {
      suggestions.push(`💡 提升创意性: 尝试更多非常规的词汇组合，引入新颖的概念`)
    }
    
    if (avgScores.unexpectedness < 0.6) {
      suggestions.push(`🎲 增强意外性: 避免过于常见的模式，加入令人惊喜的元素`)
    }
    
    if (avgScores.coherence < 0.6) {
      suggestions.push(`🔗 改善连贯性: 确保各部分语义逻辑合理，整体表达流畅`)
    }
    
    if (avgScores.cultural_resonance < 0.6) {
      suggestions.push(`🌍 强化文化共鸣: 更好地融入目标文化特色和时代元素`)
    }
    
    return suggestions
  }

  getDimensionName(dimension) {
    const names = {
      'creativity': '创意性',
      'unexpectedness': '意外性', 
      'coherence': '连贯性',
      'cultural_resonance': '文化共鸣'
    }
    return names[dimension] || dimension
  }

  showVisualizationReport() {
    if (this.iterationHistory.length === 0) {
      console.log('暂无迭代数据')
      return
    }

    console.log(`\n📊 用户名有趣性优化 - 可视化报告`)
    console.log('=' .repeat(60))
    
    console.log(`\n📈 迭代趋势分析:`)
    this.iterationHistory.forEach((iter, index) => {
      const prev = index > 0 ? this.iterationHistory[index - 1] : null
      const improvement = prev ? iter.avg_scores.overall - prev.avg_scores.overall : 0
      const trend = improvement > 0.01 ? '📈' : improvement < -0.01 ? '📉' : '➡️'
      
      let line = `  ${trend} 第${iter.iteration}轮: ${iter.avg_scores.overall.toFixed(3)}`
      if (prev) line += ` (${improvement >= 0 ? '+' : ''}${improvement.toFixed(3)})`
      console.log(line)
    })
    
    const latest = this.iterationHistory[this.iterationHistory.length - 1]
    console.log(`\n🎯 最新迭代详情 (第${latest.iteration}轮):`)
    console.log(`  📊 综合评分: ${latest.avg_scores.overall.toFixed(3)}`)
    console.log(`  🏆 最佳表现: ${latest.best_performers.slice(0, 3).join(', ')}`)
    console.log(`  ⚠️ 待改进: ${latest.worst_performers.slice(0, 3).join(', ')}`)
    
    console.log(`\n📋 维度分析:`)
    const dims = ['creativity', 'unexpectedness', 'coherence', 'cultural_resonance']
    dims.forEach(dim => {
      const score = latest.avg_scores[dim]
      const bar = '█'.repeat(Math.round(score * 20))
      const empty = '░'.repeat(20 - Math.round(score * 20))
      console.log(`  ${this.getDimensionName(dim)}: ${bar}${empty} ${score.toFixed(3)}`)
    })
  }
}

// 模拟AI响应数据
const mockAIResponses = {
  "王者剑客绝子": `\`\`\`json
{
  "creativity": 0.75,
  "unexpectedness": 0.68,
  "coherence": 0.82,
  "cultural_resonance": 0.79,
  "reasoning": "该用户名结合了传统武侠元素'王者剑客'与现代网络流行语'绝子'，创意性较强。意外性体现在传统与现代的反差组合。连贯性良好，各部分语义统一。文化共鸣度高，既有传统文化底蕴又贴合年轻群体表达习惯。建议：可进一步优化音韵搭配。"
}
\`\`\``,

  "法师君": `\`\`\`json
{
  "creativity": 0.45,
  "unexpectedness": 0.38,
  "coherence": 0.78,
  "cultural_resonance": 0.65,
  "reasoning": "该用户名采用了常见的'职业+敬称'模式，创意性和意外性相对较低。连贯性较好，语义清晰。文化共鸣中等，'君'字体现了一定的文化底蕴，但整体较为平庸。建议：增加更多个性化元素或流行文化元素。"
}
\`\`\``,

  "神级法师君": `\`\`\`json
{
  "creativity": 0.58,
  "unexpectedness": 0.52,
  "coherence": 0.85,
  "cultural_resonance": 0.72,
  "reasoning": "该用户名在'法师君'基础上增加了'神级'前缀，提升了层次感。创意性中等，意外性有所改善。连贯性很好，各部分搭配合理。文化共鸣较好，符合游戏文化表达习惯。建议：可尝试更具个性的修饰词替代'神级'。"
}
\`\`\``,

  "究极玩家yyds": `\`\`\`json
{
  "creativity": 0.82,
  "unexpectedness": 0.89,
  "coherence": 0.76,
  "cultural_resonance": 0.91,
  "reasoning": "该用户名巧妙融合了日式表达'究极'、通用词汇'玩家'和网络流行语'yyds'，创意性和意外性都很高。虽然跨文化元素较多，但整体连贯性尚可。文化共鸣极强，完美契合年轻网民的表达习惯。这是一个非常成功的现代网络用户名案例。"
}
\`\`\``,

  "传说勇者": `\`\`\`json
{
  "creativity": 0.62,
  "unexpectedness": 0.55,
  "coherence": 0.88,
  "cultural_resonance": 0.74,
  "reasoning": "该用户名采用了经典的RPG游戏命名模式，具有一定的创意性。意外性中等，在游戏文化中较为常见。连贯性很好，语义清晰有力。文化共鸣良好，符合游戏玩家的审美偏好。建议：可考虑加入更多个性化元素以提升独特性。"
}
\`\`\``
}

// 完整演示流程
async function runCompleteSimulation() {
  console.log(`🎬 完整手动AI交互演示 - 模拟版`)
  console.log('=' .repeat(60))
  console.log(`📝 本演示将模拟完整的手动AI交互流程`)
  console.log(`🤖 使用预设的AI响应数据进行演示`)
  console.log(`💡 实际使用时，你需要手动复制prompt给真实的AI模型`)

  const demo = new ManualAIDemo('zh')

  // 第1轮迭代
  console.log(`\n🚀 === 第1轮迭代演示 ===`)
  const samples1 = ['王者剑客绝子', '法师君', '神级法师君']
  demo.startIteration(samples1)

  console.log(`\n📋 模拟AI分析过程...`)
  for (const username of samples1) {
    if (mockAIResponses[username]) {
      demo.processAIResponse(username, mockAIResponses[username])
      await new Promise(resolve => setTimeout(resolve, 500)) // 模拟处理延迟
    }
  }

  console.log(`\n📊 完成第1轮迭代...`)
  demo.completeIteration()

  // 第2轮迭代
  console.log(`\n\n🚀 === 第2轮迭代演示 ===`)
  const samples2 = ['究极玩家yyds', '传说勇者']
  demo.startIteration(samples2)

  console.log(`\n📋 模拟AI分析过程...`)
  for (const username of samples2) {
    if (mockAIResponses[username]) {
      demo.processAIResponse(username, mockAIResponses[username])
      await new Promise(resolve => setTimeout(resolve, 500))
    }
  }

  console.log(`\n📊 完成第2轮迭代...`)
  demo.completeIteration()

  // 显示最终报告
  console.log(`\n\n📊 === 最终可视化报告 ===`)
  demo.showVisualizationReport()

  console.log(`\n\n🎉 演示完成！`)
  console.log(`📋 关键观察点:`)
  console.log(`  • 第1轮平均分较低，识别出改进空间`)
  console.log(`  • 第2轮引入高质量样本，整体评分提升`)
  console.log(`  • 系统成功识别出最佳和最差表现者`)
  console.log(`  • 提供了具体的优化建议`)
  console.log(`  • 可视化报告清晰展示了优化趋势`)

  console.log(`\n💡 实际使用提示:`)
  console.log(`  1. 复制系统生成的prompt发送给真实AI模型`)
  console.log(`  2. 将AI的JSON响应粘贴回来处理`)
  console.log(`  3. 观察评分变化，根据建议调整策略`)
  console.log(`  4. 多轮迭代，持续优化用户名质量`)

  return demo
}

// 运行完整演示
runCompleteSimulation().catch(console.error)
