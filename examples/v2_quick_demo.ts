/**
 * V2系统快速演示
 * 
 * 展示V2系统的基础功能和核心特性
 */

import { quickGenerate, createUsernameAPI } from '../core/v2/architecture/MainAPI'
import { CulturalStyle, SemanticDomain } from '../core/v2/types'

async function runQuickDemo() {
  console.log('🚀 V2多语言用户名生成系统演示')
  console.log('=' .repeat(50))
  
  try {
    // 1. 快速生成演示
    console.log('\n📝 1. 快速生成功能')
    console.log('-' .repeat(30))
    
    const quickNames = await quickGenerate('zh', 5, CulturalStyle.MODERN)
    console.log('现代风格中文用户名:')
    quickNames.forEach((name, index) => {
      console.log(`  ${index + 1}. ${name}`)
    })
    
    // 2. 高级生成演示
    console.log('\n🎨 2. 高级生成功能')
    console.log('-' .repeat(30))
    
    const api = await createUsernameAPI()
    
    const response = await api.generateUsernames({
      language: 'zh',
      style: CulturalStyle.ELEGANT,
      themes: [SemanticDomain.NATURE, SemanticDomain.EMOTION],
      sentiment: 'positive',
      creativity: 'high',
      count: 3,
      debug: true
    })
    
    if (response.success && response.data) {
      console.log('优雅风格 + 自然情感主题:')
      response.data.usernames.forEach((result, index) => {
        console.log(`  ${index + 1}. ${result.username}`)
        console.log(`     质量评分: ${result.quality_score.toFixed(3)}`)
        console.log(`     文化适配: ${result.cultural_fitness.toFixed(3)}`)
        console.log(`     使用模式: ${result.pattern_used}`)
        console.log(`     组成部分: ${result.components.map(c => c.word).join(' + ')}`)
        if (result.explanation) {
          console.log(`     生成解释: ${result.explanation}`)
        }
        console.log()
      })
      
      console.log(`生成信息:`)
      console.log(`  耗时: ${response.data.generation_info.total_time_ms}ms`)
      console.log(`  策略: ${response.data.generation_info.strategy_used}`)
      console.log(`  缓存命中: ${response.data.generation_info.cache_hit ? '是' : '否'}`)
    }
    
    // 3. 不同风格对比
    console.log('\n🎭 3. 不同风格对比')
    console.log('-' .repeat(30))
    
    const styles = [
      { style: CulturalStyle.TRADITIONAL, name: '传统' },
      { style: CulturalStyle.MODERN, name: '现代' },
      { style: CulturalStyle.CUTE, name: '可爱' },
      { style: CulturalStyle.ELEGANT, name: '优雅' }
    ]
    
    for (const { style, name } of styles) {
      const styleNames = await quickGenerate('zh', 2, style)
      console.log(`${name}风格: ${styleNames.join(', ')}`)
    }
    
    // 4. 主题生成演示
    console.log('\n🌟 4. 主题生成演示')
    console.log('-' .repeat(30))
    
    const themes = [
      { theme: [SemanticDomain.NATURE], name: '自然' },
      { theme: [SemanticDomain.EMOTION], name: '情感' },
      { theme: [SemanticDomain.QUALITY], name: '品质' },
      { theme: [SemanticDomain.NATURE, SemanticDomain.EMOTION], name: '自然+情感' }
    ]
    
    for (const { theme, name } of themes) {
      const themeResponse = await api.generateUsernames({
        language: 'zh',
        themes: theme,
        count: 2
      })
      
      if (themeResponse.success && themeResponse.data) {
        const names = themeResponse.data.usernames.map(u => u.username)
        console.log(`${name}主题: ${names.join(', ')}`)
      }
    }
    
    // 5. 长度控制演示
    console.log('\n📏 5. 长度控制演示')
    console.log('-' .repeat(30))
    
    const shortResponse = await api.generateUsernames({
      language: 'zh',
      min_length: 1,
      max_length: 2,
      count: 3
    })
    
    const longResponse = await api.generateUsernames({
      language: 'zh',
      min_length: 4,
      max_length: 6,
      count: 3
    })
    
    if (shortResponse.success && shortResponse.data) {
      const shortNames = shortResponse.data.usernames.map(u => u.username)
      console.log(`短用户名 (1-2字): ${shortNames.join(', ')}`)
    }
    
    if (longResponse.success && longResponse.data) {
      const longNames = longResponse.data.usernames.map(u => u.username)
      console.log(`长用户名 (4-6字): ${longNames.join(', ')}`)
    }
    
    // 6. 情感倾向演示
    console.log('\n😊 6. 情感倾向演示')
    console.log('-' .repeat(30))
    
    const positiveResponse = await api.generateUsernames({
      language: 'zh',
      sentiment: 'positive',
      count: 3
    })
    
    const neutralResponse = await api.generateUsernames({
      language: 'zh',
      sentiment: 'neutral',
      count: 3
    })
    
    if (positiveResponse.success && positiveResponse.data) {
      const positiveNames = positiveResponse.data.usernames.map(u => u.username)
      console.log(`积极情感: ${positiveNames.join(', ')}`)
    }
    
    if (neutralResponse.success && neutralResponse.data) {
      const neutralNames = neutralResponse.data.usernames.map(u => u.username)
      console.log(`中性情感: ${neutralNames.join(', ')}`)
    }
    
    // 7. 系统状态演示
    console.log('\n📊 7. 系统状态')
    console.log('-' .repeat(30))
    
    const status = await api.getSystemStatus()
    console.log(`系统状态: ${status.status}`)
    console.log(`运行时间: ${Math.round(status.uptime / 1000)}秒`)
    console.log(`总生成次数: ${status.performance.total_generations}`)
    console.log(`成功率: ${(status.performance.success_rate * 100).toFixed(1)}%`)
    console.log(`平均响应时间: ${status.performance.avg_response_time.toFixed(0)}ms`)
    console.log(`缓存命中率: ${(status.performance.cache_hit_rate * 100).toFixed(1)}%`)
    console.log(`支持语言: ${status.languages.supported.join(', ')}`)
    console.log(`已加载语言: ${status.languages.loaded.join(', ')}`)
    
    if (status.recommendations.length > 0) {
      console.log('系统建议:')
      status.recommendations.forEach(rec => {
        console.log(`  - ${rec}`)
      })
    }
    
    // 8. 性能测试
    console.log('\n⚡ 8. 性能测试')
    console.log('-' .repeat(30))
    
    const startTime = Date.now()
    const batchPromises = []
    
    for (let i = 0; i < 5; i++) {
      batchPromises.push(
        api.generateUsernames({
          language: 'zh',
          count: 2
        })
      )
    }
    
    const batchResults = await Promise.all(batchPromises)
    const endTime = Date.now()
    
    const successCount = batchResults.filter(r => r.success).length
    const totalGenerated = batchResults.reduce((sum, r) => 
      sum + (r.data?.usernames.length || 0), 0
    )
    
    console.log(`并发生成测试:`)
    console.log(`  5个并发请求，总耗时: ${endTime - startTime}ms`)
    console.log(`  成功请求: ${successCount}/5`)
    console.log(`  总生成数量: ${totalGenerated}个用户名`)
    console.log(`  平均每个用户名: ${((endTime - startTime) / totalGenerated).toFixed(1)}ms`)
    
    console.log('\n🎉 演示完成!')
    console.log('\nV2系统核心特性:')
    console.log('✨ 基于语言学第一性原理的智能生成')
    console.log('🌍 深度文化适配和多维语义理解')
    console.log('🎯 精确的质量控制和多样化生成')
    console.log('⚡ 高性能算法和智能缓存机制')
    console.log('🔧 灵活的参数配置和扩展能力')
    
  } catch (error) {
    console.error('❌ 演示过程中出现错误:', error)
  }
}

// 如果直接运行此文件，则执行演示
if (require.main === module) {
  runQuickDemo().catch(console.error)
}

export { runQuickDemo }
