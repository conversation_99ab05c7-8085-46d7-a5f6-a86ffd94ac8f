/**
 * 手动AI交互演示
 * 使用CommonJS避免模块问题，提供完整的操作演示
 */

// 模拟的手动AI交互系统
class ManualAIDemo {
  constructor(language = 'zh') {
    this.language = language
    this.iterationHistory = []
    this.currentIteration = 0
    this.currentBatchId = ''
    this.currentSamples = []
    this.currentResults = []
  }

  /**
   * 生成标准化的AI分析prompt
   */
  generateAnalysisPrompt(username, language = 'zh') {
    const languageContext = this.getLanguageContext(language)
    
    return `# 用户名有趣性专业分析

## 分析目标
请作为用户名有趣性专家，对用户名"${username}"进行深度分析。

## 语言文化背景
${languageContext}

## 评估维度说明
请从以下四个核心维度进行评分（0.0-1.0分，保留2位小数）：

### 1. 创意性 (creativity)
- 词汇组合的新颖程度和原创性
- 是否突破了常规的命名模式
- 是否展现了独特的想象力

### 2. 意外性 (unexpectedness) 
- 超出用户常规预期的程度
- 是否包含令人惊喜的元素
- 是否避免了过于平庸的表达

### 3. 连贯性 (coherence)
- 语义逻辑的合理性和流畅度
- 各组成部分是否和谐统一
- 整体表达是否自然流畅

### 4. 文化共鸣 (cultural_resonance)
- 与目标文化群体的共鸣程度
- 是否体现了文化特色和时代特征
- 是否符合该语言的表达习惯

## 输出格式要求
请严格按照以下JSON格式输出（不要添加任何其他文字）：

\`\`\`json
{
  "creativity": 0.00,
  "unexpectedness": 0.00,
  "coherence": 0.00,
  "cultural_resonance": 0.00,
  "reasoning": "详细说明各维度评分的理由，包括优势、不足和改进建议，字数控制在200字以内"
}
\`\`\`

## 注意事项
- 评分要客观公正，避免极端分数
- 推理要具体明确，避免空泛表述
- 考虑目标用户群体的接受度
- 关注文化适宜性和时代感`
  }

  getLanguageContext(language) {
    const contexts = {
      'zh': `
**中文用户名文化特征：**
- 注重音韵和谐，讲究平仄搭配
- 偏爱寓意深刻的词汇组合
- 网络文化影响显著（如"yyds"、"绝绝子"等流行语）
- 传统文化元素与现代表达的融合
- 游戏、动漫、影视作品的文化影响
- 年轻群体喜欢个性化、有态度的表达`,
      
      'en': `
**English Username Cultural Features:**
- Emphasis on brevity and memorability
- Pop culture references and wordplay
- Gaming and internet culture influence
- Balance between creativity and professionalism
- Preference for unique but pronounceable combinations
- Modern slang and trending expressions`
    }
    
    return contexts[language] || contexts['zh']
  }

  /**
   * 生成测试样本
   */
  generateTestSamples(count = 5) {
    const prefixes = ['超级', '究极', '无敌', '神级', '霸道', '传说', '绝世', '王者']
    const cores = ['大神', '高手', '玩家', '战士', '法师', '刺客', '射手', '勇者', '剑客', '忍者']
    const suffixes = ['王', '君', '者', '师', '神', '圣', 'yyds', '绝子', '小可爱', '本尊']

    const samples = []
    for (let i = 0; i < count; i++) {
      const prefix = prefixes[Math.floor(Math.random() * prefixes.length)]
      const core = cores[Math.floor(Math.random() * cores.length)]
      const suffix = suffixes[Math.floor(Math.random() * suffixes.length)]
      
      const templates = [
        `${prefix}${core}${suffix}`,
        `${core}${suffix}`,
        `${prefix}${core}`,
        `${core}`
      ]
      
      const username = templates[Math.floor(Math.random() * templates.length)]
      samples.push(username)
    }

    this.currentSamples = samples
    return samples
  }

  /**
   * 开始新迭代
   */
  startIteration(samples) {
    this.currentIteration++
    this.currentBatchId = `batch_${this.currentIteration}_${Date.now()}`
    this.currentSamples = samples || this.currentSamples
    this.currentResults = []
    
    console.log(`\n🚀 开始第 ${this.currentIteration} 轮迭代`)
    console.log(`📊 批次ID: ${this.currentBatchId}`)
    console.log(`🎯 样本数量: ${this.currentSamples.length}`)
    console.log(`📝 样本列表: ${this.currentSamples.join(', ')}`)
    
    return this.currentBatchId
  }

  /**
   * 显示分析prompt
   */
  showAnalysisPrompts() {
    console.log(`\n📋 请按顺序分析每个用户名:`)
    console.log('=' .repeat(60))
    
    this.currentSamples.forEach((username, index) => {
      console.log(`\n${index + 1}. 📝 用户名: "${username}"`)
      console.log(`${'─'.repeat(50)}`)
      console.log(`🤖 请复制以下prompt发送给AI模型:`)
      console.log(`${'─'.repeat(50)}`)
      
      const prompt = this.generateAnalysisPrompt(username)
      console.log(prompt)
      
      console.log(`${'─'.repeat(50)}`)
      console.log(`⏳ 等待AI响应后，请调用:`)
      console.log(`   demo.processAIResponse("${username}", "AI的完整响应")`)
      console.log(`${'─'.repeat(50)}`)
    })
  }

  /**
   * 处理AI响应
   */
  processAIResponse(username, aiResponse) {
    console.log(`\n📝 处理用户名: ${username}`)
    
    const result = {
      username,
      ai_response: aiResponse,
      timestamp: new Date().toISOString(),
      parsed_metrics: null
    }

    try {
      // 尝试提取JSON部分
      const jsonMatch = aiResponse.match(/```json\s*([\s\S]*?)\s*```/) || 
                       aiResponse.match(/\{[\s\S]*\}/)
      
      if (jsonMatch) {
        const jsonStr = jsonMatch[1] || jsonMatch[0]
        const parsed = JSON.parse(jsonStr)
        
        result.parsed_metrics = {
          creativity: this.validateScore(parsed.creativity),
          unexpectedness: this.validateScore(parsed.unexpectedness),
          coherence: this.validateScore(parsed.coherence),
          cultural_resonance: this.validateScore(parsed.cultural_resonance),
          reasoning: parsed.reasoning || '未提供分析理由'
        }

        const m = result.parsed_metrics
        console.log(`✅ 解析成功!`)
        console.log(`📊 评分: 创意${m.creativity.toFixed(2)} | 意外${m.unexpectedness.toFixed(2)} | 连贯${m.coherence.toFixed(2)} | 共鸣${m.cultural_resonance.toFixed(2)}`)
        console.log(`💡 分析: ${m.reasoning}`)
      } else {
        console.log(`⚠️ 未找到JSON格式，请检查AI响应`)
      }
    } catch (error) {
      console.log(`❌ 解析失败: ${error.message}`)
      console.log(`📄 原始响应: ${aiResponse.substring(0, 200)}...`)
    }

    this.currentResults.push(result)
    
    const remaining = this.currentSamples.length - this.currentResults.length
    if (remaining > 0) {
      console.log(`\n⏳ 还需要分析 ${remaining} 个用户名`)
      const nextUsername = this.currentSamples[this.currentResults.length]
      console.log(`📝 下一个: "${nextUsername}"`)
    } else {
      console.log(`\n🎉 所有用户名分析完成！请调用 demo.completeIteration() 生成报告`)
    }

    return result
  }

  validateScore(score) {
    const num = parseFloat(score)
    if (isNaN(num)) return 0.5
    return Math.max(0, Math.min(1, num))
  }

  /**
   * 完成迭代
   */
  completeIteration() {
    const validResults = this.currentResults.filter(r => r.parsed_metrics)
    
    if (validResults.length === 0) {
      console.error('❌ 没有有效的分析结果')
      return null
    }

    // 计算平均分数
    const avgScores = {
      creativity: 0,
      unexpectedness: 0,
      coherence: 0,
      cultural_resonance: 0,
      overall: 0
    }

    validResults.forEach(result => {
      const m = result.parsed_metrics
      avgScores.creativity += m.creativity
      avgScores.unexpectedness += m.unexpectedness
      avgScores.coherence += m.coherence
      avgScores.cultural_resonance += m.cultural_resonance
    })

    Object.keys(avgScores).forEach(key => {
      if (key !== 'overall') {
        avgScores[key] /= validResults.length
      }
    })

    avgScores.overall = (avgScores.creativity * 0.25 + 
                        avgScores.unexpectedness * 0.25 + 
                        avgScores.coherence * 0.25 + 
                        avgScores.cultural_resonance * 0.25)

    // 识别最佳和最差表现者
    const scoredResults = validResults.map(r => ({
      username: r.username,
      score: (r.parsed_metrics.creativity + r.parsed_metrics.unexpectedness + 
              r.parsed_metrics.coherence + r.parsed_metrics.cultural_resonance) / 4
    })).sort((a, b) => b.score - a.score)

    const bestPerformers = scoredResults.slice(0, Math.ceil(scoredResults.length * 0.4)).map(r => r.username)
    const worstPerformers = scoredResults.slice(-Math.ceil(scoredResults.length * 0.4)).map(r => r.username)

    const report = {
      iteration: this.currentIteration,
      batch_id: this.currentBatchId,
      samples: this.currentSamples,
      valid_results: validResults.length,
      total_results: this.currentResults.length,
      avg_scores: avgScores,
      best_performers: bestPerformers,
      worst_performers: worstPerformers,
      timestamp: new Date().toISOString()
    }

    this.iterationHistory.push(report)
    this.displayReport(report)
    
    return report
  }

  /**
   * 显示报告
   */
  displayReport(report) {
    console.log(`\n📊 第 ${report.iteration} 轮迭代报告`)
    console.log('=' .repeat(60))
    
    console.log(`\n📈 平均评分:`)
    console.log(`  🎨 创意性: ${report.avg_scores.creativity.toFixed(3)}`)
    console.log(`  🎲 意外性: ${report.avg_scores.unexpectedness.toFixed(3)}`)
    console.log(`  🔗 连贯性: ${report.avg_scores.coherence.toFixed(3)}`)
    console.log(`  🌍 文化共鸣: ${report.avg_scores.cultural_resonance.toFixed(3)}`)
    console.log(`  ⭐ 综合评分: ${report.avg_scores.overall.toFixed(3)}`)
    
    console.log(`\n🏆 最佳表现: ${report.best_performers.join(', ')}`)
    console.log(`⚠️ 待改进: ${report.worst_performers.join(', ')}`)
    
    // 生成洞察
    console.log(`\n💡 关键洞察:`)
    const insights = this.generateInsights(report.avg_scores)
    insights.forEach(insight => console.log(`  • ${insight}`))
    
    // 生成建议
    console.log(`\n🔧 优化建议:`)
    const suggestions = this.generateSuggestions(report.avg_scores)
    suggestions.forEach(suggestion => console.log(`  • ${suggestion}`))
  }

  generateInsights(avgScores) {
    const insights = []
    const dimensions = ['creativity', 'unexpectedness', 'coherence', 'cultural_resonance']
    const sortedDims = dimensions.sort((a, b) => avgScores[b] - avgScores[a])
    
    insights.push(`🏆 最强维度: ${this.getDimensionName(sortedDims[0])} (${avgScores[sortedDims[0]].toFixed(3)})`)
    insights.push(`⚠️ 最弱维度: ${this.getDimensionName(sortedDims[3])} (${avgScores[sortedDims[3]].toFixed(3)})`)
    
    if (avgScores.overall >= 0.7) {
      insights.push(`✨ 整体表现优秀，已达到较高水准`)
    } else if (avgScores.overall >= 0.5) {
      insights.push(`📈 整体表现中等，有明显提升空间`)
    } else {
      insights.push(`🔧 整体表现需要大幅改进`)
    }
    
    return insights
  }

  generateSuggestions(avgScores) {
    const suggestions = []
    
    if (avgScores.creativity < 0.6) {
      suggestions.push(`💡 提升创意性: 尝试更多非常规的词汇组合，引入新颖的概念`)
    }
    
    if (avgScores.unexpectedness < 0.6) {
      suggestions.push(`🎲 增强意外性: 避免过于常见的模式，加入令人惊喜的元素`)
    }
    
    if (avgScores.coherence < 0.6) {
      suggestions.push(`🔗 改善连贯性: 确保各部分语义逻辑合理，整体表达流畅`)
    }
    
    if (avgScores.cultural_resonance < 0.6) {
      suggestions.push(`🌍 强化文化共鸣: 更好地融入目标文化特色和时代元素`)
    }
    
    return suggestions
  }

  getDimensionName(dimension) {
    const names = {
      'creativity': '创意性',
      'unexpectedness': '意外性', 
      'coherence': '连贯性',
      'cultural_resonance': '文化共鸣'
    }
    return names[dimension] || dimension
  }

  /**
   * 显示可视化报告
   */
  showVisualizationReport() {
    if (this.iterationHistory.length === 0) {
      console.log('暂无迭代数据')
      return
    }

    console.log(`\n📊 用户名有趣性优化 - 可视化报告`)
    console.log('=' .repeat(60))
    
    // 迭代趋势
    console.log(`\n📈 迭代趋势分析:`)
    this.iterationHistory.forEach((iter, index) => {
      const prev = index > 0 ? this.iterationHistory[index - 1] : null
      const improvement = prev ? iter.avg_scores.overall - prev.avg_scores.overall : 0
      const trend = improvement > 0.01 ? '📈' : improvement < -0.01 ? '📉' : '➡️'
      
      let line = `  ${trend} 第${iter.iteration}轮: ${iter.avg_scores.overall.toFixed(3)}`
      if (prev) line += ` (${improvement >= 0 ? '+' : ''}${improvement.toFixed(3)})`
      console.log(line)
    })
    
    // 最新迭代详情
    const latest = this.iterationHistory[this.iterationHistory.length - 1]
    console.log(`\n🎯 最新迭代详情 (第${latest.iteration}轮):`)
    console.log(`  📊 综合评分: ${latest.avg_scores.overall.toFixed(3)}`)
    console.log(`  🏆 最佳表现: ${latest.best_performers.slice(0, 3).join(', ')}`)
    console.log(`  ⚠️ 待改进: ${latest.worst_performers.slice(0, 3).join(', ')}`)
    
    // 维度分析
    console.log(`\n📋 维度分析:`)
    const dims = ['creativity', 'unexpectedness', 'coherence', 'cultural_resonance']
    dims.forEach(dim => {
      const score = latest.avg_scores[dim]
      const bar = '█'.repeat(Math.round(score * 20))
      const empty = '░'.repeat(20 - Math.round(score * 20))
      console.log(`  ${this.getDimensionName(dim)}: ${bar}${empty} ${score.toFixed(3)}`)
    })
  }

  /**
   * 获取状态
   */
  getStatus() {
    return {
      current_iteration: this.currentIteration,
      current_batch_id: this.currentBatchId,
      samples_count: this.currentSamples.length,
      completed_analysis: this.currentResults.length,
      remaining: this.currentSamples.length - this.currentResults.length,
      total_iterations: this.iterationHistory.length
    }
  }
}

// 演示完整流程
async function demonstrateManualAIWorkflow() {
  console.log(`🚀 手动AI交互测试 - 完整演示`)
  console.log('=' .repeat(60))

  const demo = new ManualAIDemo('zh')

  console.log(`\n📋 操作流程说明:`)
  console.log(`1. 生成测试样本`)
  console.log(`2. 开始迭代并显示分析prompt`)
  console.log(`3. 手动将prompt发送给AI模型`)
  console.log(`4. 将AI响应粘贴回来处理`)
  console.log(`5. 完成迭代并查看报告`)

  // 步骤1: 生成样本
  console.log(`\n🎯 步骤1: 生成测试样本`)
  const samples = demo.generateTestSamples(3)
  console.log(`✅ 生成样本: ${samples.join(', ')}`)

  // 步骤2: 开始迭代
  console.log(`\n🚀 步骤2: 开始迭代`)
  demo.startIteration(samples)

  // 步骤3: 显示分析prompt
  console.log(`\n📋 步骤3: 显示分析prompt`)
  demo.showAnalysisPrompts()

  console.log(`\n🔧 接下来的操作:`)
  console.log(`1. 复制上面的prompt发送给AI模型`)
  console.log(`2. 获得AI响应后，调用:`)
  console.log(`   demo.processAIResponse("用户名", "AI完整响应")`)
  console.log(`3. 重复直到所有用户名分析完成`)
  console.log(`4. 调用 demo.completeIteration() 生成报告`)
  console.log(`5. 调用 demo.showVisualizationReport() 查看可视化报告`)

  // 返回demo实例供后续使用
  return demo
}

// 显示快速开始指南
function showQuickStartGuide() {
  console.log(`
🚀 手动AI交互测试 - 快速开始指南
${'='.repeat(60)}

📋 完整操作流程:

1️⃣ 创建演示实例
   const demo = new ManualAIDemo('zh')

2️⃣ 生成测试样本
   const samples = demo.generateTestSamples(5)

3️⃣ 开始迭代
   demo.startIteration(samples)
   demo.showAnalysisPrompts()

4️⃣ 逐个分析用户名
   对于每个用户名:
   a) 复制系统生成的prompt
   b) 发送给AI模型 (ChatGPT/Claude/文心一言等)
   c) 复制AI的响应
   d) 调用: demo.processAIResponse("用户名", "AI响应")

5️⃣ 完成迭代
   demo.completeIteration()

6️⃣ 查看可视化报告
   demo.showVisualizationReport()

🔄 重复步骤2-6进行多轮迭代优化

💡 提示:
- 建议从3-5个样本开始
- 确保AI响应包含完整的JSON格式
- 观察每轮迭代的评分变化
- 根据优化建议调整生成策略
`)
}

// 直接运行演示
showQuickStartGuide()
demonstrateManualAIWorkflow().then(demo => {
  console.log(`\n✅ 演示准备完成！demo实例已创建，可以开始手动测试。`)
  console.log(`\n🔧 可用的全局变量:`)
  console.log(`   demo - ManualAIDemo实例`)
  console.log(`\n📝 示例操作:`)
  console.log(`   demo.getStatus() - 查看当前状态`)
  console.log(`   demo.processAIResponse("用户名", "AI响应") - 处理AI响应`)
  console.log(`   demo.completeIteration() - 完成当前迭代`)
  console.log(`   demo.showVisualizationReport() - 显示可视化报告`)

  // 将demo实例设为全局变量方便使用
  global.demo = demo
})
