/**
 * 基于第一性原理的用户名生成系统演示
 * 
 * 展示新系统的核心功能和使用方法
 */

import { 
  createUsernameGeneratorAPI, 
  quickGenerateUsername, 
  generateUsernameWithExplanation,
  GenerateUsernameRequest 
} from '../core/v2/api/UsernameGeneratorAPI'

/** 演示基础功能 */
async function demoBasicGeneration() {
  console.log('\n=== 基础用户名生成演示 ===')
  
  try {
    // 快速生成中文用户名
    console.log('\n1. 快速生成中文用户名:')
    const chineseUsernames = await quickGenerateUsername({
      language: 'zh',
      style: 'modern',
      count: 3,
      themes: ['nature', 'technology']
    })
    chineseUsernames.forEach((username, index) => {
      console.log(`   ${index + 1}. ${username}`)
    })
    
    // 生成英文用户名
    console.log('\n2. 生成英文用户名:')
    const englishUsernames = await quickGenerateUsername({
      language: 'en',
      style: 'creative',
      count: 3,
      themes: ['art', 'music']
    })
    englishUsernames.forEach((username, index) => {
      console.log(`   ${index + 1}. ${username}`)
    })
    
  } catch (error) {
    console.error('基础生成演示失败:', error)
  }
}

/** 演示高级功能 */
async function demoAdvancedGeneration() {
  console.log('\n=== 高级用户名生成演示 ===')
  
  try {
    const api = await createUsernameGeneratorAPI()
    
    // 游戏风格用户名
    console.log('\n1. 游戏风格用户名 (带解释):')
    const gamingRequest: GenerateUsernameRequest = {
      language: 'zh',
      platform: 'gaming',
      style: 'creative',
      tone: 'playful',
      age_group: 'young_adults',
      themes: ['power', 'fantasy', 'competition'],
      count: 3,
      enable_explanations: true,
      cultural_adaptation: 0.8
    }
    
    const gamingResponse = await api.generateUsernames(gamingRequest)
    if (gamingResponse.success && gamingResponse.data) {
      gamingResponse.data.usernames.forEach((result, index) => {
        console.log(`   ${index + 1}. ${result.username} (质量: ${result.quality_score})`)
        if (result.explanation) {
          console.log(`      解释: ${result.explanation}`)
        }
        if (result.components) {
          console.log(`      组成: ${result.components.map(c => `${c.word}(${c.meaning})`).join(' + ')}`)
        }
      })
    }
    
    // 专业风格用户名
    console.log('\n2. 专业风格用户名:')
    const professionalRequest: GenerateUsernameRequest = {
      language: 'zh',
      platform: 'professional',
      style: 'professional',
      tone: 'serious',
      formality: 'formal',
      age_group: 'middle_aged',
      themes: ['wisdom', 'stability', 'expertise'],
      count: 3,
      enable_explanations: true
    }
    
    const professionalResponse = await api.generateUsernames(professionalRequest)
    if (professionalResponse.success && professionalResponse.data) {
      professionalResponse.data.usernames.forEach((result, index) => {
        console.log(`   ${index + 1}. ${result.username} (质量: ${result.quality_score})`)
        if (result.explanation) {
          console.log(`      解释: ${result.explanation}`)
        }
      })
    }
    
    // 创意风格用户名
    console.log('\n3. 创意风格用户名:')
    const creativeRequest: GenerateUsernameRequest = {
      language: 'zh',
      platform: 'creative',
      style: 'creative',
      creativity: 'experimental',
      tone: 'playful',
      age_group: 'teenagers',
      themes: ['art', 'imagination', 'innovation'],
      count: 3,
      enable_explanations: true,
      return_alternatives: true
    }
    
    const creativeResponse = await api.generateUsernames(creativeRequest)
    if (creativeResponse.success && creativeResponse.data) {
      creativeResponse.data.usernames.forEach((result, index) => {
        console.log(`   ${index + 1}. ${result.username} (质量: ${result.quality_score})`)
        if (result.explanation) {
          console.log(`      解释: ${result.explanation}`)
        }
        if (result.alternatives && result.alternatives.length > 0) {
          console.log(`      备选: ${result.alternatives.join(', ')}`)
        }
      })
    }
    
  } catch (error) {
    console.error('高级生成演示失败:', error)
  }
}

/** 演示文化适配功能 */
async function demoCulturalAdaptation() {
  console.log('\n=== 文化适配功能演示 ===')
  
  try {
    const api = await createUsernameGeneratorAPI()
    
    // 传统文化风格
    console.log('\n1. 传统文化风格:')
    const traditionalRequest: GenerateUsernameRequest = {
      language: 'zh',
      culture: 'zh-CN',
      style: 'traditional',
      themes: ['nature', 'virtue', 'wisdom'],
      age_group: 'seniors',
      formality: 'formal',
      count: 3,
      enable_explanations: true,
      cultural_adaptation: 0.9
    }
    
    const traditionalResponse = await api.generateUsernames(traditionalRequest)
    if (traditionalResponse.success && traditionalResponse.data) {
      traditionalResponse.data.usernames.forEach((result, index) => {
        console.log(`   ${index + 1}. ${result.username} (质量: ${result.quality_score})`)
        if (result.explanation) {
          console.log(`      解释: ${result.explanation}`)
        }
      })
    }
    
    // 现代网络文化风格
    console.log('\n2. 现代网络文化风格:')
    const modernRequest: GenerateUsernameRequest = {
      language: 'zh',
      culture: 'zh-CN',
      style: 'modern',
      themes: ['technology', 'social', 'entertainment'],
      age_group: 'teenagers',
      formality: 'informal',
      tone: 'playful',
      count: 3,
      enable_explanations: true,
      cultural_adaptation: 0.8
    }
    
    const modernResponse = await api.generateUsernames(modernRequest)
    if (modernResponse.success && modernResponse.data) {
      modernResponse.data.usernames.forEach((result, index) => {
        console.log(`   ${index + 1}. ${result.username} (质量: ${result.quality_score})`)
        if (result.explanation) {
          console.log(`      解释: ${result.explanation}`)
        }
      })
    }
    
  } catch (error) {
    console.error('文化适配演示失败:', error)
  }
}

/** 演示质量控制功能 */
async function demoQualityControl() {
  console.log('\n=== 质量控制功能演示 ===')
  
  try {
    const results = await generateUsernameWithExplanation({
      language: 'zh',
      style: 'modern',
      themes: ['nature', 'emotion'],
      count: 5,
      enable_explanations: true,
      debug_mode: true
    })
    
    console.log('\n生成结果 (按质量排序):')
    results
      .sort((a, b) => b.quality - a.quality)
      .forEach((result, index) => {
        console.log(`${index + 1}. ${result.username}`)
        console.log(`   质量评分: ${result.quality}`)
        console.log(`   解释: ${result.explanation}`)
        console.log('')
      })
    
  } catch (error) {
    console.error('质量控制演示失败:', error)
  }
}

/** 演示系统统计功能 */
async function demoSystemStatistics() {
  console.log('\n=== 系统统计功能演示 ===')
  
  try {
    const api = await createUsernameGeneratorAPI()
    
    // 先生成一些用户名以产生统计数据
    await api.generateUsernames({
      language: 'zh',
      count: 5,
      style: 'modern'
    })
    
    await api.generateUsernames({
      language: 'zh',
      count: 3,
      style: 'traditional'
    })
    
    // 获取统计信息
    const stats = await api.getStatistics()
    
    console.log('\n系统统计信息:')
    console.log('全局统计:', JSON.stringify(stats.global_statistics, null, 2))
    console.log('\n支持的语言:', stats.supported_languages)
    console.log('\n可用构式:')
    Object.entries(stats.available_constructions).forEach(([lang, constructions]) => {
      console.log(`  ${lang}:`)
      constructions.forEach((c: any) => {
        console.log(`    - ${c.name} (${c.category}): ${c.description}`)
      })
    })
    
  } catch (error) {
    console.error('系统统计演示失败:', error)
  }
}

/** 演示错误处理 */
async function demoErrorHandling() {
  console.log('\n=== 错误处理演示 ===')
  
  try {
    const api = await createUsernameGeneratorAPI()
    
    // 测试无效参数
    console.log('\n1. 测试无效参数:')
    const invalidResponse = await api.generateUsernames({
      language: 'invalid_language',
      count: 100 // 超出限制
    })
    
    if (!invalidResponse.success) {
      console.log(`   错误代码: ${invalidResponse.error?.code}`)
      console.log(`   错误信息: ${invalidResponse.error?.message}`)
    }
    
    // 测试极端条件
    console.log('\n2. 测试极端条件:')
    const extremeResponse = await api.generateUsernames({
      language: 'zh',
      themes: ['nonexistent_theme'],
      cultural_adaptation: 1.0,
      creativity: 'experimental',
      count: 1
    })
    
    if (extremeResponse.success) {
      console.log('   成功处理极端条件')
      console.log(`   生成结果: ${extremeResponse.data?.usernames[0]?.username}`)
    } else {
      console.log(`   错误: ${extremeResponse.error?.message}`)
    }
    
  } catch (error) {
    console.error('错误处理演示失败:', error)
  }
}

/** 主演示函数 */
async function runDemo() {
  console.log('🚀 基于第一性原理的多语言用户名生成系统演示')
  console.log('=' .repeat(60))
  
  try {
    await demoBasicGeneration()
    await demoAdvancedGeneration()
    await demoCulturalAdaptation()
    await demoQualityControl()
    await demoSystemStatistics()
    await demoErrorHandling()
    
    console.log('\n✅ 演示完成!')
    console.log('\n系统特点总结:')
    console.log('• 基于语言学第一性原理设计')
    console.log('• 支持多语言和文化适配')
    console.log('• 语义驱动的智能生成')
    console.log('• 构式语法模板系统')
    console.log('• 多维质量评估')
    console.log('• 灵活的风格和偏好控制')
    console.log('• 完善的错误处理和回退机制')
    
  } catch (error) {
    console.error('演示过程中发生错误:', error)
  }
}

// 如果直接运行此文件，则执行演示
if (require.main === module) {
  runDemo().catch(console.error)
}

export {
  runDemo,
  demoBasicGeneration,
  demoAdvancedGeneration,
  demoCulturalAdaptation,
  demoQualityControl,
  demoSystemStatistics,
  demoErrorHandling
}
