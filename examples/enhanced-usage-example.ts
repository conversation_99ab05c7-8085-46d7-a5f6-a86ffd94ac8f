/**
 * 增强用户名生成系统使用示例
 * 展示如何使用新的语义标签、文化感知和槽位控制功能
 */

import { EnhancedUsernameGenerator } from '../core/EnhancedUsernameGenerator'
import { SlotLengthController } from '../core/SlotLengthController'
import { CulturalAwarenessFilter } from '../core/CulturalAwarenessFilter'
import { UserFeedbackSystem } from '../core/UserFeedbackSystem'
import type { 
  EnhancedGenerateOptions,
  CulturalTag,
  SemanticTag
} from '../types/generator'

/**
 * 基础使用示例
 */
async function basicUsageExample() {
  console.log('=== 基础使用示例 ===')
  
  const generator = new EnhancedUsernameGenerator()
  
  // 简单生成
  const result1 = await generator.generateUsername({
    language: 'zh',
    category: 'internet',
    slot_count: 3
  })
  
  console.log('生成结果:', result1.username)
  console.log('使用模板:', result1.metadata.template_used)
  console.log('文化标签:', result1.metadata.cultural_tags)
  console.log('语义标签:', result1.metadata.semantic_tags)
  console.log('质量分数:', result1.metadata.quality_score)
  console.log('生成说明:', result1.explanation)
  console.log()
}

/**
 * 高级配置示例
 */
async function advancedConfigExample() {
  console.log('=== 高级配置示例 ===')
  
  const generator = new EnhancedUsernameGenerator()
  
  // 带偏好的生成
  const options: EnhancedGenerateOptions = {
    language: 'zh',
    category: 'internet',
    slot_count: 4,
    cultural_preference: ['网络', '流行'],
    semantic_preference: ['力量', '品质'],
    tone_preference: ['正面', '搞怪'],
    rarity_preference: 'mixed',
    enable_semantic_chain: true
  }
  
  const result = await generator.generateUsername(options)
  
  console.log('高级生成结果:', result.username)
  console.log('元数据:', result.metadata)
  console.log()
}

/**
 * 槽位长度控制示例
 */
function slotLengthControlExample() {
  console.log('=== 槽位长度控制示例 ===')
  
  const controller = new SlotLengthController()
  
  // 获取语言配置
  const zhConfig = controller.getConfig('zh')
  console.log('中文配置:', zhConfig)
  
  // 计算目标槽位数
  const targetSlots1 = controller.calculateTargetSlots('zh', 3)
  const targetSlots2 = controller.calculateTargetSlots('zh') // 使用权重分布
  
  console.log('指定长度的槽位数:', targetSlots1)
  console.log('权重分布的槽位数:', targetSlots2)
  
  // 获取槽位分布统计
  const distribution = controller.getSlotDistributionStats('zh')
  console.log('槽位分布:', distribution)
  console.log()
}

/**
 * 文化感知过滤示例
 */
function culturalFilterExample() {
  console.log('=== 文化感知过滤示例 ===')
  
  const filter = new CulturalAwarenessFilter()
  
  // 检查文化兼容性
  const compatibleTags: CulturalTag[] = ['网络', '流行']
  const incompatibleTags: CulturalTag[] = ['传统', '搞怪']
  
  const compatibleResult = filter.checkCulturalCompatibility(compatibleTags)
  const incompatibleResult = filter.checkCulturalCompatibility(incompatibleTags)
  
  console.log('兼容标签结果:', compatibleResult)
  console.log('不兼容标签结果:', incompatibleResult)
  
  // 检查语义冲突
  const semanticTags: SemanticTag[] = ['自然', '科技']
  const semanticResult = filter.checkSemanticConflicts(semanticTags)
  
  console.log('语义冲突检查:', semanticResult)
  
  // 推荐文化标签组合
  const availableTags: CulturalTag[] = ['网络', '传统', '流行', '二次元']
  const recommended = filter.recommendCulturalCombination(availableTags, 2)
  
  console.log('推荐的文化标签组合:', recommended)
  console.log()
}

/**
 * 用户反馈系统示例
 */
function userFeedbackExample() {
  console.log('=== 用户反馈系统示例 ===')
  
  const feedbackSystem = new UserFeedbackSystem()
  
  // 模拟用户反馈
  const feedbacks = [
    {
      username: '超神大佬',
      action: 'copy' as const,
      timestamp: Date.now(),
      session_id: 'session-1',
      cultural_context: ['网络' as CulturalTag],
      semantic_context: ['力量' as SemanticTag]
    },
    {
      username: '可爱小猫',
      action: 'like' as const,
      timestamp: Date.now(),
      session_id: 'session-1',
      cultural_context: ['搞怪' as CulturalTag],
      semantic_context: ['情感' as SemanticTag]
    },
    {
      username: '无聊用户',
      action: 'regenerate' as const,
      timestamp: Date.now(),
      session_id: 'session-1',
      cultural_context: ['学术' as CulturalTag],
      semantic_context: ['品质' as SemanticTag]
    }
  ]
  
  // 记录反馈
  feedbacks.forEach(feedback => {
    feedbackSystem.recordFeedback(feedback)
  })
  
  // 获取动态权重
  const dynamicWeight1 = feedbackSystem.getDynamicWeight('超神', 1.0)
  const dynamicWeight2 = feedbackSystem.getDynamicWeight('可爱', 1.0)
  
  console.log('超神的动态权重:', dynamicWeight1)
  console.log('可爱的动态权重:', dynamicWeight2)
  
  // 获取会话偏好
  const sessionPreferences = feedbackSystem.getSessionPreferences('session-1')
  console.log('会话偏好:', sessionPreferences)
  
  // 获取全局统计
  const globalStats = feedbackSystem.getGlobalStats()
  console.log('全局统计:', globalStats)
  console.log()
}

/**
 * 完整工作流示例
 */
async function completeWorkflowExample() {
  console.log('=== 完整工作流示例 ===')
  
  const generator = new EnhancedUsernameGenerator()
  const controller = new SlotLengthController()
  const filter = new CulturalAwarenessFilter()
  const feedbackSystem = new UserFeedbackSystem()
  
  // 1. 根据用户偏好确定生成参数
  const userPreferences = {
    preferredCulture: ['网络', '流行'] as CulturalTag[],
    preferredSemantic: ['力量', '品质'] as SemanticTag[],
    lengthPreference: 3
  }
  
  // 2. 计算目标槽位数
  const targetSlots = controller.calculateTargetSlots('zh', userPreferences.lengthPreference)
  console.log('目标槽位数:', targetSlots)
  
  // 3. 检查文化兼容性
  const culturalCheck = filter.checkCulturalCompatibility(userPreferences.preferredCulture)
  console.log('文化兼容性检查:', culturalCheck.compatible ? '通过' : '失败')
  
  // 4. 生成用户名
  if (culturalCheck.compatible) {
    try {
      const result = await generator.generateUsername({
        language: 'zh',
        category: 'internet',
        slot_count: targetSlots,
        cultural_preference: userPreferences.preferredCulture,
        semantic_preference: userPreferences.preferredSemantic,
        enable_semantic_chain: true
      })
      
      console.log('生成的用户名:', result.username)
      console.log('生成元数据:', result.metadata)
      
      // 5. 模拟用户反馈
      const userAction = Math.random() > 0.3 ? 'copy' : 'regenerate' // 70%概率喜欢
      
      feedbackSystem.recordFeedback({
        username: result.username,
        action: userAction,
        timestamp: Date.now(),
        session_id: 'workflow-session',
        cultural_context: result.metadata.cultural_tags,
        semantic_context: result.metadata.semantic_tags
      })
      
      console.log('用户操作:', userAction)
      
      // 6. 获取更新后的权重（用于下次生成）
      const words = result.username.match(/[\u4e00-\u9fa5]{2,}/g) || []
      words.forEach(word => {
        const newWeight = feedbackSystem.getDynamicWeight(word, 1.0)
        console.log(`词汇"${word}"的新权重:`, newWeight)
      })
      
    } catch (error) {
      console.error('生成失败:', error)
    }
  } else {
    console.log('文化偏好冲突，建议调整偏好设置')
    const recommended = filter.recommendCulturalCombination(
      ['网络', '传统', '流行', '二次元'], 
      2
    )
    console.log('推荐的文化组合:', recommended)
  }
  
  console.log()
}

/**
 * A/B测试示例
 */
async function abTestExample() {
  console.log('=== A/B测试示例 ===')
  
  const generator = new EnhancedUsernameGenerator()
  const feedbackSystem = new UserFeedbackSystem()
  
  // 模拟A/B测试：不同的权重策略
  const testVariants = [
    {
      name: 'Variant A - 保守策略',
      options: {
        language: 'zh' as const,
        category: 'internet' as const,
        slot_count: 3,
        rarity_preference: 'common' as const,
        cultural_preference: ['网络'] as CulturalTag[]
      }
    },
    {
      name: 'Variant B - 激进策略',
      options: {
        language: 'zh' as const,
        category: 'internet' as const,
        slot_count: 4,
        rarity_preference: 'rare' as const,
        cultural_preference: ['网络', '搞怪'] as CulturalTag[],
        enable_semantic_chain: true
      }
    }
  ]
  
  // 为每个变体生成用户名并收集反馈
  for (const variant of testVariants) {
    console.log(`\n测试 ${variant.name}:`)
    
    try {
      const result = await generator.generateUsername(variant.options)
      console.log('生成结果:', result.username)
      console.log('质量分数:', result.metadata.quality_score)
      
      // 模拟用户反馈（这里简化为随机）
      const satisfaction = Math.random()
      const action = satisfaction > 0.6 ? 'copy' : 'regenerate'
      
      feedbackSystem.recordFeedback({
        username: result.username,
        action,
        timestamp: Date.now(),
        session_id: `ab-test-${variant.name}`,
        cultural_context: result.metadata.cultural_tags,
        semantic_context: result.metadata.semantic_tags
      })
      
      console.log('用户满意度:', (satisfaction * 100).toFixed(1) + '%')
      console.log('用户操作:', action)
      
    } catch (error) {
      console.error(`${variant.name} 生成失败:`, error)
    }
  }
  
  // 分析A/B测试结果
  const stats = feedbackSystem.getGlobalStats()
  console.log('\nA/B测试统计结果:')
  console.log('总反馈数:', stats.total_feedback)
  console.log('表现最好的词汇:', stats.top_positive_words.slice(0, 3))
  console.log('文化标签表现:', stats.cultural_tag_performance)
}

/**
 * 运行所有示例
 */
async function runAllExamples() {
  console.log('🚀 增强用户名生成系统示例演示\n')
  
  try {
    await basicUsageExample()
    await advancedConfigExample()
    slotLengthControlExample()
    culturalFilterExample()
    userFeedbackExample()
    await completeWorkflowExample()
    await abTestExample()
    
    console.log('✅ 所有示例运行完成！')
    
  } catch (error) {
    console.error('❌ 示例运行出错:', error)
  }
}

// 如果直接运行此文件，执行所有示例
if (import.meta.url === `file://${process.argv[1]}`) {
  runAllExamples()
}

export {
  basicUsageExample,
  advancedConfigExample,
  slotLengthControlExample,
  culturalFilterExample,
  userFeedbackExample,
  completeWorkflowExample,
  abTestExample,
  runAllExamples
}
