/**
 * 完整系统演示
 * 
 * 展示基于第一性原理设计的用户名生成系统的完整功能
 */

import { createUsernameAPI, quickGenerate } from '../core/architecture/MainAPI'
import {
  CulturalStyle,
  SemanticDomain,
  LanguageCode
} from '../core/architecture/DataStructures'

/** 演示基础功能 */
async function demoBasicGeneration() {
  console.log('\n🎯 基础生成功能演示')
  console.log('=' .repeat(50))

  try {
    // 快速生成中文用户名
    console.log('\n1. 快速生成中文用户名:')
    const chineseNames = await quickGenerate('zh', 3, CulturalStyle.MODERN)
    chineseNames.forEach((name, index) => {
      console.log(`   ${index + 1}. ${name}`)
    })

    // 快速生成英文用户名
    console.log('\n2. 快速生成英文用户名:')
    const englishNames = await quickGenerate('en', 3, CulturalStyle.COOL)
    englishNames.forEach((name, index) => {
      console.log(`   ${index + 1}. ${name}`)
    })

    // 快速生成日文用户名
    console.log('\n3. 快速生成日文用户名:')
    const japaneseNames = await quickGenerate('ja', 3, CulturalStyle.CUTE)
    japaneseNames.forEach((name, index) => {
      console.log(`   ${index + 1}. ${name}`)
    })

  } catch (error) {
    console.error('❌ 基础生成演示失败:', error)
  }
}

/** 演示高级功能 */
async function demoAdvancedGeneration() {
  console.log('\n🚀 高级生成功能演示')
  console.log('=' .repeat(50))

  try {
    const api = await createUsernameAPI({
      performance: {
        max_generation_time: 3000,
        max_concurrent_generations: 5,
        enable_profiling: true
      },
      logging: {
        level: 'debug',
        enable_file_logging: false,
        log_directory: './logs'
      }
    })

    // 游戏风格用户名
    console.log('\n1. 游戏风格用户名生成:')
    const gamingResponse = await api.generateUsernames({
      language: 'zh',
      style: CulturalStyle.POWERFUL,
      themes: [SemanticDomain.FANTASY, SemanticDomain.ACTION],
      sentiment: 'positive',
      creativity: 'high',
      target_age: 'teen',
      count: 3,
      debug: true
    })

    if (gamingResponse.success && gamingResponse.data) {
      gamingResponse.data.usernames.forEach((result, index) => {
        console.log(`   ${index + 1}. ${result.username} (质量: ${result.quality_score})`)
        console.log(`      模式: ${result.pattern_used}`)
        console.log(`      组成: ${result.components.map(c => c.word).join(' + ')}`)
        if (result.explanation) {
          console.log(`      解释: ${result.explanation}`)
        }
      })
      
      console.log(`   生成时间: ${gamingResponse.data.generation_info.total_time_ms}ms`)
      console.log(`   使用策略: ${gamingResponse.data.generation_info.strategy_used}`)
    }

    // 专业风格用户名
    console.log('\n2. 专业风格用户名生成:')
    const professionalResponse = await api.generateUsernames({
      language: 'en',
      style: CulturalStyle.ELEGANT,
      themes: [SemanticDomain.QUALITY, SemanticDomain.CULTURE],
      formality: 'formal',
      creativity: 'low',
      target_age: 'adult',
      count: 3,
      generation_strategy: 'traditional'
    })

    if (professionalResponse.success && professionalResponse.data) {
      professionalResponse.data.usernames.forEach((result, index) => {
        console.log(`   ${index + 1}. ${result.username} (质量: ${result.quality_score})`)
        console.log(`      文化适配度: ${result.cultural_fitness}`)
      })
    }

    // 创意风格用户名
    console.log('\n3. 创意风格用户名生成:')
    const creativeResponse = await api.generateUsernames({
      language: 'ja',
      style: CulturalStyle.PLAYFUL,
      themes: [SemanticDomain.NATURE, SemanticDomain.EMOTION],
      sentiment: 0.8,
      creativity: 0.9,
      target_age: 'teen',
      min_length: 3,
      max_length: 8,
      count: 3,
      generation_strategy: 'creative'
    })

    if (creativeResponse.success && creativeResponse.data) {
      creativeResponse.data.usernames.forEach((result, index) => {
        console.log(`   ${index + 1}. ${result.username} (质量: ${result.quality_score})`)
      })
    }

  } catch (error) {
    console.error('❌ 高级生成演示失败:', error)
  }
}

/** 演示多语言文化适配 */
async function demoCulturalAdaptation() {
  console.log('\n🌍 多语言文化适配演示')
  console.log('=' .repeat(50))

  try {
    const api = await createUsernameAPI()

    const cultures = [
      { lang: 'zh' as LanguageCode, style: CulturalStyle.TRADITIONAL, name: '中文传统' },
      { lang: 'zh' as LanguageCode, style: CulturalStyle.MODERN, name: '中文现代' },
      { lang: 'en' as LanguageCode, style: CulturalStyle.COOL, name: '英文酷炫' },
      { lang: 'ja' as LanguageCode, style: CulturalStyle.CUTE, name: '日文可爱' }
    ]

    for (const culture of cultures) {
      console.log(`\n${culture.name}风格:`)
      
      const response = await api.generateUsernames({
        language: culture.lang,
        style: culture.style,
        themes: [SemanticDomain.NATURE, SemanticDomain.EMOTION],
        count: 3
      })

      if (response.success && response.data) {
        response.data.usernames.forEach((result, index) => {
          console.log(`   ${index + 1}. ${result.username} (文化适配: ${result.cultural_fitness.toFixed(2)})`)
        })
      }
    }

  } catch (error) {
    console.error('❌ 文化适配演示失败:', error)
  }
}

/** 演示性能和质量控制 */
async function demoPerformanceAndQuality() {
  console.log('\n⚡ 性能和质量控制演示')
  console.log('=' .repeat(50))

  try {
    const api = await createUsernameAPI({
      performance: {
        max_generation_time: 2000,
        max_concurrent_generations: 3,
        enable_profiling: true
      }
    })

    // 批量生成测试
    console.log('\n1. 批量生成性能测试:')
    const startTime = Date.now()
    
    const batchPromises = []
    for (let i = 0; i < 5; i++) {
      batchPromises.push(
        api.generateUsernames({
          language: 'zh',
          style: CulturalStyle.MODERN,
          count: 5,
          quality_threshold: 0.7
        })
      )
    }

    const batchResults = await Promise.all(batchPromises)
    const endTime = Date.now()

    console.log(`   并发生成5批用户名，总耗时: ${endTime - startTime}ms`)
    
    let totalGenerated = 0
    let totalQuality = 0
    
    batchResults.forEach((response, batchIndex) => {
      if (response.success && response.data) {
        const count = response.data.usernames.length
        const avgQuality = response.data.usernames.reduce((sum, u) => sum + u.quality_score, 0) / count
        
        console.log(`   批次${batchIndex + 1}: ${count}个用户名, 平均质量: ${avgQuality.toFixed(2)}`)
        
        totalGenerated += count
        totalQuality += avgQuality * count
      }
    })

    console.log(`   总计生成: ${totalGenerated}个用户名`)
    console.log(`   整体平均质量: ${(totalQuality / totalGenerated).toFixed(2)}`)

    // 获取系统状态
    console.log('\n2. 系统状态报告:')
    const status = await api.getSystemStatus()
    
    console.log(`   系统状态: ${status.status}`)
    console.log(`   运行时间: ${Math.round(status.uptime / 1000)}秒`)
    console.log(`   总生成次数: ${status.performance.total_generations}`)
    console.log(`   成功率: ${(status.performance.success_rate * 100).toFixed(1)}%`)
    console.log(`   平均响应时间: ${status.performance.avg_response_time.toFixed(0)}ms`)
    console.log(`   缓存命中率: ${(status.performance.cache_hit_rate * 100).toFixed(1)}%`)
    console.log(`   支持语言: ${status.languages.supported.join(', ')}`)
    console.log(`   已加载语言: ${status.languages.loaded.join(', ')}`)
    
    if (status.recommendations.length > 0) {
      console.log('   系统建议:')
      status.recommendations.forEach(rec => {
        console.log(`     - ${rec}`)
      })
    }

  } catch (error) {
    console.error('❌ 性能和质量控制演示失败:', error)
  }
}

/** 演示错误处理和边界情况 */
async function demoErrorHandling() {
  console.log('\n🛡️ 错误处理和边界情况演示')
  console.log('=' .repeat(50))

  try {
    const api = await createUsernameAPI()

    // 测试无效参数
    console.log('\n1. 无效参数测试:')
    const invalidResponse = await api.generateUsernames({
      language: 'invalid_lang' as LanguageCode,
      count: 100, // 超出限制
      min_length: 10,
      max_length: 5 // 逻辑错误
    })

    if (!invalidResponse.success) {
      console.log(`   ✓ 正确捕获错误: ${invalidResponse.error?.message}`)
    }

    // 测试极端条件
    console.log('\n2. 极端条件测试:')
    const extremeResponse = await api.generateUsernames({
      language: 'zh',
      themes: ['nonexistent_theme' as SemanticDomain],
      avoid_words: ['星', '月', '云', '风', '雨'], // 避免常用词
      quality_threshold: 0.95, // 极高质量要求
      count: 1
    })

    if (extremeResponse.success && extremeResponse.data) {
      console.log(`   ✓ 极端条件下仍能生成: ${extremeResponse.data.usernames[0]?.username}`)
    } else {
      console.log(`   ✓ 合理的失败处理: ${extremeResponse.error?.message}`)
    }

    // 测试资源限制
    console.log('\n3. 资源限制测试:')
    const resourceTestPromises = []
    for (let i = 0; i < 20; i++) { // 超出并发限制
      resourceTestPromises.push(
        api.generateUsernames({
          language: 'zh',
          count: 1
        })
      )
    }

    const resourceResults = await Promise.allSettled(resourceTestPromises)
    const successful = resourceResults.filter(r => r.status === 'fulfilled').length
    const failed = resourceResults.filter(r => r.status === 'rejected').length
    
    console.log(`   ✓ 资源管理: ${successful}个成功, ${failed}个被限制`)

  } catch (error) {
    console.error('❌ 错误处理演示失败:', error)
  }
}

/** 演示系统扩展性 */
async function demoScalability() {
  console.log('\n📈 系统扩展性演示')
  console.log('=' .repeat(50))

  try {
    const api = await createUsernameAPI()

    // 测试不同语言的扩展
    console.log('\n1. 多语言扩展测试:')
    const languages: LanguageCode[] = ['zh', 'en', 'ja']
    
    for (const lang of languages) {
      console.log(`\n   ${lang.toUpperCase()}语言测试:`)
      
      const response = await api.generateUsernames({
        language: lang,
        count: 2,
        debug: false
      })

      if (response.success && response.data) {
        response.data.usernames.forEach((result, index) => {
          console.log(`     ${index + 1}. ${result.username}`)
        })
        console.log(`     生成时间: ${response.data.generation_info.total_time_ms}ms`)
      } else {
        console.log(`     ❌ ${lang}语言生成失败: ${response.error?.message}`)
      }
    }

    // 测试不同风格的扩展
    console.log('\n2. 多风格扩展测试:')
    const styles = [
      CulturalStyle.TRADITIONAL,
      CulturalStyle.MODERN,
      CulturalStyle.CUTE,
      CulturalStyle.COOL,
      CulturalStyle.ELEGANT
    ]

    for (const style of styles) {
      const response = await api.generateUsernames({
        language: 'zh',
        style,
        count: 1
      })

      if (response.success && response.data) {
        const result = response.data.usernames[0]
        console.log(`   ${style}: ${result.username} (适配度: ${result.cultural_fitness.toFixed(2)})`)
      }
    }

  } catch (error) {
    console.error('❌ 扩展性演示失败:', error)
  }
}

/** 主演示函数 */
async function runCompleteDemo() {
  console.log('🎉 基于第一性原理的多语言用户名生成系统')
  console.log('完整功能演示')
  console.log('=' .repeat(60))

  const demos = [
    { name: '基础生成功能', fn: demoBasicGeneration },
    { name: '高级生成功能', fn: demoAdvancedGeneration },
    { name: '多语言文化适配', fn: demoCulturalAdaptation },
    { name: '性能和质量控制', fn: demoPerformanceAndQuality },
    { name: '错误处理和边界情况', fn: demoErrorHandling },
    { name: '系统扩展性', fn: demoScalability }
  ]

  for (const demo of demos) {
    try {
      await demo.fn()
      console.log(`\n✅ ${demo.name} 演示完成`)
    } catch (error) {
      console.log(`\n❌ ${demo.name} 演示失败:`, error)
    }
  }

  console.log('\n🎊 完整演示结束!')
  console.log('\n系统特点总结:')
  console.log('• 🏗️  基于第一性原理的扎实架构')
  console.log('• 🌍 完整的多语言和文化支持')
  console.log('• 🧠 智能的语义驱动生成')
  console.log('• ⚡ 高性能的算法和缓存')
  console.log('• 🎯 精确的质量控制')
  console.log('• 🔧 灵活的配置和扩展')
  console.log('• 🛡️  完善的错误处理')
  console.log('• 📊 全面的监控和优化')
}

// 如果直接运行此文件，则执行演示
if (require.main === module) {
  runCompleteDemo().catch(console.error)
}

export {
  runCompleteDemo,
  demoBasicGeneration,
  demoAdvancedGeneration,
  demoCulturalAdaptation,
  demoPerformanceAndQuality,
  demoErrorHandling,
  demoScalability
}
