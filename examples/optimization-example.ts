/**
 * AI驱动用户名优化系统使用示例
 * 展示如何为新语种建立有趣用户名生成能力
 */

import { InterestAnalysisSystem } from '../core/InterestAnalysisSystem'
import { OptimizationLoop } from '../core/OptimizationLoop'
import { generateCulturalUsername } from '../core/TestUsernameGenerator'

/**
 * 示例1: 单个用户名深度分析
 */
async function analyzeUsernameExample() {
  console.log('🔍 示例1: 单个用户名深度分析')
  console.log('=' .repeat(50))
  
  const analyzer = new InterestAnalysisSystem('zh', 'local')
  
  const testUsernames = [
    '超级大神王',      // 传统强化型
    '究极玩家yyds',    // 网络流行型  
    '神级剑客',        // 简洁有力型
    '霸道法师',        // 个性鲜明型
    '传说勇者'         // 史诗感型
  ]
  
  for (const username of testUsernames) {
    const result = await analyzer.analyzeUsername(username)
    
    console.log(`\n📝 用户名: ${username}`)
    console.log(`📊 总体有趣性: ${result.metrics.overall_interest.toFixed(3)} (${getInterestLevel(result.metrics.overall_interest)})`)
    
    // 详细维度分析
    console.log(`🎵 语言学: 音韵${result.metrics.linguistic.phonetic_appeal.toFixed(2)} | 节奏${result.metrics.linguistic.rhythm_score.toFixed(2)} | 押韵${result.metrics.linguistic.alliteration.toFixed(2)} | 和谐${result.metrics.linguistic.syllable_harmony.toFixed(2)}`)
    console.log(`💭 语义学: 创意${result.metrics.semantic.creativity.toFixed(2)} | 意外${result.metrics.semantic.unexpectedness.toFixed(2)} | 连贯${result.metrics.semantic.coherence.toFixed(2)} | 共鸣${result.metrics.semantic.cultural_resonance.toFixed(2)}`)
    console.log(`🧠 心理学: 记忆${result.metrics.psychological.memorability.toFixed(2)} | 情感${result.metrics.psychological.emotional_impact.toFixed(2)} | 个性${result.metrics.psychological.personality_projection.toFixed(2)} | 社交${result.metrics.psychological.social_appeal.toFixed(2)}`)
    console.log(`⚙️ 实用性: 独特${result.metrics.practical.uniqueness.toFixed(2)} | 可读${result.metrics.practical.pronounceability.toFixed(2)} | 长度${result.metrics.practical.length_appropriateness.toFixed(2)} | 兼容${result.metrics.practical.platform_compatibility.toFixed(2)}`)
    
    // 改进建议
    console.log(`💡 优势: ${result.detailed_feedback.strengths.join(', ')}`)
    console.log(`⚠️ 改进: ${result.detailed_feedback.suggestions.join(', ')}`)
  }
}

/**
 * 示例2: 批量分析与模式识别
 */
async function batchAnalysisExample() {
  console.log('\n\n📊 示例2: 批量分析与模式识别')
  console.log('=' .repeat(50))
  
  const analyzer = new InterestAnalysisSystem('zh', 'local')
  
  // 生成多样化样本
  const samples: string[] = []
  for (let i = 0; i < 30; i++) {
    const username = await generateCulturalUsername({ 
      language: 'zh', 
      category: 'internet',
      slot_count: Math.floor(Math.random() * 3) + 2  // 2-4个槽位
    })
    samples.push(username)
  }
  
  console.log(`🎯 生成样本数量: ${samples.length}`)
  console.log(`📝 样本预览: ${samples.slice(0, 8).join(', ')}...`)
  
  const report = await analyzer.analyzeBatch(samples)
  
  console.log(`\n📈 分析结果概览:`)
  console.log(`📊 平均有趣性: ${(report.distribution_analysis.dimension_averages.overall_interest || 0).toFixed(3)}`)
  console.log(`🎯 高分样本: ${report.distribution_analysis.outliers.high.join(', ')}`)
  console.log(`⚠️ 低分样本: ${report.distribution_analysis.outliers.low.join(', ')}`)
  
  console.log(`\n🔍 模式洞察:`)
  console.log(`✅ 成功模式数量: ${report.pattern_insights.successful_patterns.length}`)
  console.log(`❌ 问题模式数量: ${report.pattern_insights.problematic_patterns.length}`)
  
  console.log(`\n🎨 文化洞察:`)
  console.log(`🌟 有效文化元素: ${report.cultural_insights.effective_cultural_elements.join(', ')}`)
  console.log(`🔄 跨文化潜力: ${report.cultural_insights.cross_cultural_potential.join(', ')}`)
}

/**
 * 示例3: 完整优化闭环演示
 */
async function optimizationLoopExample() {
  console.log('\n\n🚀 示例3: 完整优化闭环演示')
  console.log('=' .repeat(50))
  
  const config = {
    language: 'zh',
    sample_size: 20,
    target_interest_threshold: 0.65,  // 目标: 65%有趣性
    max_iterations: 5,
    convergence_threshold: 0.005,     // 0.5%改进阈值
    ai_provider: 'local' as const
  }
  
  console.log(`🎯 优化目标: 将用户名有趣性提升至 ${config.target_interest_threshold}`)
  console.log(`📊 每轮样本: ${config.sample_size}`)
  console.log(`🔄 最大迭代: ${config.max_iterations}`)
  console.log(`📈 收敛阈值: ${config.convergence_threshold}`)
  
  const optimizer = new OptimizationLoop(config)
  const summary = await optimizer.runOptimizationLoop()
  
  console.log(`\n📋 优化总结:`)
  console.log(`🔄 实际迭代: ${summary.total_iterations}`)
  console.log(`📈 最终性能: ${summary.final_performance.toFixed(3)}`)
  console.log(`📊 总体改进: ${summary.improvement_achieved.toFixed(3)} (${(summary.improvement_achieved * 100).toFixed(1)}%)`)
  console.log(`🎯 目标达成: ${summary.final_performance >= config.target_interest_threshold ? '✅ 是' : '❌ 否'}`)
  
  console.log(`\n🔍 关键发现:`)
  summary.key_discoveries.forEach((discovery, index) => {
    console.log(`  ${index + 1}. ${discovery}`)
  })
  
  console.log(`\n⚙️ 推荐配置:`)
  console.log(`  权重设置: ${JSON.stringify(summary.recommended_settings.optimal_weights)}`)
  console.log(`  首选模板: ${summary.recommended_settings.preferred_templates.join(', ')}`)
  console.log(`  文化焦点: ${summary.recommended_settings.cultural_focus.join(', ')}`)
  
  // 导出详细结果
  const exportData = optimizer.exportResults()
  console.log(`\n💾 详细结果已导出 (${exportData.length} 字符)`)
}

/**
 * 示例4: 跨语言对比分析
 */
async function crossLanguageExample() {
  console.log('\n\n🌍 示例4: 跨语言对比分析')
  console.log('=' .repeat(50))
  
  const languages = [
    { code: 'zh', name: '中文' },
    { code: 'en', name: '英文' }
  ]
  
  const crossLanguageResults: Record<string, any> = {}
  
  for (const lang of languages) {
    console.log(`\n🌐 分析 ${lang.name} (${lang.code}) 用户名特征...`)
    
    const analyzer = new InterestAnalysisSystem(lang.code, 'local')
    
    // 生成该语言样本
    const samples: string[] = []
    for (let i = 0; i < 15; i++) {
      const username = await generateCulturalUsername({ 
        language: lang.code, 
        category: 'internet' 
      })
      samples.push(username)
    }
    
    const report = await analyzer.analyzeBatch(samples)
    
    crossLanguageResults[lang.code] = {
      language: lang.name,
      samples: samples.slice(0, 5),
      avg_interest: report.distribution_analysis.dimension_averages.overall_interest || 0,
      cultural_elements: report.cultural_insights.effective_cultural_elements
    }
    
    console.log(`  📝 样本示例: ${samples.slice(0, 5).join(', ')}`)
    console.log(`  📊 平均有趣性: ${(report.distribution_analysis.dimension_averages.overall_interest || 0).toFixed(3)}`)
  }
  
  console.log(`\n🔄 跨语言对比洞察:`)
  
  const zhResult = crossLanguageResults['zh']
  const enResult = crossLanguageResults['en']
  
  if (zhResult && enResult) {
    console.log(`📊 ${zhResult.language} vs ${enResult.language}:`)
    console.log(`  有趣性对比: ${zhResult.avg_interest.toFixed(3)} vs ${enResult.avg_interest.toFixed(3)}`)
    
    if (zhResult.avg_interest > enResult.avg_interest) {
      console.log(`  🏆 ${zhResult.language} 在当前测试中表现更优`)
    } else {
      console.log(`  🏆 ${enResult.language} 在当前测试中表现更优`)
    }
  }
  
  console.log(`\n🎯 通用优化原则:`)
  console.log(`  1. 平衡创意性与实用性`)
  console.log(`  2. 注重文化适配性`)
  console.log(`  3. 保持跨平台兼容性`)
  console.log(`  4. 考虑目标用户群体偏好`)
}

/**
 * 示例5: 新语种快速接入流程
 */
async function newLanguageOnboardingExample() {
  console.log('\n\n🆕 示例5: 新语种快速接入流程演示')
  console.log('=' .repeat(50))
  
  const newLanguage = 'ja'  // 假设接入日语
  
  console.log(`🎯 目标: 为 ${newLanguage} 语种建立用户名生成能力`)
  
  // 步骤1: 基础能力评估
  console.log(`\n📋 步骤1: 基础能力评估`)
  console.log(`  ✅ 基础词汇库: 准备中...`)
  console.log(`  ✅ 文化标签: 准备中...`)
  console.log(`  ✅ 语言特性: 分析中...`)
  
  // 步骤2: 小样本快速测试
  console.log(`\n🧪 步骤2: 小样本快速测试`)
  const quickTestConfig = {
    language: newLanguage,
    sample_size: 10,
    target_interest_threshold: 0.6,
    max_iterations: 3,
    convergence_threshold: 0.01,
    ai_provider: 'local' as const
  }
  
  console.log(`  📊 快速测试配置: ${JSON.stringify(quickTestConfig, null, 2)}`)
  console.log(`  🔄 执行快速优化...`)
  
  // 模拟快速优化结果
  const mockResults = {
    iterations: 2,
    initial_performance: 0.45,
    final_performance: 0.58,
    improvement: 0.13
  }
  
  console.log(`  📈 快速测试结果:`)
  console.log(`    初始性能: ${mockResults.initial_performance.toFixed(3)}`)
  console.log(`    最终性能: ${mockResults.final_performance.toFixed(3)}`)
  console.log(`    改进幅度: ${mockResults.improvement.toFixed(3)} (+${(mockResults.improvement * 100).toFixed(1)}%)`)
  
  // 步骤3: 效果验证
  console.log(`\n✅ 步骤3: 效果验证`)
  console.log(`  📊 大样本验证: 计划中...`)
  console.log(`  👥 用户反馈收集: 计划中...`)
  console.log(`  🔄 持续优化: 计划中...`)
  
  console.log(`\n🎉 ${newLanguage} 语种接入流程完成!`)
  console.log(`📈 预期在 2-3 周内达到生产就绪状态`)
}

// 辅助函数
function getInterestLevel(score: number): string {
  if (score >= 0.8) return '🌟 极高'
  if (score >= 0.7) return '⭐ 很高'
  if (score >= 0.6) return '✨ 较高'
  if (score >= 0.5) return '💫 中等'
  if (score >= 0.4) return '⚡ 较低'
  return '💤 很低'
}

/**
 * 主函数 - 运行所有示例
 */
async function runAllExamples() {
  console.log('🚀 AI驱动用户名优化系统 - 完整示例演示')
  console.log('=' .repeat(60))
  
  try {
    await analyzeUsernameExample()
    await batchAnalysisExample()
    await optimizationLoopExample()
    await crossLanguageExample()
    await newLanguageOnboardingExample()
    
    console.log('\n\n🎉 所有示例演示完成!')
    console.log('=' .repeat(60))
    console.log('💡 这套系统展示了如何通过AI驱动的闭环优化')
    console.log('   快速为新语种建立高质量用户名生成能力')
    console.log('🌟 核心价值: 科学化、自动化、可扩展的优化方法')
    
  } catch (error) {
    console.error('❌ 示例运行出错:', error)
  }
}

// 导出示例函数
export {
  analyzeUsernameExample,
  batchAnalysisExample,
  optimizationLoopExample,
  crossLanguageExample,
  newLanguageOnboardingExample,
  runAllExamples
}

// 如果直接运行此文件
if (require.main === module) {
  runAllExamples()
}
