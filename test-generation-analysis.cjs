/**
 * V5引擎生成过程深度分析和重复率测试
 * 分析语素库使用覆盖率、随机选择算法偏向性、重复率统计
 */

const fs = require('fs')

// 模拟V5引擎的元素库（从实际代码中提取）
const elementLibrary = {
  subjects: {
    古代人物: ['贫僧', '道士', '书生', '侠客', '状元', '举人', '秀才', '太守', '县令', '师爷'],
    现代职业: ['程序员', '设计师', '产品经理', '运营', '策划', '文案', '测试', '架构师', '前端', '后端'],
    网络身份: ['UP主', '主播', '网红', '博主', '自媒体', '数字游民', '斜杠青年', 'KOL', '网络达人', '内容创作者'],
    动物世界: ['猫', '狗', '猪', '鸟', '鱼', '蜗牛', '大象', '蚂蚁', '老鼠', '兔子', '熊猫', '企鹅', '海豚', '狮子'],
    天体宇宙: ['月亮', '星星', '太阳', '云朵', '彩虹', '闪电', '银河', '宇宙', '星球', '流星', '黑洞', '星云'],
    抽象概念: ['快乐', '悲伤', '愤怒', '温柔', '智慧', '勇敢', '梦想', '希望', '爱情', '友情', '孤独', '自由'],
    食物美食: ['芝士', '咖啡', '茶', '巧克力', '蛋糕', '面包', '牛奶', '果汁', '冰淇淋', '薯片', '火锅', '烧烤'],
    技术概念: ['WiFi', '404', '500', '数据库', '服务器', '算法', 'AI', '云端', '区块链', '元宇宙', 'VR', 'AR'],
    情绪状态: ['间歇性努力', '积极废人', '外向孤独症', '理性但感性', '想独立又想被照顾', '社交恐惧但渴望理解', '想要自由又怕孤独', '低调张扬', '懒惰但有梦想', '成熟幼稚', '高敏感低社交', '理想主义现实派', '温和愤青', '精神内耗专业户', '低落但坚韧', '焦虑型乐观主义者', '消极但自愈', '受挫但坚持', '社交电池耗尽', '选择恐惧症晚期', 'FOMO综合症', '拖延症晚期', '信息过载焦虑', '周一恐惧症', '深夜emo专业户', '早睡失败专业户', '减肥失败但快乐', '存钱失败但开心', '学习失败但努力', '社交废物但可爱', '运动失败但健康', '计划失败但乐观', '熬夜冠军但精神', '拖延症但有爱', '迷糊但温暖'],
    食物关联: ['奶茶星人', '火锅爱好者', '烧烤达人', '小龙虾专家', '螺蛳粉忠粉', '咖啡成瘾者', '甜品控', '炸鸡爱好者', '麻辣烫专家', '珍珠奶茶党', '冰淇淋狂热者', '寿司达人', '泡面专家', '汉堡爱好者', '薯条控', '披萨党', '酸辣粉忠粉', '烤肉专家', '蛋糕控', '粥品爱好者', '热汤治愈师', '妈妈菜专家', '温暖粥品师', '甜品治愈师', '零食安慰师', '温牛奶守护者', '蜂蜜茶调配师', '暖胃面条师', '红糖水专家', '温暖饺子师', '热巧克力调配师', '养生汤品师', '温暖包子师', '治愈烘焙师', '温心小食师', '饿货', '吃货', '美食家', '减肥中', '夜宵党', '外卖达人', '厨房杀手', '下厨新手', '零食囤积者', '口味挑剔者', '健康饮食者', '暴食症候群', '食物摄影师', '深夜觅食者', '节食失败者']
  },
  actions: {
    日常行为: ['吃', '睡', '玩', '工作', '学习', '休息', '思考', '发呆', '散步', '购物', '聊天', '刷手机'],
    特殊动作: ['飞', '游泳', '追', '跑', '爬', '跳', '舞蹈', '唱歌', '画画', '写作', '演讲', '表演'],
    抽象动作: ['贩卖', '收集', '制造', '修理', '设计', '创造', '破坏', '保护', '传播', '分享', '探索', '发现'],
    网络行为: ['直播', '带货', '刷视频', '点赞', '转发', '评论', '关注', '充电', '打赏', '连麦', '开播', '下播'],
    现代生活: ['洗头', '化妆', '健身', '减肥', '加班', '熬夜', '摸鱼', '划水', '开会', '汇报', '出差', '居家']
  },
  modifiers: {
    权威级别: ['首席', '高级', '资深', '专业', '认证', '官方', '特级', '顶级', '大师级', '传奇', '史诗', '钻石'],
    空间范围: ['全球', '国际', '世界级', '宇宙级', '银河系', '太阳系', '跨国', '跨界', '跨次元', '本地', '区域'],
    程度强化: ['超级', '极度', '非常', '特别', '完全', '绝对', '史诗', '终极', '至尊', '满级', '深度', '极致'],
    时间频率: ['永远', '从不', '偶尔', '经常', '总是', '间歇性', '定期', '不定期', '随时', '24小时', '全天候'],
    状态描述: ['在线', '离线', '忙碌', '空闲', '活跃', '潜水', '冒泡', '隐身', '免打扰', '勿扰', '正常', '异常']
  },
  connectors: {
    对比转折: ['但', '却', '然而', '不过', '虽然', '尽管', '反而', '相反', '倒是', '偏偏', '恰恰', '竟然'],
    并列关系: ['和', '与', '及', '以及', '还有', '同时', '一边', '一面', '既', '又', '也', '亦'],
    递进强化: ['更', '还', '甚至', '竟然', '居然', '简直', '完全', '彻底', '深度', '极致', '进一步', '更加'],
    因果关系: ['因为', '所以', '由于', '导致', '造成', '引起', '产生', '带来', '形成', '促使', '致使', '使得']
  }
}

// 生成模式
const generationPatterns = [
  { id: 'identity_elevation', name: '身份升维包装', weight: 0.96 },
  { id: 'contradiction_unity', name: '矛盾统一', weight: 0.94 },
  { id: 'temporal_displacement', name: '时空错位重组', weight: 0.95 },
  { id: 'service_personification', name: '服务拟人化', weight: 0.92 },
  { id: 'tech_expression', name: '技术化表达', weight: 0.91 },
  { id: 'emotion_state', name: '情绪状态模式', weight: 0.88 },
  { id: 'food_association', name: '食物关联模式', weight: 0.85 }
]

// 随机选择函数
function randomSelect(array) {
  return array[Math.floor(Math.random() * array.length)]
}

// 模拟生成函数
function generateByPattern(patternId) {
  let username = ''
  let elementsUsed = []

  switch (patternId) {
    case 'identity_elevation':
      const authority = randomSelect(elementLibrary.modifiers.权威级别)
      const behavior = randomSelect(elementLibrary.actions.日常行为)
      const suffix = randomSelect(['官', '师', '专家', '大使', '代表', '委员', '顾问', '总监', '主任', '经理'])
      username = `${authority}${behavior}${suffix}`
      elementsUsed = [authority, behavior, suffix]
      break

    case 'contradiction_unity':
      const positive = randomSelect(['温柔', '理性', '冷静', '勤奋', '节约', '听劝', '乐观', '自信', '坚强', '独立'])
      const connector = randomSelect(elementLibrary.connectors.对比转折)
      const negative = randomSelect(['强硬', '感性', '冲动', '懒惰', '挥霍', '反骨', '悲观', '自卑', '脆弱', '依赖'])
      username = `${positive}${connector}${negative}`
      elementsUsed = [positive, connector, negative]
      break

    case 'temporal_displacement':
      const ancient = randomSelect(elementLibrary.subjects.古代人物)
      const modern = randomSelect([
        ...elementLibrary.actions.网络行为,
        ...elementLibrary.actions.现代生活
      ])
      username = `${ancient}${modern}`
      elementsUsed = [ancient, modern]
      break

    case 'service_personification':
      const concept = randomSelect([
        ...elementLibrary.subjects.抽象概念,
        ...elementLibrary.subjects.天体宇宙
      ])
      const service = randomSelect(['邮递员', '收集员', '配送员', '制造商', '贩卖机', '专卖店', '客服', '导航员', '修理工', '设计师'])
      username = `${concept}${service}`
      elementsUsed = [concept, service]
      break

    case 'tech_expression':
      const lifeConcept = randomSelect(['人生', '梦想', '快乐', '悲伤', '爱情', '友情', '工作', '学习', '生活', '未来'])
      const techTerm = randomSelect(['正在缓冲', '连接超时', '服务器宕机', '数据库损坏', '404未找到', '503不可用', '系统维护', '正在加载'])
      username = `${lifeConcept}${techTerm}`
      elementsUsed = [lifeConcept, techTerm]
      break

    case 'emotion_state':
      const emotionWord = randomSelect(elementLibrary.subjects.情绪状态)
      if (emotionWord.includes('专业户') || emotionWord.includes('星人') || emotionWord.includes('选手') ||
          emotionWord.includes('代表') || emotionWord.includes('患者') || emotionWord.includes('党') ||
          emotionWord.includes('师') || emotionWord.includes('者') || emotionWord.includes('家') ||
          emotionWord.includes('控') || emotionWord.includes('货')) {
        username = emotionWord
        elementsUsed = [emotionWord]
      } else {
        const emotionSuffix = randomSelect(['专家', '代表', '选手', '患者', '星人'])
        username = `${emotionWord}${emotionSuffix}`
        elementsUsed = [emotionWord, emotionSuffix]
      }
      break

    case 'food_association':
      const foodWord = randomSelect(elementLibrary.subjects.食物关联)
      if (foodWord.includes('星人') || foodWord.includes('爱好者') || foodWord.includes('达人') ||
          foodWord.includes('专家') || foodWord.includes('忠粉') || foodWord.includes('成瘾者') ||
          foodWord.includes('控') || foodWord.includes('党') || foodWord.includes('狂热者') ||
          foodWord.includes('师') || foodWord.includes('货') || foodWord.includes('家') ||
          foodWord.includes('杀手') || foodWord.includes('新手') || foodWord.includes('囤积者') ||
          foodWord.includes('挑剔者') || foodWord.includes('饮食者') || foodWord.includes('症候群') ||
          foodWord.includes('摄影师') || foodWord.includes('觅食者') || foodWord.includes('失败者') ||
          foodWord.includes('守护者') || foodWord.includes('调配师') || foodWord.includes('安慰师')) {
        username = foodWord
        elementsUsed = [foodWord]
      } else {
        const foodSuffix = randomSelect(['专家', '爱好者', '达人', '星人', '党'])
        username = `${foodWord}${foodSuffix}`
        elementsUsed = [foodWord, foodSuffix]
      }
      break

    default:
      return null
  }

  return {
    username,
    pattern: patternId,
    elements_used: elementsUsed
  }
}

// 深度分析函数
function deepAnalysis() {
  console.log('🔍 开始V5引擎深度分析...\n')

  // 1. 语素库统计
  console.log('📊 语素库统计分析:')
  let totalElements = 0
  Object.keys(elementLibrary).forEach(category => {
    Object.keys(elementLibrary[category]).forEach(subcategory => {
      const count = elementLibrary[category][subcategory].length
      totalElements += count
      console.log(`  ${category}.${subcategory}: ${count}个元素`)
    })
  })
  console.log(`  总计: ${totalElements}个语素元素\n`)

  // 2. 生成1000次测试
  console.log('🎯 生成1000次重复率测试:')
  const testCount = 1000
  const results = []
  const usageStats = {}
  const patternStats = {}

  // 初始化统计
  Object.keys(elementLibrary).forEach(category => {
    Object.keys(elementLibrary[category]).forEach(subcategory => {
      elementLibrary[category][subcategory].forEach(element => {
        usageStats[element] = 0
      })
    })
  })

  generationPatterns.forEach(pattern => {
    patternStats[pattern.id] = 0
  })

  // 生成测试
  for (let i = 0; i < testCount; i++) {
    const pattern = randomSelect(generationPatterns)
    const result = generateByPattern(pattern.id)
    
    if (result) {
      results.push(result)
      patternStats[result.pattern]++
      
      // 统计元素使用
      result.elements_used.forEach(element => {
        if (usageStats[element] !== undefined) {
          usageStats[element]++
        }
      })
    }
  }

  // 3. 重复率分析
  const usernameCount = {}
  results.forEach(result => {
    usernameCount[result.username] = (usernameCount[result.username] || 0) + 1
  })

  const duplicates = Object.entries(usernameCount).filter(([_, count]) => count > 1)
  const duplicateRate = (duplicates.length / Object.keys(usernameCount).length) * 100

  console.log(`  生成总数: ${results.length}`)
  console.log(`  唯一用户名: ${Object.keys(usernameCount).length}`)
  console.log(`  重复用户名: ${duplicates.length}`)
  console.log(`  重复率: ${duplicateRate.toFixed(2)}%`)

  if (duplicates.length > 0) {
    console.log('  重复用户名详情:')
    duplicates.slice(0, 10).forEach(([username, count]) => {
      console.log(`    "${username}": ${count}次`)
    })
  }

  // 4. 模式使用分布
  console.log('\n📈 生成模式使用分布:')
  Object.entries(patternStats).forEach(([pattern, count]) => {
    const percentage = (count / testCount * 100).toFixed(1)
    console.log(`  ${pattern}: ${count}次 (${percentage}%)`)
  })

  // 5. 语素使用覆盖率
  console.log('\n🎨 语素使用覆盖率分析:')
  const usedElements = Object.entries(usageStats).filter(([_, count]) => count > 0)
  const coverageRate = (usedElements.length / totalElements * 100).toFixed(1)
  console.log(`  使用的语素: ${usedElements.length}/${totalElements} (${coverageRate}%)`)

  // 找出未使用的语素
  const unusedElements = Object.entries(usageStats).filter(([_, count]) => count === 0)
  if (unusedElements.length > 0) {
    console.log(`  未使用的语素: ${unusedElements.length}个`)
    console.log('  未使用语素示例:')
    unusedElements.slice(0, 10).forEach(([element, _]) => {
      console.log(`    "${element}"`)
    })
  }

  // 6. 高频使用语素
  const highFreqElements = Object.entries(usageStats)
    .filter(([_, count]) => count > 0)
    .sort((a, b) => b[1] - a[1])
    .slice(0, 20)

  console.log('\n🔥 高频使用语素 (Top 20):')
  highFreqElements.forEach(([element, count]) => {
    const percentage = (count / results.length * 100).toFixed(1)
    console.log(`  "${element}": ${count}次 (${percentage}%)`)
  })

  // 7. 保存详细报告
  const report = {
    timestamp: new Date().toISOString(),
    summary: {
      totalElements,
      testCount,
      uniqueUsernames: Object.keys(usernameCount).length,
      duplicateCount: duplicates.length,
      duplicateRate: duplicateRate,
      coverageRate: parseFloat(coverageRate)
    },
    patternStats,
    usageStats,
    duplicates: duplicates.slice(0, 50),
    highFreqElements: highFreqElements.slice(0, 50),
    unusedElements: unusedElements.slice(0, 100)
  }

  fs.writeFileSync('generation-analysis-report.json', JSON.stringify(report, null, 2))
  console.log('\n📄 详细报告已保存到: generation-analysis-report.json')

  return report
}

// 执行分析
if (require.main === module) {
  deepAnalysis()
}

module.exports = { deepAnalysis, generateByPattern, elementLibrary }
