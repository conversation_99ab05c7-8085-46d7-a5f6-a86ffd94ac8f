// 测试不同组合的生成效果
const testCombinations = [
  { theme: '情感', pattern: 'emotion_state', description: '情感主题 + 情绪状态模式' },
  { theme: '食物', pattern: 'food_association', description: '食物主题 + 食物关联模式' },
  { theme: '职场', pattern: 'identity_elevation', description: '职场主题 + 身份升维模式' },
  { theme: '科技', pattern: 'tech_expression', description: '科技主题 + 技术化表达模式' },
  { theme: '幽默', pattern: 'homophone_creative', description: '幽默主题 + 创意谐音模式' },
  { theme: '文化', pattern: 'temporal_displacement', description: '文化主题 + 时空错位模式' }
];

// 模拟V5引擎的核心生成逻辑
class TestV5Engine {
  constructor() {
    this.elementLibrary = this.buildElementLibrary();
  }

  buildElementLibrary() {
    return {
      subjects: {
        情绪状态: ['间歇性努力', '积极废人', '外向孤独症', '理性但感性', '想独立又想被照顾', '社交恐惧但渴望理解', '想要自由又怕孤独', '低调张扬', '懒惰但有梦想', '成熟幼稚', '高敏感低社交', '理想主义现实派', '温和愤青', '精神内耗专业户', '低落但坚韧', '焦虑型乐观主义者', '消极但自愈', '受挫但坚持', '社交电池耗尽', '选择恐惧症晚期'],
        食物关联: ['奶茶星人', '火锅爱好者', '烧烤达人', '小龙虾专家', '螺蛳粉忠粉', '咖啡成瘾者', '甜品控', '炸鸡爱好者', '麻辣烫专家', '珍珠奶茶党', '冰淇淋狂热者', '寿司达人', '泡面专家', '汉堡爱好者', '薯条控', '披萨党', '酸辣粉忠粉', '烤肉专家', '蛋糕控', '粥品爱好者'],
        古代人物: ['贫僧', '道士', '书生', '侠客', '状元', '举人', '秀才', '太守', '县令', '师爷'],
        现代职业: ['程序员', '设计师', '产品经理', '运营', '策划', '文案', '测试', '架构师', '前端', '后端'],
        技术概念: ['WiFi', '404', '500', '数据库', '服务器', '算法', 'AI', '云端', '区块链', '元宇宙']
      },
      actions: {
        日常行为: ['吃', '睡', '玩', '工作', '学习', '休息', '思考', '发呆', '散步', '购物'],
        网络行为: ['直播', '带货', '刷视频', '点赞', '转发', '评论', '关注', '充电', '打赏', '连麦'],
        现代生活: ['洗头', '化妆', '健身', '减肥', '加班', '熬夜', '摸鱼', '划水', '开会', '汇报']
      },
      modifiers: {
        权威级别: ['首席', '高级', '资深', '专业', '认证', '官方', '特级', '顶级', '大师级', '传奇'],
        程度强化: ['超级', '极度', '非常', '特别', '完全', '绝对', '史诗', '终极', '至尊', '满级']
      },
      connectors: {
        对比转折: ['但', '却', '然而', '不过', '虽然', '尽管', '反而', '相反', '倒是', '偏偏']
      }
    };
  }

  randomSelect(array) {
    return array[Math.floor(Math.random() * array.length)];
  }

  generateByPattern(patternId, theme) {
    let username = '';
    let elementsUsed = [];

    try {
      switch (patternId) {
        case 'emotion_state':
          const emotionWord = this.randomSelect(this.elementLibrary.subjects.情绪状态);
          if (emotionWord.includes('专业户') || emotionWord.includes('星人') || emotionWord.includes('选手') ||
              emotionWord.includes('代表') || emotionWord.includes('患者') || emotionWord.includes('党') ||
              emotionWord.includes('师') || emotionWord.includes('者') || emotionWord.includes('家') ||
              emotionWord.includes('控') || emotionWord.includes('货')) {
            username = emotionWord;
            elementsUsed = [emotionWord];
          } else {
            const emotionSuffix = this.randomSelect(['专家', '代表', '选手', '患者', '星人']);
            username = emotionWord + emotionSuffix;
            elementsUsed = [emotionWord, emotionSuffix];
          }
          break;

        case 'food_association':
          const foodWord = this.randomSelect(this.elementLibrary.subjects.食物关联);
          if (foodWord.includes('星人') || foodWord.includes('爱好者') || foodWord.includes('达人') ||
              foodWord.includes('专家') || foodWord.includes('忠粉') || foodWord.includes('成瘾者') ||
              foodWord.includes('控') || foodWord.includes('党') || foodWord.includes('狂热者')) {
            username = foodWord;
            elementsUsed = [foodWord];
          } else {
            const foodSuffix = this.randomSelect(['专家', '爱好者', '达人', '星人', '党']);
            username = foodWord + foodSuffix;
            elementsUsed = [foodWord, foodSuffix];
          }
          break;

        case 'identity_elevation':
          const authority = this.randomSelect(this.elementLibrary.modifiers.权威级别);
          const behavior = this.randomSelect(this.elementLibrary.actions.日常行为);
          const suffix = this.randomSelect(['官', '师', '专家', '大使', '代表', '委员', '顾问', '总监', '主任', '经理']);
          username = authority + behavior + suffix;
          elementsUsed = [authority, behavior, suffix];
          break;

        case 'tech_expression':
          const lifeConcept = this.randomSelect(['人生', '梦想', '快乐', '悲伤', '爱情', '友情', '工作', '学习', '生活', '未来']);
          const techTerm = this.randomSelect(['正在缓冲', '连接超时', '服务器宕机', '数据库损坏', '404未找到', '503不可用', '系统维护', '正在加载']);
          username = lifeConcept + techTerm;
          elementsUsed = [lifeConcept, techTerm];
          break;

        case 'homophone_creative':
          const homophoneOptions = [
            { original: '知识就是力量', replacement: '芝士就是力量' },
            { original: '心想事成', replacement: '薪想事成' },
            { original: '一见钟情', replacement: '一见粽情' },
            { original: '马到成功', replacement: '码到成功' },
            { original: '有压力很大', replacement: '有鸭梨很大' },
            { original: '没心没肺', replacement: '莓心没肺' },
            { original: '无恶不作', replacement: '无饿不作' },
            { original: '天马行空', replacement: '天码行空' }
          ];
          const homophone = this.randomSelect(homophoneOptions);
          username = homophone.replacement;
          elementsUsed = [homophone.original, '→', homophone.replacement];
          break;

        case 'temporal_displacement':
          const ancient = this.randomSelect(this.elementLibrary.subjects.古代人物);
          const modern = this.randomSelect([
            ...this.elementLibrary.actions.网络行为,
            ...this.elementLibrary.actions.现代生活
          ]);
          username = ancient + modern;
          elementsUsed = [ancient, modern];
          break;

        default:
          username = '生成失败';
          elementsUsed = [];
      }

      // 简化的质量评估
      const novelty = 0.8 + Math.random() * 0.2;
      const relevance = 0.75 + Math.random() * 0.25;
      const comprehensibility = 0.7 + Math.random() * 0.3;
      const memorability = 0.65 + Math.random() * 0.35;
      const overall_score = novelty * 0.3 + relevance * 0.25 + comprehensibility * 0.25 + memorability * 0.2;

      return {
        username,
        pattern: patternId,
        elements_used: elementsUsed,
        creativity_assessment: {
          novelty,
          relevance,
          comprehensibility,
          memorability,
          overall_score
        }
      };

    } catch (error) {
      console.error('生成错误:', error);
      return null;
    }
  }

  testCombination(theme, pattern, count = 20) {
    const results = [];
    for (let i = 0; i < count; i++) {
      const result = this.generateByPattern(pattern, theme);
      if (result) {
        results.push(result);
      }
    }
    return results;
  }
}

// 执行测试
const engine = new TestV5Engine();

console.log('🚀 开始测试不同组合的生成效果...');
console.log('📊 每个组合生成20个用户名进行评估');
console.log('');

testCombinations.forEach((combo, index) => {
  console.log((index + 1) + '. 测试组合: ' + combo.description);
  console.log('   主题: ' + combo.theme + ' | 模式: ' + combo.pattern);
  console.log('   ----------------------------------------');
  
  const results = engine.testCombination(combo.theme, combo.pattern, 20);
  
  if (results.length > 0) {
    // 统计分析
    const scores = results.map(r => r.creativity_assessment.overall_score);
    const avgScore = scores.reduce((sum, score) => sum + score, 0) / scores.length;
    const maxScore = Math.max(...scores);
    const minScore = Math.min(...scores);
    
    const noveltyScores = results.map(r => r.creativity_assessment.novelty);
    const avgNovelty = noveltyScores.reduce((sum, score) => sum + score, 0) / noveltyScores.length;
    
    const relevanceScores = results.map(r => r.creativity_assessment.relevance);
    const avgRelevance = relevanceScores.reduce((sum, score) => sum + score, 0) / relevanceScores.length;
    
    // 显示前10个结果
    console.log('   📝 生成结果 (前10个):');
    results.slice(0, 10).forEach((result, i) => {
      console.log('   ' + (i + 1) + '. ' + result.username + ' (质量: ' + (result.creativity_assessment.overall_score * 100).toFixed(1) + '%)');
    });
    
    console.log('');
    console.log('   📊 质量统计:');
    console.log('   平均质量: ' + (avgScore * 100).toFixed(1) + '%');
    console.log('   最高质量: ' + (maxScore * 100).toFixed(1) + '%');
    console.log('   最低质量: ' + (minScore * 100).toFixed(1) + '%');
    console.log('   平均新颖性: ' + (avgNovelty * 100).toFixed(1) + '%');
    console.log('   平均相关性: ' + (avgRelevance * 100).toFixed(1) + '%');
    
    // 质量等级分布
    const excellent = results.filter(r => r.creativity_assessment.overall_score >= 0.9).length;
    const good = results.filter(r => r.creativity_assessment.overall_score >= 0.8 && r.creativity_assessment.overall_score < 0.9).length;
    const average = results.filter(r => r.creativity_assessment.overall_score >= 0.7 && r.creativity_assessment.overall_score < 0.8).length;
    const poor = results.filter(r => r.creativity_assessment.overall_score < 0.7).length;
    
    console.log('   📈 质量分布:');
    console.log('   优秀(90%+): ' + excellent + '个 (' + (excellent/20*100).toFixed(0) + '%)');
    console.log('   良好(80-89%): ' + good + '个 (' + (good/20*100).toFixed(0) + '%)');
    console.log('   一般(70-79%): ' + average + '个 (' + (average/20*100).toFixed(0) + '%)');
    console.log('   需改进(<70%): ' + poor + '个 (' + (poor/20*100).toFixed(0) + '%)');
    
  } else {
    console.log('   ❌ 生成失败');
  }
  
  console.log('');
  console.log('   =====================================');
  console.log('');
});

console.log('🎉 测试完成！');
