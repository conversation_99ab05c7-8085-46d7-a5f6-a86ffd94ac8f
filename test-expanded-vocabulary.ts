/**
 * 扩展语素库测试脚本
 * 集成词汇扩展引擎的词汇，测试生成效果
 */

import { VocabularyExpansionEngine } from './server/api/vocabulary/vocabulary-expansion-engine'

// 扩展的V5引擎类 - 集成扩展语素库
class ExpandedV5Engine {
  private elementLibrary: any
  private expandedVocabulary: Map<string, any> = new Map()
  private expansionEngine: VocabularyExpansionEngine

  constructor() {
    this.expansionEngine = new VocabularyExpansionEngine()
    this.elementLibrary = this.buildBaseElementLibrary()
  }

  /**
   * 初始化扩展引擎并加载扩展词汇
   */
  async initialize() {
    console.log('🚀 初始化扩展语素库测试引擎...')
    
    // 初始化词汇扩展引擎
    await this.expansionEngine.initialize()
    
    // 执行词汇扩展
    console.log('📚 执行第一阶段扩展...')
    const phase1Result = await this.expansionEngine.executePhase1Expansion()
    console.log(`✅ 第一阶段完成，新增词汇: ${phase1Result.added_count}个`)
    
    console.log('🚀 执行大规模扩展...')
    const massiveResult = await this.expansionEngine.executeMassiveExpansion()
    console.log(`✅ 大规模扩展完成，总词汇量: ${massiveResult.total_count}个`)
    
    // 获取扩展后的词汇库
    const expandedWords = this.expansionEngine.exportVocabulary()
    console.log(`📊 扩展词汇库统计: ${expandedWords.length}个词汇`)
    
    // 按类别整理扩展词汇
    this.organizeExpandedVocabulary(expandedWords)
    
    // 更新元素库
    this.updateElementLibrary()
    
    console.log('✅ 扩展语素库初始化完成')
  }

  /**
   * 构建基础元素库
   */
  private buildBaseElementLibrary() {
    return {
      subjects: {
        古代人物: ['贫僧', '道士', '书生', '侠客', '状元', '举人', '秀才', '太守', '县令', '师爷'],
        现代职业: ['程序员', '设计师', '产品经理', '运营', '策划', '文案', '测试', '架构师', '前端', '后端'],
        网络身份: ['UP主', '主播', '网红', '博主', '自媒体', '数字游民', '斜杠青年', 'KOL', '网络达人', '内容创作者'],
        情绪状态: ['社恐', '社牛', 'emo', '佛系', '躺平', '内卷', '摆烂', '破防', '上头', '下头'],
        食物关联: ['奶茶', '咖啡', '火锅', '烧烤', '甜品', '蛋糕', '面包', '寿司', '拉面', '披萨']
      },
      actions: {
        日常行为: ['吃饭', '睡觉', '工作', '学习', '运动', '购物', '聊天', '游戏', '看剧', '听歌', '阅读', '写作'],
        网络行为: ['刷屏', '点赞', '转发', '评论', '私信', '直播', '连麦', '开黑', '上分', '氪金', '肝游戏', '追番']
      },
      modifiers: {
        权威级别: ['资深', '专业', '高级', '首席', '顶级', '权威', '官方', '认证', '特级', '超级', '终极', '至尊'],
        程度强化: ['超级', '极度', '非常', '特别', '格外', '异常', '十分', '相当', '颇为', '略微', '稍微', '有点']
      },
      connectors: {
        对比转折: ['但', '却', '然而', '不过', '可是', '只是', '偏偏', '反而', '竟然', '居然', '竟', '却又']
      }
    }
  }

  /**
   * 整理扩展词汇到分类结构中
   */
  private organizeExpandedVocabulary(expandedWords: any[]) {
    const categories = {
      emotions: [],
      professions: [],
      characteristics: [],
      traditional: [],
      popular: [],
      trend: [],
      subculture: []
    }

    expandedWords.forEach(entry => {
      if (!categories[entry.category]) {
        categories[entry.category] = []
      }
      categories[entry.category].push(entry.word)
    })

    this.expandedVocabulary.set('categories', categories)
    
    // 统计信息
    console.log('📊 扩展词汇分类统计:')
    Object.entries(categories).forEach(([category, words]) => {
      console.log(`  ${category}: ${words.length}个词汇`)
    })
  }

  /**
   * 更新元素库，集成扩展词汇
   */
  private updateElementLibrary() {
    const categories = this.expandedVocabulary.get('categories')
    
    // 扩展情绪状态词汇
    if (categories.emotions && categories.emotions.length > 0) {
      this.elementLibrary.subjects.情绪状态 = [
        ...this.elementLibrary.subjects.情绪状态,
        ...categories.emotions.slice(0, 50) // 取前50个高质量词汇
      ]
    }

    // 扩展职业词汇
    if (categories.professions && categories.professions.length > 0) {
      this.elementLibrary.subjects.现代职业 = [
        ...this.elementLibrary.subjects.现代职业,
        ...categories.professions.slice(0, 30) // 取前30个高质量词汇
      ]
    }

    // 添加新的传统文化类别
    if (categories.traditional && categories.traditional.length > 0) {
      this.elementLibrary.subjects.传统文化 = categories.traditional.slice(0, 40)
    }

    // 添加新的潮流文化类别
    if (categories.trend && categories.trend.length > 0) {
      this.elementLibrary.subjects.潮流文化 = categories.trend.slice(0, 30)
    }

    console.log('✅ 元素库更新完成，新增类别和词汇')
  }

  /**
   * 使用扩展语素库生成用户名
   */
  generateWithExpandedVocabulary(pattern: string): any {
    let username = ''
    let elementsUsed: string[] = []

    try {
      switch (pattern) {
        case 'emotion_state_expanded':
          const emotionWord = this.randomSelect(this.elementLibrary.subjects.情绪状态)
          const emotionSuffix = this.randomSelect(['专家', '代表', '选手', '患者', '星人', '达人', '爱好者'])
          username = `${emotionWord}${emotionSuffix}`
          elementsUsed = [emotionWord, emotionSuffix]
          break

        case 'traditional_modern_fusion':
          const traditional = this.randomSelect(this.elementLibrary.subjects.传统文化 || ['文士', '墨客', '雅士'])
          const modern = this.randomSelect(this.elementLibrary.subjects.现代职业)
          username = `${traditional}${modern}`
          elementsUsed = [traditional, modern]
          break

        case 'trend_culture_expression':
          const trendWord = this.randomSelect(this.elementLibrary.subjects.潮流文化 || ['国潮', '元宇宙', '破圈'])
          const suffix = this.randomSelect(['爱好者', '达人', '专家', '玩家', '体验师'])
          username = `${trendWord}${suffix}`
          elementsUsed = [trendWord, suffix]
          break

        case 'expanded_profession':
          const authority = this.randomSelect(this.elementLibrary.modifiers.权威级别)
          const profession = this.randomSelect(this.elementLibrary.subjects.现代职业)
          username = `${authority}${profession}`
          elementsUsed = [authority, profession]
          break

        case 'cultural_contradiction':
          const ancient = this.randomSelect(this.elementLibrary.subjects.传统文化 || ['诗仙', '词圣', '文人'])
          const connector = this.randomSelect(this.elementLibrary.connectors.对比转折)
          const modernTech = this.randomSelect(['程序猿', '设计狮', '产品汪'])
          username = `${ancient}${connector}${modernTech}`
          elementsUsed = [ancient, connector, modernTech]
          break

        default:
          // 使用原有模式
          return this.generateOriginalPattern(pattern)
      }

      return {
        username,
        pattern,
        elements_used: elementsUsed,
        source: 'expanded_vocabulary',
        quality_score: this.calculateQualityScore(username),
        vocabulary_stats: this.getVocabularyStats()
      }

    } catch (error) {
      console.error(`❌ 扩展生成错误:`, error)
      return null
    }
  }

  /**
   * 生成原有模式（兼容性）
   */
  private generateOriginalPattern(pattern: string): any {
    // 这里可以调用原有的V5引擎逻辑
    return {
      username: '兼容模式生成',
      pattern,
      elements_used: [],
      source: 'original_vocabulary'
    }
  }

  /**
   * 随机选择元素
   */
  private randomSelect(array: any[]): any {
    return array[Math.floor(Math.random() * array.length)]
  }

  /**
   * 计算质量分数
   */
  private calculateQualityScore(username: string): number {
    let score = 0.7 // 基础分

    // 长度评分
    if (username.length >= 3 && username.length <= 6) score += 0.1
    
    // 创意评分
    if (username.includes('专家') || username.includes('达人')) score += 0.05
    
    // 文化融合评分
    const traditionalChars = ['仙', '圣', '士', '客', '雅', '文', '诗', '词']
    const hasTraditional = traditionalChars.some(char => username.includes(char))
    if (hasTraditional) score += 0.1

    return Math.min(1.0, score)
  }

  /**
   * 获取词汇库统计信息
   */
  private getVocabularyStats() {
    const stats = this.expansionEngine.getVocabularyStats()
    return {
      total_vocabulary: stats.total_count,
      expansion_progress: stats.progress,
      average_quality: stats.average_quality,
      cultural_balance: stats.cultural_balance
    }
  }

  /**
   * 批量测试生成效果
   */
  async testGenerationEffects() {
    console.log('\n🧪 开始测试扩展语素库生成效果...')
    
    const testPatterns = [
      'emotion_state_expanded',
      'traditional_modern_fusion', 
      'trend_culture_expression',
      'expanded_profession',
      'cultural_contradiction'
    ]

    const results = []

    for (const pattern of testPatterns) {
      console.log(`\n📝 测试模式: ${pattern}`)
      
      for (let i = 0; i < 5; i++) {
        const result = this.generateWithExpandedVocabulary(pattern)
        if (result) {
          results.push(result)
          console.log(`  ${i + 1}. ${result.username} (质量: ${(result.quality_score * 100).toFixed(0)}%)`)
        }
      }
    }

    // 生成测试报告
    this.generateTestReport(results)
    
    return results
  }

  /**
   * 生成测试报告
   */
  private generateTestReport(results: any[]) {
    console.log('\n📊 测试报告:')
    console.log('=' .repeat(50))
    
    const avgQuality = results.reduce((sum, r) => sum + r.quality_score, 0) / results.length
    console.log(`平均质量分数: ${(avgQuality * 100).toFixed(1)}%`)
    
    const patternStats = {}
    results.forEach(r => {
      if (!patternStats[r.pattern]) {
        patternStats[r.pattern] = { count: 0, totalQuality: 0 }
      }
      patternStats[r.pattern].count++
      patternStats[r.pattern].totalQuality += r.quality_score
    })
    
    console.log('\n各模式表现:')
    Object.entries(patternStats).forEach(([pattern, stats]: [string, any]) => {
      const avgPatternQuality = stats.totalQuality / stats.count
      console.log(`  ${pattern}: ${(avgPatternQuality * 100).toFixed(1)}% (${stats.count}个样本)`)
    })

    if (results.length > 0 && results[0].vocabulary_stats) {
      const vocabStats = results[0].vocabulary_stats
      console.log('\n词汇库状态:')
      console.log(`  总词汇量: ${vocabStats.total_vocabulary}`)
      console.log(`  扩展进度: ${(vocabStats.expansion_progress * 100).toFixed(1)}%`)
      console.log(`  平均质量: ${(vocabStats.average_quality * 100).toFixed(1)}%`)
    }
  }
}

/**
 * 主测试函数
 */
async function runExpandedVocabularyTest() {
  try {
    console.log('🎯 扩展语素库生成效果测试')
    console.log('=' .repeat(50))
    
    // 创建扩展引擎实例
    const expandedEngine = new ExpandedV5Engine()
    
    // 初始化扩展语素库
    await expandedEngine.initialize()
    
    // 测试生成效果
    const testResults = await expandedEngine.testGenerationEffects()
    
    console.log('\n✅ 测试完成！')
    console.log(`总共生成了 ${testResults.length} 个用户名样本`)
    
    // 展示最佳结果
    const bestResults = testResults
      .sort((a, b) => b.quality_score - a.quality_score)
      .slice(0, 10)
    
    console.log('\n🏆 最佳生成结果 (Top 10):')
    bestResults.forEach((result, index) => {
      console.log(`  ${index + 1}. ${result.username} (${result.pattern}, 质量: ${(result.quality_score * 100).toFixed(0)}%)`)
    })
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error)
  }
}

// 导出测试函数
export { ExpandedV5Engine, runExpandedVocabularyTest }

// 如果直接运行此文件，执行测试
if (require.main === module) {
  runExpandedVocabularyTest()
}
