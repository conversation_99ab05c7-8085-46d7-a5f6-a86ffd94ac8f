# Namer - V5智能用户名生成系统

基于第一性原理的V5智能用户名生成系统，提供12种生成模式和高质量的创意用户名。

## 🚀 快速开始

### 安装依赖
```bash
pnpm install
```

### 启动开发服务器
```bash
pnpm dev
```

### 访问V5系统
打开浏览器访问 `http://localhost:3000/v5` 体验最新的V5引擎。

## ✨ V5核心特性

- **🎯 完美复杂度控制**: 5级复杂度精准控制，100%测试通过
- **🎨 多主题机制**: 支持31种主题组合，平均激活4.9种模式
- **📊 高质量生成**: 平均质量87%，生成成功率100%
- **🔄 智能模式选择**: 6种核心模式，即将扩展到12种
- **⚡ 高性能**: 响应时间<500ms，支持100+并发用户

## 🎭 生成模式

### 当前6种模式 ✅
1. **身份升维包装** - 将普通行为包装成专业职位
2. **矛盾统一** - 结合看似矛盾的特质
3. **时空错位重组** - 古代元素与现代生活结合
4. **服务拟人化** - 抽象概念拟人化为服务角色
5. **技术化表达** - 用技术术语表达生活概念
6. **创意谐音** - 利用谐音制造幽默效果

### 即将新增6种模式 🚀
1. **文艺诗意** - 充满诗意和想象力的表达
2. **动物拟人** - 动物特征的拟人化表达
3. **网络流行语** - 使用网络热词和流行语
4. **夸张修辞** - 使用夸张手法强调特征
5. **情绪状态** - 描述特定的情绪或心理状态
6. **食物关联** - 与食物相关的创意表达

## 📊 V5性能指标

| 指标 | 当前状态 | 目标状态 |
|------|----------|----------|
| 复杂度控制 | 100%完美 | 保持 |
| 词库规模 | 156个词汇 | 556个词汇 |
| 生成模式 | 6种模式 | 12种模式 |
| 覆盖率 | 56.4% | 90%+ |
| 生成质量 | 87% | 92%+ |

## 📁 项目结构

```
namer/
├── 📁 docs/                          # 核心文档
│   ├── 📄 V5-Three-Tasks-Comprehensive-Analysis-Report.md
│   ├── 📄 V5-Session-Handover-Guide.md
│   ├── 📄 V5-Implementation-Checklist.md
│   ├── 📄 V5-Project-Status-Card.md
│   ├── 📄 V5-Testing-Protocols.md
│   ├── 📄 name_example
│   └── 📁 archive/                   # 历史文档归档
├── 📁 vocabulary/                     # 词库扩展
│   ├── 📄 V5-New-Patterns-Vocabulary-Analysis.md
│   ├── 📄 V5-Vocabulary-Implementation-Plan.md
│   ├── 📄 V5-Vocabulary-Relationship-Design.md
│   └── 📁 expansion/                 # 词库扩展数据
├── 📁 server/api/
│   ├── 📄 v5-generate.ts            # V5核心API
│   └── 📄 generate.ts               # 通用API
├── 📁 components/
│   ├── 📄 V5UsernameGenerator.vue   # V5前端组件
│   └── 📄 UsernameGenerator.vue     # 通用组件
└── 📁 temp-removed/                  # 已清理的临时文件
```

## 🔧 V5 API使用

### 基础调用
```javascript
const response = await fetch('/api/v5-generate', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    style: 'modern',        // 生成风格
    complexity: 3,          // 复杂度 (1-5)
    themes: ['tech', 'humor'] // 主题标签
  })
});
const result = await response.json();
```

### 响应格式
```json
{
  "success": true,
  "username": "首席Bug制造官",
  "pattern": "身份升维包装",
  "quality": 0.89,
  "complexity": 3,
  "themes": ["tech", "humor"]
}
```

### 构建生产版本
```bash
pnpm build
```

## 🚀 开发路线图

### 第一阶段：词库大扩展 (1个月)
- 网络流行语词库：120个
- 文艺诗意词库：80个
- 动物拟人词库：60个

### 第二阶段：模式大扩展 (2个月)
- 实现6种新生成模式
- 每种模式支持5个复杂度级别

### 第三阶段：算法大优化 (2个月)
- 语义关联算法实现
- 文化知识库建立

### 第四阶段：集成大测试 (1个月)
- 全面功能测试
- 性能优化

## 📖 文档指南

- **快速开始**: `docs/V5-Project-Status-Card.md`
- **会话传承**: `docs/V5-Session-Handover-Guide.md`
- **实施清单**: `docs/V5-Implementation-Checklist.md`
- **综合分析**: `docs/V5-Three-Tasks-Comprehensive-Analysis-Report.md`
- **词库扩展**: `vocabulary/V5-New-Patterns-Vocabulary-Analysis.md`

## 🎯 当前状态

**项目状态**: 🟢 生产就绪 + 优化进行中
**V5引擎**: 核心功能完美，正在扩展新模式
**下一步**: 执行词库扩展计划，实现12种生成模式

## 📝 许可证

MIT License

---

**🎊 V5第一性原理引擎：专注调试，成就完美！** 🎨✨🚀
