<script setup lang="ts">
import { ref, computed } from 'vue'

// 生成状态
const isGenerating = ref(false)
const results = ref<any[]>([])
const error = ref('')
const showAdvanced = ref(false)

// 用户输入 - 设置智能默认值
const language = ref('zh')
const style = ref('modern')
const themes = ref(['生活'])
const complexity = ref(3)
const selectedPattern = ref('')

// 可用选项
const styleOptions = [
  { value: 'modern', label: '现代风格', description: '时尚前卫，符合当代审美' },
  { value: 'cool', label: '酷炫风格', description: '个性张扬，独特有型' },
  { value: 'playful', label: '活泼风格', description: '轻松有趣，充满活力' },
  { value: 'traditional', label: '传统风格', description: '经典雅致，文化底蕴' },
  { value: 'elegant', label: '优雅风格', description: '精致高贵，品味独特' },
  { value: 'emotional', label: '情感风格', description: '真实表达，情感共鸣' },
  { value: 'lifestyle', label: '生活风格', description: '贴近生活，温暖治愈' }
]

const themeOptions = [
  { value: '科技', label: '科技', icon: '💻' },
  { value: '职场', label: '职场', icon: '💼' },
  { value: '幽默', label: '幽默', icon: '😄' },
  { value: '创意', label: '创意', icon: '🎨' },
  { value: '文化', label: '文化', icon: '📚' },
  { value: '情感', label: '情感', icon: '💭' },
  { value: '美食', label: '美食', icon: '🍜' },
  { value: '生活', label: '生活', icon: '🌟' }
]

const patternOptions = [
  { value: '', label: '智能选择', description: '根据风格和主题自动选择最佳模式' },
  { value: 'identity_elevation', label: '身份升维包装', description: '将日常行为包装为权威职位' },
  { value: 'contradiction_unity', label: '矛盾统一', description: '将对立特质巧妙融合' },
  { value: 'temporal_displacement', label: '时空错位重组', description: '将不同时空的元素创意组合' },
  { value: 'service_personification', label: '服务拟人化', description: '将抽象概念具象化为服务角色' },
  { value: 'tech_expression', label: '技术化表达', description: '用技术术语表达生活状态' },

  { value: 'emotion_state', label: '情绪状态模式', description: '基于现代人情绪状态的用户名生成' },
  { value: 'food_association', label: '食物关联模式', description: '基于食物文化的用户名生成' }
]

// 复杂度描述
const complexityDescription = computed(() => {
  const descriptions = {
    1: '简单直接，易于理解',
    2: '轻度创意，朗朗上口',
    3: '中等创意，平衡有趣',
    4: '高度创意，富有内涵',
    5: '极致创意，深度思考'
  }
  return descriptions[complexity.value as keyof typeof descriptions] || ''
})

// 主题切换
const toggleTheme = (theme: string) => {
  const index = themes.value.indexOf(theme)
  if (index > -1) {
    themes.value.splice(index, 1)
  } else {
    themes.value.push(theme)
  }
}

// 切换高级配置
const toggleAdvanced = () => {
  showAdvanced.value = !showAdvanced.value
}

// 生成用户名
const generateUsernames = async () => {
  if (isGenerating.value) return

  isGenerating.value = true
  error.value = ''
  results.value = []

  try {
    const response = await $fetch('/api/v5-generate', {
      method: 'POST',
      body: {
        language: language.value,
        style: style.value,
        themes: themes.value,
        complexity: complexity.value,
        count: 1, // 固定为1个
        pattern: selectedPattern.value || null
      }
    })

    if (response.success) {
      results.value = response.results
      console.log('生成成功:', response)
    } else {
      error.value = response.error || '生成失败'
    }
  } catch (err) {
    console.error('生成错误:', err)
    error.value = '网络错误，请重试'
  } finally {
    isGenerating.value = false
  }
}

// 复制用户名
const copyUsername = async (username: string) => {
  try {
    await navigator.clipboard.writeText(username)
    // 可以添加复制成功的提示
  } catch (err) {
    console.error('复制失败:', err)
  }
}

// 质量等级颜色
const getQualityColor = (score: number) => {
  if (score >= 0.9) return '#10b981' // 绿色
  if (score >= 0.8) return '#3b82f6' // 蓝色
  if (score >= 0.7) return '#f59e0b' // 黄色
  return '#ef4444' // 红色
}

// 质量等级文字
const getQualityLabel = (score: number) => {
  if (score >= 0.9) return '优秀'
  if (score >= 0.8) return '良好'
  if (score >= 0.7) return '一般'
  return '需改进'
}
</script>

<template>
  <div class="v5-generator">
    <div class="generator-container">
      <!-- 简化的生成区域 -->
      <div class="simple-generate-section">
        <!-- 用户友好的介绍 -->
        <div class="intro-section">
          <h2 class="intro-title">发现你的专属中文用户名</h2>
          <p class="intro-subtitle">3秒获得有趣又有内涵的网名，让你在网络世界中独一无二</p>
          <div class="intro-features">
            <span class="feature-tag">✨ 创意无限</span>
            <span class="feature-tag">🎭 文化内涵</span>
            <span class="feature-tag">⚡ 即时生成</span>
          </div>
        </div>

        <!-- 生成按钮 -->
        <button
          @click="generateUsernames"
          :disabled="isGenerating"
          class="main-generate-button"
        >
          <span v-if="isGenerating" class="loading-spinner"></span>
          <span v-else class="generate-icon">✨</span>
          {{ isGenerating ? '创造中...' : '开始创造我的专属用户名' }}
        </button>

        <!-- 高级配置切换 -->
        <button @click="toggleAdvanced" class="advanced-toggle">
          <span class="toggle-icon">⚙️</span>
          {{ showAdvanced ? '收起个性化选项' : '更多个性化选项' }}
          <span class="arrow-icon" :class="{ 'rotated': showAdvanced }">▼</span>
        </button>
      </div>

      <!-- 高级配置区域 -->
      <div v-if="showAdvanced" class="advanced-settings">
        <div class="settings-section">
          <!-- 风格选择 -->
          <div class="setting-group">
            <label class="setting-label">生成风格</label>
            <div class="style-options">
              <div
                v-for="option in styleOptions"
                :key="option.value"
                class="style-option"
                :class="{ 'active': style === option.value }"
                @click="style = option.value"
              >
                <div class="style-name">{{ option.label }}</div>
                <div class="style-desc">{{ option.description }}</div>
              </div>
            </div>
          </div>

          <!-- 主题选择 -->
          <div class="setting-group">
            <label class="setting-label">主题标签</label>
            <div class="theme-options">
              <div
                v-for="theme in themeOptions"
                :key="theme.value"
                class="theme-option"
                :class="{ 'active': themes.includes(theme.value) }"
                @click="toggleTheme(theme.value)"
              >
                <span class="theme-icon">{{ theme.icon }}</span>
                <span class="theme-label">{{ theme.label }}</span>
              </div>
            </div>
          </div>

          <!-- 生成模式 -->
          <div class="setting-group">
            <label class="setting-label">生成模式</label>
            <select v-model="selectedPattern" class="pattern-select">
              <option
                v-for="pattern in patternOptions"
                :key="pattern.value"
                :value="pattern.value"
              >
                {{ pattern.label }}
              </option>
            </select>
            <div class="pattern-description">
              {{ patternOptions.find(p => p.value === selectedPattern)?.description || '根据风格和主题自动选择最佳模式' }}
            </div>
          </div>

          <!-- 创意复杂度 -->
          <div class="setting-group">
            <label class="setting-label">创意复杂度: {{ complexity }}</label>
            <input
              v-model.number="complexity"
              type="range"
              min="1"
              max="5"
              class="complexity-slider"
            />
            <div class="complexity-desc">{{ complexityDescription }}</div>
          </div>
        </div>
      </div>

      <!-- 错误提示 -->
      <div v-if="error" class="error-message">
        ❌ {{ error }}
      </div>

      <!-- 生成结果 -->
      <div v-if="results.length > 0" class="results-section">
        <div class="results-grid">
          <div
            v-for="(result, index) in results"
            :key="index"
            class="result-card"
            @click="copyUsername(result.username)"
          >
            <div class="result-main">
              <div class="result-username">{{ result.username }}</div>
              <div class="result-hint">点击复制</div>
            </div>
            <div class="result-actions">
              <button @click.stop="copyUsername(result.username)" class="copy-btn">
                📋
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.v5-generator {
  max-width: 800px;
  margin: 0 auto;
  padding: 1rem;
}

.generator-container {
  background: white;
  border-radius: 16px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* 简化的生成区域 */
.simple-generate-section {
  padding: 3rem 2rem 2rem;
  text-align: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

/* 介绍区域 */
.intro-section {
  margin-bottom: 2.5rem;
}

.intro-title {
  font-size: 2.2rem;
  font-weight: 800;
  margin-bottom: 1rem;
  line-height: 1.2;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.intro-subtitle {
  font-size: 1.1rem;
  margin-bottom: 1.5rem;
  opacity: 0.95;
  line-height: 1.5;
  max-width: 500px;
  margin-left: auto;
  margin-right: auto;
}

.intro-features {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 0.75rem;
  margin-bottom: 0.5rem;
}

.feature-tag {
  background: rgba(255, 255, 255, 0.2);
  padding: 0.4rem 0.8rem;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 500;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.feature-tag:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
}

.main-generate-button {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
  padding: 1.2rem 3rem;
  border-radius: 50px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.main-generate-button:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
}

.main-generate-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.generate-icon {
  font-size: 1.3rem;
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.advanced-toggle {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 0.6rem 1.2rem;
  border-radius: 25px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  backdrop-filter: blur(10px);
}

.advanced-toggle:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

.toggle-icon {
  font-size: 1rem;
}

.arrow-icon {
  transition: transform 0.3s ease;
  font-size: 0.8rem;
}

.arrow-icon.rotated {
  transform: rotate(180deg);
}

/* 高级配置区域 */
.advanced-settings {
  background: #f8fafc;
  border-top: 1px solid #e2e8f0;
  animation: slideDown 0.3s ease;
}

@keyframes slideDown {
  from {
    opacity: 0;
    max-height: 0;
  }
  to {
    opacity: 1;
    max-height: 1000px;
  }
}

.settings-section {
  padding: 2rem;
}

.setting-group {
  margin-bottom: 2rem;
}

.setting-label {
  display: block;
  font-weight: 600;
  color: #374151;
  margin-bottom: 1rem;
  font-size: 1rem;
}

.style-options {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 0.75rem;
}

.style-option {
  padding: 0.75rem;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
  background: white;
}

.style-option:hover {
  border-color: #667eea;
  transform: translateY(-1px);
}

.style-option.active {
  border-color: #667eea;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.style-name {
  font-weight: 600;
  margin-bottom: 0.25rem;
  font-size: 0.9rem;
}

.style-desc {
  font-size: 0.8rem;
  opacity: 0.8;
}

.theme-options {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
}

.theme-option {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.6rem 1rem;
  border: 2px solid #e5e7eb;
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: white;
  font-size: 0.9rem;
}

.theme-option:hover {
  border-color: #667eea;
  transform: translateY(-1px);
}

.theme-option.active {
  border-color: #667eea;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.theme-icon {
  font-size: 1rem;
}

.theme-label {
  font-weight: 500;
}

.pattern-select {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 0.9rem;
  background: white;
  transition: border-color 0.3s ease;
}

.pattern-select:focus {
  outline: none;
  border-color: #667eea;
}

.pattern-description {
  margin-top: 0.5rem;
  font-size: 0.8rem;
  color: #6b7280;
  font-style: italic;
}

.complexity-slider {
  width: 100%;
  margin: 0.5rem 0;
  accent-color: #667eea;
}

.complexity-desc {
  font-size: 0.8rem;
  color: #6b7280;
  text-align: center;
}

.error-message {
  margin: 1rem 2rem;
  padding: 1rem;
  background: #fee2e2;
  color: #dc2626;
  border-radius: 8px;
  text-align: center;
}

.results-section {
  padding: 2rem;
  background: #f9fafb;
}

.results-grid {
  display: grid;
  gap: 1rem;
}

.result-card {
  background: white;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.result-card:hover {
  border-color: #667eea;
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(102, 126, 234, 0.2);
}

.result-main {
  flex: 1;
}

.result-username {
  font-size: 1.4rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.result-hint {
  font-size: 0.9rem;
  color: #9ca3af;
}

.result-actions {
  margin-left: 1rem;
}

.copy-btn {
  background: #f3f4f6;
  border: none;
  border-radius: 8px;
  padding: 0.75rem;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1.2rem;
}

.copy-btn:hover {
  background: #e5e7eb;
  transform: scale(1.1);
}

@media (max-width: 768px) {
  .v5-generator {
    padding: 0.5rem;
  }

  .simple-generate-section {
    padding: 2rem 1rem 1.5rem;
  }

  .intro-title {
    font-size: 1.8rem;
  }

  .intro-subtitle {
    font-size: 1rem;
  }

  .intro-features {
    gap: 0.5rem;
  }

  .feature-tag {
    font-size: 0.8rem;
    padding: 0.3rem 0.6rem;
  }

  .main-generate-button {
    padding: 1rem 2rem;
    font-size: 1rem;
  }

  .settings-section {
    padding: 1.5rem;
  }

  .style-options {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }

  .theme-options {
    justify-content: center;
    gap: 0.5rem;
  }

  .theme-option {
    padding: 0.5rem 0.8rem;
    font-size: 0.8rem;
  }

  .result-card {
    padding: 1rem;
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .result-actions {
    margin-left: 0;
    align-self: flex-end;
  }
}

/* 暗黑模式适配 */
:root.dark .generator-container {
  background: #374151;
}

:root.dark .advanced-settings {
  background: #4b5563;
  border-top-color: #6b7280;
}

:root.dark .setting-label {
  color: #f9fafb;
}

:root.dark .style-option,
:root.dark .theme-option {
  background: #4b5563;
  border-color: #6b7280;
  color: #f9fafb;
}

:root.dark .style-option:hover,
:root.dark .theme-option:hover {
  border-color: #667eea;
}

:root.dark .pattern-select {
  background: #4b5563;
  border-color: #6b7280;
  color: #f9fafb;
}

:root.dark .result-card {
  background: #4b5563;
  border-color: #6b7280;
}

:root.dark .result-username {
  color: #f9fafb;
}

:root.dark .result-hint {
  color: #d1d5db;
}

:root.dark .copy-btn {
  background: #6b7280;
  color: #f9fafb;
}

:root.dark .copy-btn:hover {
  background: #9ca3af;
}
</style>
