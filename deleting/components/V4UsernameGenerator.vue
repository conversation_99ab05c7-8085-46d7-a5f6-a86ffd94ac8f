<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'

const { t, locale } = useI18n()

// V4引擎专用状态
const language = ref('zh') // V4只支持中文
const style = ref('modern')
const themes = ref(['modern', 'tech'])
const complexity = ref(3)
const username = ref('')
const usernameList = ref<string[]>([])
const isCopied = ref(false)
const isLoading = ref(false)

// V4详细结果
const v4Details = ref<any>(null)
const showV4Details = ref(false)

// 风格选项
const styleOptions = [
  { value: 'modern', label: '现代风格', description: '时尚前卫，紧跟潮流' },
  { value: 'cool', label: '酷炫风格', description: '个性张扬，与众不同' },
  { value: 'playful', label: '玩味风格', description: '轻松有趣，充满创意' },
  { value: 'traditional', label: '传统风格', description: '文化底蕴，优雅内敛' }
]

// 主题选项
const themeOptions = [
  { value: 'modern', label: '现代生活' },
  { value: 'tech', label: '科技数码' },
  { value: 'workplace', label: '职场生活' },
  { value: 'humor', label: '幽默搞笑' },
  { value: 'creative', label: '创意文艺' },
  { value: 'culture', label: '传统文化' }
]

// 复杂度描述
const complexityDesc = computed(() => {
  const descriptions = {
    1: '简洁明了',
    2: '轻松有趣', 
    3: '创意十足',
    4: '深度内涵',
    5: '极致艺术'
  }
  return descriptions[complexity.value as keyof typeof descriptions] || '创意十足'
})

// 生成新用户名
async function generateNewUsername() {
  try {
    isLoading.value = true
    v4Details.value = null

    console.log('🎭 调用V4终极引擎API')
    
    const response = await $fetch('/api/v4-generate', {
      method: 'POST',
      body: {
        language: language.value,
        style: style.value,
        themes: themes.value,
        complexity: complexity.value,
        count: 1
      }
    })

    if (response.success && response.results.length > 0) {
      const result = response.results[0]
      username.value = result.username
      v4Details.value = result
      
      console.log(`✅ V4生成成功: ${username.value}`)
      console.log(`📊 质量评分: ${(result.interest_analysis.overall_score * 100).toFixed(1)}%`)
      console.log(`🎯 策略: ${result.strategy.name}`)
    } else {
      throw new Error(response.error || 'V4引擎生成失败')
    }
  } catch (error) {
    console.error('V4生成错误:', error)
    username.value = 'V4-error-' + Math.floor(Math.random() * 1000)
    v4Details.value = null
  } finally {
    isLoading.value = false
    if (username.value) usernameList.value.unshift(username.value)
  }
}

// 复制用户名到剪贴板
function copyToClipboard() {
  if (!username.value) return
  
  navigator.clipboard.writeText(username.value).then(() => {
    isCopied.value = true
    setTimeout(() => {
      isCopied.value = false
    }, 2000)
  }).catch(err => {
    console.error('Failed to copy username:', err)
  })
}

// 切换主题选择
function toggleTheme(theme: string) {
  const index = themes.value.indexOf(theme)
  if (index > -1) {
    themes.value.splice(index, 1)
  } else {
    themes.value.push(theme)
  }
}

// 监听参数变化自动生成
watch([style, themes, complexity], () => {
  if (themes.value.length > 0) {
    generateNewUsername()
  }
}, { deep: true })

// 组件加载时生成初始用户名
onMounted(() => {
  generateNewUsername()
})
</script>

<template>
  <div class="v4-generator-container">
    <div class="card v4-card">
      <div class="card-header">
        <h2 class="text-center v4-title">
          <span class="v4-badge">V4</span>
          终极有趣引擎
        </h2>
        <p class="v4-subtitle">基于深度"有趣"理论的智能用户名生成器</p>
      </div>
      
      <div class="card-body">
        <!-- 风格选择 -->
        <div class="option-section">
          <label class="option-label">
            <span class="label-icon">🎨</span>
            选择风格
          </label>
          <div class="style-grid">
            <div
              v-for="styleOption in styleOptions"
              :key="styleOption.value"
              class="style-card"
              :class="{ 'selected': style === styleOption.value }"
              @click="style = styleOption.value"
            >
              <div class="style-name">{{ styleOption.label }}</div>
              <div class="style-desc">{{ styleOption.description }}</div>
            </div>
          </div>
        </div>

        <!-- 主题选择 -->
        <div class="option-section">
          <label class="option-label">
            <span class="label-icon">🏷️</span>
            选择主题 ({{ themes.length }}/3)
          </label>
          <div class="theme-grid">
            <div
              v-for="themeOption in themeOptions"
              :key="themeOption.value"
              class="theme-tag"
              :class="{ 'selected': themes.includes(themeOption.value) }"
              @click="toggleTheme(themeOption.value)"
            >
              {{ themeOption.label }}
            </div>
          </div>
        </div>

        <!-- 复杂度选择 -->
        <div class="option-section">
          <label class="option-label">
            <span class="label-icon">⚡</span>
            创意复杂度 ({{ complexity }} - {{ complexityDesc }})
          </label>
          <div class="complexity-slider">
            <input
              type="range"
              v-model="complexity"
              min="1"
              max="5"
              class="range-slider v4-slider"
            />
            <div class="complexity-labels">
              <span>简洁</span>
              <span>创意</span>
              <span>深度</span>
              <span>艺术</span>
            </div>
          </div>
        </div>
        
        <!-- 生成结果 -->
        <div class="result-container">
          <div class="username-display v4-display">
            <div v-if="isLoading" class="loading-indicator">
              <div class="v4-spinner"></div>
              <span>V4引擎创作中...</span>
            </div>
            <span v-else class="username-text v4-username">{{ username }}</span>
            <button 
              @click="copyToClipboard" 
              class="copy-button v4-copy"
              :class="{ 'copied': isCopied }"
              :disabled="isLoading"
            >
              <span v-if="!isCopied">复制</span>
              <span v-else>已复制</span>
            </button>
          </div>
          
          <button
            @click="generateNewUsername"
            class="generate-button v4-generate"
            :disabled="isLoading || themes.length === 0"
          >
            <span v-if="isLoading">🎭 V4引擎创作中...</span>
            <span v-else>🚀 生成新用户名</span>
          </button>

          <!-- V4详细信息 -->
          <div v-if="v4Details" class="v4-details-container">
            <div class="v4-details-header">
              <div class="quality-display">
                <span class="quality-label">有趣度评分</span>
                <span class="quality-score">{{ (v4Details.interest_analysis.overall_score * 100).toFixed(1) }}%</span>
              </div>
              <div class="strategy-display">
                <span class="strategy-label">创意策略</span>
                <span class="strategy-name">{{ v4Details.strategy.name }}</span>
              </div>
              <button
                @click="showV4Details = !showV4Details"
                class="details-toggle v4-toggle"
                :class="{ 'expanded': showV4Details }"
              >
                {{ showV4Details ? '收起详情' : '查看详情' }}
              </button>
            </div>

            <div v-if="showV4Details" class="v4-details-content">
              <div class="explanation-section">
                <h4>🎯 创意解读</h4>
                <p class="explanation-text">{{ v4Details.explanation }}</p>
              </div>

              <div class="analysis-grid">
                <div class="analysis-item">
                  <span class="analysis-label">认知冲突</span>
                  <div class="analysis-bar">
                    <div class="bar-fill" :style="{ width: (v4Details.interest_analysis.cognitive_conflict * 100) + '%' }"></div>
                  </div>
                  <span class="analysis-value">{{ (v4Details.interest_analysis.cognitive_conflict * 100).toFixed(0) }}%</span>
                </div>
                
                <div class="analysis-item">
                  <span class="analysis-label">情感共鸣</span>
                  <div class="analysis-bar">
                    <div class="bar-fill" :style="{ width: (v4Details.interest_analysis.emotional_resonance * 100) + '%' }"></div>
                  </div>
                  <span class="analysis-value">{{ (v4Details.interest_analysis.emotional_resonance * 100).toFixed(0) }}%</span>
                </div>
                
                <div class="analysis-item">
                  <span class="analysis-label">文化共识</span>
                  <div class="analysis-bar">
                    <div class="bar-fill" :style="{ width: (v4Details.interest_analysis.cultural_consensus * 100) + '%' }"></div>
                  </div>
                  <span class="analysis-value">{{ (v4Details.interest_analysis.cultural_consensus * 100).toFixed(0) }}%</span>
                </div>
                
                <div class="analysis-item">
                  <span class="analysis-label">时代相关</span>
                  <div class="analysis-bar">
                    <div class="bar-fill" :style="{ width: (v4Details.interest_analysis.temporal_relevance * 100) + '%' }"></div>
                  </div>
                  <span class="analysis-value">{{ (v4Details.interest_analysis.temporal_relevance * 100).toFixed(0) }}%</span>
                </div>
              </div>

              <div class="cultural-elements">
                <h4>🎨 文化元素</h4>
                <div class="element-tags">
                  <span v-for="element in v4Details.cultural_elements" :key="element" class="element-tag">
                    {{ element }}
                  </span>
                </div>
              </div>

              <div class="psychological-appeal">
                <h4>💭 心理诉求</h4>
                <div class="appeal-tags">
                  <span v-for="appeal in v4Details.psychological_appeal" :key="appeal" class="appeal-tag">
                    {{ appeal }}
                  </span>
                </div>
              </div>
            </div>
          </div>

          <!-- 历史记录 -->
          <div v-if="usernameList.length > 1" class="history-section">
            <h4>📚 生成历史</h4>
            <div class="history-grid">
              <span
                v-for="(name, idx) in usernameList.slice(1, 6)"
                :key="idx"
                class="history-item"
                @click="username = name"
              >
                {{ name }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.v4-generator-container {
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
  padding: 1rem;
}

.v4-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  color: white;
}

.card-header {
  padding: 2rem;
  text-align: center;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.v4-title {
  font-size: 2.5rem;
  font-weight: 800;
  margin: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
}

.v4-badge {
  background: linear-gradient(45deg, #ff6b6b, #feca57);
  padding: 0.5rem 1rem;
  border-radius: 12px;
  font-size: 1.2rem;
  font-weight: 900;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.v4-subtitle {
  margin: 0.5rem 0 0 0;
  opacity: 0.9;
  font-size: 1.1rem;
}

.card-body {
  padding: 2rem;
  background: rgba(255, 255, 255, 0.95);
  color: #333;
}

.option-section {
  margin-bottom: 2rem;
}

.option-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 700;
  font-size: 1.1rem;
  margin-bottom: 1rem;
  color: #4a5568;
}

.label-icon {
  font-size: 1.2rem;
}

.style-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 1rem;
}

.style-card {
  padding: 1rem;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: white;
}

.style-card:hover {
  border-color: #667eea;
  transform: translateY(-2px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.style-card.selected {
  border-color: #667eea;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
}

.style-name {
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.style-desc {
  font-size: 0.9rem;
  opacity: 0.8;
}

.theme-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 0.8rem;
}

.theme-tag {
  padding: 0.6rem 1.2rem;
  border: 2px solid #e2e8f0;
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: white;
  font-weight: 500;
}

.theme-tag:hover {
  border-color: #667eea;
}

.theme-tag.selected {
  background: linear-gradient(45deg, #667eea, #764ba2);
  color: white;
  border-color: transparent;
}

.complexity-slider {
  margin-top: 1rem;
}

.v4-slider {
  width: 100%;
  height: 8px;
  border-radius: 4px;
  background: linear-gradient(to right, #667eea, #764ba2);
  outline: none;
  -webkit-appearance: none;
}

.v4-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: white;
  border: 3px solid #667eea;
  cursor: pointer;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.complexity-labels {
  display: flex;
  justify-content: space-between;
  margin-top: 0.5rem;
  font-size: 0.9rem;
  color: #666;
}

.v4-display {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  border: none;
  color: white;
  padding: 1.5rem;
  border-radius: 16px;
  margin-bottom: 1rem;
}

.v4-username {
  font-size: 1.5rem;
  font-weight: 700;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.v4-copy {
  background: rgba(255, 255, 255, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.3);
  color: white;
  backdrop-filter: blur(10px);
}

.v4-copy:hover {
  background: rgba(255, 255, 255, 0.3);
}

.v4-generate {
  width: 100%;
  padding: 1rem;
  background: linear-gradient(45deg, #667eea, #764ba2);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.v4-generate:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 16px rgba(102, 126, 234, 0.4);
}

.v4-details-container {
  margin-top: 2rem;
  background: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.v4-details-header {
  padding: 1.5rem;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
}

.quality-display, .strategy-display {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.quality-score, .strategy-name {
  font-size: 1.2rem;
  font-weight: 700;
}

.v4-toggle {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  cursor: pointer;
}

.v4-details-content {
  padding: 1.5rem;
}

.explanation-text {
  background: #f7fafc;
  padding: 1rem;
  border-radius: 8px;
  border-left: 4px solid #667eea;
  margin: 0;
}

.analysis-grid {
  display: grid;
  gap: 1rem;
  margin: 1.5rem 0;
}

.analysis-item {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.analysis-label {
  min-width: 80px;
  font-weight: 600;
  color: #4a5568;
}

.analysis-bar {
  flex: 1;
  height: 8px;
  background: #e2e8f0;
  border-radius: 4px;
  overflow: hidden;
}

.bar-fill {
  height: 100%;
  background: linear-gradient(45deg, #667eea, #764ba2);
  transition: width 0.5s ease;
}

.analysis-value {
  min-width: 40px;
  text-align: right;
  font-weight: 600;
  color: #667eea;
}

.element-tags, .appeal-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.element-tag, .appeal-tag {
  background: linear-gradient(45deg, #667eea, #764ba2);
  color: white;
  padding: 0.3rem 0.8rem;
  border-radius: 12px;
  font-size: 0.9rem;
  font-weight: 500;
}

.history-section {
  margin-top: 2rem;
  padding-top: 1rem;
  border-top: 1px solid #e2e8f0;
}

.history-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.history-item {
  background: #f7fafc;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid #e2e8f0;
}

.history-item:hover {
  background: #667eea;
  color: white;
}

.v4-spinner {
  width: 24px;
  height: 24px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-top: 3px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 0.5rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
}
</style>
