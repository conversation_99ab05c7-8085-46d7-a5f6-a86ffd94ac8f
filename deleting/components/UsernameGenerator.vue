<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { generateMeaningfulUsername, generateCulturalUsername, getLanguageSlotConfig } from '~/utils/generator'
import { generateV2ChineseUsername, generateV2ChineseUsernameWithDetails, isV2Supported } from '~/utils/v2-generator'
import type { V2GenerationResult } from '~/utils/v2-generator'

const { t, locale } = useI18n()

// 状态
const language = ref(locale.value)
const generatorType = ref('random')
const includeTrends = ref(true)
const username = ref('')
const usernameList = ref<string[]>([])
const isCopied = ref(false)
const isLoading = ref(false)

// V2系统相关状态
const useV2System = ref(true) // 默认启用V2系统
const v2Details = ref<V2GenerationResult | null>(null)
const showV2Details = ref(false)

// 计算槽位数配置
const slotConfig = computed(() => {
  return getLanguageSlotConfig(language.value)
})

// 计算当前语言的槽位数范围
const slotRange = computed(() => {
  return {
    min: slotConfig.value.min,
    max: slotConfig.value.max
  }
})

// 用户名槽位数，默认使用语言特定的默认槽位数
const slotCount = ref(slotConfig.value.default)

// 进度条样式，基于槽位数百分比生成线性渐变
const sliderStyle = computed(() => {
  const pct = ((slotCount.value - slotRange.value.min) / (slotRange.value.max - slotRange.value.min)) * 100
  return {
    background: `linear-gradient(to right, #833AB4 0%, #FD1D1D ${pct}%, #FCB045 ${pct}%, #e0e0e0 ${pct}%)`
  }
})

// 监听语言变化，更新槽位数范围
watch(language, (newLanguage) => {
  const config = getLanguageSlotConfig(newLanguage)
  // 调整槽位数到新语言的合法范围
  if (slotCount.value < config.min) {
    slotCount.value = config.min
  } else if (slotCount.value > config.max) {
    slotCount.value = config.max
  }
  generateNewUsername()
})

// 生成新用户名
async function generateNewUsername() {
  try {
    isLoading.value = true
    v2Details.value = null

    // 检查是否使用V2系统
    if (useV2System.value && isV2Supported(language.value)) {
      console.log('🚀 使用V2系统生成中文用户名')

      // 使用V2系统生成详细结果
      const v2Result = await generateV2ChineseUsernameWithDetails(
        slotCount.value,
        language.value,
        generatorType.value,
        includeTrends.value
      )

      if (v2Result) {
        username.value = v2Result.username
        v2Details.value = v2Result
        console.log(`✅ V2生成成功: ${username.value} (质量: ${v2Result.quality.toFixed(3)})`)
        console.log(`📝 解释: ${v2Result.explanation}`)
      } else {
        throw new Error('V2系统生成失败')
      }
    } else {
      // 使用原有系统
      console.log('📱 使用原有系统生成用户名')

      if (generatorType.value === 'cultural') {
        username.value = await generateCulturalUsername(
          language.value,
          undefined,
          {
            slot_count: slotCount.value,
            includeTrends: includeTrends.value
          }
        )
      } else {
        // 使用有意义的用户名生成器，传入槽位数而非长度
        username.value = await generateMeaningfulUsername(slotCount.value, language.value)
      }
    }
  } catch (error) {
    console.error('Error generating username:', error)
    username.value = 'error-' + Math.floor(Math.random() * 1000)
    v2Details.value = null
  } finally {
    isLoading.value = false
    if (username.value) usernameList.value.unshift(username.value)
  }
}

// 复制用户名到剪贴板
function copyToClipboard() {
  if (!username.value) return
  
  navigator.clipboard.writeText(username.value).then(() => {
    isCopied.value = true
    setTimeout(() => {
      isCopied.value = false
    }, 2000)
  }).catch(err => {
    console.error('Failed to copy username:', err)
  })
}

// 组件加载时生成初始用户名
onMounted(() => {
  generateNewUsername()
})
</script>

<template>
  <div class="username-generator-container">
    <div class="card instagram-card">
      <div class="card-header">
        <h2 class="text-center gradient-text">{{ $t('generator.title') }}</h2>
      </div>
      
      <div class="card-body">
        <div class="options-container">
          <div class="language-selector">
            <label for="language" class="option-label">{{ $t('generator.language') }}</label>
            <select id="language" v-model="language" class="select-input">
              <option value="en">English</option>
              <option value="zh">中文</option>
              <option value="ja">日本語</option>
              <option value="ru">Русский</option>
            </select>
          </div>
          
          <div class="generator-type">
            <label for="generatorType" class="option-label">{{ $t('generator.type') }}</label>
            <div class="radio-group">
              <label class="radio-label">
                <input type="radio" v-model="generatorType" value="random" />
                {{ $t('generator.random') }}
              </label>
              <label class="radio-label">
                <input type="radio" v-model="generatorType" value="cultural" />
                {{ $t('generator.cultural') }}
              </label>
            </div>
          </div>

          <!-- V2系统控制 -->
          <div class="v2-system-control" v-if="isV2Supported(language)">
            <label class="option-label">
              <span class="v2-badge">V2</span> 高级中文生成器
            </label>
            <div class="checkbox-group">
              <label class="checkbox-label">
                <input type="checkbox" v-model="useV2System" />
                启用V2智能生成系统
              </label>
            </div>
          </div>
          
          <div class="slot-slider">
            <label for="slotCount" class="option-label">
              {{ $t('generator.complexity') }} ({{ slotCount }})
            </label>
            <div class="slider-container">
              <input
                type="range"
                id="slotCount"
                v-model="slotCount"
                :min="slotRange.min"
                :max="slotRange.max"
                class="range-slider"
                :style="sliderStyle"
              />
              <div class="slot-labels">
                <span class="slot-label">{{ $t('generator.simple') }}</span>
                <span class="slot-label">{{ $t('generator.complex') }}</span>
              </div>
            </div>
          </div>
        </div>
        
        <div class="result-container">
          <div class="username-display">
            <div v-if="isLoading" class="loading-indicator">
              <div class="loading-spinner"></div>
            </div>
            <span v-else class="username-text">{{ username }}</span>
            <button 
              @click="copyToClipboard" 
              class="copy-button"
              :class="{ 'copied': isCopied }"
              :disabled="isLoading"
            >
              <span v-if="!isCopied">{{ $t('generator.copy') }}</span>
              <span v-else>{{ $t('generator.copied') }}</span>
            </button>
          </div>
          
          <button
            @click="generateNewUsername"
            class="generate-button"
            :disabled="isLoading"
          >
            <span v-if="isLoading">{{ $t('generator.generating') || 'Generating...' }}</span>
            <span v-else>{{ $t('generator.generate') }}</span>
          </button>

          <!-- V2详细信息显示 -->
          <div v-if="v2Details && useV2System" class="v2-details-container">
            <div class="v2-details-header">
              <span class="v2-badge">V2</span>
              <span class="quality-score">质量评分: {{ (v2Details.quality * 100).toFixed(1) }}%</span>
              <button
                @click="showV2Details = !showV2Details"
                class="details-toggle"
                :class="{ 'expanded': showV2Details }"
              >
                {{ showV2Details ? '收起详情' : '查看详情' }}
              </button>
            </div>

            <div v-if="showV2Details" class="v2-details-content">
              <div class="explanation">
                <h4>生成解释</h4>
                <p>{{ v2Details.explanation }}</p>
              </div>

              <div class="components">
                <h4>词汇组成</h4>
                <div class="component-list">
                  <span
                    v-for="(comp, idx) in v2Details.components"
                    :key="idx"
                    class="component-item"
                    :title="`词性: ${comp.pos}, 语义域: ${comp.domains.join(', ')}`"
                  >
                    {{ comp.word }}
                  </span>
                </div>
              </div>

              <div class="pattern-info">
                <h4>语法模式</h4>
                <p>{{ v2Details.pattern.name }} ({{ v2Details.pattern.structure.join(' + ') }})</p>
              </div>
            </div>
          </div>

          <ul class="history-list" v-if="usernameList.length">
            <li v-for="(name, idx) in usernameList" :key="idx">{{ name }}</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.username-generator-container {
  width: 100%;
  max-width: 600px;
  margin: 0 auto;
  padding: 1rem;
}

.instagram-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
}

.instagram-card:hover {
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.card-header {
  padding: 1.5rem 1rem;
  border-bottom: 1px solid #f0f0f0;
}

.gradient-text {
  background: linear-gradient(45deg, #833AB4, #FD1D1D, #FCB045);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  font-weight: 700;
  font-size: 1.8rem;
  margin: 0;
}

.card-body {
  padding: 1.5rem;
}

.options-container {
  display: flex;
  flex-direction: column;
  gap: 1.2rem;
  margin-bottom: 1.5rem;
}

.option-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: #262626;
}

.select-input {
  width: 100%;
  padding: 0.75rem;
  border-radius: 8px;
  border: 1px solid #dbdbdb;
  background-color: #fafafa;
  font-size: 1rem;
  transition: border-color 0.2s;
}

.select-input:focus {
  border-color: #833AB4;
  outline: none;
}

.radio-group {
  display: flex;
  gap: 1rem;
}

.radio-label, .checkbox-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
}

.radio-label input, .checkbox-label input {
  accent-color: #833AB4;
}

.slider-container {
  width: 100%;
}

.range-slider {
  width: 100%;
  height: 6px;
  border-radius: 3px;
  outline: none;
  -webkit-appearance: none;
}

.range-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: white;
  border: 2px solid #FD1D1D;
  cursor: pointer;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
}

.result-container {
  margin-top: 2rem;
}

.username-display {
  background: #fafafa;
  border: 1px solid #efefef;
  border-radius: 8px;
  padding: 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  min-height: 50px;
}

.username-text {
  font-size: 1.2rem;
  font-weight: 600;
  color: #262626;
  word-break: break-all;
}

.copy-button {
  background: transparent;
  border: 1px solid #dbdbdb;
  border-radius: 4px;
  padding: 0.5rem 1rem;
  cursor: pointer;
  transition: all 0.2s;
  font-weight: 600;
}

.copy-button:hover {
  background: #f0f0f0;
}

.copy-button.copied {
  background: #58D68D;
  color: white;
  border-color: #58D68D;
}

.generate-button {
  width: 100%;
  padding: 0.75rem;
  background: linear-gradient(45deg, #833AB4, #FD1D1D, #FCB045);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.generate-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.generate-button:active {
  transform: translateY(0);
}

.history-list {
  list-style: none;
  padding: 0;
  margin: 0;
  margin-top: 1rem;
}

.history-list li {
  padding: 0.5rem;
  border-bottom: 1px solid #efefef;
}

.history-list li:last-child {
  border-bottom: none;
}

/* 暗黑模式适配 */
:root.dark .instagram-card {
  background: #121212;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

:root.dark .card-header {
  border-bottom-color: #2a2a2a;
}

:root.dark .option-label {
  color: #e0e0e0;
}

:root.dark .select-input {
  background-color: #2a2a2a;
  border-color: #3a3a3a;
  color: #e0e0e0;
}

:root.dark .username-display {
  background: #2a2a2a;
  border-color: #3a3a3a;
}

:root.dark .username-text {
  color: #e0e0e0;
}

:root.dark .copy-button {
  border-color: #3a3a3a;
  color: #e0e0e0;
}

:root.dark .copy-button:hover {
  background: #3a3a3a;
}

.loading-indicator {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 40px;
  width: 100%;
}

.loading-spinner {
  width: 24px;
  height: 24px;
  border: 3px solid rgba(131, 58, 180, 0.2);
  border-radius: 50%;
  border-top-color: #833AB4;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.generate-button:disabled,
.copy-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

:root.dark .loading-spinner {
  border-color: rgba(131, 58, 180, 0.2);
  border-top-color: #60A5FA;
}

/* V2系统样式 */
.v2-system-control {
  padding: 1rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 8px;
  color: white;
}

.v2-badge {
  background: linear-gradient(45deg, #FF6B6B, #4ECDC4);
  color: white;
  padding: 0.2rem 0.5rem;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: bold;
  margin-right: 0.5rem;
}

.checkbox-group {
  margin-top: 0.5rem;
}

.checkbox-label {
  color: white;
  font-weight: 500;
}

.v2-details-container {
  margin-top: 1rem;
  border: 2px solid #667eea;
  border-radius: 8px;
  overflow: hidden;
}

.v2-details-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 0.75rem 1rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.quality-score {
  font-weight: 600;
  font-size: 0.9rem;
}

.details-toggle {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 4px;
  padding: 0.3rem 0.6rem;
  cursor: pointer;
  font-size: 0.8rem;
  transition: all 0.2s;
}

.details-toggle:hover {
  background: rgba(255, 255, 255, 0.3);
}

.details-toggle.expanded {
  background: rgba(255, 255, 255, 0.3);
}

.v2-details-content {
  padding: 1rem;
  background: #f8f9fa;
}

.v2-details-content h4 {
  margin: 0 0 0.5rem 0;
  color: #495057;
  font-size: 0.9rem;
  font-weight: 600;
}

.explanation p {
  margin: 0;
  color: #6c757d;
  line-height: 1.5;
  font-size: 0.9rem;
}

.components {
  margin-top: 1rem;
}

.component-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.component-item {
  background: #e9ecef;
  color: #495057;
  padding: 0.3rem 0.6rem;
  border-radius: 4px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: help;
  transition: all 0.2s;
}

.component-item:hover {
  background: #dee2e6;
  transform: translateY(-1px);
}

.pattern-info {
  margin-top: 1rem;
}

.pattern-info p {
  margin: 0;
  color: #6c757d;
  font-size: 0.9rem;
}

/* 暗黑模式适配 */
:root.dark .v2-system-control {
  background: linear-gradient(135deg, #4a5568 0%, #2d3748 100%);
}

:root.dark .v2-details-header {
  background: linear-gradient(135deg, #4a5568 0%, #2d3748 100%);
}

:root.dark .v2-details-content {
  background: #2a2a2a;
}

:root.dark .v2-details-content h4 {
  color: #e0e0e0;
}

:root.dark .explanation p,
:root.dark .pattern-info p {
  color: #a0a0a0;
}

:root.dark .component-item {
  background: #3a3a3a;
  color: #e0e0e0;
}

:root.dark .component-item:hover {
  background: #4a4a4a;
}
</style>