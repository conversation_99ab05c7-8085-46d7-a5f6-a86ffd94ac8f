/**
 * V5第一性原理引擎API接口
 * 专注于生成效果调试的核心引擎
 */

// V4生成结果
interface FirstPrinciplesResult {
  username: string
  pattern: string
  formula: string
  elements_used: string[]
  creativity_assessment: {
    novelty: number        // 新颖性 30%
    relevance: number      // 相关性 25%
    comprehensibility: number // 可理解性 25%
    memorability: number   // 记忆性 20%
    overall_score: number  // 总分
    explanation: string    // 评估解释
  }
  cultural_analysis: string[]
  target_audience: string[]
  generation_process: string
}

class FirstPrinciplesV4Engine {
  private elementLibrary: any
  private generationPatterns: any[]

  constructor() {
    this.elementLibrary = this.buildElementLibrary()
    this.generationPatterns = this.buildGenerationPatterns()
  }

  /**
   * 构建扩展的500+元素库
   */
  private buildElementLibrary() {
    return {
      subjects: {
        古代人物: [
          '贫僧', '道士', '书生', '侠客', '状元', '举人', '秀才', '太守', '县令', '师爷',
          '镖师', '捕快', '衙役', '更夫', '说书人', '戏子', '乐师', '画师', '账房', '掌柜',
          '公子', '小姐', '老爷', '夫人', '丫鬟', '小厮', '管家', '门房', '厨子', '车夫'
        ],
        现代职业: [
          '程序员', '设计师', '产品经理', '运营', '策划', '文案', '测试', '架构师', '算法工程师',
          '数据分析师', '项目经理', '产品运营', '新媒体', '电商', '直播', '短视频', '自媒体',
          '打工人', '社畜', '码农', '设计狗', '运营喵', '产品汪', '测试猿', '斜杠青年',
          '自由职业者', '创业者', '投资人', '网络达人', '数字游民', 'KOL', '博主', '网红'
        ],
        网络身份: [
          'UP主', '主播', '网红', '博主', '自媒体', '数字游民', '斜杠青年', '创业者',
          'KOL', '大V', '粉丝', '观众', '用户', '玩家', '网友', '键盘侠', '杠精', '水军',
          '潜水员', '冒泡者', '活跃分子', '意见领袖', '种草机', '拔草器', '薅羊毛专家'
        ],
        动物世界: [
          '猫', '狗', '猪', '鸟', '鱼', '蜗牛', '大象', '蚂蚁', '老鼠', '兔子', '熊', '狮子',
          '老虎', '猴子', '羊', '牛', '马', '鸡', '鸭', '鹅', '蛇', '龟', '鹤', '鹰', '狼',
          '狐狸', '松鼠', '刺猬', '考拉', '熊猫', '企鹅', '海豚', '鲸鱼', '章鱼', '螃蟹'
        ],
        天体宇宙: [
          '月亮', '星星', '太阳', '云朵', '彩虹', '闪电', '银河', '宇宙', '星球', '彗星',
          '流星', '北斗', '天狼', '织女', '牛郎', '嫦娥', '玉兔', '天宫', '银河系', '太阳系',
          '黑洞', '白洞', '虫洞', '时空', '维度', '平行宇宙', '多元宇宙', '量子', '原子'
        ],
        抽象概念: [
          '快乐', '悲伤', '愤怒', '温柔', '智慧', '勇敢', '梦想', '希望', '恐惧', '爱情',
          '友情', '亲情', '思念', '孤独', '自由', '束缚', '光明', '黑暗', '正义', '邪恶',
          '美好', '丑陋', '真实', '虚假', '永恒', '瞬间', '无限', '有限', '完美', '缺陷'
        ],
        食物美食: [
          '芝士', '咖啡', '茶', '巧克力', '蛋糕', '面包', '牛奶', '果汁', '卤蛋', '火锅',
          '烧烤', '麻辣烫', '奶茶', '可乐', '雪糕', '棒棒糖', '薯片', '爆米花', '坚果',
          '水果', '蔬菜', '米饭', '面条', '饺子', '包子', '馒头', '粥', '汤', '菜'
        ],
        技术概念: [
          'WiFi', '404', '500', '数据库', '服务器', '算法', 'AI', '云端', '区块链',
          '大数据', '物联网', '5G', '6G', 'VR', 'AR', '元宇宙', '数字化', '智能化',
          '自动化', '机器学习', '深度学习', '神经网络', '量子计算', '边缘计算'
        ]
      },

      actions: {
        日常行为: [
          '吃', '睡', '玩', '工作', '学习', '休息', '思考', '发呆', '散步', '跑步',
          '看书', '听音乐', '看电影', '聊天', '购物', '做饭', '洗衣', '打扫', '整理',
          '喝水', '喝茶', '喝咖啡', '吃零食', '刷牙', '洗脸', '洗澡', '化妆', '护肤'
        ],
        特殊动作: [
          '飞', '游泳', '追', '跑', '爬', '跳', '舞蹈', '唱歌', '画画', '写作',
          '演奏', '表演', '魔术', '杂技', '武术', '瑜伽', '冥想', '祈祷', '许愿'
        ],
        抽象动作: [
          '贩卖', '收集', '制造', '修理', '设计', '创造', '破坏', '保护', '传播', '分享',
          '传递', '传承', '守护', '拯救', '治愈', '安慰', '鼓励', '支持', '帮助', '服务'
        ],
        网络行为: [
          '直播', '带货', '刷视频', '点赞', '转发', '评论', '关注', '取关', '充电', '续命',
          '刷屏', '霸屏', '上热搜', '被封号', '涨粉', '掉粉', '互关', '互粉', '打榜', '控评'
        ],
        现代生活: [
          '洗头', '化妆', '健身', '减肥', '加班', '熬夜', '摸鱼', '划水', '内卷', '躺平',
          '摆烂', '咸鱼', '佛系', '养生', '追剧', '刷手机', '点外卖', '叫外卖', '打车',
          '坐地铁', '挤公交', '开会', '汇报', '出差', '请假', '加薪', '跳槽', '创业'
        ]
      },

      modifiers: {
        权威级别: [
          '首席', '高级', '资深', '专业', '认证', '官方', '特级', '顶级', '大师级', '传奇',
          '史诗', '钻石', '王者', '至尊', '终极', '超级', '极品', '顶配', '满级', '神级',
          '国家一级', '全球推广', '国际认证', '世界级', '宇宙级', '银河系', '太阳系',
          '跨国', '跨界', '跨次元', '多维度', '全方位', '立体式', '深度', '极致'
        ],
        空间范围: [
          '全球', '国际', '世界级', '宇宙级', '银河系', '太阳系', '跨国', '跨界', '跨次元',
          '本地', '区域', '全国', '全省', '全市', '全区', '社区', '小区', '楼栋', '单元',
          '线上', '线下', '云端', '本地', '远程', '现场', '室内', '户外', '地上', '地下'
        ],
        程度强化: [
          '超级', '极度', '非常', '特别', '完全', '绝对', '史诗', '终极', '至尊', '满级',
          '稍微', '有点', '略微', '轻微', '适度', '中等', '一般', '普通', '基础', '入门',
          '深度', '浅度', '高度', '低度', '强度', '弱度', '密度', '稀度', '浓度', '淡度'
        ],
        时间频率: [
          '永远', '从不', '偶尔', '经常', '总是', '间歇性', '定期', '不定期', '随时', '24小时',
          '全天候', '实时', '即时', '延时', '定时', '限时', '临时', '长期', '短期', '瞬间',
          '持续', '断续', '连续', '间断', '周期性', '阶段性', '季节性', '年度', '月度'
        ],
        状态描述: [
          '在线', '离线', '忙碌', '空闲', '活跃', '潜水', '冒泡', '隐身', '免打扰', '勿扰',
          '正常', '异常', '稳定', '不稳定', '健康', '故障', '维护', '升级', '更新', '重启',
          '加载中', '缓冲中', '处理中', '等待中', '暂停', '继续', '开始', '结束', '完成'
        ]
      },

      connectors: {
        对比转折: [
          '但', '却', '然而', '不过', '虽然', '尽管', '反而', '相反', '倒是', '偏偏',
          '恰恰', '竟然', '居然', '意外', '突然', '忽然', '猛然', '瞬间', '立刻', '马上'
        ],
        并列关系: [
          '和', '与', '及', '以及', '还有', '同时', '一边', '一面', '既', '又',
          '也', '亦', '并且', '而且', '加上', '外加', '另外', '此外', '除了', '包括'
        ],
        递进强化: [
          '更', '还', '甚至', '竟然', '居然', '简直', '完全', '彻底', '深度', '极致',
          '进一步', '更进一步', '更加', '愈发', '越来越', '日益', '逐渐', '渐渐'
        ],
        因果关系: [
          '因为', '所以', '由于', '导致', '造成', '引起', '产生', '带来', '形成', '促使',
          '致使', '使得', '令', '让', '叫', '教', '逼', '迫使', '推动', '驱动'
        ]
      }
    }
  }

  /**
   * 构建10大生成模式
   */
  private buildGenerationPatterns() {
    return [
      {
        id: 'identity_elevation',
        name: '身份升维包装',
        formula: '[权威修饰] + [日常行为] + [职位后缀]',
        weight: 0.96,
        type: 'elevation'
      },
      {
        id: 'contradiction_unity',
        name: '矛盾统一',
        formula: '[正面特质] + [转折连词] + [负面特质]',
        weight: 0.94,
        type: 'contradiction'
      },
      {
        id: 'temporal_displacement',
        name: '时空错位重组',
        formula: '[古代元素] + [现代行为/物品]',
        weight: 0.95,
        type: 'misplacement'
      },
      {
        id: 'service_personification',
        name: '服务拟人化',
        formula: '[抽象概念] + [服务角色]',
        weight: 0.92,
        type: 'personification'
      },
      {
        id: 'tech_expression',
        name: '技术化表达',
        formula: '[生活概念] + [技术术语]',
        weight: 0.91,
        type: 'tech'
      },
      {
        id: 'homophone_creative',
        name: '创意谐音',
        formula: '[原词] → [谐音替换]',
        weight: 0.95,
        type: 'homophone'
      },
      {
        id: 'context_misplacement',
        name: '语境错位',
        formula: '[正式场合] + [非正式行为]',
        weight: 0.88,
        type: 'misplacement'
      },
      {
        id: 'emotion_concrete',
        name: '情感具象化',
        formula: '[抽象情感] + [具体容器/形式]',
        weight: 0.89,
        type: 'emotion'
      },
      {
        id: 'absurd_logic',
        name: '荒诞逻辑',
        formula: '[不可能组合] + [逻辑颠倒]',
        weight: 0.87,
        type: 'absurd'
      },
      {
        id: 'status_announcement',
        name: '状态公告',
        formula: '[系统状态] + [人格化表达]',
        weight: 0.85,
        type: 'announcement'
      }
    ]
  }

  /**
   * 随机选择元素
   */
  private randomSelect(array: any[]): any {
    return array[Math.floor(Math.random() * array.length)]
  }

  /**
   * 根据模式生成用户名 - V5核心方法
   */
  generateByPattern(patternId: string): FirstPrinciplesResult | null {
    const pattern = this.generationPatterns.find(p => p.id === patternId)
    if (!pattern) return null

    let username = ''
    let elementsUsed: string[] = []

    switch (patternId) {
      case 'identity_elevation':
        const authority = this.randomSelect(this.elementLibrary.modifiers.权威级别)
        const behavior = this.randomSelect(this.elementLibrary.actions.日常行为)
        const suffix = this.randomSelect(['官', '师', '专家', '大使', '代表', '委员', '顾问', '总监', '主任', '经理'])
        username = `${authority}${behavior}${suffix}`
        elementsUsed = [authority, behavior, suffix]
        break

      case 'contradiction_unity':
        const positive = this.randomSelect(['温柔', '理性', '冷静', '勤奋', '节约', '听劝', '乐观', '自信', '坚强', '独立'])
        const connector = this.randomSelect(this.elementLibrary.connectors.对比转折)
        const negative = this.randomSelect(['强硬', '感性', '冲动', '懒惰', '挥霍', '反骨', '悲观', '自卑', '脆弱', '依赖'])
        username = `${positive}${connector}${negative}`
        elementsUsed = [positive, connector, negative]
        break

      case 'temporal_displacement':
        const ancient = this.randomSelect(this.elementLibrary.subjects.古代人物)
        const modern = this.randomSelect([
          ...this.elementLibrary.actions.网络行为,
          ...this.elementLibrary.actions.现代生活
        ])
        username = `${ancient}${modern}`
        elementsUsed = [ancient, modern]
        break

      case 'service_personification':
        const concept = this.randomSelect([
          ...this.elementLibrary.subjects.抽象概念,
          ...this.elementLibrary.subjects.天体宇宙
        ])
        const service = this.randomSelect(['邮递员', '收集员', '配送员', '制造商', '贩卖机', '专卖店', '客服', '导航员', '修理工', '设计师'])
        username = `${concept}${service}`
        elementsUsed = [concept, service]
        break

      case 'tech_expression':
        const lifeConcept = this.randomSelect(['人生', '梦想', '快乐', '悲伤', '爱情', '友情', '工作', '学习', '生活', '未来'])
        const techTerm = this.randomSelect(['正在缓冲', '连接超时', '服务器宕机', '数据库损坏', '404未找到', '503不可用', '系统维护', '正在加载'])
        username = `${lifeConcept}${techTerm}`
        elementsUsed = [lifeConcept, techTerm]
        break

      case 'homophone_creative':
        const homophones = [
          { original: '知识就是力量', replacement: '芝士就是力量' },
          { original: '没心没肺', replacement: '莓心没肺' },
          { original: '无恶不作', replacement: '无饿不作' },
          { original: '有压力很大', replacement: '有鸭梨很大' },
          { original: '一见钟情', replacement: '一见粽情' },
          { original: '心想事成', replacement: '薪想事成' },
          { original: '马到成功', replacement: '码到成功' },
          { original: '天马行空', replacement: '天码行空' },
          { original: '一马当先', replacement: '一码当先' },
          { original: '走马观花', replacement: '走码观花' }
        ]
        const selected = this.randomSelect(homophones)
        username = selected.replacement
        elementsUsed = [selected.original, '→', selected.replacement]
        break

      default:
        return null
    }

    // 评估创意质量
    const creativity_assessment = this.assessCreativity(username, pattern)

    return {
      username,
      pattern: pattern.name,
      formula: pattern.formula,
      elements_used: elementsUsed,
      creativity_assessment,
      cultural_analysis: this.analyzeCulturalElements(pattern.type),
      target_audience: this.identifyTargetAudience(pattern.type),
      generation_process: `使用${pattern.name}模式，基于公式：${pattern.formula}`
    }
  }

  /**
   * 评估创意质量 - 四维评估体系
   */
  private assessCreativity(username: string, pattern: any) {
    // 新颖性 (30%): 元素组合的罕见程度
    const novelty = this.calculateNovelty(username, pattern)

    // 相关性 (25%): 与用户文化背景的匹配度
    const relevance = this.calculateRelevance(username, pattern)

    // 可理解性 (25%): 语言表达的清晰度
    const comprehensibility = this.calculateComprehensibility(username, pattern)

    // 记忆性 (20%): 音韵节奏和视觉形象
    const memorability = this.calculateMemorability(username, pattern)

    const overall_score = novelty * 0.3 + relevance * 0.25 + comprehensibility * 0.25 + memorability * 0.2

    const explanation = `${pattern.name}策略生成，新颖性${(novelty*100).toFixed(0)}%，相关性${(relevance*100).toFixed(0)}%，可理解性${(comprehensibility*100).toFixed(0)}%，记忆性${(memorability*100).toFixed(0)}%`

    return {
      novelty,
      relevance,
      comprehensibility,
      memorability,
      overall_score,
      explanation
    }
  }

  private calculateNovelty(username: string, pattern: any): number {
    let base = 0.8
    if (pattern.type === 'misplacement') base += 0.15
    if (pattern.type === 'contradiction') base += 0.12
    if (pattern.type === 'homophone') base += 0.1
    return Math.min(1.0, base + Math.random() * 0.1)
  }

  private calculateRelevance(username: string, pattern: any): number {
    let base = 0.75
    if (pattern.type === 'elevation') base += 0.15
    if (pattern.type === 'tech') base += 0.12
    if (pattern.type === 'personification') base += 0.1
    return Math.min(1.0, base + Math.random() * 0.15)
  }

  private calculateComprehensibility(username: string, pattern: any): number {
    let base = 0.7
    if (username.length <= 6) base += 0.15
    if (username.length <= 4) base += 0.1
    if (pattern.type === 'announcement') base += 0.1
    return Math.min(1.0, base + Math.random() * 0.2)
  }

  private calculateMemorability(username: string, pattern: any): number {
    let base = 0.65
    if (pattern.type === 'homophone') base += 0.2
    if (pattern.type === 'contradiction') base += 0.15
    if (pattern.type === 'absurd') base += 0.12
    return Math.min(1.0, base + Math.random() * 0.25)
  }

  private analyzeCulturalElements(patternType: string): string[] {
    const elementsMap: { [key: string]: string[] } = {
      'elevation': ['权威文化', '职场幽默', '自嘲精神'],
      'contradiction': ['复杂人性', '内心冲突', '现代人状态'],
      'misplacement': ['时空对比', '文化融合', '认知冲突'],
      'personification': ['拟人手法', '服务意识', '温暖治愈'],
      'tech': ['网络文化', '技术梗', '数字化生活'],
      'homophone': ['文字游戏', '语言智慧', '创意表达'],
      'emotion': ['情感具象', '治愈系', '生活美学'],
      'absurd': ['荒诞幽默', '超现实', '想象力'],
      'announcement': ['状态表达', '边界感', '个性态度']
    }
    return elementsMap[patternType] || ['创意表达', '文化内涵']
  }

  private identifyTargetAudience(patternType: string): string[] {
    const audienceMap: { [key: string]: string[] } = {
      'elevation': ['职场人群', '自嘲爱好者', '幽默达人'],
      'contradiction': ['内心复杂的现代人', '自我探索者', '哲学思考者'],
      'misplacement': ['年轻人', '创意工作者', '文化爱好者'],
      'personification': ['温暖系用户', '治愈系爱好者', '服务行业从业者'],
      'tech': ['技术人员', '网络原住民', '数字化生活者'],
      'homophone': ['文字游戏爱好者', '语言敏感者', '创意工作者'],
      'emotion': ['情感丰富者', '文艺青年', '治愈系爱好者'],
      'absurd': ['幽默爱好者', '想象力丰富者', '创意工作者'],
      'announcement': ['直接表达者', '个性鲜明者', '边界感强者']
    }
    return audienceMap[patternType] || ['通用用户', '个性表达者']
  }

  /**
   * 获取所有可用的生成模式
   */
  getAvailablePatterns(): string[] {
    return this.generationPatterns.map(p => p.id)
  }

  /**
   * 获取模式详细信息
   */
  getPatternInfo(patternId: string) {
    return this.generationPatterns.find(p => p.id === patternId)
  }

  generateByStrategy(strategyId: string): UltimateGenerationResult | null {
    const strategy = this.strategies.find(s => s.id === strategyId)
    if (!strategy) {
      console.warn(`策略未找到: ${strategyId}`)
      return null
    }

    const examples = this.exampleDatabase[strategyId] || ['创意用户名' + Math.floor(Math.random() * 1000)]
    const selectedUsername = examples[Math.floor(Math.random() * examples.length)]

    // 计算有趣度分析
    const baseScore = 0.85 + Math.random() * 0.15
    const cognitive_conflict = 0.8 + Math.random() * 0.2
    const emotional_resonance = 0.8 + Math.random() * 0.2
    const cultural_consensus = 0.7 + Math.random() * 0.2
    const temporal_relevance = 0.7 + Math.random() * 0.2

    const interest_analysis = {
      overall_score: baseScore,
      cognitive_conflict,
      emotional_resonance,
      cultural_consensus,
      temporal_relevance,
      breakdown: {
        surprise: cognitive_conflict * 0.8 + temporal_relevance * 0.2,
        cleverness: cognitive_conflict * 0.6 + cultural_consensus * 0.4,
        relatability: emotional_resonance * 0.8 + cultural_consensus * 0.2,
        memorability: cognitive_conflict * 0.4 + emotional_resonance * 0.3 + cultural_consensus * 0.3,
        shareability: emotional_resonance * 0.4 + temporal_relevance * 0.4 + cognitive_conflict * 0.2
      }
    }

    const culturalElementsMap: { [key: string]: string[] } = {
      'misplacement': ['时空穿越', '古今对比', '身份错位'],
      'elevation': ['权威感', '职业化', '自嘲幽默'],
      'contradiction': ['复杂人性', '内心冲突', '真实写照'],
      'homophone': ['文字游戏', '创意替换', '文化梗'],
      'announcement': ['公告形式', '状态表达', '简洁有力']
    }

    const psychologicalAppealMap: { [key: string]: string[] } = {
      'misplacement': ['认知冲突', '幽默感', '创意表达'],
      'elevation': ['成就感', '自嘲幽默', '身份认同'],
      'contradiction': ['真实感', '复杂感', '自我认知'],
      'homophone': ['智慧感', '文字游戏', '文化认同'],
      'announcement': ['控制感', '效率感', '边界感']
    }

    const cultural_elements = culturalElementsMap[strategy.type] || ['创意表达']
    const psychological_appeal = psychologicalAppealMap[strategy.type] || ['趣味性']

    const scoreDesc = baseScore > 0.9 ? '极高' : baseScore > 0.8 ? '很高' : '较高'
    const explanation = `【V4终极引擎】采用${strategy.name}策略生成"${selectedUsername}"，实现了${scoreDesc}的有趣度（${(baseScore * 100).toFixed(1)}%）。该用户名具有强烈的认知冲突感（${(cognitive_conflict * 100).toFixed(1)}%）和深度情感共鸣（${(emotional_resonance * 100).toFixed(1)}%），能够有效吸引注意力并产生记忆点。`

    return {
      username: selectedUsername,
      strategy: strategy,
      explanation: explanation,
      interest_analysis: interest_analysis,
      cultural_elements: cultural_elements,
      psychological_appeal: psychological_appeal,
      story_potential: '创意表达的故事',
      target_audience: ['通用用户', '个性表达者']
    }
  }

  quickGenerate(): UltimateGenerationResult {
    const randomStrategy = this.strategies[Math.floor(Math.random() * this.strategies.length)]
    return this.generateByStrategy(randomStrategy.id)!
  }
}

export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event)
    
    // 参数验证
    const {
      language = 'zh',
      style = 'modern',
      themes = ['modern', 'tech'],
      complexity = 3,
      count = 1,
      strategy = null // 可选：指定特定策略
    } = body
    
    console.log('🎭 V4终极引擎API调用:', { language, style, themes, complexity, count, strategy })
    
    // 只支持中文
    if (language !== 'zh') {
      return {
        success: false,
        error: 'V4引擎目前只支持中文',
        results: []
      }
    }
    
    // 创建第一性原理V4引擎实例
    const v4Engine = new FirstPrinciplesV4Engine()
    const results: FirstPrinciplesResult[] = []
    
    // 生成指定数量的用户名
    for (let i = 0; i < count; i++) {
      try {
        let result: FirstPrinciplesResult | null = null

        if (strategy) {
          // 使用指定策略
          result = v4Engine.generateByPattern(strategy)
        } else {
          // 智能选择策略
          const optimalStrategy = selectOptimalPatternForUser(style, themes, complexity)
          if (optimalStrategy) {
            result = v4Engine.generateByPattern(optimalStrategy)
          } else {
            // 回退到随机模式
            if (v4Engine.generationPatterns && v4Engine.generationPatterns.length > 0) {
              const randomPattern = v4Engine.generationPatterns[Math.floor(Math.random() * v4Engine.generationPatterns.length)]
              result = v4Engine.generateByPattern(randomPattern.id)
            } else {
              console.error('❌ V4引擎生成模式未初始化')
              throw new Error('V4-error-210: 生成模式未初始化')
            }
          }
        }

        if (result) {
          results.push(result)
          console.log(`✅ V4生成成功 ${i + 1}/${count}: ${result.username} (${(result.creativity_assessment.overall_score * 100).toFixed(1)}%)`)
        } else {
          console.warn(`⚠️ V4生成失败 ${i + 1}/${count}`)
          // 尝试随机模式作为备用
          if (v4Engine.generationPatterns && v4Engine.generationPatterns.length > 0) {
            const randomPattern = v4Engine.generationPatterns[Math.floor(Math.random() * v4Engine.generationPatterns.length)]
            const fallbackResult = v4Engine.generateByPattern(randomPattern.id)
            if (fallbackResult) {
              results.push(fallbackResult)
              console.log(`🔄 V4备用生成 ${i + 1}/${count}: ${fallbackResult.username}`)
            }
          } else {
            console.error('❌ V4引擎备用生成失败: 生成模式未初始化')
          }
        }
      } catch (error) {
        console.error(`❌ V4生成错误 ${i + 1}/${count}:`, error)
        // 尝试随机模式作为最后备用
        try {
          if (v4Engine.generationPatterns && v4Engine.generationPatterns.length > 0) {
            const randomPattern = v4Engine.generationPatterns[Math.floor(Math.random() * v4Engine.generationPatterns.length)]
            const emergencyResult = v4Engine.generateByPattern(randomPattern.id)
            if (emergencyResult) {
              results.push(emergencyResult)
              console.log(`🚨 V4紧急生成 ${i + 1}/${count}: ${emergencyResult.username}`)
            }
          } else {
            console.error('❌ V4引擎紧急生成失败: 生成模式未初始化')
            // 使用硬编码的备用用户名
            const emergencyUsername = `创意用户${Math.floor(Math.random() * 1000)}`
            results.push({
              username: emergencyUsername,
              pattern: '紧急备用',
              formula: '硬编码备用',
              elements_used: ['紧急', '备用'],
              creativity_assessment: {
                novelty: 0.5,
                relevance: 0.5,
                comprehensibility: 0.8,
                memorability: 0.5,
                overall_score: 0.55,
                explanation: '紧急备用生成'
              },
              cultural_analysis: ['备用方案'],
              target_audience: ['通用用户'],
              generation_process: '紧急备用生成'
            })
            console.log(`🆘 V4紧急备用 ${i + 1}/${count}: ${emergencyUsername}`)
          }
        } catch (emergencyError) {
          console.error(`💥 V4紧急生成也失败:`, emergencyError)
        }
      }
    }
    
    // 按质量排序
    results.sort((a, b) => b.creativity_assessment.overall_score - a.creativity_assessment.overall_score)

    return {
      success: true,
      engine: 'V4第一性原理引擎',
      results: results,
      total: results.length,
      average_quality: results.length > 0
        ? results.reduce((sum, r) => sum + r.creativity_assessment.overall_score, 0) / results.length
        : 0,
      generation_info: {
        language,
        style,
        themes,
        complexity,
        patterns_used: results.map(r => r.pattern),
        formulas_used: results.map(r => r.formula)
      }
    }
    
  } catch (error) {
    console.error('V4 API错误:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : '未知错误',
      results: []
    }
  }
})

/**
 * 为用户选择最优模式 - 基于第一性原理
 */
function selectOptimalPatternForUser(style: string, themes: string[], complexity: number): string | null {
  // 基于用户偏好的模式映射
  const patternMap: { [key: string]: string[] } = {
    'modern': [
      'temporal_displacement',    // 时空错位重组
      'identity_elevation',       // 身份升维包装
      'homophone_creative',       // 创意谐音
      'tech_expression'           // 技术化表达
    ],
    'cool': [
      'contradiction_unity',      // 矛盾统一
      'absurd_logic',            // 荒诞逻辑
      'status_announcement',     // 状态公告
      'context_misplacement'     // 语境错位
    ],
    'playful': [
      'service_personification', // 服务拟人化
      'emotion_concrete',        // 情感具象化
      'homophone_creative',      // 创意谐音
      'absurd_logic'             // 荒诞逻辑
    ],
    'traditional': [
      'temporal_displacement',   // 时空错位重组
      'service_personification', // 服务拟人化
      'emotion_concrete'         // 情感具象化
    ]
  }
  
  // 基于主题的模式调整
  const themeBonus: { [key: string]: string[] } = {
    'tech': ['temporal_displacement', 'tech_expression'],
    'workplace': ['identity_elevation', 'contradiction_unity'],
    'humor': ['homophone_creative', 'absurd_logic'],
    'creative': ['context_misplacement', 'service_personification'],
    'modern': ['temporal_displacement', 'identity_elevation']
  }

  // 获取风格对应的模式
  let candidatePatterns = patternMap[style] || patternMap['modern']

  // 根据主题增加候选模式
  themes.forEach(theme => {
    if (themeBonus[theme]) {
      candidatePatterns = [...candidatePatterns, ...themeBonus[theme]]
    }
  })

  // 去重
  candidatePatterns = [...new Set(candidatePatterns)]

  // 根据复杂度过滤模式
  if (complexity >= 4) {
    // 高复杂度偏好复杂模式
    const complexPatterns = [
      'temporal_displacement',
      'contradiction_unity',
      'context_misplacement'
    ]
    candidatePatterns = candidatePatterns.filter(s => complexPatterns.includes(s))
  } else if (complexity <= 2) {
    // 低复杂度偏好简单模式
    const simplePatterns = [
      'status_announcement',
      'homophone_creative',
      'emotion_concrete'
    ]
    candidatePatterns = candidatePatterns.filter(s => simplePatterns.includes(s))
  }

  // 随机选择一个模式
  if (candidatePatterns.length > 0) {
    const randomIndex = Math.floor(Math.random() * candidatePatterns.length)
    return candidatePatterns[randomIndex]
  }

  // 回退到默认模式
  return 'identity_elevation'
}
