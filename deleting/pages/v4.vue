<script setup lang="ts">
import { ref } from 'vue'

// 页面元数据
useHead({
  title: 'V4终极有趣引擎 - 智能用户名生成器',
  meta: [
    {
      name: 'description',
      content: '基于深度"有趣"理论的V4终极引擎，采用10大核心策略和4维评估体系，为您创造具有深度文化内涵的高质量用户名。'
    },
    {
      name: 'keywords',
      content: 'V4引擎,用户名生成器,有趣理论,创意策略,文化内涵,智能生成'
    }
  ]
})

// 页面状态
const showIntro = ref(true)
const showFeatures = ref(false)

// 特色功能数据
const features = [
  {
    icon: '🎭',
    title: '10大核心策略',
    description: '时空错位重组、职业化升维包装、性格矛盾统一等创意策略',
    examples: ['古代网红博主', '首席干饭官', '温柔且强硬']
  },
  {
    icon: '📊',
    title: '4维终极评估',
    description: '认知冲突、情感共鸣、文化共识、时代相关四维科学评估',
    examples: ['认知冲突90%', '情感共鸣95%', '文化共识85%']
  },
  {
    icon: '🎨',
    title: '深度文化解读',
    description: '每个用户名都包含丰富的文化元素和心理诉求分析',
    examples: ['文化元素', '心理诉求', '创意解读']
  },
  {
    icon: '🎯',
    title: '智能用户匹配',
    description: '根据风格偏好和主题需求精准匹配最优策略',
    examples: ['现代风格', '职场主题', '创意复杂度']
  }
]

// 策略展示数据
const strategies = [
  {
    name: '时空错位重组',
    description: '将不同时空的元素创意组合',
    example: '古代网红博主',
    score: 93,
    elements: ['时空穿越', '古今对比', '身份错位']
  },
  {
    name: '职业化升维包装',
    description: '将日常行为包装为权威职位',
    example: '首席干饭官',
    score: 96,
    elements: ['权威感', '职业化', '自嘲幽默']
  },
  {
    name: '性格矛盾统一',
    description: '将对立特质巧妙融合',
    example: '温柔且强硬',
    score: 94,
    elements: ['复杂人性', '内心冲突', '真实写照']
  },
  {
    name: '创意谐音',
    description: '运用汉语谐音的智慧',
    example: '芝士就是力量',
    score: 95,
    elements: ['文字游戏', '创意替换', '文化梗']
  }
]
</script>

<template>
  <div class="v4-page">
    <!-- 页面头部 -->
    <div class="hero-section">
      <div class="hero-content">
        <h1 class="hero-title">
          <span class="v4-badge-large">V4</span>
          终极有趣引擎
        </h1>
        <p class="hero-subtitle">
          基于深度"有趣"理论的智能用户名生成器
        </p>
        <p class="hero-description">
          采用10大核心策略和4维评估体系，为您创造具有深度文化内涵的高质量用户名
        </p>
        
        <div class="hero-stats">
          <div class="stat-item">
            <span class="stat-number">10</span>
            <span class="stat-label">核心策略</span>
          </div>
          <div class="stat-item">
            <span class="stat-number">4</span>
            <span class="stat-label">评估维度</span>
          </div>
          <div class="stat-item">
            <span class="stat-number">94%</span>
            <span class="stat-label">平均质量</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要生成器 -->
    <div class="generator-section">
      <V4UsernameGenerator />
    </div>

    <!-- 特色功能介绍 -->
    <div class="features-section" v-if="showIntro">
      <div class="section-header">
        <h2>🚀 V4引擎核心特色</h2>
        <p>从随机组合到智能创意的革命性升级</p>
      </div>
      
      <div class="features-grid">
        <div
          v-for="feature in features"
          :key="feature.title"
          class="feature-card"
        >
          <div class="feature-icon">{{ feature.icon }}</div>
          <h3 class="feature-title">{{ feature.title }}</h3>
          <p class="feature-description">{{ feature.description }}</p>
          <div class="feature-examples">
            <span
              v-for="example in feature.examples"
              :key="example"
              class="example-tag"
            >
              {{ example }}
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- 策略展示 -->
    <div class="strategies-section" v-if="showFeatures">
      <div class="section-header">
        <h2>🎭 核心策略展示</h2>
        <p>每种策略都是对"有趣"本质的深度洞察</p>
      </div>
      
      <div class="strategies-grid">
        <div
          v-for="strategy in strategies"
          :key="strategy.name"
          class="strategy-card"
        >
          <div class="strategy-header">
            <h3 class="strategy-name">{{ strategy.name }}</h3>
            <div class="strategy-score">{{ strategy.score }}%</div>
          </div>
          <p class="strategy-description">{{ strategy.description }}</p>
          <div class="strategy-example">
            <span class="example-label">示例:</span>
            <span class="example-text">{{ strategy.example }}</span>
          </div>
          <div class="strategy-elements">
            <span
              v-for="element in strategy.elements"
              :key="element"
              class="element-tag"
            >
              {{ element }}
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- 页面控制 -->
    <div class="page-controls">
      <button
        @click="showIntro = !showIntro"
        class="control-button"
        :class="{ 'active': showIntro }"
      >
        {{ showIntro ? '隐藏特色' : '显示特色' }}
      </button>
      <button
        @click="showFeatures = !showFeatures"
        class="control-button"
        :class="{ 'active': showFeatures }"
      >
        {{ showFeatures ? '隐藏策略' : '显示策略' }}
      </button>
    </div>

    <!-- 页面底部 -->
    <div class="footer-section">
      <div class="footer-content">
        <h3>🎯 V4引擎的革命性突破</h3>
        <div class="breakthrough-grid">
          <div class="breakthrough-item">
            <strong>从工具到艺术品工厂</strong>
            <p>不再是简单的用户名生成工具，而是能够创造具有深度文化内涵的数字身份艺术品</p>
          </div>
          <div class="breakthrough-item">
            <strong>从随机到智能</strong>
            <p>从随机词汇组合升级为策略驱动创意，实现真正的智能化生成</p>
          </div>
          <div class="breakthrough-item">
            <strong>从主观到客观</strong>
            <p>建立科学的四维评估体系，将"有趣"从主观感受转化为客观可量化指标</p>
          </div>
          <div class="breakthrough-item">
            <strong>从通用到个性化</strong>
            <p>根据用户画像精准匹配策略，实现真正的个性化生成</p>
          </div>
        </div>
        
        <div class="footer-note">
          <p>
            <strong>V4终极引擎</strong> - 不仅仅是技术的进步，更是对"有趣"本质的深度洞察和创意生成方法论的重大突破！
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.v4-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 2rem 0;
}

.hero-section {
  text-align: center;
  padding: 3rem 1rem;
  color: white;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 900;
  margin: 0 0 1rem 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.v4-badge-large {
  background: linear-gradient(45deg, #ff6b6b, #feca57);
  padding: 1rem 2rem;
  border-radius: 20px;
  font-size: 2rem;
  color: white;
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
}

.hero-subtitle {
  font-size: 1.5rem;
  margin: 0 0 1rem 0;
  opacity: 0.9;
}

.hero-description {
  font-size: 1.1rem;
  margin: 0 0 2rem 0;
  opacity: 0.8;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.hero-stats {
  display: flex;
  justify-content: center;
  gap: 3rem;
  flex-wrap: wrap;
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 2.5rem;
  font-weight: 900;
  color: #feca57;
}

.stat-label {
  display: block;
  font-size: 1rem;
  opacity: 0.8;
}

.generator-section {
  margin: 2rem 0;
}

.features-section, .strategies-section {
  padding: 3rem 1rem;
  background: rgba(255, 255, 255, 0.95);
  margin: 2rem 0;
}

.section-header {
  text-align: center;
  margin-bottom: 3rem;
}

.section-header h2 {
  font-size: 2.5rem;
  font-weight: 800;
  color: #4a5568;
  margin: 0 0 1rem 0;
}

.section-header p {
  font-size: 1.2rem;
  color: #666;
  margin: 0;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.feature-card {
  background: white;
  padding: 2rem;
  border-radius: 16px;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
  text-align: center;
  transition: transform 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-4px);
}

.feature-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.feature-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #4a5568;
  margin: 0 0 1rem 0;
}

.feature-description {
  color: #666;
  margin: 0 0 1.5rem 0;
  line-height: 1.6;
}

.feature-examples {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  justify-content: center;
}

.example-tag {
  background: linear-gradient(45deg, #667eea, #764ba2);
  color: white;
  padding: 0.3rem 0.8rem;
  border-radius: 12px;
  font-size: 0.9rem;
  font-weight: 500;
}

.strategies-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.strategy-card {
  background: white;
  padding: 2rem;
  border-radius: 16px;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.strategy-card:hover {
  transform: translateY(-4px);
}

.strategy-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.strategy-name {
  font-size: 1.3rem;
  font-weight: 700;
  color: #4a5568;
  margin: 0;
}

.strategy-score {
  background: linear-gradient(45deg, #667eea, #764ba2);
  color: white;
  padding: 0.3rem 0.8rem;
  border-radius: 12px;
  font-weight: 600;
}

.strategy-description {
  color: #666;
  margin: 0 0 1rem 0;
  line-height: 1.6;
}

.strategy-example {
  background: #f7fafc;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1rem;
}

.example-label {
  font-weight: 600;
  color: #4a5568;
}

.example-text {
  color: #667eea;
  font-weight: 600;
  margin-left: 0.5rem;
}

.strategy-elements {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.element-tag {
  background: #e2e8f0;
  color: #4a5568;
  padding: 0.3rem 0.8rem;
  border-radius: 12px;
  font-size: 0.9rem;
  font-weight: 500;
}

.page-controls {
  text-align: center;
  margin: 2rem 0;
}

.control-button {
  background: rgba(255, 255, 255, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 0.8rem 1.5rem;
  border-radius: 12px;
  margin: 0 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 600;
}

.control-button:hover {
  background: rgba(255, 255, 255, 0.3);
}

.control-button.active {
  background: white;
  color: #667eea;
}

.footer-section {
  padding: 3rem 1rem;
  background: rgba(255, 255, 255, 0.95);
  margin-top: 2rem;
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
  text-align: center;
}

.footer-content h3 {
  font-size: 2rem;
  font-weight: 800;
  color: #4a5568;
  margin: 0 0 2rem 0;
}

.breakthrough-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.breakthrough-item {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  text-align: left;
}

.breakthrough-item strong {
  display: block;
  color: #667eea;
  font-size: 1.1rem;
  margin-bottom: 0.5rem;
}

.breakthrough-item p {
  color: #666;
  margin: 0;
  line-height: 1.6;
}

.footer-note {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  padding: 2rem;
  border-radius: 16px;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.footer-note p {
  margin: 0;
  font-size: 1.1rem;
  line-height: 1.6;
}

@media (max-width: 768px) {
  .hero-title {
    font-size: 2.5rem;
  }
  
  .v4-badge-large {
    font-size: 1.5rem;
    padding: 0.8rem 1.5rem;
  }
  
  .hero-stats {
    gap: 2rem;
  }
  
  .features-grid, .strategies-grid {
    grid-template-columns: 1fr;
  }
  
  .breakthrough-grid {
    grid-template-columns: 1fr;
  }
}
</style>
