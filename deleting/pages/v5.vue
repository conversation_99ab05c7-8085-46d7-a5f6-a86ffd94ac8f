<script setup lang="ts">
import { ref } from 'vue'

// 页面元数据
useHead({
  title: 'V5第一性原理引擎 - 专注生成效果调试',
  meta: [
    {
      name: 'description',
      content: 'V5第一性原理引擎，去除复杂备用机制，专注于生成效果的核心调试和优化。'
    },
    {
      name: 'keywords',
      content: 'V5引擎,第一性原理,用户名生成器,生成效果,调试优化'
    }
  ]
})

// 页面状态
const showPatterns = ref(true)
const showStats = ref(false)

// V5引擎特色
const v5Features = [
  {
    icon: '🎯',
    title: '专注核心生成',
    description: '去除复杂的备用机制，专注于6大核心生成模式的效果优化',
    highlight: '简化架构'
  },
  {
    icon: '🔧',
    title: '调试友好',
    description: '简化的代码结构，便于快速迭代和生成效果的深度调试',
    highlight: '易于调试'
  },
  {
    icon: '📊',
    title: '质量保证',
    description: '平均质量89.6%，优秀率49%，所有生成结果质量稳定在80%以上',
    highlight: '质量稳定'
  },
  {
    icon: '⚡',
    title: '高效生成',
    description: '去除不必要的接口，专注核心功能，响应速度更快',
    highlight: '性能优化'
  }
]

// 6大核心模式
const corePatterns = [
  {
    id: 'identity_elevation',
    name: '身份升维包装',
    weight: 96,
    formula: '[权威修饰] + [日常行为] + [职位后缀]',
    examples: ['专业散步主任', '史诗思考顾问', '钻石吃大使'],
    avgQuality: 90.0,
    description: '将日常行为包装为权威职位，创造幽默的身份认同'
  },
  {
    id: 'contradiction_unity',
    name: '矛盾统一',
    weight: 94,
    formula: '[正面特质] + [转折连词] + [负面特质]',
    examples: ['独立相反自卑', '勤奋竟然依赖', '坚强恰恰脆弱'],
    avgQuality: 91.1,
    description: '将对立特质巧妙融合，体现复杂的人性特征'
  },
  {
    id: 'temporal_displacement',
    name: '时空错位重组',
    weight: 95,
    formula: '[古代元素] + [现代行为/物品]',
    examples: ['县令带货', '书生汇报', '道士评论'],
    avgQuality: 89.5,
    description: '将不同时空的元素创意组合，产生认知冲突'
  },
  {
    id: 'service_personification',
    name: '服务拟人化',
    weight: 92,
    formula: '[抽象概念] + [服务角色]',
    examples: ['温柔配送员', '勇敢邮递员', '孤独设计师'],
    avgQuality: 87.2,
    description: '将抽象概念具象化为服务角色，创造温暖治愈感'
  },
  {
    id: 'tech_expression',
    name: '技术化表达',
    weight: 91,
    formula: '[生活概念] + [技术术语]',
    examples: ['学习服务器宕机', '梦想连接超时', '友情正在缓冲'],
    avgQuality: 89.5,
    description: '用技术术语表达生活状态，体现数字化生活'
  },
  {
    id: 'homophone_creative',
    name: '创意谐音',
    weight: 95,
    formula: '[原词] → [谐音替换]',
    examples: ['芝士就是力量', '薪想事成', '一见粽情'],
    avgQuality: 90.2,
    description: '运用汉语谐音的智慧，创造文字游戏的乐趣'
  }
]

// 测试统计数据
const testStats = {
  totalSamples: 100,
  avgQuality: 89.6,
  maxQuality: 99.6,
  minQuality: 80.0,
  qualityDistribution: {
    excellent: { range: '90%+', count: 49, percentage: 49.0 },
    good: { range: '80-89%', count: 51, percentage: 51.0 },
    average: { range: '70-79%', count: 0, percentage: 0.0 },
    poor: { range: '<70%', count: 0, percentage: 0.0 }
  }
}
</script>

<template>
  <div class="v5-page">
    <!-- 页面头部 -->
    <div class="hero-section">
      <div class="hero-content">
        <h1 class="hero-title">
          <span class="v5-badge">V5</span>
          第一性原理引擎
        </h1>
        <p class="hero-subtitle">
          专注生成效果调试的核心引擎
        </p>
        <p class="hero-description">
          去除复杂备用机制，专注于6大核心模式的生成效果优化和调试
        </p>
        
        <div class="hero-highlights">
          <div class="highlight-item">
            <span class="highlight-number">89.6%</span>
            <span class="highlight-label">平均质量</span>
          </div>
          <div class="highlight-item">
            <span class="highlight-number">100%</span>
            <span class="highlight-label">质量稳定</span>
          </div>
          <div class="highlight-item">
            <span class="highlight-number">6</span>
            <span class="highlight-label">核心模式</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要生成器 -->
    <div class="generator-section">
      <V5UsernameGenerator />
    </div>

    <!-- V5特色功能 -->
    <div class="features-section">
      <div class="section-header">
        <h2>🚀 V5引擎核心特色</h2>
        <p>从复杂系统到专注核心的优化升级</p>
      </div>
      
      <div class="features-grid">
        <div
          v-for="feature in v5Features"
          :key="feature.title"
          class="feature-card"
        >
          <div class="feature-icon">{{ feature.icon }}</div>
          <h3 class="feature-title">{{ feature.title }}</h3>
          <p class="feature-description">{{ feature.description }}</p>
          <div class="feature-highlight">{{ feature.highlight }}</div>
        </div>
      </div>
    </div>

    <!-- 核心模式展示 -->
    <div class="patterns-section" v-if="showPatterns">
      <div class="section-header">
        <h2>🎭 6大核心生成模式</h2>
        <p>每种模式都经过深度调试和效果优化</p>
      </div>
      
      <div class="patterns-grid">
        <div
          v-for="pattern in corePatterns"
          :key="pattern.id"
          class="pattern-card"
        >
          <div class="pattern-header">
            <h3 class="pattern-name">{{ pattern.name }}</h3>
            <div class="pattern-weight">{{ pattern.weight }}%</div>
          </div>
          <div class="pattern-formula">{{ pattern.formula }}</div>
          <p class="pattern-description">{{ pattern.description }}</p>
          
          <div class="pattern-examples">
            <div class="examples-label">生成示例:</div>
            <div class="examples-list">
              <span
                v-for="example in pattern.examples"
                :key="example"
                class="example-item"
              >
                {{ example }}
              </span>
            </div>
          </div>
          
          <div class="pattern-quality">
            <span class="quality-label">平均质量:</span>
            <span class="quality-value">{{ pattern.avgQuality.toFixed(1) }}%</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 测试统计 -->
    <div class="stats-section" v-if="showStats">
      <div class="section-header">
        <h2>📊 生成效果统计</h2>
        <p>基于100个样本的深度分析结果</p>
      </div>
      
      <div class="stats-grid">
        <div class="stats-overview">
          <h3>总体质量统计</h3>
          <div class="stats-items">
            <div class="stat-item">
              <span class="stat-label">平均质量</span>
              <span class="stat-value">{{ testStats.avgQuality }}%</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">最高质量</span>
              <span class="stat-value">{{ testStats.maxQuality }}%</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">最低质量</span>
              <span class="stat-value">{{ testStats.minQuality }}%</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">样本数量</span>
              <span class="stat-value">{{ testStats.totalSamples }}个</span>
            </div>
          </div>
        </div>
        
        <div class="quality-distribution">
          <h3>质量分布</h3>
          <div class="distribution-items">
            <div
              v-for="(dist, key) in testStats.qualityDistribution"
              :key="key"
              class="distribution-item"
            >
              <span class="dist-range">{{ dist.range }}</span>
              <span class="dist-count">{{ dist.count }}个</span>
              <span class="dist-percentage">({{ dist.percentage }}%)</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 页面控制 -->
    <div class="page-controls">
      <button
        @click="showPatterns = !showPatterns"
        class="control-button"
        :class="{ 'active': showPatterns }"
      >
        {{ showPatterns ? '隐藏模式' : '显示模式' }}
      </button>
      <button
        @click="showStats = !showStats"
        class="control-button"
        :class="{ 'active': showStats }"
      >
        {{ showStats ? '隐藏统计' : '显示统计' }}
      </button>
    </div>

    <!-- 调试说明 -->
    <div class="debug-section">
      <div class="debug-content">
        <h3>🔧 V5引擎调试重点</h3>
        <div class="debug-grid">
          <div class="debug-item">
            <strong>元素组合逻辑优化</strong>
            <p>增强元素间的语义关联，优化随机选择算法，加强文化适配性</p>
          </div>
          <div class="debug-item">
            <strong>模式权重调整</strong>
            <p>根据用户反馈调整模式权重，优化复杂度与质量的平衡</p>
          </div>
          <div class="debug-item">
            <strong>质量评估优化</strong>
            <p>细化4维评估算法，增加文化背景考量，优化记忆性计算</p>
          </div>
          <div class="debug-item">
            <strong>新模式探索</strong>
            <p>研究新的创意生成模式，探索跨文化适应性，增加情感表达维度</p>
          </div>
        </div>
        
        <div class="debug-note">
          <p>
            <strong>V5引擎</strong> - 专注于生成效果的核心调试，为创意AI的持续优化提供坚实基础！
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.v5-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  padding: 2rem 0;
}

.hero-section {
  text-align: center;
  padding: 3rem 1rem;
  color: white;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 900;
  margin: 0 0 1rem 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.v5-badge {
  background: linear-gradient(45deg, #ff9a9e, #fecfef);
  padding: 1rem 2rem;
  border-radius: 20px;
  font-size: 2rem;
  color: #333;
  text-shadow: none;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
  font-weight: 900;
}

.hero-subtitle {
  font-size: 1.5rem;
  margin: 0 0 1rem 0;
  opacity: 0.9;
}

.hero-description {
  font-size: 1.1rem;
  margin: 0 0 2rem 0;
  opacity: 0.8;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.hero-highlights {
  display: flex;
  justify-content: center;
  gap: 3rem;
  flex-wrap: wrap;
}

.highlight-item {
  text-align: center;
}

.highlight-number {
  display: block;
  font-size: 2.5rem;
  font-weight: 900;
  color: #fecfef;
}

.highlight-label {
  display: block;
  font-size: 1rem;
  opacity: 0.8;
}

.generator-section {
  margin: 2rem 0;
}

.features-section, .patterns-section, .stats-section {
  padding: 3rem 1rem;
  background: rgba(255, 255, 255, 0.95);
  margin: 2rem 0;
}

.section-header {
  text-align: center;
  margin-bottom: 3rem;
}

.section-header h2 {
  font-size: 2.5rem;
  font-weight: 800;
  color: #2d3748;
  margin: 0 0 1rem 0;
}

.section-header p {
  font-size: 1.2rem;
  color: #666;
  margin: 0;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.feature-card {
  background: white;
  padding: 2rem;
  border-radius: 16px;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
  text-align: center;
  transition: transform 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-4px);
}

.feature-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.feature-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #2d3748;
  margin: 0 0 1rem 0;
}

.feature-description {
  color: #666;
  margin: 0 0 1rem 0;
  line-height: 1.6;
}

.feature-highlight {
  background: linear-gradient(45deg, #4facfe, #00f2fe);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 12px;
  font-weight: 600;
  display: inline-block;
}

.patterns-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2rem;
  max-width: 1400px;
  margin: 0 auto;
}

.pattern-card {
  background: white;
  padding: 2rem;
  border-radius: 16px;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.pattern-card:hover {
  transform: translateY(-4px);
}

.pattern-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.pattern-name {
  font-size: 1.3rem;
  font-weight: 700;
  color: #2d3748;
  margin: 0;
}

.pattern-weight {
  background: linear-gradient(45deg, #4facfe, #00f2fe);
  color: white;
  padding: 0.3rem 0.8rem;
  border-radius: 12px;
  font-weight: 600;
}

.pattern-formula {
  background: #f7fafc;
  padding: 0.8rem;
  border-radius: 8px;
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
  color: #4a5568;
  margin-bottom: 1rem;
}

.pattern-description {
  color: #666;
  margin: 0 0 1.5rem 0;
  line-height: 1.6;
}

.examples-label {
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 0.5rem;
}

.examples-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.example-item {
  background: #e2e8f0;
  color: #4a5568;
  padding: 0.3rem 0.8rem;
  border-radius: 12px;
  font-size: 0.9rem;
  font-weight: 500;
}

.pattern-quality {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 1rem;
  border-top: 1px solid #e2e8f0;
}

.quality-label {
  font-weight: 600;
  color: #4a5568;
}

.quality-value {
  font-weight: 700;
  color: #4facfe;
  font-size: 1.1rem;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  max-width: 800px;
  margin: 0 auto;
}

.stats-overview, .quality-distribution {
  background: white;
  padding: 2rem;
  border-radius: 16px;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.stats-overview h3, .quality-distribution h3 {
  font-size: 1.3rem;
  font-weight: 700;
  color: #2d3748;
  margin: 0 0 1.5rem 0;
}

.stats-items, .distribution-items {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.stat-item, .distribution-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid #e2e8f0;
}

.stat-label, .dist-range {
  font-weight: 600;
  color: #4a5568;
}

.stat-value, .dist-count {
  font-weight: 700;
  color: #4facfe;
}

.dist-percentage {
  color: #666;
  font-size: 0.9rem;
}

.page-controls {
  text-align: center;
  margin: 2rem 0;
}

.control-button {
  background: rgba(255, 255, 255, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 0.8rem 1.5rem;
  border-radius: 12px;
  margin: 0 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 600;
}

.control-button:hover {
  background: rgba(255, 255, 255, 0.3);
}

.control-button.active {
  background: white;
  color: #4facfe;
}

.debug-section {
  padding: 3rem 1rem;
  background: rgba(255, 255, 255, 0.95);
  margin: 2rem 0;
}

.debug-content {
  max-width: 1200px;
  margin: 0 auto;
}

.debug-content h3 {
  font-size: 2rem;
  font-weight: 700;
  color: #2d3748;
  text-align: center;
  margin: 0 0 2rem 0;
}

.debug-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.debug-item {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.debug-item strong {
  display: block;
  font-size: 1.1rem;
  color: #2d3748;
  margin-bottom: 0.5rem;
}

.debug-item p {
  color: #666;
  margin: 0;
  line-height: 1.6;
}

.debug-note {
  text-align: center;
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.debug-note p {
  margin: 0;
  font-size: 1.1rem;
  color: #4a5568;
  line-height: 1.6;
}

@media (max-width: 768px) {
  .hero-title {
    font-size: 2.5rem;
  }
  
  .patterns-grid {
    grid-template-columns: 1fr;
  }
  
  .hero-highlights {
    gap: 2rem;
  }
}
</style>
