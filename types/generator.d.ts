// Enhanced type definitions for the improved generator system

// 语义标签枚举
export type SemanticTag = 
  | '自然'     // 自然元素：山、海、星、月
  | '情感'     // 情感状态：快乐、忧伤、激动
  | '动作'     // 动作行为：飞翔、奔跑、舞蹈
  | '品质'     // 品质特征：勇敢、智慧、优雅
  | '时空'     // 时空概念：黎明、永恒、瞬间
  | '力量'     // 力量相关：强大、无敌、超越
  | '神秘'     // 神秘元素：幻想、魔法、未知
  | '科技'     // 科技概念：数字、网络、未来

// 文化标签枚举
export type CulturalTag = 
  | '传统'     // 传统文化：古风、经典
  | '网络'     // 网络文化：游戏、社交
  | '二次元'   // 二次元文化：动漫、萌系
  | '流行'     // 流行文化：时尚、潮流
  | '学术'     // 学术文化：专业、严肃
  | '搞怪'     // 搞怪文化：幽默、沙雕

// 词性标签枚举（基于thinking.md的POS系统）
export type POSTag = 
  | 'PREF'     // 夸张/情绪前缀：超、究极、暴躁
  | 'CORE'     // 核心名词/主体：兔头、高玩、大神
  | 'SUF'      // 称号/身份后缀：大魔王、守护者
  | 'LINK'     // 柔性连接：的、之、·
  | 'EMO'      // Emoji/拟声：🔥、喵
  | 'LOC'      // 地域/场景：成都、深夜
  | 'ACTION'   // 动作/状态：暴走、摸鱼

// 语调枚举
export type Tone = '正面' | '中性' | '负面' | '搞怪'

// 增强的组件项定义
export interface EnhancedWordItem {
  word: string
  weight: number
  semantic_tags: SemanticTag[]
  cultural_tags: CulturalTag[]
  pos_tags: POSTag[]
  tone: Tone
  tags?: string[]  // 保持向后兼容
  rarity?: 'common' | 'uncommon' | 'rare' | 'trending'  // 稀有度
  era?: 'ancient' | 'modern' | 'future'  // 时代感
}

// 语义约束定义
export interface SemanticConstraints {
  [slotName: string]: {
    semantic_tags?: SemanticTag[]
    cultural_tags?: CulturalTag[]
    pos_tags?: POSTag[]
    tone?: Tone[]
    exclude_words?: string[]  // 排除特定词汇
    min_rarity?: 'common' | 'uncommon' | 'rare'  // 最低稀有度
  }
}

// 增强的语法模板定义
export interface EnhancedGrammarTemplate {
  name: string
  pattern: string[]  // ["PREF#adj", "CORE#noun", "SUF#suffix"]
  semantic_constraints: SemanticConstraints
  cultural_style: CulturalTag[]
  min_slots: number
  max_slots: number
  weight: number
  examples: string[]
  description?: string  // 模板描述
  suitable_scenarios?: string[]  // 适用场景
}

// 增强的模式定义
export interface EnhancedPattern {
  name: string
  weight: number
  templates: EnhancedGrammarTemplate[]
  components: Record<string, EnhancedWordItem[]>
  cultural_context: CulturalTag[]  // 文化背景
  semantic_theme: SemanticTag[]    // 语义主题
}

// 增强的文化数据定义
export interface EnhancedCulturalData {
  meta: {
    language: string
    category: string
    version: string
    updated_at: string
    checksum?: string
    semantic_version: string  // 语义版本
  }
  patterns: EnhancedPattern[]
  semantic_associations?: Record<string, string[]>  // 语义联想图谱
  cultural_rules?: {
    preferred_combinations: Array<{
      tags: CulturalTag[]
      boost_weight: number
    }>
    forbidden_combinations: Array<{
      tags: CulturalTag[]
      reason: string
    }>
  }
}

// 生成选项增强
export interface EnhancedGenerateOptions {
  language?: string
  category?: string
  length?: number
  slot_count?: number  // 新增：槽位数控制
  includeTrends?: boolean
  maxRetry?: number
  cultural_preference?: CulturalTag[]  // 文化偏好
  semantic_preference?: SemanticTag[]  // 语义偏好
  tone_preference?: Tone[]  // 语调偏好
  rarity_preference?: 'common' | 'mixed' | 'rare'  // 稀有度偏好
  avoid_repetition?: boolean  // 避免重复
  enable_semantic_chain?: boolean  // 启用语义链
}

// 用户反馈数据
export interface UserFeedback {
  username: string
  action: 'copy' | 'regenerate' | 'like' | 'dislike' | 'share'
  timestamp: number
  user_id?: string
  session_id: string
  cultural_context?: CulturalTag[]
  semantic_context?: SemanticTag[]
}

// 权重学习数据
export interface WeightLearningData {
  word: string
  positive_feedback: number
  negative_feedback: number
  total_exposures: number
  last_updated: number
  cultural_context: Record<string, number>  // 不同文化背景下的表现
  semantic_context: Record<string, number>  // 不同语义背景下的表现
}

// 槽位长度配置
export interface SlotLengthConfig {
  language: string
  min_slots: number
  max_slots: number
  default_slots: number
  slot_weight_distribution: Record<number, number>  // 不同槽位数的权重分布
}

// 文化感知过滤器配置
export interface CulturalFilterConfig {
  cultural_compatibility_matrix: Record<string, Record<string, number>>  // 文化兼容性矩阵
  semantic_conflict_rules: Array<{
    conflicting_tags: SemanticTag[]
    severity: 'warning' | 'error'
    message: string
  }>
  temporal_context_rules: Record<string, {
    preferred_eras: string[]
    avoided_eras: string[]
  }>
}

// 语义联想系统
export interface SemanticAssociationSystem {
  associations: Record<string, string[]>  // 词汇联想图谱
  similarity_threshold: number
  max_chain_length: number
}

// A/B测试配置
export interface ABTestConfig {
  test_id: string
  variant: 'A' | 'B'
  traffic_split: number  // 0-1之间的流量分配
  test_parameters: {
    weight_boost_factor?: number
    cultural_preference_strength?: number
    semantic_chain_probability?: number
  }
}

// 生成结果元数据
export interface GenerationMetadata {
  template_used: string
  components_selected: Record<string, string>
  cultural_tags: CulturalTag[]
  semantic_tags: SemanticTag[]
  generation_time_ms: number
  retry_count: number
  slot_count: number
  quality_score?: number
}

// 完整的生成结果
export interface EnhancedGenerationResult {
  username: string
  metadata: GenerationMetadata
  alternatives?: string[]  // 备选方案
  explanation?: string     // 生成说明
}
