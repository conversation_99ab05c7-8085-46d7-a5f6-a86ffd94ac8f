<template>
  <div class="canva-generator">
    <!-- 简洁的生成界面 -->
    <div class="generator-content">
      <!-- 主要生成区域 -->
      <div class="generate-section">
        <h3 class="generate-title">开始创造你的专属用户名</h3>
        <p class="generate-description">点击按钮，立即获得为你量身定制的有趣用户名</p>
        
        <!-- 生成按钮 -->
        <button
          @click="generateUsername"
          :disabled="isGenerating"
          class="main-generate-btn"
        >
          <span v-if="isGenerating" class="loading-spinner"></span>
          <span v-else class="generate-icon">✨</span>
          {{ isGenerating ? '创造中...' : '开始创造我的专属用户名' }}
        </button>
        
        <!-- 高级选项切换 -->
        <button @click="toggleAdvanced" class="advanced-toggle-btn">
          <span class="toggle-icon">⚙️</span>
          {{ showAdvanced ? '收起高级选项' : '高级选项' }}
          <span class="arrow-icon" :class="{ 'rotated': showAdvanced }">▼</span>
        </button>
      </div>

      <!-- 高级配置区域 -->
      <div v-if="showAdvanced" class="advanced-section">
        <div class="advanced-content">
          <h4 class="advanced-title">个性化定制</h4>
          
          <!-- 风格选择 -->
          <div class="config-group">
            <label class="config-label">生成风格</label>
            <div class="style-grid">
              <div
                v-for="option in styleOptions"
                :key="option.value"
                class="style-option"
                :class="{ 'active': style === option.value }"
                @click="style = option.value"
              >
                <div class="style-name">{{ option.label }}</div>
                <div class="style-desc">{{ option.description }}</div>
              </div>
            </div>
          </div>

          <!-- 主题选择 -->
          <div class="config-group">
            <label class="config-label">主题标签</label>
            <div class="theme-grid">
              <div
                v-for="theme in themeOptions"
                :key="theme.value"
                class="theme-option"
                :class="{ 'active': themes.includes(theme.value) }"
                @click="toggleTheme(theme.value)"
              >
                <span class="theme-icon">{{ theme.icon }}</span>
                <span class="theme-label">{{ theme.label }}</span>
              </div>
            </div>
          </div>

          <!-- 复杂度控制 -->
          <div class="config-group">
            <label class="config-label">创意复杂度: {{ complexity }}</label>
            <input
              v-model.number="complexity"
              type="range"
              min="1"
              max="5"
              class="complexity-slider"
            />
            <div class="complexity-desc">{{ complexityDescription }}</div>
          </div>

          <!-- 生成数量 -->
          <div class="config-group">
            <label class="config-label">生成数量</label>
            <div class="count-options">
              <div
                v-for="num in [1, 2, 3]"
                :key="num"
                class="count-option"
                :class="{ 'active': count === num }"
                @click="count = num"
              >
                {{ num }}个
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 结果展示区域 -->
      <div v-if="results.length > 0" class="results-section">
        <h4 class="results-title">为你生成的专属用户名</h4>
        <div class="results-grid">
          <div
            v-for="(result, index) in results"
            :key="index"
            class="result-card"
            @click="copyToClipboard(result.username)"
          >
            <!-- <div class="result-content">
              <div class="result-username">{{ result.username }}</div>
              <div class="result-hint">点击复制</div>
            </div> -->
            <div class="result-action">
              <button @click.stop="copyToClipboard(result.username)" class="copy-button">
                📋
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 复制成功提示 -->
    <div v-if="copySuccess" class="copy-toast">
      ✅ 已复制到剪贴板
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

// 响应式数据
const isGenerating = ref(false)
const results = ref<any[]>([])
const showAdvanced = ref(false)
const copySuccess = ref(false)

// 配置选项
const style = ref('modern')
const themes = ref(['生活'])
const complexity = ref(3)
const count = ref(1)

// 配置选项数据
const styleOptions = [
  { value: 'modern', label: '现代', description: '时尚前卫的表达方式' },
  { value: 'classic', label: '经典', description: '传统优雅的命名风格' },
  { value: 'creative', label: '创意', description: '富有想象力的组合' },
  { value: 'professional', label: '专业', description: '正式商务的表达' },
  { value: 'casual', label: '随性', description: '轻松自然的风格' },
  { value: 'artistic', label: '文艺', description: '富有艺术气息' },
  { value: 'trendy', label: '潮流', description: '紧跟时代潮流' }
]

const themeOptions = [
  { value: '科技', label: '科技', icon: '💻' },
  { value: '职场', label: '职场', icon: '💼' },
  { value: '幽默', label: '幽默', icon: '😄' },
  { value: '创意', label: '创意', icon: '🎨' },
  { value: '文化', label: '文化', icon: '📚' },
  { value: '情感', label: '情感', icon: '💭' },
  { value: '美食', label: '美食', icon: '🍜' },
  { value: '生活', label: '生活', icon: '🌟' }
]

// 计算属性
const complexityDescription = computed(() => {
  const descriptions = {
    1: '简洁明了',
    2: '轻松有趣',
    3: '平衡创意',
    4: '丰富表达',
    5: '复杂深度'
  }
  return descriptions[complexity.value] || '平衡创意'
})

// 方法
const toggleAdvanced = () => {
  showAdvanced.value = !showAdvanced.value
}

const toggleTheme = (theme: string) => {
  const index = themes.value.indexOf(theme)
  if (index > -1) {
    if (themes.value.length > 1) {
      themes.value.splice(index, 1)
    }
  } else {
    themes.value.push(theme)
  }
}

const generateUsername = async () => {
  if (isGenerating.value) return
  
  isGenerating.value = true
  
  try {
    const response = await $fetch<{success: boolean, results: any[]}>('/api/v5-generate', {
      method: 'POST',
      body: {
        language: 'zh',
        style: style.value,
        themes: themes.value,
        complexity: complexity.value,
        count: count.value
      }
    })
    
    if (response.success) {
      results.value = response.results
    }
  } catch (error) {
    console.error('生成失败:', error)
  } finally {
    isGenerating.value = false
  }
}

const copyToClipboard = async (text: string) => {
  try {
    await navigator.clipboard.writeText(text)
    copySuccess.value = true
    setTimeout(() => {
      copySuccess.value = false
    }, 2000)
  } catch (error) {
    console.error('复制失败:', error)
  }
}
</script>

<style scoped>
.canva-generator {
  position: relative;
}

.generator-content {
  padding: 40px;
}

/* 生成区域 */
.generate-section {
  text-align: center;
  margin-bottom: 40px;
}

.generate-title {
  font-size: 1.8rem;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 12px 0;
}

.generate-description {
  font-size: 1rem;
  color: #6b7280;
  margin: 0 0 32px 0;
  line-height: 1.6;
}

.main-generate-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 16px 32px;
  border-radius: 50px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.main-generate-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(102, 126, 234, 0.4);
}

.main-generate-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.generate-icon {
  font-size: 1.2rem;
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.advanced-toggle-btn {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
  border: 1px solid rgba(102, 126, 234, 0.3);
  padding: 8px 16px;
  border-radius: 25px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.advanced-toggle-btn:hover {
  background: rgba(102, 126, 234, 0.2);
}

.arrow-icon {
  transition: transform 0.3s ease;
  font-size: 0.8rem;
}

.arrow-icon.rotated {
  transform: rotate(180deg);
}

/* 高级配置区域 */
.advanced-section {
  background: #f8fafc;
  border-radius: 16px;
  margin-bottom: 32px;
  animation: slideDown 0.3s ease;
}

@keyframes slideDown {
  from {
    opacity: 0;
    max-height: 0;
  }
  to {
    opacity: 1;
    max-height: 1000px;
  }
}

.advanced-content {
  padding: 32px;
}

.advanced-title {
  font-size: 1.3rem;
  font-weight: 600;
  color: #374151;
  margin: 0 0 24px 0;
  text-align: center;
}

.config-group {
  margin-bottom: 24px;
}

.config-label {
  display: block;
  font-weight: 600;
  color: #374151;
  margin-bottom: 12px;
  font-size: 1rem;
}

.style-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.style-option {
  background: white;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
}

.style-option:hover {
  border-color: #667eea;
  transform: translateY(-1px);
}

.style-option.active {
  border-color: #667eea;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.style-name {
  font-weight: 600;
  margin-bottom: 4px;
}

.style-desc {
  font-size: 0.85rem;
  opacity: 0.8;
}

.theme-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.theme-option {
  display: flex;
  align-items: center;
  gap: 8px;
  background: white;
  border: 2px solid #e5e7eb;
  border-radius: 20px;
  padding: 8px 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.theme-option:hover {
  border-color: #667eea;
  transform: translateY(-1px);
}

.theme-option.active {
  border-color: #667eea;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.complexity-slider {
  width: 100%;
  margin: 8px 0;
  accent-color: #667eea;
}

.complexity-desc {
  text-align: center;
  font-size: 0.9rem;
  color: #6b7280;
}

.count-options {
  display: flex;
  gap: 0.75rem;
  justify-content: center;
}

.count-option {
  background: white;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  padding: 0.75rem 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 600;
  min-width: 60px;
  text-align: center;
}

.count-option:hover {
  border-color: #667eea;
  transform: translateY(-1px);
}

.count-option.active {
  border-color: #667eea;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

/* 结果展示 */
.results-section {
  margin-top: 32px;
}

.results-title {
  font-size: 1.3rem;
  font-weight: 600;
  color: #374151;
  margin: 0 0 20px 0;
  text-align: center;
}

.results-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.result-card {
  background: white;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.result-card:hover {
  border-color: #667eea;
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(102, 126, 234, 0.2);
}

.result-username {
  font-size: 1.2rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
}

.result-hint {
  font-size: 0.85rem;
  color: #9ca3af;
}

.copy-button {
  background: #f3f4f6;
  border: none;
  border-radius: 8px;
  padding: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1.1rem;
}

.copy-button:hover {
  background: #e5e7eb;
  transform: scale(1.1);
}

/* 复制提示 */
.copy-toast {
  position: fixed;
  top: 20px;
  right: 20px;
  background: #10b981;
  color: white;
  padding: 12px 20px;
  border-radius: 8px;
  box-shadow: 0 8px 24px rgba(16, 185, 129, 0.3);
  z-index: 1000;
  animation: slideIn 0.3s ease;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .generator-content {
    padding: 24px;
  }
  
  .generate-title {
    font-size: 1.5rem;
  }
  
  .main-generate-btn {
    padding: 14px 24px;
    font-size: 1rem;
  }
  
  .style-grid {
    grid-template-columns: 1fr;
  }
  
  .theme-grid {
    justify-content: center;
  }
  
  .results-grid {
    grid-template-columns: 1fr;
  }
}
</style>
