<template>
  <div class="optimized-generator">
    <!-- 简单模式界面 -->
    <div v-if="!advancedMode" class="simple-mode">
      <div class="simple-header">
        <h2 class="simple-title">{{ hasGenerated ? '再来一个？' : '开始你的专属之旅' }}</h2>
        <p class="simple-desc">{{ hasGenerated ? '不满意？我们有更多惊喜等着你' : '点击按钮，立即获得为你量身定制的用户名' }}</p>
      </div>

      <!-- 生成按钮 -->
      <div class="generate-section">
        <button 
          @click="generateUsername" 
          :disabled="isLoading"
          class="generate-btn"
          :class="{ 'loading': isLoading }"
        >
          <span v-if="isLoading" class="loading-spinner"></span>
          <span v-else-if="!hasGenerated">{{ getRandomCTA() }}</span>
          <span v-else>{{ getRandomContinue() }}</span>
        </button>
        
        <!-- 高级模式切换 -->
        <button @click="toggleAdvancedMode" class="advanced-toggle">
          <span>🔧</span>
          {{ showAdvanced ? '收起高级选项' : '高级选项' }}
        </button>
      </div>

      <!-- 结果展示 -->
      <div v-if="results.length > 0" class="results-section">
        <div class="results-grid">
          <div 
            v-for="(result, index) in results" 
            :key="index"
            class="result-card"
            @click="copyToClipboard(result.username)"
          >
            <div class="result-main">
              <div class="result-username">{{ result.username }}</div>
              <div class="result-hint">点击复制</div>
            </div>
            <div class="result-actions">
              <button @click.stop="copyToClipboard(result.username)" class="copy-btn">
                📋
              </button>
            </div>
          </div>
        </div>

        <!-- 详情展开区域 -->
        <div class="details-section">
          <button @click="showDetails = !showDetails" class="details-toggle">
            <span>{{ showDetails ? '隐藏详情' : '查看详情' }}</span>
            <span class="toggle-icon" :class="{ 'rotated': showDetails }">▼</span>
          </button>
          
          <div v-if="showDetails" class="details-content">
            <div v-for="(result, index) in results" :key="index" class="detail-item">
              <h4>{{ result.username }}</h4>
              <div class="detail-info">
                <div class="quality-info">
                  <span class="quality-label">质量评分:</span>
                  <span class="quality-score" :class="getQualityClass(result.creativity_assessment.overall_score)">
                    {{ (result.creativity_assessment.overall_score * 100).toFixed(1) }}%
                  </span>
                </div>
                <div class="pattern-info">
                  <span class="pattern-label">生成模式:</span>
                  <span class="pattern-name">{{ getPatternName(result.pattern) }}</span>
                </div>
                <div class="elements-info">
                  <span class="elements-label">组成元素:</span>
                  <span class="elements-list">{{ result.elements_used.join(' + ') }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 高级模式界面 -->
    <div v-else class="advanced-mode">
      <div class="advanced-header">
        <h2 class="advanced-title">高级定制模式</h2>
        <p class="advanced-desc">精确控制生成参数，打造完美用户名</p>
        <button @click="toggleAdvancedMode" class="back-btn">
          ← 返回简单模式
        </button>
      </div>

      <!-- 高级选项 -->
      <div v-if="showAdvanced" class="advanced-options">
        <div class="advanced-content">
          <h4 class="advanced-title">高级配置选项</h4>

          <!-- 风格选择 -->
          <div class="config-group">
            <label class="config-label">生成风格</label>
            <div class="style-grid">
              <div
                v-for="option in styleOptions"
                :key="option.value"
                class="style-option"
                :class="{ 'active': style === option.value }"
                @click="style = option.value"
              >
                <div class="style-name">{{ option.label }}</div>
                <div class="style-desc">{{ option.description }}</div>
              </div>
            </div>
          </div>

          <!-- 主题选择 -->
          <div class="config-group">
            <label class="config-label">主题标签</label>
            <div class="theme-grid">
              <div
                v-for="theme in themeOptions"
                :key="theme.value"
                class="theme-option"
                :class="{ 'active': themes.includes(theme.value) }"
                @click="toggleTheme(theme.value)"
              >
                <span class="theme-icon">{{ theme.icon }}</span>
                <span class="theme-label">{{ theme.label }}</span>
              </div>
            </div>
          </div>

          <!-- 生成模式 -->
          <div class="config-group">
            <label class="config-label">生成模式</label>
            <select v-model="selectedPattern" class="pattern-select">
              <option
                v-for="pattern in patternOptions"
                :key="pattern.value"
                :value="pattern.value"
              >
                {{ pattern.label }}
              </option>
            </select>
            <div class="pattern-description">
              {{ patternOptions.find(p => p.value === selectedPattern)?.description || '根据风格和主题自动选择最佳模式' }}
            </div>
          </div>

          <!-- 创意复杂度 -->
          <div class="config-group">
            <label class="config-label">创意复杂度: {{ complexity }}</label>
            <input
              v-model.number="complexity"
              type="range"
              min="1"
              max="5"
              class="complexity-slider"
            />
            <div class="complexity-desc">{{ complexityDescription }}</div>
          </div>

          <!-- 生成数量 -->
          <div class="config-group">
            <label class="config-label">生成数量</label>
            <div class="count-options">
              <div
                v-for="num in [1, 2, 3]"
                :key="num"
                class="count-option"
                :class="{ 'active': count === num }"
                @click="count = num"
              >
                {{ num }}个
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 复制成功提示 -->
    <div v-if="copySuccess" class="copy-toast">
      ✅ 已复制到剪贴板
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'

// 响应式数据
const advancedMode = ref(false)
const showAdvanced = ref(false)
const hasGenerated = ref(false)
const isLoading = ref(false)
const results = ref([])
const showDetails = ref(false)
const copySuccess = ref(false)

// 配置选项
const style = ref('modern')
const themes = ref(['生活'])
const complexity = ref(3)
const selectedPattern = ref('')
const count = ref(1)

// 随机行动召唤文案
const ctaTexts = [
  '✨ 试试看',
  '🎯 给我一个惊喜',
  '🚀 开始探索',
  '🎭 发现我的名字',
  '⚡ 立即生成',
  '🎨 创造专属ID',
  '🌟 来点灵感'
]

// 随机继续操作文案
const continueTexts = [
  '🔄 换一个',
  '🎲 再试试',
  '✨ 更多选择',
  '🎯 下一个',
  '🚀 继续探索',
  '🎭 换个风格'
]

// 导入配置化选项
import {
  getAllStyleOptions,
  getAllThemeOptions,
  getAllPatternOptions
} from '~/config/generation-config'

// 配置选项数据 - 使用配置化管理
const styleOptions = getAllStyleOptions().map(style => ({
  value: style.id,
  label: style.label,
  description: style.description
}))

const themeOptions = getAllThemeOptions().map(theme => ({
  value: theme.id,
  label: theme.label,
  icon: theme.icon
}))

const patternOptions = [
  { value: '', label: '智能选择', description: '根据风格和主题自动选择最佳模式' },
  ...getAllPatternOptions().map(pattern => ({
    value: pattern.id,
    label: pattern.name,
    description: pattern.description
  }))
]

// 模式名称映射 - 使用配置化管理
const patternNames = Object.fromEntries(
  getAllPatternOptions().map(pattern => [pattern.id, pattern.name])
)

// 计算属性
const complexityDescription = computed(() => {
  const descriptions = {
    1: '简洁明了',
    2: '轻松有趣',
    3: '平衡创意',
    4: '丰富表达',
    5: '复杂深度'
  }
  return descriptions[complexity.value] || '平衡创意'
})

// 方法
const getRandomCTA = () => {
  return ctaTexts[Math.floor(Math.random() * ctaTexts.length)]
}

const getRandomContinue = () => {
  return continueTexts[Math.floor(Math.random() * continueTexts.length)]
}

const toggleAdvancedMode = () => {
  advancedMode.value = !advancedMode.value
  // 进入高级模式时自动展开高级选项
  if (advancedMode.value) {
    showAdvanced.value = true
  }
}

const toggleTheme = (theme: string) => {
  const index = themes.value.indexOf(theme)
  if (index > -1) {
    if (themes.value.length > 1) {
      themes.value.splice(index, 1)
    }
  } else {
    themes.value.push(theme)
  }
}

const generateUsername = async () => {
  isLoading.value = true

  try {
    const requestBody = {
      language: 'zh',
      style: style.value,
      themes: themes.value,
      complexity: complexity.value,
      count: count.value
    }

    // 如果指定了特定模式，添加到请求中
    if (selectedPattern.value) {
      requestBody.pattern = selectedPattern.value
    }

    const response = await $fetch('/api/v5-generate', {
      method: 'POST',
      body: requestBody
    })

    if (response.success) {
      results.value = response.results as V5GenerationResult[]
      hasGenerated.value = true
      showDetails.value = false
    }
  } catch (error) {
    console.error('生成失败:', error)
  } finally {
    isLoading.value = false
  }
}

const copyToClipboard = async (text: string) => {
  try {
    await navigator.clipboard.writeText(text)
    copySuccess.value = true
    setTimeout(() => {
      copySuccess.value = false
    }, 2000)
  } catch (error) {
    console.error('复制失败:', error)
  }
}

const getQualityClass = (score: number) => {
  if (score >= 0.9) return 'excellent'
  if (score >= 0.8) return 'good'
  if (score >= 0.7) return 'average'
  return 'poor'
}

const getPatternName = (pattern: string) => {
  return patternNames[pattern] || pattern
}

// 组件挂载时的初始化
onMounted(() => {
  // 可以在这里添加初始化逻辑
})
</script>

<style scoped>
.optimized-generator {
  padding: 2rem;
  max-width: 100%;
  position: relative;
}

/* 简单模式样式 */
.simple-mode {
  text-align: center;
}

.simple-header {
  margin-bottom: 3rem;
}

.simple-title {
  font-size: 2rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 1rem;
}

.simple-desc {
  font-size: 1.1rem;
  color: #6b7280;
  max-width: 500px;
  margin: 0 auto;
  line-height: 1.6;
}

.generate-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  margin-bottom: 3rem;
}

.generate-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 1rem 3rem;
  border-radius: 50px;
  font-size: 1.2rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 8px 24px rgba(102, 126, 234, 0.3);
  min-width: 200px;
  position: relative;
}

.generate-btn:hover:not(.loading) {
  transform: translateY(-2px);
  box-shadow: 0 12px 32px rgba(102, 126, 234, 0.4);
}

.generate-btn.loading {
  opacity: 0.7;
  cursor: not-allowed;
}

.loading-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.advanced-toggle {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
  border: 1px solid rgba(102, 126, 234, 0.3);
  padding: 0.5rem 1rem;
  border-radius: 25px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.advanced-toggle:hover {
  background: rgba(102, 126, 234, 0.2);
  transform: translateY(-1px);
}

/* 结果展示样式 */
.results-section {
  margin-top: 2rem;
}

.results-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.result-card {
  background: white;
  border: 2px solid #e5e7eb;
  border-radius: 16px;
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.result-card:hover {
  border-color: #667eea;
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(102, 126, 234, 0.2);
}

.result-main {
  flex: 1;
}

.result-username {
  font-size: 1.3rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.result-hint {
  font-size: 0.9rem;
  color: #9ca3af;
}

.copy-btn {
  background: #f3f4f6;
  border: none;
  border-radius: 8px;
  padding: 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1.2rem;
}

.copy-btn:hover {
  background: #e5e7eb;
  transform: scale(1.1);
}

/* 详情展示样式 */
.details-section {
  border-top: 1px solid #e5e7eb;
  padding-top: 2rem;
}

.details-toggle {
  background: none;
  border: none;
  color: #667eea;
  font-size: 1rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 0 auto 1rem;
  transition: all 0.3s ease;
}

.details-toggle:hover {
  color: #4f46e5;
}

.toggle-icon {
  transition: transform 0.3s ease;
}

.toggle-icon.rotated {
  transform: rotate(180deg);
}

.details-content {
  background: #f9fafb;
  border-radius: 12px;
  padding: 1.5rem;
  text-align: left;
}

.detail-item {
  margin-bottom: 1.5rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.detail-item:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.detail-item h4 {
  font-size: 1.2rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1rem;
}

.detail-info {
  display: grid;
  gap: 0.5rem;
}

.quality-score.excellent { color: #10b981; }
.quality-score.good { color: #3b82f6; }
.quality-score.average { color: #f59e0b; }
.quality-score.poor { color: #ef4444; }

/* 高级选项样式 */
.advanced-options {
  background: #f8fafc;
  border-radius: 16px;
  margin-top: 2rem;
  animation: slideDown 0.3s ease;
}

@keyframes slideDown {
  from {
    opacity: 0;
    max-height: 0;
  }
  to {
    opacity: 1;
    max-height: 1000px;
  }
}

.advanced-content {
  padding: 2rem;
}

.advanced-title {
  font-size: 1.3rem;
  font-weight: 600;
  color: #374151;
  margin: 0 0 1.5rem 0;
  text-align: center;
}

.config-group {
  margin-bottom: 1.5rem;
}

.config-label {
  display: block;
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.75rem;
  font-size: 1rem;
}

.style-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 0.75rem;
}

.style-option {
  background: white;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  padding: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
}

.style-option:hover {
  border-color: #667eea;
  transform: translateY(-1px);
}

.style-option.active {
  border-color: #667eea;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.style-name {
  font-weight: 600;
  margin-bottom: 0.25rem;
}

.style-desc {
  font-size: 0.85rem;
  opacity: 0.8;
}

.theme-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
}

.theme-option {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: white;
  border: 2px solid #e5e7eb;
  border-radius: 20px;
  padding: 0.5rem 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.theme-option:hover {
  border-color: #667eea;
  transform: translateY(-1px);
}

.theme-option.active {
  border-color: #667eea;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.pattern-select {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 1rem;
  background: white;
  cursor: pointer;
  transition: border-color 0.3s ease;
}

.pattern-select:focus {
  outline: none;
  border-color: #667eea;
}

.pattern-description {
  margin-top: 0.5rem;
  font-size: 0.9rem;
  color: #6b7280;
  text-align: center;
}

.complexity-slider {
  width: 100%;
  margin: 0.5rem 0;
  accent-color: #667eea;
}

.complexity-desc {
  text-align: center;
  font-size: 0.9rem;
  color: #6b7280;
}

.count-options {
  display: flex;
  gap: 0.75rem;
  justify-content: center;
}

.count-option {
  background: white;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  padding: 0.75rem 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 600;
  min-width: 60px;
  text-align: center;
}

.count-option:hover {
  border-color: #667eea;
  transform: translateY(-1px);
}

.count-option.active {
  border-color: #667eea;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

/* 复制提示 */
.copy-toast {
  position: fixed;
  top: 2rem;
  right: 2rem;
  background: #10b981;
  color: white;
  padding: 1rem 1.5rem;
  border-radius: 8px;
  box-shadow: 0 8px 24px rgba(16, 185, 129, 0.3);
  z-index: 1000;
  animation: slideIn 0.3s ease;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .optimized-generator {
    padding: 1rem;
  }
  
  .simple-title {
    font-size: 1.5rem;
  }
  
  .generate-btn {
    padding: 0.8rem 2rem;
    font-size: 1.1rem;
  }
  
  .results-grid {
    grid-template-columns: 1fr;
  }
  
  .result-card {
    padding: 1rem;
  }
  
  .copy-toast {
    top: 1rem;
    right: 1rem;
    left: 1rem;
    text-align: center;
  }
}

/* 暗黑模式适配 */
:root.dark .simple-title,
:root.dark .advanced-title {
  color: #f9fafb;
}

:root.dark .simple-desc,
:root.dark .advanced-desc {
  color: #d1d5db;
}

:root.dark .result-card {
  background: #374151;
  border-color: #4b5563;
}

:root.dark .result-username {
  color: #f9fafb;
}

:root.dark .result-hint {
  color: #9ca3af;
}

:root.dark .details-content {
  background: #374151;
}

:root.dark .detail-item h4 {
  color: #f9fafb;
}
</style>
