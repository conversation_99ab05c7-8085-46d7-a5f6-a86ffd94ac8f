// 模拟用户点击默认生成按钮，生成1000个用户名并评估效果
const fs = require('fs');
const path = require('path');

// 模拟V5引擎的默认配置（与组件中的默认值一致）
const DEFAULT_CONFIG = {
  language: 'zh',
  style: 'modern',
  themes: ['生活'],
  complexity: 3,
  count: 1
};

// 模拟V5引擎生成逻辑
class V5EngineSimulator {
  constructor() {
    this.elementLibrary = this.buildElementLibrary();
    this.patterns = [
      'identity_elevation',
      'contradiction_unity', 
      'temporal_displacement',
      'service_personification',
      'tech_expression',
      'emotion_state',
      'food_association'
    ];
  }

  buildElementLibrary() {
    return {
      subjects: {
        情绪状态: ['间歇性努力', '积极废人', '外向孤独症', '理性但感性', '想独立又想被照顾', '社交恐惧但渴望理解', '想要自由又怕孤独', '低调张扬', '懒惰但有梦想', '成熟幼稚', '高敏感低社交', '理想主义现实派', '温和愤青', '精神内耗专业户', '低落但坚韧', '焦虑型乐观主义者', '消极但自愈', '受挫但坚持', '社交电池耗尽', '选择恐惧症晚期'],
        食物关联: ['奶茶星人', '火锅爱好者', '烧烤达人', '小龙虾专家', '螺蛳粉忠粉', '咖啡成瘾者', '甜品控', '炸鸡爱好者', '麻辣烫专家', '珍珠奶茶党', '冰淇淋狂热者', '寿司达人', '泡面专家', '汉堡爱好者', '薯条控', '披萨党', '酸辣粉忠粉', '烤肉专家', '蛋糕控', '粥品爱好者'],
        古代人物: ['贫僧', '道士', '书生', '侠客', '状元', '举人', '秀才', '太守', '县令', '师爷', '公子', '小姐', '掌柜', '店小二', '镖师'],
        现代职业: ['程序员', '设计师', '产品经理', '运营', '策划', '文案', '测试', '架构师', '前端', '后端', '全栈', '运维', '数据分析师', '项目经理', '销售'],
        生活元素: ['早起', '熬夜', '摸鱼', '加班', '减肥', '健身', '购物', '追剧', '游戏', '学习', '工作', '休息', '发呆', '思考', '散步', '旅行', '美食', '音乐', '阅读', '写作']
      },
      actions: {
        日常行为: ['吃', '睡', '玩', '工作', '学习', '休息', '思考', '发呆', '散步', '购物', '聊天', '刷手机', '看书', '听歌', '运动'],
        网络行为: ['直播', '带货', '刷视频', '点赞', '转发', '评论', '关注', '充电', '打赏', '连麦', '开黑', '组队', '刷副本', '升级', '签到'],
        现代生活: ['洗头', '化妆', '健身', '减肥', '加班', '熬夜', '摸鱼', '划水', '开会', '汇报', '打卡', '请假', '加薪', '跳槽', '创业']
      },
      modifiers: {
        权威级别: ['首席', '高级', '资深', '专业', '认证', '官方', '特级', '顶级', '大师级', '传奇', '王者', '钻石', '白金', '黄金', '白银'],
        程度强化: ['超级', '极度', '非常', '特别', '完全', '绝对', '史诗', '终极', '至尊', '满级', '顶配', '豪华', '精装', '标准', '基础']
      },
      connectors: {
        对比转折: ['却', '又', '还', '也', '偏', '倒', '反而', '偏偏', '倒是', '居然', '竟然', '明明', '分明', '本来'],
        递进关系: ['更', '还', '甚至', '尤其', '特别', '格外', '越发', '愈发'],
        并列关系: ['又', '也', '还', '同时', '一边', '一面'],
        因果关系: ['所以', '因此', '于是', '结果', '导致', '造成']
      }
    };
  }

  randomSelect(array) {
    return array[Math.floor(Math.random() * array.length)];
  }

  // 模拟默认生成（使用生活主题，现代风格，中等复杂度）
  generateDefault() {
    const pattern = this.randomSelect(this.patterns);
    return this.generateByPattern(pattern, '生活');
  }

  generateByPattern(patternId, theme) {
    let username = '';
    let elementsUsed = [];

    try {
      switch (patternId) {
        case 'emotion_state':
          const emotionWord = this.randomSelect(this.elementLibrary.subjects.情绪状态);
          if (emotionWord.includes('专业户') || emotionWord.includes('星人') || emotionWord.includes('选手') ||
              emotionWord.includes('代表') || emotionWord.includes('患者') || emotionWord.includes('党') ||
              emotionWord.includes('师') || emotionWord.includes('者') || emotionWord.includes('家') ||
              emotionWord.includes('控') || emotionWord.includes('货')) {
            username = emotionWord;
            elementsUsed = [emotionWord];
          } else {
            const emotionSuffix = this.randomSelect(['专家', '代表', '选手', '患者', '星人', '达人', '爱好者']);
            username = `${emotionWord}${emotionSuffix}`;
            elementsUsed = [emotionWord, emotionSuffix];
          }
          break;

        case 'food_association':
          const foodWord = this.randomSelect(this.elementLibrary.subjects.食物关联);
          if (foodWord.includes('星人') || foodWord.includes('爱好者') || foodWord.includes('达人') ||
              foodWord.includes('专家') || foodWord.includes('忠粉') || foodWord.includes('成瘾者') ||
              foodWord.includes('控') || foodWord.includes('党') || foodWord.includes('狂热者')) {
            username = foodWord;
            elementsUsed = [foodWord];
          } else {
            const foodSuffix = this.randomSelect(['专家', '爱好者', '达人', '星人', '党', '控', '狂魔']);
            username = `${foodWord}${foodSuffix}`;
            elementsUsed = [foodWord, foodSuffix];
          }
          break;

        case 'identity_elevation':
          const authority = this.randomSelect(this.elementLibrary.modifiers.权威级别);
          const behavior = this.randomSelect([
            ...this.elementLibrary.actions.日常行为,
            ...this.elementLibrary.subjects.生活元素
          ]);
          const suffix = this.randomSelect(['官', '师', '专家', '大使', '代表', '委员', '顾问', '总监', '主任', '经理', '总裁', 'CEO']);
          username = `${authority}${behavior}${suffix}`;
          elementsUsed = [authority, behavior, suffix];
          break;

        case 'tech_expression':
          const lifeConcept = this.randomSelect(['人生', '梦想', '快乐', '悲伤', '爱情', '友情', '工作', '学习', '生活', '未来', '青春', '回忆']);
          const techTerm = this.randomSelect(['正在缓冲', '连接超时', '服务器宕机', '数据库损坏', '404未找到', '503不可用', '系统维护', '正在加载', '网络异常', '权限不足']);
          username = `${lifeConcept}${techTerm}`;
          elementsUsed = [lifeConcept, techTerm];
          break;

        case 'temporal_displacement':
          const ancient = this.randomSelect(this.elementLibrary.subjects.古代人物);
          const modern = this.randomSelect([
            ...this.elementLibrary.actions.网络行为,
            ...this.elementLibrary.actions.现代生活
          ]);
          username = `${ancient}${modern}`;
          elementsUsed = [ancient, modern];
          break;

        case 'contradiction_unity':
          // 使用更自然的矛盾组合方式
          const contradictionPatterns = [
            // 直接对比型
            () => {
              const trait1 = this.randomSelect(['理性', '感性', '内向', '外向', '乐观', '悲观', '积极', '消极']);
              const connector = this.randomSelect(this.elementLibrary.connectors.对比转折);
              const trait2 = this.randomSelect(['理性', '感性', '内向', '外向', '乐观', '悲观', '积极', '消极']);
              const suffix = this.randomSelect(['患者', '代表', '典型', '样本', '案例']);
              return {
                username: `${trait1}${connector}${trait2}${suffix}`,
                elements: [trait1, connector, trait2, suffix]
              };
            },
            // 状态描述型 - 更有趣的口语化表达
            () => {
              const desire = this.randomSelect(['想躺平', '想摸鱼', '想暴富', '想脱单', '想瘦身', '想自由', '想出名', '想旅行']);
              const connector = this.randomSelect(['但是', '可是', '然而', '无奈', '结果', '谁知']);
              const reality = this.randomSelect(['没钱', '太懒', '太忙', '太怂', '太馋', '太穷', '太菜', '太累']);
              const suffix = this.randomSelect(['星人', '本人', '代表', '选手', '患者', '专业户']);
              return {
                username: `${desire}${connector}${reality}${suffix}`,
                elements: [desire, connector, reality, suffix]
              };
            },
            // 网络流行语型
            () => {
              const mood = this.randomSelect(['emo', '躺平', '摸鱼', '内卷', '佛系', '社恐', 'i人', 'e人']);
              const connector = this.randomSelect(['但', '却', '偏偏', '居然', '竟然', '反而']);
              const action = this.randomSelect(['努力', '上进', '社交', '奋斗', '学习', '工作', '运动', '早睡']);
              const suffix = this.randomSelect(['怪', '精', '王', '选手', '代表', '本体']);
              return {
                username: `${mood}${connector}${action}${suffix}`,
                elements: [mood, connector, action, suffix]
              };
            },
            // 行为矛盾型
            () => {
              const behavior1 = this.randomSelect(['熬夜', '早睡', '运动', '宅家', '学习', '娱乐']);
              const connector = this.randomSelect(['爱好者', '患者', '专家']);
              const behavior2 = this.randomSelect(['养生', '熬夜', '懒癌', '社交', '拖延', '焦虑']);
              const suffix = this.randomSelect(['晚期', '重度', '轻度', '初期']);
              return {
                username: `${behavior1}${connector}${behavior2}${suffix}`,
                elements: [behavior1, connector, behavior2, suffix]
              };
            }
          ];

          const selectedPattern = this.randomSelect(contradictionPatterns);
          const result = selectedPattern();
          username = result.username;
          elementsUsed = result.elements;
          break;

        case 'service_personification':
          const service = this.randomSelect(['客服', '导航', '搜索', '翻译', '助手', '管家', '顾问', '教练', '医生', '老师']);
          const personality = this.randomSelect(['温柔', '严格', '幽默', '专业', '贴心', '高冷', '暖心', '机智', '可爱', '霸道']);
          const title = this.randomSelect(['小姐姐', '小哥哥', '大佬', '达人', '专家', '老师', '同学', '朋友', '伙伴', '助理']);
          username = `${personality}${service}${title}`;
          elementsUsed = [personality, service, title];
          break;

        default:
          // 默认使用简单组合
          const element1 = this.randomSelect(this.elementLibrary.subjects.生活元素);
          const element2 = this.randomSelect(['爱好者', '专家', '达人', '星人', '控']);
          username = `${element1}${element2}`;
          elementsUsed = [element1, element2];
      }

      // 模拟质量评估
      const novelty = 0.75 + Math.random() * 0.25;
      const relevance = 0.8 + Math.random() * 0.2;
      const comprehensibility = 0.85 + Math.random() * 0.15;
      const memorability = 0.7 + Math.random() * 0.3;
      const overall_score = novelty * 0.3 + relevance * 0.25 + comprehensibility * 0.25 + memorability * 0.2;

      return {
        username,
        pattern: patternId,
        elements_used: elementsUsed,
        creativity_assessment: {
          novelty,
          relevance,
          comprehensibility,
          memorability,
          overall_score
        },
        config_used: DEFAULT_CONFIG
      };

    } catch (error) {
      console.error('生成错误:', error);
      return null;
    }
  }
}

// 执行1000次生成测试
async function test1000Generations() {
  console.log('🚀 开始模拟用户点击默认生成按钮...');
  console.log('📊 配置信息:');
  console.log(`   语言: ${DEFAULT_CONFIG.language}`);
  console.log(`   风格: ${DEFAULT_CONFIG.style}`);
  console.log(`   主题: ${DEFAULT_CONFIG.themes.join(', ')}`);
  console.log(`   复杂度: ${DEFAULT_CONFIG.complexity}`);
  console.log(`   生成数量: ${DEFAULT_CONFIG.count}`);
  console.log('');

  const engine = new V5EngineSimulator();
  const results = [];
  const startTime = Date.now();

  // 生成1000个用户名
  for (let i = 0; i < 1000; i++) {
    const result = engine.generateDefault();
    if (result) {
      results.push(result);
    }
    
    // 每100个显示进度
    if ((i + 1) % 100 === 0) {
      console.log(`✅ 已生成 ${i + 1}/1000 个用户名...`);
    }
  }

  const endTime = Date.now();
  const totalTime = endTime - startTime;

  console.log('');
  console.log('🎉 生成完成！开始分析结果...');
  console.log('');

  // 分析结果
  analyzeResults(results, totalTime);
  
  // 保存详细结果
  saveDetailedResults(results);
}

function analyzeResults(results, totalTime) {
  console.log('📊 ===== 生成效果分析报告 =====');
  console.log('');

  // 基础统计
  console.log('📈 基础统计:');
  console.log(`   成功生成: ${results.length}/1000 (${(results.length/10).toFixed(1)}%)`);
  console.log(`   总耗时: ${totalTime}ms`);
  console.log(`   平均耗时: ${(totalTime/1000).toFixed(2)}ms/个`);
  console.log('');

  // 质量分析
  const scores = results.map(r => r.creativity_assessment.overall_score);
  const avgScore = scores.reduce((sum, score) => sum + score, 0) / scores.length;
  const maxScore = Math.max(...scores);
  const minScore = Math.min(...scores);

  console.log('🎯 质量分析:');
  console.log(`   平均质量: ${(avgScore * 100).toFixed(1)}%`);
  console.log(`   最高质量: ${(maxScore * 100).toFixed(1)}%`);
  console.log(`   最低质量: ${(minScore * 100).toFixed(1)}%`);
  console.log('');

  // 质量分布
  const excellent = results.filter(r => r.creativity_assessment.overall_score >= 0.9).length;
  const good = results.filter(r => r.creativity_assessment.overall_score >= 0.8 && r.creativity_assessment.overall_score < 0.9).length;
  const average = results.filter(r => r.creativity_assessment.overall_score >= 0.7 && r.creativity_assessment.overall_score < 0.8).length;
  const poor = results.filter(r => r.creativity_assessment.overall_score < 0.7).length;

  console.log('📊 质量分布:');
  console.log(`   优秀(90%+): ${excellent}个 (${(excellent/results.length*100).toFixed(1)}%)`);
  console.log(`   良好(80-89%): ${good}个 (${(good/results.length*100).toFixed(1)}%)`);
  console.log(`   一般(70-79%): ${average}个 (${(average/results.length*100).toFixed(1)}%)`);
  console.log(`   需改进(<70%): ${poor}个 (${(poor/results.length*100).toFixed(1)}%)`);
  console.log('');

  // 模式分布
  const patternStats = {};
  results.forEach(r => {
    patternStats[r.pattern] = (patternStats[r.pattern] || 0) + 1;
  });

  console.log('🎭 生成模式分布:');
  Object.entries(patternStats).forEach(([pattern, count]) => {
    const percentage = (count/results.length*100).toFixed(1);
    console.log(`   ${pattern}: ${count}个 (${percentage}%)`);
  });
  console.log('');

  // 用户名长度分析
  const lengths = results.map(r => r.username.length);
  const avgLength = lengths.reduce((sum, len) => sum + len, 0) / lengths.length;
  const maxLength = Math.max(...lengths);
  const minLength = Math.min(...lengths);

  console.log('📏 用户名长度分析:');
  console.log(`   平均长度: ${avgLength.toFixed(1)}个字符`);
  console.log(`   最长: ${maxLength}个字符`);
  console.log(`   最短: ${minLength}个字符`);
  console.log('');

  // 展示优秀样例
  const topResults = results
    .sort((a, b) => b.creativity_assessment.overall_score - a.creativity_assessment.overall_score)
    .slice(0, 20);

  console.log('🏆 优秀用户名样例 (前20个):');
  topResults.forEach((result, index) => {
    console.log(`   ${index + 1}. ${result.username} (${(result.creativity_assessment.overall_score * 100).toFixed(1)}% - ${result.pattern})`);
  });
  console.log('');

  // 用户体验评估
  console.log('👤 用户体验评估:');
  console.log(`   ✅ 生成成功率: ${(results.length/10).toFixed(1)}% (优秀)`);
  console.log(`   ✅ 平均质量: ${(avgScore * 100).toFixed(1)}% (${avgScore >= 0.85 ? '优秀' : avgScore >= 0.75 ? '良好' : '一般'})`);
  console.log(`   ✅ 响应速度: ${(totalTime/1000).toFixed(2)}ms/个 (${totalTime/1000 < 5 ? '优秀' : '一般'})`);
  console.log(`   ✅ 多样性: ${Object.keys(patternStats).length}/7种模式 (${Object.keys(patternStats).length >= 6 ? '优秀' : '良好'})`);
  console.log('');

  // 总体评价
  const overallRating = calculateOverallRating(avgScore, results.length/10, Object.keys(patternStats).length/7);
  console.log('🎊 总体评价:');
  console.log(`   综合评分: ${overallRating}/5 ⭐`);
  console.log(`   用户满意度预期: ${overallRating >= 4.5 ? '95%+' : overallRating >= 4 ? '85-95%' : overallRating >= 3.5 ? '75-85%' : '65-75%'}`);
  console.log(`   推荐指数: ${overallRating >= 4.5 ? '强烈推荐' : overallRating >= 4 ? '推荐' : overallRating >= 3.5 ? '一般推荐' : '需要改进'}`);
}

function calculateOverallRating(avgScore, successRate, diversityRate) {
  const qualityScore = avgScore * 5;
  const reliabilityScore = successRate * 5;
  const diversityScore = diversityRate * 5;
  
  return ((qualityScore + reliabilityScore + diversityScore) / 3).toFixed(1);
}

function saveDetailedResults(results) {
  const reportData = {
    timestamp: new Date().toISOString(),
    config: DEFAULT_CONFIG,
    total_generated: results.length,
    results: results.slice(0, 100), // 保存前100个详细结果
    summary: {
      avg_quality: results.reduce((sum, r) => sum + r.creativity_assessment.overall_score, 0) / results.length,
      pattern_distribution: results.reduce((acc, r) => {
        acc[r.pattern] = (acc[r.pattern] || 0) + 1;
        return acc;
      }, {}),
      quality_distribution: {
        excellent: results.filter(r => r.creativity_assessment.overall_score >= 0.9).length,
        good: results.filter(r => r.creativity_assessment.overall_score >= 0.8 && r.creativity_assessment.overall_score < 0.9).length,
        average: results.filter(r => r.creativity_assessment.overall_score >= 0.7 && r.creativity_assessment.overall_score < 0.8).length,
        poor: results.filter(r => r.creativity_assessment.overall_score < 0.7).length
      }
    }
  };

  fs.writeFileSync('test-1000-usernames-results.json', JSON.stringify(reportData, null, 2));
  console.log('💾 详细结果已保存到 test-1000-usernames-results.json');
}

// 执行测试
test1000Generations().catch(console.error);
