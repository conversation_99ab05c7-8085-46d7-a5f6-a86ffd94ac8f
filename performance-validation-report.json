{"timestamp": "2025-06-19T12:45:53.292Z", "testDuration": "18ms", "summary": {"totalTests": 22, "passedTests": 18, "failedTests": 4, "overallStatus": "🟢 良好"}, "results": {"performance": {"morphemeLibraryLoading": {"averageLoadTime": "0.39", "maxLoadTime": "1.44", "minLoadTime": "0.14", "successRate": "100.0", "totalMorphemes": 673, "tests": [{"iteration": 1, "loadTime": 0.611485, "morphemeCount": 673, "success": true}, {"iteration": 2, "loadTime": 0.289402, "morphemeCount": 673, "success": true}, {"iteration": 3, "loadTime": 0.173815, "morphemeCount": 673, "success": true}, {"iteration": 4, "loadTime": 0.163386, "morphemeCount": 673, "success": true}, {"iteration": 5, "loadTime": 0.425417, "morphemeCount": 673, "success": true}, {"iteration": 6, "loadTime": 0.168936, "morphemeCount": 673, "success": true}, {"iteration": 7, "loadTime": 1.443945, "morphemeCount": 673, "success": true}, {"iteration": 8, "loadTime": 0.279193, "morphemeCount": 673, "success": true}, {"iteration": 9, "loadTime": 0.164739, "morphemeCount": 673, "success": true}, {"iteration": 10, "loadTime": 0.136756, "morphemeCount": 673, "success": true}]}, "generation": {"averageGenerationTime": "0.06", "successRate": "100.0", "tests": [{"testCase": {"style": "modern", "themes": ["科技"], "complexity": 3, "count": 1}, "generationTime": 0.228337, "results": [{"username": "资深专业设计师", "pattern": "mock_pattern", "quality": 0.7305189479443284, "elements_used": ["mock_element_1", "mock_element_2"]}], "success": true}, {"testCase": {"style": "creative", "themes": ["情感"], "complexity": 4, "count": 2}, "generationTime": 0.01083, "results": [{"username": "资深专业学者", "pattern": "mock_pattern", "quality": 0.8337188459464855, "elements_used": ["mock_element_1", "mock_element_2"]}, {"username": "卓越专业程序员", "pattern": "mock_pattern", "quality": 0.9757114047136198, "elements_used": ["mock_element_1", "mock_element_2"]}], "success": true}, {"testCase": {"style": "classic", "themes": ["传统"], "complexity": 5, "count": 3}, "generationTime": 0.008436, "results": [{"username": "优秀创新诗人", "pattern": "mock_pattern", "quality": 0.7657371793210472, "elements_used": ["mock_element_1", "mock_element_2"]}, {"username": "优秀温柔设计师", "pattern": "mock_pattern", "quality": 0.7660261770646297, "elements_used": ["mock_element_1", "mock_element_2"]}, {"username": "杰出优雅设计师", "pattern": "mock_pattern", "quality": 0.9723704791066838, "elements_used": ["mock_element_1", "mock_element_2"]}], "success": true}, {"testCase": {"style": "professional", "themes": ["职场"], "complexity": 2, "count": 1}, "generationTime": 0.007805, "results": [{"username": "杰出创新程序员", "pattern": "mock_pattern", "quality": 0.9301145436036152, "elements_used": ["mock_element_1", "mock_element_2"]}], "success": true}, {"testCase": {"style": "casual", "themes": ["生活"], "complexity": 3, "count": 2}, "generationTime": 0.027962, "results": [{"username": "资深优雅程序员", "pattern": "mock_pattern", "quality": 0.7585557693094275, "elements_used": ["mock_element_1", "mock_element_2"]}, {"username": "资深温柔创作者", "pattern": "mock_pattern", "quality": 0.9837920187119507, "elements_used": ["mock_element_1", "mock_element_2"]}], "success": true}]}, "memoryUsage": {"initialMemory": {"rss": 46579712, "heapTotal": 6565888, "heapUsed": 4551136, "external": 1430936, "arrayBuffers": 10515}, "finalMemory": {"rss": 46710784, "heapTotal": 6565888, "heapUsed": 4634872, "external": 1430976, "arrayBuffers": 10515}, "memoryIncrease": {"rss": 131072, "heapUsed": 83736, "heapTotal": 0}}}, "quality": {"diversity": {"score": 0.72, "totalGenerated": 100, "uniqueCount": 72, "details": "Based on 100 mock generations"}, "uniqueness": {"score": 0.8892044047064902, "details": "Based on morpheme combination analysis"}, "culturalRelevance": {"score": 0.8648686212617249, "details": "Based on traditional and modern cultural elements integration"}, "readability": {"score": 0.9691772977371669, "details": "Based on character length and phonetic analysis"}}, "compatibility": {"configurationIntegrity": {"elementLibraryConfig": {"passed": true, "details": {"hasSubjects": true, "hasTraits": true, "hasModifiers": true, "hasNewCategories": true, "fileSize": 13275}}, "expandedLibraryConfig": {"passed": true, "details": {"hasNewConfigs": true, "fileSize": 8826}}, "syntaxValidation": {"passed": true, "details": {"bracesMatched": true, "bracketsMatched": true, "openBraces": 66, "closeBraces": 66, "openBrackets": 111, "closeBrackets": 111}}}, "apiEndpoint": {"passed": true, "details": {"apiFileExists": true}}, "configurationLoading": {"passed": true, "details": {"configFilesExist": true, "checkedPaths": 2}}, "errorHandling": {"passed": true, "details": {"errorHandlingImplemented": true}}}, "errors": []}}