<script setup lang="ts">
import { useHead } from '#app'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

useHead({
  title: t('app.title'),
  meta: [
    {
      name: 'description',
      content: t('app.description')
    }
  ]
})
</script>

<template>
  <div class="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
    <!-- Hero Section -->
    <div class="hero-section">
      <div class="container mx-auto px-4 py-16">
        <div class="text-center max-w-4xl mx-auto">
          <!-- 主标题 -->
          <h1 class="hero-title">
            发现你的专属中文用户名
          </h1>

          <!-- 副标题 -->
          <p class="hero-subtitle">
            3秒获得有趣又有内涵的网名，让你在网络世界中独一无二
          </p>

          <!-- 特色标签 -->
          <div class="feature-tags">
            <span class="tag">✨ 创意无限</span>
            <span class="tag">🎭 文化内涵</span>
            <span class="tag">⚡ 即时生成</span>
            <span class="tag">🎯 个性专属</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 生成器区域 -->
    <div class="generator-section">
      <div class="container mx-auto px-4">
        <ClientOnly>
          <OptimizedUsernameGenerator />
        </ClientOnly>
      </div>
    </div>

    <!-- 价值展示区域 -->
    <div class="value-section">
      <div class="container mx-auto px-4 py-16">
        <div class="text-center mb-12">
          <h2 class="section-title">为什么选择我们？</h2>
          <p class="section-subtitle">不只是随机组合，而是智能创作</p>
        </div>

        <div class="value-grid">
          <div class="value-card">
            <div class="value-icon">🎨</div>
            <h3>创意无限</h3>
            <p>基于3000+真实语素，智能组合出既有趣又有意义的用户名</p>
          </div>

          <div class="value-card">
            <div class="value-icon">🏮</div>
            <h3>文化底蕴</h3>
            <p>融合传统文化与现代表达，让你的网名既时尚又有内涵</p>
          </div>

          <div class="value-card">
            <div class="value-icon">⚡</div>
            <h3>即时体验</h3>
            <p>无需注册，无需等待，点击即可获得专属用户名</p>
          </div>

          <div class="value-card">
            <div class="value-icon">🎯</div>
            <h3>个性匹配</h3>
            <p>多种风格主题，精准匹配你的个性和使用场景</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 使用场景展示 -->
    <div class="scenarios-section">
      <div class="container mx-auto px-4 py-16">
        <div class="text-center mb-12">
          <h2 class="section-title">适用场景</h2>
          <p class="section-subtitle">无论何时何地，都能找到合适的用户名</p>
        </div>

        <div class="scenarios-grid">
          <div class="scenario-card">
            <div class="scenario-icon">🎮</div>
            <h4>游戏昵称</h4>
            <p>让你在游戏世界中脱颖而出</p>
          </div>

          <div class="scenario-card">
            <div class="scenario-icon">📱</div>
            <h4>社交媒体</h4>
            <p>微博、抖音、小红书的个性ID</p>
          </div>

          <div class="scenario-card">
            <div class="scenario-icon">✍️</div>
            <h4>创作笔名</h4>
            <p>写作、设计、艺术创作的专用名</p>
          </div>

          <div class="scenario-card">
            <div class="scenario-icon">💼</div>
            <h4>职场身份</h4>
            <p>专业又有趣的工作昵称</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* Hero Section */
.hero-section {
  position: relative;
  overflow: hidden;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 800;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  margin-bottom: 1.5rem;
  line-height: 1.2;
}

.hero-subtitle {
  font-size: 1.25rem;
  color: #6b7280;
  margin-bottom: 2rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.6;
}

.feature-tags {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 3rem;
}

.tag {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 25px;
  font-size: 0.9rem;
  font-weight: 500;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}

.tag:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(102, 126, 234, 0.4);
}

/* Generator Section */
.generator-section {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border-radius: 24px;
  margin: 0 auto 4rem;
  max-width: 800px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Value Section */
.value-section {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
  position: relative;
}

.value-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.1);
  pointer-events: none;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  position: relative;
  z-index: 1;
}

.section-subtitle {
  font-size: 1.1rem;
  opacity: 0.9;
  position: relative;
  z-index: 1;
}

.value-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  position: relative;
  z-index: 1;
}

.value-card {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 2rem;
  text-align: center;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.value-card:hover {
  transform: translateY(-5px);
  background: rgba(255, 255, 255, 0.2);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.value-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  display: block;
}

.value-card h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: white;
}

.value-card p {
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.6;
  font-size: 0.95rem;
}

/* Scenarios Section */
.scenarios-section {
  background: white;
}

.scenarios-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
}

.scenario-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 16px;
  padding: 2rem 1.5rem;
  text-align: center;
  transition: all 0.3s ease;
  box-shadow: 0 8px 24px rgba(102, 126, 234, 0.3);
}

.scenario-card:hover {
  transform: translateY(-5px) scale(1.02);
  box-shadow: 0 12px 32px rgba(102, 126, 234, 0.4);
}

.scenario-icon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  display: block;
}

.scenario-card h4 {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.scenario-card p {
  font-size: 0.9rem;
  opacity: 0.9;
  line-height: 1.4;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .hero-title {
    font-size: 2.5rem;
  }

  .hero-subtitle {
    font-size: 1.1rem;
  }

  .section-title {
    font-size: 2rem;
  }

  .feature-tags {
    gap: 0.5rem;
  }

  .tag {
    font-size: 0.8rem;
    padding: 0.4rem 0.8rem;
  }

  .value-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .scenarios-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }
}

@media (max-width: 480px) {
  .hero-title {
    font-size: 2rem;
  }

  .scenarios-grid {
    grid-template-columns: 1fr;
  }
}

/* 暗黑模式适配 */
:root.dark .hero-subtitle {
  color: #9ca3af;
}

:root.dark .generator-section {
  background: rgba(31, 41, 55, 0.8);
  border: 1px solid rgba(75, 85, 99, 0.3);
}

:root.dark .scenarios-section {
  background: #111827;
}

:root.dark .section-title {
  color: #f9fafb;
}

:root.dark .section-subtitle {
  color: #d1d5db;
}
</style>
