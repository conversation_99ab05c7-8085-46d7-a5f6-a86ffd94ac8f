<template>
  <div class="canva-style-page">
    <!-- 优化的Canva风格头部 -->
    <header class="canva-header">
      <div class="container mx-auto px-4">
        <div class="header-content">
          <NuxtLink to="/" class="header-logo">
            <h1 class="canva-title">用户名生成器</h1>
          </NuxtLink>
          <p class="canva-subtitle">专业的中文用户名创意生成平台</p>
          
          <!-- 导航链接 -->
          <div class="header-nav">
            <NuxtLink to="/" class="nav-link">首页</NuxtLink>
            <NuxtLink to="/v4" class="nav-link">V4引擎</NuxtLink>
            <NuxtLink to="/v5" class="nav-link">V5引擎</NuxtLink>
            <NuxtLink to="/about" class="nav-link">关于我们</NuxtLink>
          </div>
        </div>
      </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="canva-main">
      <div class="container mx-auto px-4">
        <!-- Hero区域 -->
        <section class="hero-section">
          <div class="hero-content">
            <h2 class="hero-title">发现你的专属中文用户名</h2>
            <p class="hero-description">
              3秒获得有趣又有内涵的网名，让你在网络世界中独一无二
            </p>
            <div class="hero-features">
              <div class="feature-item">
                <div class="feature-icon">✨</div>
                <span>创意无限</span>
              </div>
              <div class="feature-item">
                <div class="feature-icon">🎭</div>
                <span>文化内涵</span>
              </div>
              <div class="feature-item">
                <div class="feature-icon">⚡</div>
                <span>即时生成</span>
              </div>
              <div class="feature-item">
                <div class="feature-icon">🎯</div>
                <span>个性专属</span>
              </div>
            </div>
          </div>
        </section>

        <!-- 生成器区域 -->
        <section class="generator-section">
          <div class="generator-card">
            <ClientOnly>
              <CanvaStyleGenerator />
            </ClientOnly>
          </div>
        </section>

        <!-- 特色展示区域 -->
        <section class="features-showcase">
          <h2 class="section-title">我们的优势</h2>
          <p class="section-subtitle">不只是随机组合，而是智能创作</p>
          
          <div class="showcase-grid">
            <div class="showcase-item">
              <div class="showcase-icon">🎨</div>
              <h3>智能创作</h3>
              <p>基于3000+真实语素，智能组合出既有趣又有意义的用户名</p>
            </div>
            <div class="showcase-item">
              <div class="showcase-icon">🏮</div>
              <h3>文化底蕴</h3>
              <p>融合传统文化与现代表达，让你的网名既时尚又有内涵</p>
            </div>
            <div class="showcase-item">
              <div class="showcase-icon">⚡</div>
              <h3>即时体验</h3>
              <p>无需注册，无需等待，点击即可获得专属用户名</p>
            </div>
            <div class="showcase-item">
              <div class="showcase-icon">🎯</div>
              <h3>个性匹配</h3>
              <p>多种风格主题，精准匹配你的个性和使用场景</p>
            </div>
          </div>
        </section>
        
        <!-- 使用场景展示 -->
        <section class="scenarios-section">
          <h2 class="section-title">适用场景</h2>
          <p class="section-subtitle">无论何时何地，都能找到合适的用户名</p>
          
          <div class="scenarios-grid">
            <div class="scenario-card">
              <div class="scenario-icon">🎮</div>
              <h4>游戏昵称</h4>
              <p>让你在游戏世界中脱颖而出</p>
            </div>
            <div class="scenario-card">
              <div class="scenario-icon">📱</div>
              <h4>社交媒体</h4>
              <p>微博、抖音、小红书的个性ID</p>
            </div>
            <div class="scenario-card">
              <div class="scenario-icon">✍️</div>
              <h4>创作笔名</h4>
              <p>写作、设计、艺术创作的专用名</p>
            </div>
            <div class="scenario-card">
              <div class="scenario-icon">💼</div>
              <h4>职场身份</h4>
              <p>专业又有趣的工作昵称</p>
            </div>
          </div>
        </section>
      </div>
    </main>

    <!-- 优化的Canva风格页脚 -->
    <footer class="canva-footer">
      <div class="container mx-auto px-4">
        <div class="footer-content">
          <div class="footer-grid">
            <div class="footer-section">
              <h4 class="footer-title">关于我们</h4>
              <p>专注于中文用户名生成的创意平台，让您的网络身份更加独特有趣。</p>
            </div>
            <div class="footer-section">
              <h4 class="footer-title">快速链接</h4>
              <div class="footer-links">
                <NuxtLink to="/" class="footer-link">首页</NuxtLink>
                <NuxtLink to="/v4" class="footer-link">V4引擎</NuxtLink>
                <NuxtLink to="/v5" class="footer-link">V5引擎</NuxtLink>
                <NuxtLink to="/about" class="footer-link">关于我们</NuxtLink>
              </div>
            </div>
            <div class="footer-section">
              <h4 class="footer-title">联系我们</h4>
              <p>有任何问题或建议，欢迎随时联系我们</p>
              <p class="footer-contact"><EMAIL></p>
            </div>
          </div>
          <div class="footer-bottom">
            <p>&copy; 2025 用户名生成服务. 专业的中文用户名创意平台</p>
          </div>
        </div>
      </div>
    </footer>
  </div>
</template>

<script setup>
// 页面元数据
definePageMeta({
  layout: 'default'
});

useHead({
  title: '用户名生成服务 - 专业的中文用户名创意平台',
  meta: [
    { name: 'description', content: '3秒获得有趣又有内涵的中文用户名，让你在网络世界中独一无二' },
    { name: 'keywords', content: '用户名生成,中文网名,创意用户名,个性昵称,V5引擎,V4引擎' }
  ]
});
</script>

<style scoped>
/* Canva风格的设计系统 - 优化配色与整体项目一致 */
.canva-style-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #3B82F6 0%, #8B5CF6 100%);
  font-family: 'Inter var', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.container {
  max-width: 1366px;
  margin: 0 auto;
  padding: 0 24px;
}

/* 优化的头部样式 */
.canva-header {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding: 20px 0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: white;
}

.header-logo {
  text-decoration: none;
  color: white;
}

.canva-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 8px 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  background: linear-gradient(135deg, #ffffff 0%, #e0e7ff 100%);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

.canva-subtitle {
  font-size: 1.1rem;
  margin: 0 0 16px 0;
  opacity: 0.9;
}

.header-nav {
  display: flex;
  gap: 24px;
  margin-top: 8px;
}

.nav-link {
  color: white;
  text-decoration: none;
  font-weight: 500;
  padding: 6px 12px;
  border-radius: 20px;
  transition: all 0.3s ease;
}

.nav-link:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

/* 主要内容区域 */
.canva-main {
  padding: 40px 0 80px;
}

/* Hero区域 */
.hero-section {
  text-align: center;
  margin-bottom: 60px;
}

.hero-content {
  max-width: 800px;
  margin: 0 auto;
  color: white;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 800;
  margin: 0 0 24px 0;
  line-height: 1.2;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  background: linear-gradient(135deg, #ffffff 0%, #e0e7ff 100%);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

.hero-description {
  font-size: 1.25rem;
  margin: 0 0 40px 0;
  line-height: 1.6;
  opacity: 0.95;
}

.hero-features {
  display: flex;
  justify-content: center;
  gap: 16px;
  flex-wrap: wrap;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 12px;
  background: rgba(255, 255, 255, 0.15);
  padding: 12px 20px;
  border-radius: 50px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.feature-item:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-2px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.feature-icon {
  font-size: 1.5rem;
}

/* 生成器区域 */
.generator-section {
  margin-bottom: 80px;
}

.generator-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  overflow: hidden;
  transition: transform 0.3s ease;
}

.generator-card:hover {
  transform: translateY(-5px);
}

/* 特色展示区域 */
.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  text-align: center;
  margin: 0 0 12px 0;
  background: linear-gradient(135deg, #ffffff 0%, #e0e7ff 100%);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

.section-subtitle {
  font-size: 1.2rem;
  text-align: center;
  color: white;
  opacity: 0.9;
  margin: 0 0 40px 0;
}

.features-showcase {
  margin-bottom: 80px;
}

.showcase-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
}

.showcase-item {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  padding: 32px;
  text-align: center;
  color: white;
  transition: all 0.3s ease;
}

.showcase-item:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-4px);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.15);
}

.showcase-icon {
  font-size: 3rem;
  margin-bottom: 16px;
}

.showcase-item h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0 0 12px 0;
}

.showcase-item p {
  font-size: 1rem;
  line-height: 1.6;
  margin: 0;
  opacity: 0.9;
}

/* 使用场景区域 */
.scenarios-section {
  margin-bottom: 60px;
}

.scenarios-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 24px;
}

.scenario-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  padding: 24px;
  text-align: center;
  color: white;
  transition: all 0.3s ease;
}

.scenario-card:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-4px);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.15);
}

.scenario-icon {
  font-size: 2.5rem;
  margin-bottom: 16px;
}

.scenario-card h4 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 8px 0;
}

.scenario-card p {
  font-size: 0.95rem;
  line-height: 1.5;
  margin: 0;
  opacity: 0.9;
}

/* 优化的页脚样式 */
.canva-footer {
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding: 60px 0 24px;
  color: white;
}

.footer-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 40px;
  margin-bottom: 40px;
}

.footer-section {
  padding: 0 10px;
}

.footer-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 16px 0;
  color: #e0e7ff;
}

.footer-section p {
  margin: 0 0 12px 0;
  opacity: 0.8;
  line-height: 1.6;
}

.footer-links {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.footer-link {
  color: white;
  opacity: 0.8;
  text-decoration: none;
  transition: all 0.2s ease;
  padding: 4px 0;
}

.footer-link:hover {
  opacity: 1;
  transform: translateX(4px);
}

.footer-contact {
  font-weight: 500;
  color: #e0e7ff;
}

.footer-bottom {
  text-align: center;
  padding-top: 24px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-bottom p {
  margin: 0;
  opacity: 0.7;
  font-size: 0.9rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .container {
    padding: 0 16px;
  }
  
  .canva-title {
    font-size: 2rem;
  }
  
  .hero-title {
    font-size: 2.5rem;
  }
  
  .hero-description {
    font-size: 1.1rem;
  }
  
  .hero-features {
    gap: 12px;
  }
  
  .feature-item {
    padding: 10px 16px;
    font-size: 0.9rem;
  }
  
  .header-nav {
    gap: 12px;
  }
  
  .nav-link {
    padding: 4px 10px;
    font-size: 0.9rem;
  }
  
  .section-title {
    font-size: 2rem;
  }
  
  .footer-grid {
    grid-template-columns: 1fr;
    gap: 30px;
  }
}

@media (max-width: 480px) {
  .hero-title {
    font-size: 2rem;
  }
  
  .hero-features {
    flex-direction: column;
    align-items: center;
  }
  
  .feature-item {
    width: 100%;
    justify-content: center;
    max-width: 280px;
  }
  
  .header-nav {
    flex-wrap: wrap;
    justify-content: center;
  }
}

/* 暗黑模式适配 */
:root.dark .generator-card {
  background: rgba(31, 41, 55, 0.95);
  border: 1px solid rgba(75, 85, 99, 0.3);
}

:root.dark .showcase-item,
:root.dark .scenario-card {
  background: rgba(31, 41, 55, 0.3);
  border: 1px solid rgba(75, 85, 99, 0.3);
}
</style>
