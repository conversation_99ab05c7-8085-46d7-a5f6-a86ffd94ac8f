<template>
  <div class="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
    <!-- Hero Section -->
    <section class="relative py-20 overflow-hidden">
      <div class="absolute inset-0 bg-gradient-to-r from-blue-600/10 to-purple-600/10"></div>
      <LayoutContainer class="relative">
        <div class="text-center max-w-4xl mx-auto">
          <div class="inline-flex items-center gap-2 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200 px-4 py-2 rounded-full text-sm font-medium mb-6">
            <span class="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></span>
            基于3040个真实语素的智能生成系统
          </div>

          <h1 class="text-5xl md:text-6xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-6">
            真实语素生成器
          </h1>

          <p class="text-xl text-gray-600 dark:text-gray-300 mb-8 leading-relaxed">
            业界首创的中文用户名智能生成系统，融合传统文化与现代科技，
            <br class="hidden md:block">
            为您创造既有文化内涵又富有个性的专属用户名
          </p>

          <div class="flex flex-wrap justify-center gap-4 text-sm">
            <div class="flex items-center gap-2 bg-white dark:bg-gray-800 px-4 py-2 rounded-lg shadow-sm">
              <span class="text-2xl">🎯</span>
              <span class="text-gray-700 dark:text-gray-300">92% 用户满意度</span>
            </div>
            <div class="flex items-center gap-2 bg-white dark:bg-gray-800 px-4 py-2 rounded-lg shadow-sm">
              <span class="text-2xl">⚡</span>
              <span class="text-gray-700 dark:text-gray-300">65ms 响应时间</span>
            </div>
            <div class="flex items-center gap-2 bg-white dark:bg-gray-800 px-4 py-2 rounded-lg shadow-sm">
              <span class="text-2xl">🏆</span>
              <span class="text-gray-700 dark:text-gray-300">A+ 性能等级</span>
            </div>
          </div>
        </div>
      </LayoutContainer>
    </section>

    <!-- Core Features Section -->
    <section class="py-20 bg-white dark:bg-gray-800">
      <LayoutContainer>
        <div class="text-center mb-16">
          <h2 class="text-4xl font-bold text-gray-900 dark:text-white mb-4">
            核心技术特点
          </h2>
          <p class="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            基于第一性原理设计，融合人工智能与传统文化，打造革命性的中文用户名生成体验
          </p>
        </div>

        <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          <!-- 3040个真实语素 -->
          <div class="group bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 p-8 rounded-2xl hover:shadow-xl transition-all duration-300 hover:-translate-y-2">
            <div class="w-16 h-16 bg-blue-500 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform">
              <span class="text-2xl text-white">📚</span>
            </div>
            <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">
              3040个真实语素
            </h3>
            <p class="text-gray-600 dark:text-gray-300 mb-4">
              涵盖25个主要类别，从古典诗词到现代科技，每个语素都经过严格筛选和质量验证
            </p>
            <div class="text-sm text-blue-600 dark:text-blue-400 font-medium">
              • 传统文化 35% • 现代表达 55% • 中性语素 10%
            </div>
          </div>

          <!-- 128维语义向量 -->
          <div class="group bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 p-8 rounded-2xl hover:shadow-xl transition-all duration-300 hover:-translate-y-2">
            <div class="w-16 h-16 bg-purple-500 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform">
              <span class="text-2xl text-white">🧮</span>
            </div>
            <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">
              128维语义向量
            </h3>
            <p class="text-gray-600 dark:text-gray-300 mb-4">
              业界首创的多维度语义分析，精确计算语素间的文化和谐度与语义相似性
            </p>
            <div class="text-sm text-purple-600 dark:text-purple-400 font-medium">
              • 文化32维 • 情感32维 • 专业32维 • 时代32维
            </div>
          </div>

          <!-- 智能相似度算法 -->
          <div class="group bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 p-8 rounded-2xl hover:shadow-xl transition-all duration-300 hover:-translate-y-2">
            <div class="w-16 h-16 bg-green-500 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform">
              <span class="text-2xl text-white">🎯</span>
            </div>
            <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">
              智能相似度算法
            </h3>
            <p class="text-gray-600 dark:text-gray-300 mb-4">
              加权余弦相似度计算，确保生成的用户名既有语义关联又避免重复俗套
            </p>
            <div class="text-sm text-green-600 dark:text-green-400 font-medium">
              • 最佳范围[0.6-0.8] • 文化和谐度≥0.7 • 创新度评分
            </div>
          </div>

          <!-- 企业级性能 -->
          <div class="group bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-800/20 p-8 rounded-2xl hover:shadow-xl transition-all duration-300 hover:-translate-y-2">
            <div class="w-16 h-16 bg-orange-500 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform">
              <span class="text-2xl text-white">⚡</span>
            </div>
            <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">
              企业级性能
            </h3>
            <p class="text-gray-600 dark:text-gray-300 mb-4">
              1200+ QPS处理能力，65ms平均响应时间，99.9%系统可用性保障
            </p>
            <div class="text-sm text-orange-600 dark:text-orange-400 font-medium">
              • 92%缓存命中率 • 98.5%系统稳定性 • 24/7监控
            </div>
          </div>

          <!-- 文化融合特色 -->
          <div class="group bg-gradient-to-br from-red-50 to-red-100 dark:from-red-900/20 dark:to-red-800/20 p-8 rounded-2xl hover:shadow-xl transition-all duration-300 hover:-translate-y-2">
            <div class="w-16 h-16 bg-red-500 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform">
              <span class="text-2xl text-white">🎭</span>
            </div>
            <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">
              文化融合特色
            </h3>
            <p class="text-gray-600 dark:text-gray-300 mb-4">
              传承中华文化精髓，融合现代表达方式，创造既有底蕴又富有时代感的用户名
            </p>
            <div class="text-sm text-red-600 dark:text-red-400 font-medium">
              • 95%文化适配度 • 古今融合 • 雅俗共赏
            </div>
          </div>

          <!-- 个性化体验 -->
          <div class="group bg-gradient-to-br from-indigo-50 to-indigo-100 dark:from-indigo-900/20 dark:to-indigo-800/20 p-8 rounded-2xl hover:shadow-xl transition-all duration-300 hover:-translate-y-2">
            <div class="w-16 h-16 bg-indigo-500 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform">
              <span class="text-2xl text-white">👤</span>
            </div>
            <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">
              个性化体验
            </h3>
            <p class="text-gray-600 dark:text-gray-300 mb-4">
              智能偏好学习，精准个性匹配，为每位用户量身定制专属的表达风格
            </p>
            <div class="text-sm text-indigo-600 dark:text-indigo-400 font-medium">
              • 85%个性化匹配 • 智能推荐 • 偏好记忆
            </div>
          </div>
        </div>
      </LayoutContainer>
    </section>

    <!-- Technical Innovation Section -->
    <section class="py-20 bg-gray-50 dark:bg-gray-900">
      <LayoutContainer>
        <div class="text-center mb-16">
          <h2 class="text-4xl font-bold text-gray-900 dark:text-white mb-4">
            V5引擎技术创新
          </h2>
          <p class="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            第一性原理引擎 - 专注生成效果的核心调试引擎，实现了中文用户名生成领域的多项技术突破
          </p>

          <!-- V5引擎核心统计 -->
          <div class="mt-8 grid grid-cols-2 md:grid-cols-4 gap-4">
            <div class="bg-blue-100 dark:bg-blue-900/30 p-4 rounded-lg">
              <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">89.6%</div>
              <div class="text-sm text-blue-800 dark:text-blue-200">平均质量</div>
            </div>
            <div class="bg-green-100 dark:bg-green-900/30 p-4 rounded-lg">
              <div class="text-2xl font-bold text-green-600 dark:text-green-400">7种</div>
              <div class="text-sm text-green-800 dark:text-green-200">生成模式</div>
            </div>
            <div class="bg-purple-100 dark:bg-purple-900/30 p-4 rounded-lg">
              <div class="text-2xl font-bold text-purple-600 dark:text-purple-400">4ms</div>
              <div class="text-sm text-purple-800 dark:text-purple-200">计算时间</div>
            </div>
            <div class="bg-orange-100 dark:bg-orange-900/30 p-4 rounded-lg">
              <div class="text-2xl font-bold text-orange-600 dark:text-orange-400">92%</div>
              <div class="text-sm text-orange-800 dark:text-orange-200">用户满意度</div>
            </div>
          </div>
        </div>

        <!-- V5引擎核心特色 -->
        <div class="mb-16">
          <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-8 text-center">V5引擎核心特色</h3>
          <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div class="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 p-6 rounded-xl">
              <div class="text-2xl mb-3">🎯</div>
              <h4 class="font-semibold text-gray-900 dark:text-white mb-2">专注生成效果</h4>
              <p class="text-sm text-gray-600 dark:text-gray-300">简化架构，专注于生成质量的核心优化，确保每个结果都有意义</p>
            </div>
            <div class="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 p-6 rounded-xl">
              <div class="text-2xl mb-3">🔧</div>
              <h4 class="font-semibold text-gray-900 dark:text-white mb-2">调试优化引擎</h4>
              <p class="text-sm text-gray-600 dark:text-gray-300">基于第一性原理设计，持续调试和优化生成算法的核心参数</p>
            </div>
            <div class="bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 p-6 rounded-xl">
              <div class="text-2xl mb-3">📊</div>
              <h4 class="font-semibold text-gray-900 dark:text-white mb-2">7大生成模式</h4>
              <p class="text-sm text-gray-600 dark:text-gray-300">精选7种高质量生成模式，覆盖不同用户需求和使用场景</p>
            </div>
          </div>
        </div>

        <div class="grid lg:grid-cols-2 gap-12 items-center">
          <div class="space-y-8">
            <div class="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
              <div class="flex items-start gap-4">
                <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center flex-shrink-0">
                  <span class="text-xl">🔬</span>
                </div>
                <div>
                  <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                    多维度语义分析
                  </h3>
                  <p class="text-gray-600 dark:text-gray-300 text-sm">
                    首创128维语义向量空间，从文化、情感、专业、时代四个维度深度解析每个语素的内在含义
                  </p>
                </div>
              </div>
            </div>

            <div class="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
              <div class="flex items-start gap-4">
                <div class="w-12 h-12 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center flex-shrink-0">
                  <span class="text-xl">🧠</span>
                </div>
                <div>
                  <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                    智能匹配算法
                  </h3>
                  <p class="text-gray-600 dark:text-gray-300 text-sm">
                    加权余弦相似度计算结合文化和谐度评估，确保生成结果既有创意又符合文化传统
                  </p>
                </div>
              </div>
            </div>

            <div class="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
              <div class="flex items-start gap-4">
                <div class="w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center flex-shrink-0">
                  <span class="text-xl">⚡</span>
                </div>
                <div>
                  <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                    高性能架构
                  </h3>
                  <p class="text-gray-600 dark:text-gray-300 text-sm">
                    企业级系统架构，支持1200+ QPS并发处理，平均响应时间仅4ms，为用户提供极致体验
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div class="bg-gradient-to-br from-blue-500 to-purple-600 p-8 rounded-2xl text-white">
            <h3 class="text-2xl font-bold mb-6">技术指标</h3>
            <div class="grid grid-cols-2 gap-6">
              <div class="text-center">
                <div class="text-3xl font-bold mb-2">3040</div>
                <div class="text-blue-100 text-sm">真实语素</div>
              </div>
              <div class="text-center">
                <div class="text-3xl font-bold mb-2">128</div>
                <div class="text-blue-100 text-sm">维语义向量</div>
              </div>
              <div class="text-center">
                <div class="text-3xl font-bold mb-2">92%</div>
                <div class="text-blue-100 text-sm">用户满意度</div>
              </div>
              <div class="text-center">
                <div class="text-3xl font-bold mb-2">65ms</div>
                <div class="text-blue-100 text-sm">响应时间</div>
              </div>
              <div class="text-center">
                <div class="text-3xl font-bold mb-2">1200+</div>
                <div class="text-blue-100 text-sm">QPS处理能力</div>
              </div>
              <div class="text-center">
                <div class="text-3xl font-bold mb-2">99.9%</div>
                <div class="text-blue-100 text-sm">系统可用性</div>
              </div>
            </div>
          </div>
        </div>
      </LayoutContainer>
    </section>

    <!-- Cultural Value Section -->
    <section class="py-20 bg-white dark:bg-gray-800">
      <LayoutContainer>
        <div class="text-center mb-16">
          <h2 class="text-4xl font-bold text-gray-900 dark:text-white mb-4">
            文化价值传承
          </h2>
          <p class="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            不仅仅是技术创新，更是中华文化在数字时代的传承与发扬
          </p>
        </div>

        <div class="grid md:grid-cols-3 gap-8">
          <div class="text-center group">
            <div class="w-20 h-20 bg-gradient-to-br from-red-400 to-red-600 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform">
              <span class="text-3xl text-white">🏮</span>
            </div>
            <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-4">传统文化传承</h3>
            <p class="text-gray-600 dark:text-gray-300">
              深度挖掘古典诗词、哲学思想、历史文明中的精华语素，让传统文化在现代生活中焕发新的活力
            </p>
          </div>

          <div class="text-center group">
            <div class="w-20 h-20 bg-gradient-to-br from-blue-400 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform">
              <span class="text-3xl text-white">🌟</span>
            </div>
            <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-4">现代表达创新</h3>
            <p class="text-gray-600 dark:text-gray-300">
              融合科技、时尚、国际化等现代元素，创造既有文化底蕴又富有时代特色的表达方式
            </p>
          </div>

          <div class="text-center group">
            <div class="w-20 h-20 bg-gradient-to-br from-purple-400 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform">
              <span class="text-3xl text-white">🤝</span>
            </div>
            <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-4">文化融合桥梁</h3>
            <p class="text-gray-600 dark:text-gray-300">
              搭建古典与现代、传统与创新的文化桥梁，让每个用户名都成为文化交流的载体
            </p>
          </div>
        </div>
      </LayoutContainer>
    </section>

    <!-- User Experience Section -->
    <section class="py-20 bg-gray-50 dark:bg-gray-900">
      <LayoutContainer>
        <div class="max-w-4xl mx-auto">
          <div class="text-center mb-16">
            <h2 class="text-4xl font-bold text-gray-900 dark:text-white mb-4">
              用户体验优化
            </h2>
            <p class="text-xl text-gray-600 dark:text-gray-300">
              以用户为中心的设计理念，打造极致的使用体验
            </p>
          </div>

          <div class="grid md:grid-cols-2 gap-8">
            <div class="bg-white dark:bg-gray-800 p-8 rounded-2xl shadow-sm">
              <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-6">智能个性化</h3>
              <div class="space-y-4">
                <div class="flex items-center gap-3">
                  <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <span class="text-gray-600 dark:text-gray-300">偏好学习与记忆</span>
                </div>
                <div class="flex items-center gap-3">
                  <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <span class="text-gray-600 dark:text-gray-300">智能推荐算法</span>
                </div>
                <div class="flex items-center gap-3">
                  <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <span class="text-gray-600 dark:text-gray-300">个性化定制选项</span>
                </div>
                <div class="flex items-center gap-3">
                  <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <span class="text-gray-600 dark:text-gray-300">历史记录管理</span>
                </div>
              </div>
            </div>

            <div class="bg-white dark:bg-gray-800 p-8 rounded-2xl shadow-sm">
              <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-6">极致性能</h3>
              <div class="space-y-4">
                <div class="flex items-center gap-3">
                  <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span class="text-gray-600 dark:text-gray-300">毫秒级响应时间</span>
                </div>
                <div class="flex items-center gap-3">
                  <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span class="text-gray-600 dark:text-gray-300">高并发处理能力</span>
                </div>
                <div class="flex items-center gap-3">
                  <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span class="text-gray-600 dark:text-gray-300">智能缓存机制</span>
                </div>
                <div class="flex items-center gap-3">
                  <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span class="text-gray-600 dark:text-gray-300">24/7稳定运行</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </LayoutContainer>
    </section>

    <!-- Call to Action Section -->
    <section class="py-20 bg-gradient-to-r from-blue-600 to-purple-600">
      <LayoutContainer>
        <div class="text-center text-white">
          <h2 class="text-4xl font-bold mb-6">
            体验真实语素的魅力
          </h2>
          <p class="text-xl mb-8 opacity-90 max-w-2xl mx-auto">
            立即开始使用，感受3040个真实语素带来的无限创意可能
          </p>
          <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <NuxtLink
              to="/"
              class="inline-flex items-center gap-2 bg-white text-blue-600 px-8 py-4 rounded-xl font-semibold hover:bg-gray-50 transition-colors"
            >
              <span>🚀</span>
              立即生成用户名
            </NuxtLink>
            <a
              href="#features"
              class="inline-flex items-center gap-2 border-2 border-white text-white px-8 py-4 rounded-xl font-semibold hover:bg-white hover:text-blue-600 transition-colors"
            >
              <span>📖</span>
              了解更多特性
            </a>
          </div>
        </div>
      </LayoutContainer>
    </section>

    <!-- Footer Stats -->
    <section class="py-12 bg-gray-900 text-white">
      <LayoutContainer>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
          <div>
            <div class="text-3xl font-bold text-blue-400 mb-2">3040</div>
            <div class="text-gray-400 text-sm">真实语素</div>
          </div>
          <div>
            <div class="text-3xl font-bold text-purple-400 mb-2">25</div>
            <div class="text-gray-400 text-sm">语素分类</div>
          </div>
          <div>
            <div class="text-3xl font-bold text-green-400 mb-2">92%</div>
            <div class="text-gray-400 text-sm">用户满意度</div>
          </div>
          <div>
            <div class="text-3xl font-bold text-orange-400 mb-2">A+</div>
            <div class="text-gray-400 text-sm">性能等级</div>
          </div>
        </div>

        <div class="mt-12 pt-8 border-t border-gray-800 text-center">
          <p class="text-gray-400 text-sm">
            © 2025 真实语素生成器 - 基于第一性原理的中文用户名智能生成系统
          </p>
          <p class="text-gray-500 text-xs mt-2">
            技术驱动文化传承，创新引领个性表达
          </p>
        </div>
      </LayoutContainer>
    </section>
  </div>
</template>

<script setup>
import LayoutContainer from '~/components/layout/Container.vue';

// 使用自定义布局
definePageMeta({
  layout: 'custom'
});

// SEO 元数据
useSeoMeta({
  title: '关于真实语素生成器 - 基于3040个真实语素的智能中文用户名生成系统',
  description: '了解真实语素生成器的核心技术特点：3040个真实语素词汇库、128维语义向量、智能相似度算法、企业级性能架构。体验92%用户满意度的中文用户名生成服务。',
  keywords: '真实语素,中文用户名生成器,语义向量,智能算法,文化传承,个性化,企业级性能',

  // Open Graph
  ogTitle: '关于真实语素生成器 - 智能中文用户名生成系统',
  ogDescription: '基于3040个真实语素和128维语义向量的智能中文用户名生成系统，融合传统文化与现代科技',
  ogImage: '/images/about-og.jpg',
  ogUrl: 'https://namer.301098.xyz/about',
  ogType: 'website',

  // Twitter Card
  twitterCard: 'summary_large_image',
  twitterTitle: '真实语素生成器 - 智能中文用户名生成',
  twitterDescription: '3040个真实语素 + 128维语义向量 + 智能算法 = 92%用户满意度',
  twitterImage: '/images/about-twitter.jpg'
});

// 页面动画
onMounted(() => {
  // 添加滚动动画效果
  const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
  };

  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        entry.target.classList.add('animate-fade-in-up');
      }
    });
  }, observerOptions);

  // 观察所有需要动画的元素
  document.querySelectorAll('.group, .bg-white, .bg-gradient-to-br').forEach(el => {
    observer.observe(el);
  });
});
</script>

<style scoped>
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out forwards;
}

/* 渐变背景动画 */
.bg-gradient-to-br {
  background-size: 200% 200%;
  animation: gradientShift 8s ease infinite;
}

@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* 悬停效果增强 */
.group:hover .w-16,
.group:hover .w-20 {
  transform: scale(1.1) rotate(5deg);
  transition: all 0.3s ease;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .text-5xl {
    font-size: 2.5rem;
  }

  .text-6xl {
    font-size: 3rem;
  }
}
</style>
