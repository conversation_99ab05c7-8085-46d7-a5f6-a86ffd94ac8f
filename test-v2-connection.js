/**
 * 测试V2系统连接
 * 
 * 验证Vue组件是否能正确调用V2生成器
 */

// 模拟V2生成器的核心功能
const v2Vocabulary = {
  nouns: [
    { word: '星', domains: ['nature'], cultural_scores: { modern: 0.8, traditional: 0.9, elegant: 0.9, cute: 0.7, cool: 0.6 } },
    { word: '月', domains: ['nature'], cultural_scores: { modern: 0.8, traditional: 0.9, elegant: 0.9, cute: 0.8, cool: 0.5 } },
    { word: '云', domains: ['nature', 'tech'], cultural_scores: { modern: 0.9, traditional: 0.8, elegant: 0.9, cute: 0.7, cool: 0.8 } },
    { word: '风', domains: ['nature'], cultural_scores: { modern: 0.7, traditional: 0.9, elegant: 0.8, cute: 0.6, cool: 0.8 } },
    { word: '光', domains: ['nature', 'quality'], cultural_scores: { modern: 0.9, traditional: 0.8, elegant: 0.8, cute: 0.6, cool: 0.8 } },
    { word: '心', domains: ['emotion', 'social'], cultural_scores: { modern: 0.8, traditional: 0.9, elegant: 0.8, cute: 0.9, cool: 0.6 } },
    { word: '梦', domains: ['emotion', 'fantasy'], cultural_scores: { modern: 0.8, traditional: 0.8, elegant: 0.8, cute: 0.9, cool: 0.6 } },
    { word: '码', domains: ['tech', 'modern'], cultural_scores: { modern: 0.95, traditional: 0.2, elegant: 0.3, cute: 0.4, cool: 0.9 } },
    { word: '网', domains: ['tech', 'social'], cultural_scores: { modern: 0.9, traditional: 0.3, elegant: 0.4, cute: 0.5, cool: 0.8 } }
  ],
  adjectives: [
    { word: '美', domains: ['quality'], cultural_scores: { modern: 0.8, traditional: 0.9, elegant: 0.9, cute: 0.8, cool: 0.6 } },
    { word: '新', domains: ['quality', 'modern'], cultural_scores: { modern: 0.95, traditional: 0.6, elegant: 0.7, cute: 0.6, cool: 0.8 } },
    { word: '亮', domains: ['quality'], cultural_scores: { modern: 0.8, traditional: 0.7, elegant: 0.7, cute: 0.6, cool: 0.8 } },
    { word: '静', domains: ['quality', 'emotion'], cultural_scores: { modern: 0.7, traditional: 0.9, elegant: 0.9, cute: 0.6, cool: 0.8 } },
    { word: '雅', domains: ['quality', 'culture'], cultural_scores: { modern: 0.7, traditional: 0.95, elegant: 0.98, cute: 0.5, cool: 0.7 } },
    { word: '酷', domains: ['quality', 'modern'], cultural_scores: { modern: 0.95, traditional: 0.3, elegant: 0.4, cute: 0.4, cool: 0.98 } },
    { word: '萌', domains: ['quality', 'emotion'], cultural_scores: { modern: 0.9, traditional: 0.2, elegant: 0.3, cute: 0.98, cool: 0.3 } },
    { word: '潮', domains: ['quality', 'modern'], cultural_scores: { modern: 0.95, traditional: 0.2, elegant: 0.3, cute: 0.6, cool: 0.9 } }
  ],
  verbs: [
    { word: '飞', domains: ['action', 'nature'], cultural_scores: { modern: 0.8, traditional: 0.8, elegant: 0.7, cute: 0.7, cool: 0.9 } },
    { word: '舞', domains: ['action', 'culture'], cultural_scores: { modern: 0.7, traditional: 0.9, elegant: 0.9, cute: 0.8, cool: 0.6 } },
    { word: '笑', domains: ['action', 'emotion'], cultural_scores: { modern: 0.9, traditional: 0.8, elegant: 0.7, cute: 0.9, cool: 0.6 } }
  ]
};

const patterns = [
  { id: 'single_noun', name: '单一名词', structure: ['n'] },
  { id: 'adj_noun', name: '形容词+名词', structure: ['adj', 'n'] },
  { id: 'noun_adj', name: '名词+形容词', structure: ['n', 'adj'] },
  { id: 'verb_noun', name: '动词+名词', structure: ['v', 'n'] },
  { id: 'adj_noun_noun', name: '形容词+名词+名词', structure: ['adj', 'n', 'n'] }
];

// 测试V2生成器功能
function testV2Generator() {
  console.log('🚀 开始测试V2生成器连接');
  console.log('='.repeat(50));
  
  // 测试不同复杂度
  const complexityTests = [
    { slotCount: 2, expected: 'simple' },
    { slotCount: 3, expected: 'medium' },
    { slotCount: 4, expected: 'complex' },
    { slotCount: 5, expected: 'advanced' }
  ];
  
  complexityTests.forEach(test => {
    console.log(`\n📊 测试复杂度 ${test.slotCount}:`);
    
    for (let i = 0; i < 3; i++) {
      const result = generateV2Username(test.slotCount, 'modern');
      console.log(`  ${i + 1}. ${result.username} (质量: ${(result.quality * 100).toFixed(1)}%)`);
      console.log(`     解释: ${result.explanation}`);
    }
  });
  
  // 测试不同风格
  console.log('\n🎨 测试不同风格:');
  const styles = ['modern', 'traditional', 'cute', 'cool', 'elegant'];
  
  styles.forEach(style => {
    const result = generateV2Username(3, style);
    console.log(`  ${style}: ${result.username} (${(result.quality * 100).toFixed(1)}%)`);
  });
  
  console.log('\n✅ V2生成器测试完成！');
}

// V2生成器模拟函数
function generateV2Username(slotCount, style) {
  // 根据复杂度选择模式
  let availablePatterns = patterns;
  if (slotCount <= 2) {
    availablePatterns = patterns.filter(p => p.structure.length <= 1);
  } else if (slotCount <= 3) {
    availablePatterns = patterns.filter(p => p.structure.length <= 2);
  } else if (slotCount <= 4) {
    availablePatterns = patterns.filter(p => p.structure.length <= 3);
  }
  
  const pattern = availablePatterns[Math.floor(Math.random() * availablePatterns.length)];
  
  // 选择词汇
  const components = [];
  for (const pos of pattern.structure) {
    let candidates = [];
    if (pos === 'n') candidates = v2Vocabulary.nouns;
    else if (pos === 'adj') candidates = v2Vocabulary.adjectives;
    else if (pos === 'v') candidates = v2Vocabulary.verbs;
    
    // 根据风格过滤
    candidates = candidates.filter(word => 
      (word.cultural_scores[style] || 0) > 0.5
    );
    
    // 避免重复
    candidates = candidates.filter(word => 
      !components.some(c => c.word === word.word)
    );
    
    if (candidates.length === 0) {
      if (pos === 'n') candidates = v2Vocabulary.nouns;
      else if (pos === 'adj') candidates = v2Vocabulary.adjectives;
      else if (pos === 'v') candidates = v2Vocabulary.verbs;
    }
    
    const selected = candidates[Math.floor(Math.random() * candidates.length)];
    components.push({
      word: selected.word,
      pos: pos,
      domains: selected.domains,
      cultural_scores: selected.cultural_scores
    });
  }
  
  const username = components.map(c => c.word).join('');
  const quality = Math.min(1.0, 0.7 + Math.random() * 0.3);
  const explanation = `采用${pattern.name}模式，组合${components.map(c => `"${c.word}"`).join(' + ')}，体现${style}风格特色。`;
  
  return {
    username,
    quality,
    explanation,
    components,
    pattern
  };
}

// 运行测试
testV2Generator();

// 导出测试函数供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    testV2Generator,
    generateV2Username,
    v2Vocabulary,
    patterns
  };
}
