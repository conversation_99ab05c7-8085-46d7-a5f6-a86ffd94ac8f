/**
 * 手动AI交互测试指南和演示工具
 * 提供完整的操作流程和可视化界面
 */

import ManualAIInteractionSystem from './ManualAIInteractionSystem'
import { generateCulturalUsername } from '../core/TestUsernameGenerator'

/**
 * 手动测试控制器
 */
export class ManualTestingController {
  private aiSystem: ManualAIInteractionSystem
  private currentBatchId: string = ''
  private currentSamples: string[] = []
  private currentResults: any[] = []

  constructor(language: string = 'zh') {
    this.aiSystem = new ManualAIInteractionSystem(language)
  }

  /**
   * 步骤1: 生成测试样本
   */
  async generateTestSamples(count: number = 5): Promise<string[]> {
    console.log(`\n🎯 步骤1: 生成 ${count} 个测试样本`)
    console.log('=' .repeat(50))

    const samples: string[] = []
    for (let i = 0; i < count; i++) {
      try {
        const username = await generateCulturalUsername({ 
          language: 'zh', 
          category: 'internet',
          slot_count: Math.floor(Math.random() * 3) + 2
        })
        samples.push(username)
      } catch (error) {
        console.warn(`生成第${i+1}个样本失败:`, error)
      }
    }

    this.currentSamples = samples
    console.log(`✅ 成功生成样本: ${samples.join(', ')}`)
    
    return samples
  }

  /**
   * 步骤2: 开始新迭代
   */
  startIteration(): string {
    this.currentBatchId = this.aiSystem.startNewIteration(this.currentSamples)
    this.currentResults = []
    
    console.log(`\n📋 接下来请按顺序分析每个用户名:`)
    this.currentSamples.forEach((username, index) => {
      console.log(`\n${index + 1}. 用户名: "${username}"`)
      console.log(`   请复制以下prompt发送给AI:`)
      console.log(`   ` + '-'.repeat(40))
      
      const prompt = this.aiSystem.generateAnalysisPrompt(username)
      console.log(`\n${prompt}\n`)
      console.log(`   ` + '-'.repeat(40))
      console.log(`   ⏳ 等待AI响应，然后调用 processAIResponse("${username}", "AI响应内容")`)
    })

    return this.currentBatchId
  }

  /**
   * 步骤3: 处理AI响应
   */
  processAIResponse(username: string, aiResponse: string): void {
    if (!this.currentBatchId) {
      console.error('❌ 请先调用 startIteration() 开始迭代')
      return
    }

    const result = this.aiSystem.processUserAnalysis(username, aiResponse, this.currentBatchId)
    this.currentResults.push(result)

    const remaining = this.currentSamples.length - this.currentResults.length
    if (remaining > 0) {
      console.log(`\n⏳ 还需要分析 ${remaining} 个用户名`)
      const nextUsername = this.currentSamples[this.currentResults.length]
      console.log(`📝 下一个: "${nextUsername}"`)
    } else {
      console.log(`\n✅ 所有用户名分析完成！请调用 completeIteration() 生成报告`)
    }
  }

  /**
   * 步骤4: 完成迭代
   */
  completeIteration(): void {
    if (this.currentResults.length !== this.currentSamples.length) {
      console.error(`❌ 分析未完成，还需要 ${this.currentSamples.length - this.currentResults.length} 个结果`)
      return
    }

    try {
      const report = this.aiSystem.completeIteration(this.currentBatchId, this.currentResults)
      this.displayIterationReport(report)
      
      console.log(`\n🎉 第 ${report.iteration} 轮迭代完成！`)
      console.log(`📊 可调用 showVisualizationReport() 查看完整报告`)
    } catch (error) {
      console.error('❌ 完成迭代失败:', error)
    }
  }

  /**
   * 显示迭代报告
   */
  private displayIterationReport(report: any): void {
    console.log(`\n📊 第 ${report.iteration} 轮迭代报告`)
    console.log('=' .repeat(50))
    
    console.log(`\n📈 平均评分:`)
    console.log(`  🎨 创意性: ${report.summary.avg_scores.creativity.toFixed(3)}`)
    console.log(`  🎲 意外性: ${report.summary.avg_scores.unexpectedness.toFixed(3)}`)
    console.log(`  🔗 连贯性: ${report.summary.avg_scores.coherence.toFixed(3)}`)
    console.log(`  🌍 文化共鸣: ${report.summary.avg_scores.cultural_resonance.toFixed(3)}`)
    console.log(`  ⭐ 综合评分: ${report.summary.avg_scores.overall.toFixed(3)}`)
    
    console.log(`\n🏆 最佳表现: ${report.summary.best_performers.join(', ')}`)
    console.log(`⚠️ 待改进: ${report.summary.worst_performers.join(', ')}`)
    
    console.log(`\n💡 关键洞察:`)
    report.summary.insights.forEach((insight: string) => {
      console.log(`  • ${insight}`)
    })
    
    console.log(`\n🔧 优化建议:`)
    report.optimization_suggestions.forEach((suggestion: string) => {
      console.log(`  • ${suggestion}`)
    })
  }

  /**
   * 显示可视化报告
   */
  showVisualizationReport(): void {
    const report = this.aiSystem.generateVisualizationReport()
    console.log(report)
  }

  /**
   * 导出数据
   */
  exportData(): string {
    const data = this.aiSystem.exportData()
    console.log(`\n💾 数据已导出 (${data.length} 字符)`)
    return data
  }

  /**
   * 获取当前状态
   */
  getStatus(): any {
    return {
      current_batch_id: this.currentBatchId,
      samples_count: this.currentSamples.length,
      completed_analysis: this.currentResults.length,
      remaining: this.currentSamples.length - this.currentResults.length,
      total_iterations: this.aiSystem.getIterationHistory().length
    }
  }

  /**
   * 重置当前迭代
   */
  resetCurrentIteration(): void {
    this.currentBatchId = ''
    this.currentSamples = []
    this.currentResults = []
    console.log(`🔄 当前迭代已重置`)
  }
}

/**
 * 快速开始指南
 */
export function showQuickStartGuide(): void {
  console.log(`
🚀 手动AI交互测试 - 快速开始指南
${'='.repeat(60)}

📋 完整操作流程:

1️⃣ 初始化测试控制器
   const controller = new ManualTestingController('zh')

2️⃣ 生成测试样本
   await controller.generateTestSamples(5)

3️⃣ 开始迭代
   controller.startIteration()
   // 系统会显示每个用户名的分析prompt

4️⃣ 逐个分析用户名
   对于每个用户名:
   a) 复制系统生成的prompt
   b) 发送给AI模型 (ChatGPT/Claude/文心一言等)
   c) 复制AI的响应
   d) 调用: controller.processAIResponse("用户名", "AI响应")

5️⃣ 完成迭代
   controller.completeIteration()
   // 系统会生成详细的分析报告

6️⃣ 查看可视化报告
   controller.showVisualizationReport()

7️⃣ 导出数据 (可选)
   const data = controller.exportData()

🔄 重复步骤2-6进行多轮迭代优化

💡 提示:
- 建议从5个样本开始，验证流程后再增加
- 每轮迭代后观察评分变化趋势
- 关注优化建议，调整生成策略
- 保存导出的数据用于后续分析

⚠️ 注意事项:
- 确保AI响应包含完整的JSON格式
- 如果解析失败，检查JSON格式是否正确
- 可以随时调用 controller.getStatus() 查看进度
`)
}

/**
 * 演示完整工作流
 */
export async function demonstrateFullWorkflow(): Promise<void> {
  console.log(`\n🎬 演示完整的手动AI交互流程`)
  console.log('=' .repeat(60))

  const controller = new ManualTestingController('zh')

  // 步骤1: 生成样本
  const samples = await controller.generateTestSamples(3)
  
  // 步骤2: 开始迭代
  controller.startIteration()
  
  console.log(`\n📝 演示说明:`)
  console.log(`1. 上面显示了3个用户名的分析prompt`)
  console.log(`2. 你需要将每个prompt复制给AI模型`)
  console.log(`3. 然后将AI的响应粘贴回来处理`)
  console.log(`4. 完成所有分析后调用 completeIteration()`)
  
  console.log(`\n🔧 实际操作示例:`)
  console.log(`controller.processAIResponse("${samples[0]}", "AI返回的JSON响应")`)
  console.log(`controller.processAIResponse("${samples[1]}", "AI返回的JSON响应")`)
  console.log(`controller.processAIResponse("${samples[2]}", "AI返回的JSON响应")`)
  console.log(`controller.completeIteration()`)
}
