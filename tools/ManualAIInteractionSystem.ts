/**
 * 手动AI交互系统
 * 支持手动复制粘贴与大模型交互，并提供可视化观测
 */

export interface ManualAnalysisInput {
  username: string
  language: string
  iteration: number
  batch_id: string
}

export interface ManualAnalysisOutput {
  username: string
  ai_response: string  // 从大模型复制回来的原始响应
  parsed_metrics?: {
    creativity: number
    unexpectedness: number
    coherence: number
    cultural_resonance: number
    reasoning: string
  }
  timestamp: string
}

export interface IterationReport {
  iteration: number
  batch_id: string
  samples: string[]
  analysis_results: ManualAnalysisOutput[]
  summary: {
    avg_scores: Record<string, number>
    best_performers: string[]
    worst_performers: string[]
    insights: string[]
  }
  optimization_suggestions: string[]
}

/**
 * 手动AI交互管理器
 */
export class ManualAIInteractionSystem {
  private iterationHistory: IterationReport[] = []
  private currentIteration: number = 0
  private language: string

  constructor(language: string = 'zh') {
    this.language = language
  }

  /**
   * 生成标准化的AI分析prompt
   */
  generateAnalysisPrompt(username: string, language: string = 'zh'): string {
    const languageContext = this.getLanguageContext(language)
    
    return `# 用户名有趣性专业分析

## 分析目标
请作为用户名有趣性专家，对用户名"${username}"进行深度分析。

## 语言文化背景
${languageContext}

## 评估维度说明
请从以下四个核心维度进行评分（0.0-1.0分，保留2位小数）：

### 1. 创意性 (creativity)
- 词汇组合的新颖程度和原创性
- 是否突破了常规的命名模式
- 是否展现了独特的想象力

### 2. 意外性 (unexpectedness) 
- 超出用户常规预期的程度
- 是否包含令人惊喜的元素
- 是否避免了过于平庸的表达

### 3. 连贯性 (coherence)
- 语义逻辑的合理性和流畅度
- 各组成部分是否和谐统一
- 整体表达是否自然流畅

### 4. 文化共鸣 (cultural_resonance)
- 与目标文化群体的共鸣程度
- 是否体现了文化特色和时代特征
- 是否符合该语言的表达习惯

## 输出格式要求
请严格按照以下JSON格式输出（不要添加任何其他文字）：

\`\`\`json
{
  "creativity": 0.00,
  "unexpectedness": 0.00,
  "coherence": 0.00,
  "cultural_resonance": 0.00,
  "reasoning": "详细说明各维度评分的理由，包括优势、不足和改进建议，字数控制在200字以内"
}
\`\`\`

## 注意事项
- 评分要客观公正，避免极端分数
- 推理要具体明确，避免空泛表述
- 考虑目标用户群体的接受度
- 关注文化适宜性和时代感`
  }

  /**
   * 获取语言文化背景描述
   */
  private getLanguageContext(language: string): string {
    const contexts: Record<string, string> = {
      zh: `假设你是一位用户名评估专家，请从以下维度评估这个用户名的有趣性：
1. 创意性 (creativity)：0-1分，评估用户名的创新性和独特构思
2. 意外性 (unexpectedness)：0-1分，评估用户名带来的惊喜感和出乎意料程度
3. 连贯性 (coherence)：0-1分，评估用户名的语义连贯性和逻辑性
4. 文化共鸣 (cultural_resonance)：0-1分，评估用户名与目标文化的契合度

请以JSON格式返回评分和分析理由：
\`\`\`json
{
  "creativity": 0.85,
  "unexpectedness": 0.75,
  "coherence": 0.9,
  "cultural_resonance": 0.8,
  "reasoning": "这里是你的详细分析..."
}
\`\`\``,
      en: `Imagine you're a username evaluation expert. Please assess the interestingness of this username based on these dimensions:
1. Creativity: 0-1 score, evaluating innovation and unique concept
2. Unexpectedness: 0-1 score, evaluating surprise factor and unexpectedness
3. Coherence: 0-1 score, evaluating semantic coherence and logic
4. Cultural Resonance: 0-1 score, evaluating alignment with target culture

Please return scores and analysis reason in JSON format:
\`\`\`json
{
  "creativity": 0.85,
  "unexpectedness": 0.75,
  "coherence": 0.9,
  "cultural_resonance": 0.8,
  "reasoning": "Your detailed analysis here..."
}
\`\`\``,
      ja: `ユーザー名評価の専門家として、以下の観点からこのユーザー名の面白さを評価してください：
1. 創造性 (creativity)：0-1点、ユーザー名の革新性とユニークな発想を評価
2. 意外性 (unexpectedness)：0-1点、ユーザー名がもたらす驚きと予想外の度合いを評価
3. 一貫性 (coherence)：0-1点、ユーザー名の意味的な一貫性と論理性を評価
4. 文化的共鳴 (cultural_resonance)：0-1点、ユーザー名と対象文化との適合度を評価

JSONフォーマットでスコアと分析理由を返してください：
\`\`\`json
{
  "creativity": 0.85,
  "unexpectedness": 0.75,
  "coherence": 0.9,
  "cultural_resonance": 0.8,
  "reasoning": "ここに詳細な分析を記入..."
}
\`\`\``
    }
    
    return contexts[language] || contexts['zh']
  }

  /**
   * 解析AI响应
   */
  parseAIResponse(username: string, aiResponse: string): ManualAnalysisOutput {
    const result: ManualAnalysisOutput = {
      username,
      ai_response: aiResponse,
      timestamp: new Date().toISOString()
    }

    try {
      // 尝试提取JSON部分
      const jsonMatch = aiResponse.match(/```json\s*([\s\S]*?)\s*```/) || 
                       aiResponse.match(/\{[\s\S]*\}/)
      
      if (jsonMatch) {
        const jsonStr = jsonMatch[1] || jsonMatch[0]
        const parsed = JSON.parse(jsonStr)
        
        result.parsed_metrics = {
          creativity: this.validateScore(parsed.creativity),
          unexpectedness: this.validateScore(parsed.unexpectedness),
          coherence: this.validateScore(parsed.coherence),
          cultural_resonance: this.validateScore(parsed.cultural_resonance),
          reasoning: parsed.reasoning || '未提供分析理由'
        }
      }
    } catch (error) {
      console.warn(`解析AI响应失败: ${error}`)
    }

    return result
  }

  /**
   * 验证评分有效性
   */
  private validateScore(score: any): number {
    const num = parseFloat(score)
    if (isNaN(num)) return 0.5
    return Math.max(0, Math.min(1, num))
  }

  /**
   * 开始新的迭代
   */
  startNewIteration(samples: string[]): string {
    this.currentIteration++
    const batchId = `batch_${this.currentIteration}_${Date.now()}`
    
    console.log(`\n🚀 开始第 ${this.currentIteration} 轮迭代`)
    console.log(`📊 批次ID: ${batchId}`)
    console.log(`🎯 样本数量: ${samples.length}`)
    console.log(`📝 样本列表: ${samples.join(', ')}`)
    
    return batchId
  }

  /**
   * 处理单个用户名的分析
   */
  processUserAnalysis(username: string, aiResponse: string, batchId: string): ManualAnalysisOutput {
    const result = this.parseAIResponse(username, aiResponse)
    
    console.log(`\n📝 用户名: ${username}`)
    if (result.parsed_metrics) {
      const m = result.parsed_metrics
      console.log(`📊 评分: 创意${m.creativity.toFixed(2)} | 意外${m.unexpectedness.toFixed(2)} | 连贯${m.coherence.toFixed(2)} | 共鸣${m.cultural_resonance.toFixed(2)}`)
      console.log(`💡 分析: ${m.reasoning}`)
    } else {
      console.log(`⚠️ 解析失败，请检查AI响应格式`)
    }
    
    return result
  }

  /**
   * 完成迭代并生成报告
   */
  completeIteration(batchId: string, analysisResults: ManualAnalysisOutput[]): IterationReport {
    const validResults = analysisResults.filter(r => r.parsed_metrics)
    
    if (validResults.length === 0) {
      throw new Error('没有有效的分析结果')
    }

    // 计算平均分数
    const avgScores: Record<string, number> = {
      creativity: 0,
      unexpectedness: 0,
      coherence: 0,
      cultural_resonance: 0,
      overall: 0
    }

    validResults.forEach(result => {
      const m = result.parsed_metrics!
      avgScores.creativity += m.creativity
      avgScores.unexpectedness += m.unexpectedness
      avgScores.coherence += m.coherence
      avgScores.cultural_resonance += m.cultural_resonance
    })

    Object.keys(avgScores).forEach(key => {
      if (key !== 'overall') {
        avgScores[key] /= validResults.length
      }
    })

    avgScores.overall = (avgScores.creativity * 0.25 + 
                        avgScores.unexpectedness * 0.25 + 
                        avgScores.coherence * 0.25 + 
                        avgScores.cultural_resonance * 0.25)

    // 识别最佳和最差表现者
    const scoredResults = validResults.map(r => ({
      username: r.username,
      score: (r.parsed_metrics!.creativity + r.parsed_metrics!.unexpectedness + 
              r.parsed_metrics!.coherence + r.parsed_metrics!.cultural_resonance) / 4
    })).sort((a, b) => b.score - a.score)

    const bestPerformers = scoredResults.slice(0, Math.ceil(scoredResults.length * 0.3)).map(r => r.username)
    const worstPerformers = scoredResults.slice(-Math.ceil(scoredResults.length * 0.3)).map(r => r.username)

    // 生成洞察
    const insights = this.generateInsights(validResults, avgScores)
    const optimizationSuggestions = this.generateOptimizationSuggestions(validResults, avgScores)

    const report: IterationReport = {
      iteration: this.currentIteration,
      batch_id: batchId,
      samples: analysisResults.map(r => r.username),
      analysis_results: analysisResults,
      summary: {
        avg_scores: avgScores,
        best_performers: bestPerformers,
        worst_performers: worstPerformers,
        insights
      },
      optimization_suggestions: optimizationSuggestions
    }

    this.iterationHistory.push(report)
    return report
  }

  /**
   * 生成洞察
   */
  private generateInsights(results: ManualAnalysisOutput[], avgScores: Record<string, number>): string[] {
    const insights: string[] = []
    
    // 维度分析
    const dimensions = ['creativity', 'unexpectedness', 'coherence', 'cultural_resonance']
    const sortedDims = dimensions.sort((a, b) => avgScores[b] - avgScores[a])
    
    insights.push(`🏆 最强维度: ${this.getDimensionName(sortedDims[0])} (${avgScores[sortedDims[0]].toFixed(3)})`)
    insights.push(`⚠️ 最弱维度: ${this.getDimensionName(sortedDims[3])} (${avgScores[sortedDims[3]].toFixed(3)})`)
    
    // 分数分布分析
    if (avgScores.overall >= 0.7) {
      insights.push(`✨ 整体表现优秀，已达到较高水准`)
    } else if (avgScores.overall >= 0.5) {
      insights.push(`📈 整体表现中等，有明显提升空间`)
    } else {
      insights.push(`🔧 整体表现需要大幅改进`)
    }
    
    return insights
  }

  /**
   * 生成优化建议
   */
  private generateOptimizationSuggestions(results: ManualAnalysisOutput[], avgScores: Record<string, number>): string[] {
    const suggestions: string[] = []
    
    if (avgScores.creativity < 0.6) {
      suggestions.push(`💡 提升创意性: 尝试更多非常规的词汇组合，引入新颖的概念`)
    }
    
    if (avgScores.unexpectedness < 0.6) {
      suggestions.push(`🎲 增强意外性: 避免过于常见的模式，加入令人惊喜的元素`)
    }
    
    if (avgScores.coherence < 0.6) {
      suggestions.push(`🔗 改善连贯性: 确保各部分语义逻辑合理，整体表达流畅`)
    }
    
    if (avgScores.cultural_resonance < 0.6) {
      suggestions.push(`🌍 强化文化共鸣: 更好地融入目标文化特色和时代元素`)
    }
    
    return suggestions
  }

  /**
   * 获取维度中文名称
   */
  private getDimensionName(dimension: string): string {
    const names: Record<string, string> = {
      creativity: '创意性',
      unexpectedness: '意外性',
      coherence: '连贯性',
      cultural_resonance: '文化共鸣'
    }
    
    return names[dimension] || dimension
  }

  /**
   * 生成可视化报告
   */
  generateVisualizationReport(): string {
    if (this.iterationHistory.length === 0) {
      return '暂无迭代数据'
    }

    let report = `\n📊 用户名有趣性优化 - 可视化报告\n`
    report += `${'='.repeat(60)}\n`
    
    // 迭代趋势
    report += `\n📈 迭代趋势分析:\n`
    this.iterationHistory.forEach((iter, index) => {
      const prev = index > 0 ? this.iterationHistory[index - 1] : null
      const improvement = prev ? iter.summary.avg_scores.overall - prev.summary.avg_scores.overall : 0
      const trend = improvement > 0.01 ? '📈' : improvement < -0.01 ? '📉' : '➡️'
      
      report += `  ${trend} 第${iter.iteration}轮: ${iter.summary.avg_scores.overall.toFixed(3)}`
      if (prev) report += ` (${improvement >= 0 ? '+' : ''}${improvement.toFixed(3)})`
      report += `\n`
    })
    
    // 最新迭代详情
    const latest = this.iterationHistory[this.iterationHistory.length - 1]
    report += `\n🎯 最新迭代详情 (第${latest.iteration}轮):\n`
    report += `  📊 综合评分: ${latest.summary.avg_scores.overall.toFixed(3)}\n`
    report += `  🏆 最佳表现: ${latest.summary.best_performers.slice(0, 3).join(', ')}\n`
    report += `  ⚠️ 待改进: ${latest.summary.worst_performers.slice(0, 3).join(', ')}\n`
    
    // 维度分析
    report += `\n📋 维度分析:\n`
    const dims = ['creativity', 'unexpectedness', 'coherence', 'cultural_resonance']
    dims.forEach(dim => {
      const score = latest.summary.avg_scores[dim]
      const bar = '█'.repeat(Math.round(score * 20))
      const empty = '░'.repeat(20 - Math.round(score * 20))
      report += `  ${this.getDimensionName(dim)}: ${bar}${empty} ${score.toFixed(3)}\n`
    })
    
    // 洞察和建议
    report += `\n💡 关键洞察:\n`
    latest.summary.insights.forEach(insight => {
      report += `  • ${insight}\n`
    })
    
    report += `\n🔧 优化建议:\n`
    latest.optimization_suggestions.forEach(suggestion => {
      report += `  • ${suggestion}\n`
    })
    
    return report
  }

  /**
   * 导出数据
   */
  exportData(): string {
    return JSON.stringify({
      language: this.language,
      current_iteration: this.currentIteration,
      iteration_history: this.iterationHistory,
      export_timestamp: new Date().toISOString()
    }, null, 2)
  }

  /**
   * 获取迭代历史
   */
  getIterationHistory(): IterationReport[] {
    return this.iterationHistory
  }
}

export default ManualAIInteractionSystem
