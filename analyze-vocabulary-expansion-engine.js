/**
 * 词汇扩展引擎深度分析脚本
 * 统计和分析 vocabulary-expansion-engine.ts 中的所有词汇
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 读取词汇扩展引擎文件
function readVocabularyExpansionEngine() {
  const filePath = path.join(__dirname, 'server/api/vocabulary/vocabulary-expansion-engine.ts');
  return fs.readFileSync(filePath, 'utf8');
}

// 提取数组中的词汇
function extractWordsFromArray(content, arrayName) {
  const regex = new RegExp(`${arrayName}:\\s*\\[([\\s\\S]*?)\\]`, 'g');
  const matches = content.match(regex);

  if (!matches) return [];

  const words = [];
  matches.forEach(match => {
    // 提取方括号内的内容
    const arrayContent = match.match(/\[([\s\S]*?)\]/)[1];
    // 提取所有引号内的词汇
    const wordMatches = arrayContent.match(/'([^']+)'/g);
    if (wordMatches) {
      wordMatches.forEach(wordMatch => {
        const word = wordMatch.replace(/'/g, '');
        if (word && word.length > 0) {
          words.push(word);
        }
      });
    }
  });

  return words;
}

// 分析词汇特征
function analyzeWordCharacteristics(words) {
  const analysis = {
    totalCount: words.length,
    lengthDistribution: {},
    culturalContext: {
      traditional: 0,
      modern: 0,
      neutral: 0
    },
    categories: {
      emotions: 0,
      professions: 0,
      characteristics: 0,
      traditional_cultural: 0,
      popular_modern: 0,
      trend_culture: 0,
      subculture: 0,
      others: 0
    },
    qualityEstimate: {
      high: 0,    // 估计高质量词汇 (≥0.8)
      medium: 0,  // 估计中等质量词汇 (0.6-0.8)
      low: 0      // 估计低质量词汇 (<0.6)
    }
  };

  words.forEach(word => {
    // 长度分布
    const length = word.length;
    analysis.lengthDistribution[length] = (analysis.lengthDistribution[length] || 0) + 1;

    // 文化语境分析
    if (isTraditionalWord(word)) {
      analysis.culturalContext.traditional++;
    } else if (isModernWord(word)) {
      analysis.culturalContext.modern++;
    } else {
      analysis.culturalContext.neutral++;
    }

    // 类别分析
    const category = categorizeWord(word);
    analysis.categories[category]++;

    // 质量估计
    const quality = estimateWordQuality(word);
    if (quality >= 0.8) analysis.qualityEstimate.high++;
    else if (quality >= 0.6) analysis.qualityEstimate.medium++;
    else analysis.qualityEstimate.low++;
  });

  return analysis;
}

// 判断是否为传统文化词汇
function isTraditionalWord(word) {
  const traditionalKeywords = ['仙', '圣', '士', '客', '雅', '文', '诗', '词', '书', '墨', '古', '传统', '经典'];
  const traditionalSuffixes = ['者', '人', '君', '翁', '童'];

  return traditionalKeywords.some(keyword => word.includes(keyword)) ||
         traditionalSuffixes.some(suffix => word.endsWith(suffix));
}

// 判断是否为现代词汇
function isModernWord(word) {
  const modernKeywords = ['网红', '博主', 'UP主', '主播', '数字', '智能', '科技', '互联网', '元宇宙'];
  const modernSuffixes = ['师', '员', '官', '长', '总', '专家', '达人'];

  return modernKeywords.some(keyword => word.includes(keyword)) ||
         modernSuffixes.some(suffix => word.endsWith(suffix));
}

// 词汇分类
function categorizeWord(word) {
  // 情感词汇
  const emotionKeywords = ['温暖', '温柔', '热情', '冷静', '平静', '欢乐', '愉快', '深情', '真诚', '纯真'];
  if (emotionKeywords.some(keyword => word.includes(keyword))) return 'emotions';

  // 职业词汇
  const professionKeywords = ['师', '员', '家', '者', '手', '工程师', '设计师', '经理', '总监'];
  if (professionKeywords.some(keyword => word.includes(keyword))) return 'professions';

  // 特征词汇
  const characteristicKeywords = ['专业', '创新', '独特', '优雅', '精致', '高效', '卓越'];
  if (characteristicKeywords.some(keyword => word.includes(keyword))) return 'characteristics';

  // 传统文化
  if (isTraditionalWord(word)) return 'traditional_cultural';

  // 流行现代
  const popularKeywords = ['网红', '爆款', '潮流', '时尚', '酷', '萌', '治愈', '佛系'];
  if (popularKeywords.some(keyword => word.includes(keyword))) return 'popular_modern';

  // 潮流文化
  const trendKeywords = ['二次元', '元宇宙', '内卷', '躺平', '破圈', 'emo', '社恐'];
  if (trendKeywords.some(keyword => word.includes(keyword))) return 'trend_culture';

  // 亚文化
  const subcultureKeywords = ['电竞', '游戏', '音乐', '美食', '时尚', '说唱'];
  if (subcultureKeywords.some(keyword => word.includes(keyword))) return 'subculture';

  return 'others';
}

// 估计词汇质量
function estimateWordQuality(word) {
  let score = 0.5; // 基础分

  // 长度评分 (2-4字为最佳)
  if (word.length >= 2 && word.length <= 4) {
    score += 0.2;
  } else if (word.length === 1 || word.length === 5) {
    score += 0.1;
  }

  // 文化深度评分
  if (isTraditionalWord(word)) score += 0.15;

  // 现代感评分
  if (isModernWord(word)) score += 0.1;

  // 语音美感评分
  const goodSoundChars = ['音', '韵', '美', '雅', '优', '清'];
  if (goodSoundChars.some(char => word.includes(char))) score += 0.1;

  // 避免过于网络化
  const overNetworkWords = ['666', '233', 'yyds', '绝绝子', 'awsl'];
  if (overNetworkWords.some(netWord => word.includes(netWord))) score -= 0.2;

  return Math.min(1, Math.max(0.3, score));
}

// 主分析函数
function analyzeVocabularyExpansionEngine() {
  console.log('🔍 词汇扩展引擎深度分析');
  console.log('='.repeat(60));

  try {
    const content = readVocabularyExpansionEngine();

    // 提取各类词汇数组
    const categories = {
      // 基础情感词汇
      basic_emotions: extractWordsFromArray(content, 'basic_emotions'),
      positive_emotions: extractWordsFromArray(content, 'positive_emotions'),
      deep_emotions: extractWordsFromArray(content, 'deep_emotions'),
      artistic_emotions: extractWordsFromArray(content, 'artistic_emotions'),
      modern_emotions: extractWordsFromArray(content, 'modern_emotions'),

      // 职业词汇
      traditional_professions: extractWordsFromArray(content, 'traditional_professions'),
      modern_professions: extractWordsFromArray(content, 'modern_professions'),
      creative_professions: extractWordsFromArray(content, 'creative_professions'),
      emerging_professions: extractWordsFromArray(content, 'emerging_professions'),
      service_professions: extractWordsFromArray(content, 'service_professions'),

      // 特征词汇
      personality_traits: extractWordsFromArray(content, 'personality_traits'),
      ability_traits: extractWordsFromArray(content, 'ability_traits'),
      quality_traits: extractWordsFromArray(content, 'quality_traits'),
      style_traits: extractWordsFromArray(content, 'style_traits'),
      state_traits: extractWordsFromArray(content, 'state_traits'),

      // 传统文化
      classical_poetry: extractWordsFromArray(content, 'classical_poetry'),
      traditional_concepts: extractWordsFromArray(content, 'traditional_concepts'),
      classical_expressions: extractWordsFromArray(content, 'classical_expressions'),
      traditional_virtues: extractWordsFromArray(content, 'traditional_virtues'),
      scholar_titles: extractWordsFromArray(content, 'scholar_titles'),

      // 流行文化
      daily_life: extractWordsFromArray(content, 'daily_life'),
      internet_popular: extractWordsFromArray(content, 'internet_popular'),
      modern_expressions: extractWordsFromArray(content, 'modern_expressions'),
      emotional_expressions: extractWordsFromArray(content, 'emotional_expressions'),

      // 大规模扩展
      massiveEmotionWords: extractWordsFromArray(content, 'massiveEmotionWords'),
      massiveProfessionWords: extractWordsFromArray(content, 'massiveProfessionWords'),
      massiveCharacteristicWords: extractWordsFromArray(content, 'massiveCharacteristicWords'),

      // 潮流文化
      anime_culture: extractWordsFromArray(content, 'anime_culture'),
      internet_subculture: extractWordsFromArray(content, 'internet_subculture'),
      emerging_concepts: extractWordsFromArray(content, 'emerging_concepts'),
      gen_z_culture: extractWordsFromArray(content, 'gen_z_culture'),
      trend_concepts: extractWordsFromArray(content, 'trend_concepts'),

      // 亚文化
      gaming_culture: extractWordsFromArray(content, 'gaming_culture'),
      music_culture: extractWordsFromArray(content, 'music_culture'),
      fashion_culture: extractWordsFromArray(content, 'fashion_culture'),
      food_culture: extractWordsFromArray(content, 'food_culture')
    };

    // 统计各类别词汇数量
    console.log('📊 各类别词汇统计:');
    console.log('-'.repeat(40));

    let totalWords = 0;
    const categoryStats = {};

    Object.entries(categories).forEach(([category, words]) => {
      if (words.length > 0) {
        console.log(`${category}: ${words.length}个词汇`);
        totalWords += words.length;

        // 按大类归类
        const mainCategory = getMainCategory(category);
        categoryStats[mainCategory] = (categoryStats[mainCategory] || 0) + words.length;
      }
    });

    console.log('-'.repeat(40));
    console.log(`总词汇数量: ${totalWords}个`);

    // 主要类别统计
    console.log('\n📈 主要类别分布:');
    console.log('-'.repeat(40));
    Object.entries(categoryStats).forEach(([category, count]) => {
      const percentage = (count / totalWords * 100).toFixed(1);
      console.log(`${category}: ${count}个 (${percentage}%)`);
    });

    return {
      totalWords,
      categories,
      categoryStats,
      content
    };

  } catch (error) {
    console.error('❌ 分析过程中发生错误:', error);
    return null;
  }
}

// 获取主要类别
function getMainCategory(subcategory) {
  if (subcategory.includes('emotion')) return '情感词汇';
  if (subcategory.includes('profession')) return '职业词汇';
  if (subcategory.includes('trait')) return '特征词汇';
  if (subcategory.includes('classical') || subcategory.includes('traditional') || subcategory.includes('scholar')) return '传统文化';
  if (subcategory.includes('daily') || subcategory.includes('internet') || subcategory.includes('modern')) return '流行现代';
  if (subcategory.includes('anime') || subcategory.includes('gen_z') || subcategory.includes('trend') || subcategory.includes('emerging')) return '潮流文化';
  if (subcategory.includes('gaming') || subcategory.includes('music') || subcategory.includes('fashion') || subcategory.includes('food')) return '亚文化';
  if (subcategory.includes('massive')) return '大规模扩展';
  return '其他';
}

// 运行分析
const result = analyzeVocabularyExpansionEngine();

if (result) {
  console.log('\n✅ 词汇扩展引擎分析完成！');
  console.log(`📚 发现总计 ${result.totalWords} 个扩展词汇`);
}