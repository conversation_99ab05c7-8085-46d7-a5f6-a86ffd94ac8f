# 📚 文档索引与整理说明

本仓库的文档较多，现按与 **V5第一性原理引擎** 的相关性进行分类整理，便于后续追溯、更新与维护。

## 🚀 最新更新：V5引擎升级 (2025-06-14)
项目已从V4升级到V5第一性原理引擎，专注于生成效果调试和架构简化。

---

## 1️⃣ 需求与概念设计（核心保留）
| 文件 | 说明 |
|------|------|
| `thinking.md` | 初始需求与问题拆解，覆盖多语种生成思路。|
| `generation_cn_scheme.md` | 中文用户名语法模板与槽位方案详述。|
| `blueprint.md` | 技术蓝图（数据层 / 生成引擎 / 过滤器）。|
| `data_generation_guide.md` | 词库与模板 JSON 的维护与月度更新指引。|

## 2️⃣ V5引擎核心文档（最新）
| 文件 | 说明 |
|------|------|
| `V5_PROJECT_STATUS_REPORT.md` | V5引擎项目状态总报告，包含升级对比和核心成就。|
| `V5-implementation-report.md` | V5引擎实施报告，详述V4→V5升级过程和技术细节。|
| `v4-first-principles-api.md` | V4引擎API文档（已弃用，保留参考）。|
| `developer-integration-guide.md` | 开发者集成指南，包含SDK和最佳实践。|

## 3️⃣ 规划与进度跟踪（核心保留）
| 文件 | 说明 |
|------|------|
| `plan.md` | 整体阶段性目标与里程碑。|
| `progress-tracker.md` | 每阶段任务与完成情况登记（已更新V5进度）。|
| `phase0-report.md` | 已完成的基线梳理报告。|

## 4️⃣ 实现细节与增强（核心保留）
| 文件 | 说明 |
|------|------|
| `enhanced-system-guide.md` | 生成器高级特性（趋势词注入、槽位后处理等）。|
| `plan.svg` | 方案流程示意图。|
| `INTERESTING_THEORY.md` | "有趣"理论核心文档。|
| `USERNAME_PATTERN_ANALYSIS.md` | 用户名模式深度分析。|

## 5️⃣ 历史版本文档（已归档）
以下文件为V3及之前版本的文档，当前工程聚焦V5引擎，故暂移至归档状态，保留作为历史参考。

| 文件 | 说明 |
|------|------|
| `PROJECT_STATUS_REPORT.md` | V3项目状态报告（已被V5版本替代）。|
| `V3_SYSTEM_DESIGN.md` | V3系统设计文档。|
| `V3.1_UPGRADE_SUMMARY.md` | V3.1升级总结。|

## 6️⃣ AI 驱动评估 / 优化相关（已归档）
以下文件主要探讨**依赖外部大模型**的评估与优化流程，当前工程聚焦V5引擎调试，故暂移至归档目录，未来需要时可再启用。

| 文件 |
|------|
| `Solution-Summary.md` |
| `Final-Implementation-Summary.md` |
| `Manual-AI-Testing-Guide.md` |
| `AI-Driven-Optimization-System.md` |

> 📁 以上文件已**标记为归档**（保留原路径），后续如需启用可再整合进主文档体系。

---

### V5引擎文档维护约定
1. 任何新文档请先判断与V5引擎的直接关联性，再决定放置路径：
   • V5引擎核心功能 / 调试优化 ⇒ `docs/`
   • 研发计划 / 进展跟踪 ⇒ `docs/`
   • 历史版本或与V5引擎弱关联的内容 ⇒ `docs/archive/`
2. 更新文档时务必在此索引同步增删，保持可追溯。
3. V5引擎相关的新文档应优先标注版本和更新日期。

### 文档优先级
- **🔥 高优先级**: V5引擎相关文档，调试和优化指南
- **📋 中优先级**: 通用设计文档，数据规范
- **📁 低优先级**: 历史版本文档，归档内容

---

> 最后更新：2025-06-14 (V5引擎升级完成)
> 文档状态：V5引擎专注调试阶段
> 下次更新：V5引擎调试优化完成后