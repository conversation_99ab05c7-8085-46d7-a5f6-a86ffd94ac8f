<!DOCTYPE html>
<html dir="ltr" lang="en" class="theme light classic">
    <head>
        <base href="/">
        <meta charset="utf-8">
        <title>用户名生成服务界面设计建议</title>
        <meta name="app-name" content="export_website"/>
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <link href="_assets/a0684b0780c739e9.vendor.ltr.css" rel="stylesheet" integrity="sha512-JwMCpiHdk95MoUTatfaZJwstzeDnWfvWMJiwnSxZfPmgeCe4yvQDQ+ONMQjIy/Ht72r0TmlE+gvZnYRnpdLdVg==" crossorigin="anonymous">
        <link href="_assets/5c5d1f697318d154.ltr.css" rel="stylesheet" integrity="sha512-ZBPXl6L7KqOC1GJNF84s+B5lcIhSIL5H7ruV6E9kR993qtiK7Loo+Y2LmXJ57IEaod6yC/8CbEPBcmYgmcX8zA==" crossorigin="anonymous">
        <link href="_assets/static_font_4.ltr.css" rel="stylesheet">
        <link href="_assets/fb0e24e56e55fa0f.runtime.js" rel="preload" as="script" crossorigin="anonymous" integrity="sha512-9mRXIUj3k+63+m8Byya+zrhBv05kzSQq7ou95Y8+JM5RL+mv3RogXx2Q11G5y/1UoAmVT2yqoND9vXU1gvAj7A==" nonce="850cf7ea-0983-4821-b184-c3b8366d87fb">
        <link href="_assets/cb43e4c21b5b6695.i3d79q.vendor.js" rel="preload" as="script" crossorigin="anonymous" integrity="sha512-WtSCRiJUMYxa1x4RParK+Jjovnk53OnXvsxqc+551N52i/tTk0rj3b/trD68Cz/dEaHonR7cJmRGa4quhb7nvg==" nonce="850cf7ea-0983-4821-b184-c3b8366d87fb">
        <link href="_assets/76d4afe67582a8de.vendor.js" rel="preload" as="script" crossorigin="anonymous" integrity="sha512-1MK/4EmjBrs2uuhUHubTdccP1FePY6RLHHqhm19E0dIs8tqnt9mG/rsIm5ugo3WDoyQE0Wt6t9obkxvxkXu5jA==" nonce="850cf7ea-0983-4821-b184-c3b8366d87fb">
        <link href="_assets/c89ca7d5130c19d7.strings.js" rel="preload" as="script" crossorigin="anonymous" integrity="sha512-Izya9bfSccHhbXiv1s+3ttpFHAJTkceGblCBRjVbuLhvaNKaOOHpuSeOReUum7MRJu1wTQ3f8bmMv9STGyc1ng==" nonce="850cf7ea-0983-4821-b184-c3b8366d87fb">
        <link href="_assets/50ee8aee4562cf48.en.js" rel="preload" as="script" crossorigin="anonymous" integrity="sha512-OxQ4g8sKXjacoOsB7neBr8y2L+YrvbafNPQe8PnEGrMcqh5LbJTQEz/ikO4ax5I3ZpdOJOUqcARNSPlX7oBYVQ==" nonce="850cf7ea-0983-4821-b184-c3b8366d87fb">
        <link href="_assets/43bb58a6e2f38471.js" rel="preload" as="script" crossorigin="anonymous" integrity="sha512-CkhhwQ659eETwFoerj53MEXq17bXMyrq/TU8n4FCDW5FovlNTMeHIARUz9F9G8yG+OkIYoF1LQa+YKGYw8ssnw==" nonce="850cf7ea-0983-4821-b184-c3b8366d87fb">
        <meta name="referrer" content="strict-origin-when-cross-origin">
        <meta property="og:title" content="用户名生成服务界面设计建议">
        <meta property="og:type" content="website">
        <link rel="shortcut icon" href="_assets/images/2d0b56e7e51cf11036ad8734bdb67e2d.png">
        <link rel="icon" href="_assets/images/e53c4bd8da5e491d9ab09e7cf0daf874.png" sizes="192x192">
        <link rel="apple-touch-icon" href="_assets/images/725b756a69a7d4c235070e51acd85560.png" sizes="180x180">
        <script nonce="850cf7ea-0983-4821-b184-c3b8366d87fb">
            document.addEventListener('contextmenu', (e) => {
                const isMedia = ['img', 'image', 'video', 'svg', 'picture'].some(tagName => tagName.localeCompare(e.target.tagName, undefined, {
                    sensitivity: 'base'
                }) === 0, );
                isMedia && e.preventDefault();
            }
            );
        </script>
        <script nonce="850cf7ea-0983-4821-b184-c3b8366d87fb">
            const lang = navigator.language ? navigator.language : 'en';
            window.canva_installFooter = (container) => {
                if (!(container instanceof HTMLDivElement)) {
                    return;
                }
                fetch('_footer?lang=' + encodeURIComponent(lang)).then(response => {
                    if (response.status !== 200) {
                        return;
                    }
                    response.text().then(footerStr => {
                        const div = document.createElement('div');
                        div.innerHTML = footerStr;
                        for (const child of [...div.children]) {
                            if (child.tagName.toLowerCase() !== 'script') {
                                container.append(child);
                            }
                        }

                        ( () => {
                            !function(e) {
                                "use strict";
                                const t = document.getElementById("modal_backdrop")
                                  , n = document.getElementById("modal")
                                  , o = document.getElementById("captcha-form")
                                  , c = document.getElementById("report_button")
                                  , d = document.getElementById("form_report")
                                  , s = document.getElementById("form_cancel")
                                  , l = document.getElementById("form_submit_reason")
                                  , a = document.getElementById("form_go_back")
                                  , r = document.getElementById("form_submit_captcha")
                                  , m = document.getElementById("form_close")
                                  , i = document.getElementById("form_close_initial")
                                  , u = document.getElementById("report_reason_0")
                                  , p = document.getElementById("error_message")
                                  , _ = document.getElementById("error_message_captcha")
                                  , y = new Map;
                                y.set(0, document.getElementById("form_step_terms")),
                                y.set(1, document.getElementById("form_step_report_reason")),
                                y.set(4, document.getElementById("form_step_report_other"));
                                const E = document.getElementById("form_step_report_ip");
                                E && y.set(5, E),
                                y.set(2, document.getElementById("form_step_captcha")),
                                y.set(3, document.getElementById("form_step_success"));
                                const f = document.getElementById("report_reason_4")
                                  , g = document.getElementById("form_close_ip")
                                  , h = document.getElementById("form_go_back_ip")
                                  , I = document.getElementById("report_reason_other")
                                  , k = document.getElementById("form_close_other")
                                  , w = document.getElementById("form_go_back_other");
                                function v() {
                                    t.classList.remove("active"),
                                    n.classList.remove("active"),
                                    c.classList.remove("active"),
                                    c.focus()
                                }
                                function B(e) {
                                    y.forEach(( (t, n) => {
                                        n === e ? (t.style.display = "block",
                                        L(t)) : t.style.display = "none"
                                    }
                                    ))
                                }
                                let b, C = !1;
                                const T = "NETEASE" === window.C_CAPTCHA_IMPLEMENTATION ? () => b : () => {
                                    const e = o.elements.namedItem("g-recaptcha-response");
                                    return null == e ? void 0 : e.value
                                }
                                ;
                                t.onclick = v,
                                s.onclick = v,
                                m.onclick = v,
                                i.onclick = v,
                                g && (g.onclick = v),
                                k.onclick = v,
                                c.onclick = function() {
                                    y.forEach(( (e, t) => {
                                        e.style.display = 0 === t ? "block" : "none"
                                    }
                                    )),
                                    t.classList.add("active"),
                                    n.classList.add("active"),
                                    c.classList.add("active"),
                                    u.checked = !0,
                                    setTimeout(( () => {
                                        L(y.get(0))
                                    }
                                    ), 350)
                                }
                                ,
                                d.onclick = d.dataset.reportUrl ? function() {
                                    const {origin: e, pathname: t} = window.location
                                      , n = e + t
                                      , o = d.dataset.reportUrl + encodeURIComponent(n);
                                    window.open(o)
                                }
                                : () => B(1),
                                l.onclick = () => {
                                    null != E && f.checked ? B(5) : I.checked ? B(4) : (B(2),
                                    function() {
                                        if (C)
                                            return;
                                        const e = document.createElement("script");
                                        e.src = "NETEASE" === window.C_CAPTCHA_IMPLEMENTATION ? "https://cstaticdun.126.net/load.min.js" : "https://www.google.com/recaptcha/api.js",
                                        e.async = !0,
                                        e.defer = !0,
                                        document.head.appendChild(e),
                                        C = !0,
                                        e.onload = "NETEASE" === window.C_CAPTCHA_IMPLEMENTATION ? () => {
                                            var e;
                                            null === (e = window.initNECaptcha) || void 0 === e || e.call(window, {
                                                captchaId: window.C_CAPTCHA_KEY,
                                                element: "#netease-captcha",
                                                protocol: "https",
                                                width: "auto",
                                                onVerify: (e, t) => {
                                                    b = t.validate
                                                }
                                            })
                                        }
                                        : () => {}
                                    }())
                                }
                                ,
                                a.onclick = () => B(1),
                                h && (h.onclick = () => B(1)),
                                w.onclick = () => B(1),
                                o.addEventListener("submit", (function(e) {
                                    e.preventDefault(),
                                    p.style.display = "none",
                                    _.style.display = "none";
                                    const t = function() {
                                        let e = "";
                                        const t = document.getElementsByName("report_reason");
                                        for (let n = 0; n < t.length; n++) {
                                            const o = t[n];
                                            o.checked && (e = o.value)
                                        }
                                        return e
                                    }()
                                      , n = T();
                                    if (!n)
                                        return void (_.style.display = "block");
                                    const o = {
                                        reason: t,
                                        challenge: n
                                    }
                                      , c = window.location.origin + window.location.pathname + "/_api/report";
                                    r.classList.add("loading"),
                                    fetch(c, {
                                        method: "POST",
                                        body: JSON.stringify(o),
                                        headers: {
                                            "Content-Type": "application/json; charset=utf-8"
                                        }
                                    }).then((e => {
                                        r.classList.remove("loading"),
                                        e.ok ? B(3) : p.style.display = "block"
                                    }
                                    ))
                                }
                                ));
                                const A = new Map
                                  , L = e => {
                                    const t = A.get(e);
                                    null != t && document.removeEventListener("keydown", t);
                                    const n = e.querySelectorAll('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])')
                                      , o = e
                                      , c = n[n.length - 1]
                                      , d = function(e) {
                                        ("Tab" === e.key || 9 === e.keyCode) && (e.shiftKey ? document.activeElement === o && (c.focus(),
                                        e.preventDefault()) : document.activeElement === c && (o.focus(),
                                        e.preventDefault()))
                                    };
                                    A.set(e, d),
                                    document.addEventListener("keydown", d),
                                    o.focus()
                                }
                                ;
                                e.keepFocus = L,
                                Object.defineProperty(e, "__esModule", {
                                    value: !0
                                })
                            }({});
                        }
                        )();
                    }
                    );
                }
                );
            }
        </script>
        <script nonce="850cf7ea-0983-4821-b184-c3b8366d87fb">
            window.C_CAPTCHA_IMPLEMENTATION = 'RECAPTCHA';
        </script>
        <script nonce="850cf7ea-0983-4821-b184-c3b8366d87fb">
            window.C_CAPTCHA_KEY = '6Ldk59waAAAAAMPqkICbJjfMivZLCGtTpa6Wn6zO';
        </script>
    </head>
    <body>
        <script nonce="850cf7ea-0983-4821-b184-c3b8366d87fb">
            document.documentElement.classList.replace('adaptive', window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light');
        </script>
        <div id="root"></div>
        <script nonce="850cf7ea-0983-4821-b184-c3b8366d87fb">
            (function() {
                window['__canva_public_path__'] = '_assets\/';
                window['bootstrap'] = JSON.parse('{"base":{"A?":"B","L":false,"N":false,"E":"a2cb1676c53bd1ae91d934c7c3bf1b0789720321","K":1750263177,"F":{"A?":"B"},"G":"CLIENT_FULL","I":"A","O":{"A?":"B"},"M":"/_online","U":[],"V":[],"a":{"K":"export_website","H":{}},"d":{"A":"wss://www.canva.com/_stream","B":{"F":"export_website"}},"b":{"A?":"H","C":{"a":"WEB"},"D":{"a":"WEB","c":"en"},"E":{},"P":[],"Q":[],"R":false,"S":false}},"page":{"A":{"B":{"A?":"A","A":"TAGcbLa0hys","B":1},"C":{"A":1366.0,"B":768.0,"C":"D"},"Q":[],"R":[],"P":"en","D":"用户名生成服务界面设计建议","E":"","F":[],"G":{},"Y":{},"a":[],"b":[],"V":[],"W":[],"A":[{"A?":"j","Z":{"A?":"A","A":"TAGcbLa0hys","B":1},"a":"PBCm3LHDZsZ8LrhJ","R":false,"S":false,"U":false,"Q":{},"v":"B","t":[{"A?":"i","a":"PBNnxPFpgNJlK6Zp","R":false,"S":false,"U":false,"Q":{},"W":[],"X":[],"H":[],"L":{},"C":{"A":1366.0,"B":768.0,"C":"D"},"D":{"C":"#ffffff","D":0.0,"F":false,"J":false,"G":false,"H":false},"r":[],"E":[{"A?":"O","A":0.0,"B":0.0,"D":1366.0,"C":768.0,"E":0.0,"F":0.0,"BD":[],"BE":[],"BA":[],"BB":[],"Q":false,"S":false,"P":[],"5":{},"T":{},"W":{},"a":"_website-element-widget?version=v2&pageIndex=0&elementIndex=0&elementType=C&locale=en","b":1366.0,"c":768.0}],"I":[]}],"u":[],"L":{}}],"I":[]},"Z":{"A?":"E","M":"en","e":false,"f":{"B":"#ffffff","C":"#304254"},"g":[{"A?":"A","K":"PBCm3LHDZsZ8LrhJ","L":"/","e":{"A":true}}]},"B":[{"A":"YACgEZ1cb1Q","B":0,"C":"Arimo","D":[{"style":"BOLD_ITALICS","files":[{"url":"_assets/fonts/7fc2786f9023d4d812357cb787e9ad4b.woff","format":"WOFF","unicodeRanges":[],"sizeBytes":237660},{"url":"_assets/fonts/4de29305db46625343735e840e3d27aa.otf","format":"OTF","unicodeRanges":[],"sizeBytes":435028},{"url":"_assets/fonts/1060345c54d396e76d73f1da7ee200bd.woff2","format":"WOFF2","unicodeRanges":[],"sizeBytes":203232}],"subsettedFiles":[],"scriptSubsetFiles":[],"metadata":{"head":{"unitsPerEm":2048,"yMin":-771,"yMax":2109},"hhea":{"ascender":1854,"descender":-434,"lineGap":67},"os2":{"sTypoAscender":1491,"sTypoDescender":-431,"sTypoLineGap":307,"usWinAscent":1854,"usWinDescent":434,"fsSelection":33}}},{"style":"BOLD","files":[{"url":"_assets/fonts/98c4d2c0223fc8474641c77f923528e9.woff2","format":"WOFF2","unicodeRanges":[],"sizeBytes":194064},{"url":"_assets/fonts/0bb563f2f60ffd4e62b64cb021c33776.otf","format":"OTF","unicodeRanges":[],"sizeBytes":438516},{"url":"_assets/fonts/9fd726aeae98b22824f0dc8947036e77.woff","format":"WOFF","unicodeRanges":[],"sizeBytes":234696}],"subsettedFiles":[],"scriptSubsetFiles":[],"metadata":{"head":{"unitsPerEm":2048,"yMin":-771,"yMax":2116},"hhea":{"ascender":1854,"descender":-434,"lineGap":67},"os2":{"sTypoAscender":1491,"sTypoDescender":-431,"sTypoLineGap":307,"usWinAscent":1854,"usWinDescent":434,"fsSelection":32}}},{"style":"REGULAR","files":[{"url":"_assets/fonts/daee28464eba92254cb149feea6599e3.otf","format":"OTF","unicodeRanges":[],"sizeBytes":436180},{"url":"_assets/fonts/4ff8a742aa834e35f10ead140b735c45.woff","format":"WOFF","unicodeRanges":[],"sizeBytes":234380},{"url":"_assets/fonts/f8f199f09526f79e87644ed227e0f651.woff2","format":"WOFF2","unicodeRanges":[],"sizeBytes":194188}],"subsettedFiles":[],"scriptSubsetFiles":[],"metadata":{"head":{"unitsPerEm":2048,"yMin":-621,"yMax":2007},"hhea":{"ascender":1854,"descender":-434,"lineGap":67},"os2":{"sTypoAscender":1491,"sTypoDescender":-431,"sTypoLineGap":307,"usWinAscent":1854,"usWinDescent":434,"fsSelection":64}}},{"style":"ITALICS","files":[{"url":"_assets/fonts/d257a7100844bc3f98c9021168b6249e.woff2","format":"WOFF2","unicodeRanges":[],"sizeBytes":201132},{"url":"_assets/fonts/79d14bb61301a10c73c6cb293c29b309.otf","format":"OTF","unicodeRanges":[],"sizeBytes":440740},{"url":"_assets/fonts/ac9b8a560f98d1757a4565a2419d33c3.woff","format":"WOFF","unicodeRanges":[],"sizeBytes":240632}],"subsettedFiles":[],"scriptSubsetFiles":[],"metadata":{"head":{"unitsPerEm":2048,"yMin":-621,"yMax":2077},"hhea":{"ascender":1854,"descender":-434,"lineGap":67},"os2":{"sTypoAscender":1491,"sTypoDescender":-425,"sTypoLineGap":307,"usWinAscent":1854,"usWinDescent":434,"fsSelection":1}}}],"E":false}],"E":[],"F":[],"G":[],"H":[{"A":"https://www.canva-hosted-embed.com/codelet/be83jg5kbq0bm7jb","Q":false,"B":"Canva","D":1750263177218,"E":"\x3ciframe src=\\"https://be83jg5kbq0bm7jb.canva-hosted-embed.com/codelet/AAEAEGJlODNqZzVrYnEwYm03amIAAAAAAZeI9vwCYOXd6PGuWRsP4rux8fJGsprHX87Zw8I2XAFr_VcyaBM/\\" sandbox=\\"allow-same-origin allow-scripts allow-popups allow-forms\\" allowfullscreen=\\"true\\" width=\\"800\\" height=\\"450\\" style=\\"position: absolute; top: 0; left: 0; width: 100%; height: 100%; border: none; border-radius: 8px;\\" allow=\\"fullscreen\\">\x3c/iframe>","F":1366,"G":768},{"A":"_website-element-widget?version=v2&pageIndex=0&elementIndex=0&elementType=C&locale=en","Q":false,"B":"Canva","D":1750263177279,"E":"\x3ciframe src=\\"_website-element-widget?version=v2&pageIndex=0&elementIndex=0&elementType=C&locale=en\\" width=\\"1366\\" height=\\"768\\" frameborder=\\"0\\" sandbox=\\"allow-same-origin allow-scripts allow-popups allow-forms allow-popups-to-escape-sandbox\\">\x3c/iframe>","F":1366,"G":768}],"S":[],"K":{"B":"#ffffff","C":"#304254"},"Q":{"A":false,"B":true,"M":true},"R":false,"T":true,"U":false,"V":false},"ui":{"N":false,"A":true,"B":"A","C":false,"D":false,"K":"A","I":false,"J":false}}');
                window['flags'] = JSON.parse('{"5d40ae58":true,"978ce15d":150000.0}');
                window['cmsg'] = window['cmsg'] || {
                    locale: "en",
                    strings: {}
                };
            }
            )();
        </script>
        <script crossorigin="anonymous" src="_assets/fb0e24e56e55fa0f.runtime.js" defer integrity="sha512-9mRXIUj3k+63+m8Byya+zrhBv05kzSQq7ou95Y8+JM5RL+mv3RogXx2Q11G5y/1UoAmVT2yqoND9vXU1gvAj7A==" nonce="850cf7ea-0983-4821-b184-c3b8366d87fb"></script>
        <script crossorigin="anonymous" src="_assets/cb43e4c21b5b6695.i3d79q.vendor.js" defer integrity="sha512-WtSCRiJUMYxa1x4RParK+Jjovnk53OnXvsxqc+551N52i/tTk0rj3b/trD68Cz/dEaHonR7cJmRGa4quhb7nvg==" nonce="850cf7ea-0983-4821-b184-c3b8366d87fb"></script>
        <script crossorigin="anonymous" src="_assets/76d4afe67582a8de.vendor.js" defer integrity="sha512-1MK/4EmjBrs2uuhUHubTdccP1FePY6RLHHqhm19E0dIs8tqnt9mG/rsIm5ugo3WDoyQE0Wt6t9obkxvxkXu5jA==" nonce="850cf7ea-0983-4821-b184-c3b8366d87fb"></script>
        <script crossorigin="anonymous" src="_assets/c89ca7d5130c19d7.strings.js" integrity="sha512-Izya9bfSccHhbXiv1s+3ttpFHAJTkceGblCBRjVbuLhvaNKaOOHpuSeOReUum7MRJu1wTQ3f8bmMv9STGyc1ng==" nonce="850cf7ea-0983-4821-b184-c3b8366d87fb"></script>
        <script crossorigin="anonymous" src="_assets/50ee8aee4562cf48.en.js" integrity="sha512-OxQ4g8sKXjacoOsB7neBr8y2L+YrvbafNPQe8PnEGrMcqh5LbJTQEz/ikO4ax5I3ZpdOJOUqcARNSPlX7oBYVQ==" nonce="850cf7ea-0983-4821-b184-c3b8366d87fb"></script>
        <script crossorigin="anonymous" src="_assets/43bb58a6e2f38471.js" defer integrity="sha512-CkhhwQ659eETwFoerj53MEXq17bXMyrq/TU8n4FCDW5FovlNTMeHIARUz9F9G8yG+OkIYoF1LQa+YKGYw8ssnw==" nonce="850cf7ea-0983-4821-b184-c3b8366d87fb"></script>
    </body>
</html>
