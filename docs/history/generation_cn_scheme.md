# 中文用户名生成语法框架（v1）

> 适用语言：**中文 (zh)**
> 可迁移性：思路可泛化到 **日语/韩语/西语** 等黏着型或后缀型语言；英文需调整词序规则。

---

## 1. 语义角色（POS – Part-of-Slug）
| POS  | 说明               | 示例          |
|------|--------------------|---------------|
| PREF | 夸张/情绪前缀       | 超究极、暴躁   |
| CORE | 核心名词/主体       | 兔头、高玩     |
| SUF  | 称号/身份后缀       | 大魔王、守护者 |
| LINK | 柔性连接           | 的、之、·      |
| EMO  | Emoji/拟声          | 🔥、喵         |
| LOC  | 地域/场景           | 成都、深夜     |
| ACTION | 动作/状态         | 暴走、摸鱼     |

每个词条 JSON 样例：
```jsonc
{ "word": "暴躁", "pos": "PREF", "tone": "搞怪", "weight": 1.2 }
```

## 2. 语法模式（Grammar Pattern）
| Pattern标识 | token 序列                               | 典型长度 |
|-------------|------------------------------------------|----------|
| A 修饰型    | PREF?  CORE  SUF?                        | 4-8      |
| B X的Y      | CORE  LINK  CORE/SUF                     | 4-8      |
| C 场景型    | LOC  PREF?  CORE  SUF                    | 6-10     |
| D 成语强化  | PREF  CHENGYU  SUF?                      | 7-12     |
| E 拟声型    | ONOM  CORE  EMO?                         | 3-7      |

模板以 token 序列而非字符串保存，可天然避免重复。

## 3. 生成流程
1. **预加载**：DataStore 构建 `pos →词条` Map，剔除缩写/低语义词。
2. **Pattern 过滤**：按目标 `len` 选择 `minLen ≤ len ≤ maxLen` 的模式集合。
3. **抽样 & 约束满足**
   - 逐 token 采样：`Alias(weight * 长度系数)` 选词。
   - 若采到词已用 → 重新采样，防重复。
4. **自动连词**：若相邻 token 无自然衔接且末 token ≠ LINK，则按规则插入 `LINK`。
5. **后验校验**：
   - 中文字符≥2；末尾≠纯数字/emoji；连续重复中文≤1。
   - 敏感词 Trie 检测。
   不通过 → 重试；重试失败 → fallback（数字或固定后缀）。
6. **调味**：10-30% 概率附加 Emoji/4位数字，供唯一性，但计入 UI 去重时可剔除。

## 4. 质量指标
- 语义合法率 ≥ 95%（正则 & 规则通过 / 样本数）。
- 有效重复率（剔除数字/emoji）≤ 5% in 5k 采样。
- 平均生成耗时 ≤ 1 ms（单线程 JS）。

## 5. 迁移到其他语言
- **日语**：保留 PREF/CORE/SUF，增加 `KANA` 读音支持；LINK 用「の・」
- **韩语**：LINK="의"；Suf 为「님 / 요정」等。
- **西语**：词序 = 形容词+名词；LINK 用空格即可；需性数一致规则。

---

## TODO
- 词条补充：场景、拟声、后缀 50+ 条；成语 100 条。
- 模式 JSON 定义文件：`patterns.zh.json`。
- Generator 重构：基于 token/pos 生成。
- 单测升级：`vitest semantic.test.ts` 语义合法率断言。
