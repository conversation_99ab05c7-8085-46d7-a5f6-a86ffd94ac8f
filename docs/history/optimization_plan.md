# 🛠️ Cultural 用户名生成器优化路线图

> 本文档存档了 2025-06 制定的功能升级方案，并将随落地过程更新进度。请在每个阶段完成后，在下方「进度更新」部分登记。

## 📅 阶段划分

| 阶段 | 目标 | 关键输出 | 预计状态 |
|------|------|----------|---------|
| Phase 0 | 基线梳理 | 代码评审、问题清单 | ✅ 已完成 2025-06-13 |
| Phase 1 | 夸张前缀开关 | `fancyPrefix` 参数 & 逻辑 | ✅ 已完成 2025-06-13 |
| Phase 2 | 风格体系与模板筛选 | • JSON `meta.style` 字段<br/>• 生成器 `style` 参数<br/>• 模板过滤实现 | ⏳ 进行中 |
| Phase 3 | 语义标签驱动 | • 词库 `tags` 细化<br/>• `{tag}` 占位符解析 | ⏳ 待启动 |
| Phase 4 | 音韵/押韵增强 | • 拼音库接入<br/>• `rhythm` 策略参数 | ⏳ 待启动 |
| Phase 5 | 用户反馈闭环 | • UI 采集点赞<br/>• `OptimizationLoop` 接入 | ⏳ 待启动 |

## 📈 进度更新
- **2025-06-13** Phase 0-1 已合并至 `core/UsernameGenerator.ts`（新增 `fancyPrefix` 参数）。
- **2025-06-13** 创建本优化路线图文档。
- **2025-06-13** Phase 2 进度：① `style` 参数与 Pattern 过滤逻辑 ✅；② 复杂度驱动模板筛选 + 智能补槽 ✅；③ `traditional.json`、`pop.json` 添加 `style` 字段与高槽模板示例，`link`/`suffix` 词池补充 ✅。后续将扩展其余 JSON 并提供可视化复杂度测试脚本。
- **2025-06-13** Phase 2 追加：补充 5 槽高复杂度模板于 `traditional.json`(literary) 与 `pop.json`(cute)，并加入通用 `link`/`suffix` 组件，滑块效果可见化提升 ✅。

请在每次合并完成后追加更新条目。 