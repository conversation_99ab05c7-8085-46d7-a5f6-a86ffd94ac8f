# 中文长模板 / 长词条数据补充指南

> 本文说明如何通过 AI (GPT-4o) 或爬虫，批量生成适用于用户名生成器的 **长模板**、**长词条** 数据。

---

## 1. 数据层次

| 层级 | 说明 | 更新频率 |
|------|------|----------|
| **基础词库** | 经典常用词、成语、城市、食物等。稳定，不随趋势变动。 | 几乎不变，年度审计 |
| **潮流词库** | B 站热梗、微博热搜、游戏/ACG 新番等流行语。 | 每季度或月度 |

两层都按「components → wordItem」结构存储。潮流库可在热更新时覆盖或追加到同名组件。

---

## 2. AI 生成

### 2.1 Prompt 模板
```text
你是一位擅长中文梗文化的创作者，请按照 JSON 数组格式输出 30 条“长度 ≥ 4 字的短语”，要求：
1. 每条 phrase 不含空格、不含标点。
2. 具备网络流行或二次元风格，可与美食/地域/成语混搭。
3. 不包含敏感/低俗词。
4. 不得重复，与已给出的列表去重。

输出示例：
[
  "超究极画龙点睛",
  "成都火辣兔头党"
]
```

可按类别分别询问：
- _成语强化梗_（4~6 字）
- _地域美食狂热_（5~8 字）
- _ACG 长词条_ 等。

### 2.2 筛选脚本
- 去重：对比现有 JSON。  
- 长度校验：`len(word) ≥ 4`。
- 敏感词过滤：使用 `SensitiveTrie` 即可。

合格数据写入 `/data/cultural/zh/long.json` 对应组件。

---

## 3. 爬虫来源（备选）
| 来源 | 目标 | 过滤要点 |
|------|------|----------|
| bilibili 弹幕词频 | 新番 & 热门视频高频词 | 去表情包、数字弹幕 |
| 微博热搜 | 当日热词 | 排除人物姓名、事件类 |
| Steam/手游排行榜 | 游戏名 + 职业、称号 | 排除英文、过长标题 |

---

## 4. 文件格式
```jsonc
{
  "word": "超究极画龙点睛",  // 词条
  "weight": 1.0,               // 权重，可基于热度归一化
  "tags": ["成语梗", "长词"]  // 可选标签
}
```

同一组件放在同一数组，权重可统一 1.0 或按热度调整到 0.5 ~ 2.0。

---

## 5. 提交 & 版本
1. 修改 `data/cultural/zh/long.json`。  
2. `pnpm test` 确认通过（重复率、敏感词）。  
3. `npm version patch` & `pnpm build-data`（脚本生成 manifest 校验）。  
4. MR / PR，CI 自动发布 `@namer/data-zh` 新版本。
