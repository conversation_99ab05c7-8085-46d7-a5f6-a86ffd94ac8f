# 当前 Cultural 用户名生成流程说明

> 本文档基于 2025-06-13 版本的 `core/UsernameGenerator.ts` 与最新数据 JSON，总结离线中文用户名生成的完整处理链与关键特性。

---

## 0. 输入参数
```ts
GenerateOptions {
  language?: 'zh' | 'en' | 'ja' (默认 'zh')
  category?: string           // 指定文化类别
  slot_count?: number         // 目标语义槽位数（复杂度滑块）
  includeTrends?: boolean     // 是否注入趋势词（默认 true）
  fancyPrefix?: boolean       // 是否允许夸张词槽（默认 true）
  style?: string | string[]   // 风格过滤，如 'literary' | 'cute' | 'exaggerated'
  maxRetry?: number           // 生成失败重试次数（默认 3）
}
```

## 1. 数据加载 (`BrowserDataStore.initLanguage`)
1. 加载 `/data/cultural/<lang>/*.json`→ pattern & components
2. 加载 `/data/trends/current/<lang>.json`→ trending words
3. 加载 `/data/sensitive/<lang>.txt`→ 敏感词集

所有数据缓存在内存，后续调用零 IO。

## 2. Pattern 预选
```
stepA = cultural.patterns                  // 全量 Pattern
stepB = style ? filter by style : stepA    // 按 style 筛选
patternPool = stepB.length ? stepB : stepA // 空则回退全量
```
> style 默认为 `exaggerated`，未标记 Pattern 视作 `exaggerated`。

## 3. Pattern 抽样
`Alias` 表权重采样 → 得到 1 个 Pattern（含 templates & components）。

## 4. Template 筛选
```
hiTemplates = templates  (slot ≥ target)
loTemplates = templates  (slot ≤ target)
if (hiTemplates) pool = hiTemplates else if (loTemplates) pool = loTemplates
```
然后 Alias 采样 1 个 Template。

## 5. Slot-Aware 填充
```
fillTemplateSlotAware(templateStr, components, targetSlots)
```
- 若 cultural JSON 提供 `alias_tables`，初始化阶段会缓存到 `patternAlias`，未来可 O(1) 直接采样（当前版本仍在填充函数内即时构建）。
- 占位符 `{key}` → 对应词表抽词，去重并按权重抽。

## 6. 智能补槽 (`enhanceToTargetSlots`)
若当前槽位 `< targetSlots`，按序补充：
1. `suffix` / `SUF`
2. `prefix` / `PREF`
3. 常用词池 (`星/云/风…`) + `link`（之/的/·）

## 7. 夸张槽处理
```
if fancyPrefix && targetSlots ≥ 4 && 未含夸张词 且 当前槽 < targetSlots
   在首部插入 emphasiseWords (超/无敌/究极…)
```
→ 夸张词只在"高复杂度"环境出现，且为独立槽位。

## 8. Post-Process
1. 连续词块或字去重 (`霞霞`→`霞`)
2. 去数字串 / 去 Emoji / 去 VS16
3. 随机在 ≥2 字块之间插 `之`
4. 裁剪槽位超标部分（优先移除后缀块）
5. 夸张词重复合并

## 9. 校验链
```
!isSensitive(finalName)            // 敏感词过滤
&& isSemanticValid(finalName)      // 至少 2 汉字、无数字、无缩写…
&& isStructureValid(finalName)     // 不以 link 词开/结、无连续 link
```
若失败进入 **重试循环**：最多 `maxRetry`（默认 3）次。重试会重新抽 Pattern / Template / 词汇；若全部失败，则返回保底名 `用户星云` 等。

## 10. 输出
返回最终用户名字符串；前端可用 `countSemanticSlots` 计算实际槽位，与滑块目标对比。

---

## 特点与当前局限
1. **离线运行**：纯前端 JSON，O(1) 采样，毫秒级生成。
2. **语法安全**：模板封装，代码不拼凑自由文本。
3. **文化/风格可切换**：`style` 过滤 + 多 JSON 分类。
4. **复杂度可控**：目标槽位 = 复杂度滑块，模板优先 + 智能补槽保证达标。
5. **夸张词条件触发**：仅在高复杂度场景出现，避免滥用。
6. **数据驱动**：新增语言 / 风格只需补 JSON，无须改代码。

待优化：
• 多槽「原生模板」仍偏少，复杂度主要靠补槽；需补充更多 4-6 槽模板。
• 槽位计数基于简单字符块，无法区分"仙剑" vs "清 风"。
• 权重未自适应用户反馈；后续接入点赞调权。

---

> 最后更新：2025-06-13 