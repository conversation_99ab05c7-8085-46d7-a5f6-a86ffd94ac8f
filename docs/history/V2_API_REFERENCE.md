# 📚 V2系统 API 参考文档

## 🎯 API 概述

V2系统提供了简洁而强大的API接口，支持多语言用户名生成、文化适配、质量控制等高级功能。

### 基础信息
- **版本**: V2.0.0
- **协议**: 函数调用 (非HTTP)
- **数据格式**: TypeScript接口
- **错误处理**: Promise-based异常处理

## 🚀 快速开始

### 安装和初始化

```typescript
import { createUsernameAPI, quickGenerate } from './core/v2/architecture/MainAPI'

// 快速生成用户名
const usernames = await quickGenerate('zh', 5, 'modern')
console.log(usernames) // ['星辰', '月影', '云梦', '风语', '雨韵']

// 创建API实例
const api = await createUsernameAPI()
const response = await api.generateUsernames({
  language: 'zh',
  style: 'modern',
  count: 3
})
```

## 📋 核心API

### 1. 主要生成接口

#### `generateUsernames(request: GenerateUsernameRequest): Promise<GenerateUsernameResponse>`

生成用户名的主要接口，支持丰富的参数配置。

**请求参数:**

```typescript
interface GenerateUsernameRequest {
  // 基础参数
  language?: 'zh' | 'en' | 'ja'           // 目标语言
  count?: number                          // 生成数量 (1-50)
  
  // 风格和偏好
  style?: 'traditional' | 'modern' | 'cute' | 'cool' | 'elegant' | 'playful' | 'mysterious' | 'powerful'
  themes?: string[]                       // 主题: ['nature', 'technology', 'emotion', ...]
  avoid_themes?: string[]                 // 避免的主题
  
  // 情感和语调
  sentiment?: 'positive' | 'neutral' | 'negative' | number  // 情感倾向 (-1到1)
  formality?: 'formal' | 'informal' | 'mixed' | number      // 正式程度 (0到1)
  creativity?: 'low' | 'medium' | 'high' | number           // 创意程度 (0到1)
  
  // 目标用户
  target_age?: 'child' | 'teen' | 'adult' | 'senior'       // 目标年龄群体
  
  // 约束条件
  min_length?: number                     // 最小长度
  max_length?: number                     // 最大长度
  avoid_words?: string[]                  // 避免的词汇
  require_unique?: boolean                // 是否要求唯一性
  
  // 高级选项
  generation_strategy?: 'balanced' | 'creative' | 'traditional' | 'fast'
  quality_threshold?: number              // 质量阈值 (0到1)
  enable_cache?: boolean                  // 是否启用缓存
  
  // 调试和分析
  debug?: boolean                         // 调试模式
  profile?: boolean                       // 性能分析
}
```

**响应格式:**

```typescript
interface GenerateUsernameResponse {
  success: boolean
  data?: {
    usernames: UsernameResult[]
    generation_info: GenerationInfo
    debug_info?: DebugInfo
    profile_info?: ProfileInfo
  }
  error?: {
    code: string
    message: string
    details?: any
  }
}

interface UsernameResult {
  username: string                        // 生成的用户名
  quality_score: number                   // 质量评分 (0-1)
  components: ComponentInfo[]             // 组成部分
  pattern_used: string                    // 使用的语法模式
  cultural_fitness: number                // 文化适配度
  explanation?: string                    // 生成解释
}
```

**使用示例:**

```typescript
// 基础使用
const response = await api.generateUsernames({
  language: 'zh',
  count: 5
})

// 高级配置
const response = await api.generateUsernames({
  language: 'zh',
  style: 'elegant',
  themes: ['nature', 'emotion'],
  sentiment: 'positive',
  formality: 'mixed',
  creativity: 'high',
  target_age: 'adult',
  min_length: 4,
  max_length: 8,
  count: 3,
  debug: true
})
```

### 2. 便捷函数

#### `quickGenerate(language, count, style): Promise<string[]>`

快速生成用户名的便捷函数。

```typescript
// 生成5个现代风格的中文用户名
const usernames = await quickGenerate('zh', 5, 'modern')

// 生成3个酷炫风格的英文用户名
const usernames = await quickGenerate('en', 3, 'cool')
```

#### `createUsernameAPI(config?): Promise<UsernameGeneratorAPI>`

创建API实例的便捷函数。

```typescript
const api = await createUsernameAPI({
  data_directory: './custom_data',
  performance: {
    max_generation_time: 3000,
    enable_profiling: true
  }
})
```

### 3. 系统管理接口

#### `getSystemStatus(): Promise<SystemStatusResponse>`

获取系统运行状态和性能指标。

```typescript
const status = await api.getSystemStatus()
console.log({
  status: status.status,                  // 'healthy' | 'degraded' | 'unhealthy'
  uptime: status.uptime,                  // 运行时间(ms)
  performance: status.performance,        // 性能指标
  resources: status.resources,            // 资源使用情况
  languages: status.languages            // 语言支持情况
})
```

#### `getPerformanceReport(): Promise<PerformanceReport>`

获取详细的性能分析报告。

```typescript
const report = await api.getPerformanceReport()
console.log({
  summary: report.summary,                // 性能摘要
  bottlenecks: report.bottlenecks,       // 性能瓶颈
  recommendations: report.recommendations // 优化建议
})
```

#### `clearCache(): Promise<void>`

清除系统缓存。

```typescript
await api.clearCache()
```

#### `reloadLanguageData(language): Promise<void>`

重新加载指定语言的数据。

```typescript
await api.reloadLanguageData('zh')
```

## 🎨 使用场景示例

### 场景1: 游戏用户名生成

```typescript
const gamingUsernames = await api.generateUsernames({
  language: 'zh',
  style: 'powerful',
  themes: ['fantasy', 'action'],
  sentiment: 'positive',
  creativity: 'high',
  target_age: 'teen',
  count: 10,
  generation_strategy: 'creative'
})
```

### 场景2: 专业平台用户名

```typescript
const professionalUsernames = await api.generateUsernames({
  language: 'en',
  style: 'elegant',
  themes: ['quality', 'culture'],
  formality: 'formal',
  creativity: 'low',
  target_age: 'adult',
  count: 5,
  generation_strategy: 'traditional'
})
```

### 场景3: 社交媒体用户名

```typescript
const socialUsernames = await api.generateUsernames({
  language: 'ja',
  style: 'cute',
  themes: ['nature', 'emotion'],
  sentiment: 0.8,
  creativity: 0.7,
  target_age: 'teen',
  min_length: 3,
  max_length: 6,
  count: 8,
  require_unique: true
})
```

### 场景4: 批量生成和质量控制

```typescript
const highQualityUsernames = await api.generateUsernames({
  language: 'zh',
  style: 'modern',
  themes: ['technology', 'innovation'],
  quality_threshold: 0.8,
  count: 20,
  debug: true
})

// 过滤高质量结果
const topUsernames = highQualityUsernames.data?.usernames
  .filter(u => u.quality_score > 0.9)
  .map(u => u.username)
```

## 🔧 配置选项

### 系统配置

```typescript
interface SystemConfig {
  data_directory: string                  // 数据目录路径
  cache: {
    max_size: number                      // 最大缓存条目数
    default_ttl: number                   // 默认TTL(毫秒)
    cleanup_interval: number              // 清理间隔
    enable_stats: boolean                 // 启用统计
  }
  performance: {
    max_generation_time: number           // 最大生成时间
    max_concurrent_generations: number    // 最大并发数
    enable_profiling: boolean             // 启用性能分析
  }
  logging: {
    level: 'debug' | 'info' | 'warn' | 'error'
    enable_file_logging: boolean
    log_directory: string
  }
  supported_languages: string[]           // 支持的语言列表
  defaults: {
    language: string
    style: string
    count: number
    creativity: number
  }
}
```

## ❌ 错误处理

### 错误代码

| 错误代码 | 描述 | 解决方案 |
|----------|------|----------|
| `INVALID_REQUEST` | 请求参数无效 | 检查参数格式和范围 |
| `RESOURCE_UNAVAILABLE` | 系统资源不足 | 稍后重试或减少并发 |
| `GENERATION_FAILED` | 生成过程失败 | 检查数据完整性 |
| `LANGUAGE_NOT_SUPPORTED` | 不支持的语言 | 使用支持的语言代码 |
| `DATA_LOADING_ERROR` | 数据加载失败 | 检查数据文件完整性 |

### 错误处理示例

```typescript
try {
  const response = await api.generateUsernames({
    language: 'zh',
    count: 5
  })
  
  if (response.success) {
    console.log('生成成功:', response.data?.usernames)
  } else {
    console.error('生成失败:', response.error?.message)
  }
} catch (error) {
  console.error('系统错误:', error)
}
```

## 📊 性能指标

### 基准性能

| 指标 | 目标值 | 说明 |
|------|--------|------|
| **生成时间** | < 100ms | 单次生成5个用户名 |
| **内存占用** | < 200MB | 系统峰值内存使用 |
| **并发支持** | 10+ | 同时处理的请求数 |
| **缓存命中率** | > 80% | 缓存有效性 |
| **成功率** | > 99% | 生成成功率 |

### 性能监控

```typescript
// 获取性能指标
const metrics = await api.getPerformanceReport()
console.log({
  avg_generation_time: metrics.summary.avg_generation_time,
  cache_efficiency: metrics.summary.cache_efficiency,
  memory_efficiency: metrics.summary.memory_efficiency,
  success_rate: metrics.summary.success_rate
})
```

## 🔄 版本兼容性

### V1 到 V2 迁移

```typescript
// V1 调用方式 (保持兼容)
import { generateUsername as v1Generate } from './core/v1/...'

// V2 调用方式
import { quickGenerate } from './core/v2/architecture/MainAPI'

// 迁移示例
const v1Result = await v1Generate({ language: 'zh', count: 5 })
const v2Result = await quickGenerate('zh', 5, 'modern')
```

### 向后兼容性

V2系统设计时考虑了向后兼容性，提供了适配层来支持V1的调用方式，确保平滑迁移。

---

**注**: 详细的实现示例请参考 `examples/complete_system_demo.ts`
