# 开发者集成指南

## 🚀 快速开始

### 1. 基础集成

```javascript
// 基础用户名生成
const response = await fetch('/api/v4-generate', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    language: 'zh',
    style: 'modern',
    themes: ['tech'],
    complexity: 3,
    count: 1
  })
});

const data = await response.json();
console.log('生成的用户名:', data.results[0].username);
```

### 2. 个性化集成

```javascript
// 带用户画像的个性化生成
async function generatePersonalizedUsername(userId) {
  // 1. 获取用户画像
  const profile = await fetch(`/api/user-profile/${userId}`);
  const userProfile = await profile.json();
  
  // 2. 获取推荐模式
  const recommendations = await fetch('/api/pattern-recommendation', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ user_id: userId, count: 3 })
  });
  const patterns = await recommendations.json();
  
  // 3. 使用推荐模式生成
  const generation = await fetch('/api/v4-generate', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      language: 'zh',
      style: userProfile.preferences.style,
      themes: userProfile.preferences.themes,
      complexity: userProfile.preferences.complexity,
      user_id: userId,
      strategy: patterns.recommendations[0].pattern_id
    })
  });
  
  return await generation.json();
}
```

## 📦 SDK和工具包

### JavaScript/TypeScript SDK

```typescript
interface GenerationRequest {
  language: 'zh' | 'en';
  style: 'modern' | 'cool' | 'playful' | 'traditional' | 'elegant';
  themes: string[];
  complexity: number;
  count?: number;
  user_id?: string;
  strategy?: string;
}

interface GenerationResult {
  username: string;
  pattern: string;
  formula: string;
  elements_used: string[];
  creativity_assessment: {
    novelty: number;
    relevance: number;
    comprehensibility: number;
    memorability: number;
    overall_score: number;
    explanation: string;
  };
  cultural_analysis: string[];
  target_audience: string[];
  generation_process: string;
}

class V4FirstPrinciplesClient {
  constructor(private baseUrl: string) {}
  
  async generate(request: GenerationRequest): Promise<GenerationResult[]> {
    const response = await fetch(`${this.baseUrl}/api/v4-generate`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(request)
    });
    
    if (!response.ok) {
      throw new Error(`Generation failed: ${response.statusText}`);
    }
    
    const data = await response.json();
    return data.results;
  }
  
  async getUserProfile(userId: string): Promise<UserProfile> {
    const response = await fetch(`${this.baseUrl}/api/user-profile/${userId}`);
    if (!response.ok) {
      throw new Error(`Profile fetch failed: ${response.statusText}`);
    }
    return await response.json();
  }
  
  async updateUserProfile(userId: string, updates: Partial<UserProfile>): Promise<void> {
    const response = await fetch(`${this.baseUrl}/api/user-profile/${userId}`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(updates)
    });
    
    if (!response.ok) {
      throw new Error(`Profile update failed: ${response.statusText}`);
    }
  }
  
  async submitFeedback(feedback: UserFeedback): Promise<void> {
    const response = await fetch(`${this.baseUrl}/api/feedback`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(feedback)
    });
    
    if (!response.ok) {
      throw new Error(`Feedback submission failed: ${response.statusText}`);
    }
  }
}

// 使用示例
const client = new V4FirstPrinciplesClient('https://api.namer.com');

const results = await client.generate({
  language: 'zh',
  style: 'modern',
  themes: ['tech', 'humor'],
  complexity: 3,
  user_id: 'user_123'
});

console.log('生成结果:', results[0].username);
```

### Python SDK

```python
import requests
from typing import List, Dict, Optional

class V4FirstPrinciplesClient:
    def __init__(self, base_url: str):
        self.base_url = base_url
    
    def generate(self, 
                language: str = 'zh',
                style: str = 'modern', 
                themes: List[str] = None,
                complexity: int = 3,
                count: int = 1,
                user_id: Optional[str] = None,
                strategy: Optional[str] = None) -> List[Dict]:
        
        payload = {
            'language': language,
            'style': style,
            'themes': themes or ['humor'],
            'complexity': complexity,
            'count': count
        }
        
        if user_id:
            payload['user_id'] = user_id
        if strategy:
            payload['strategy'] = strategy
            
        response = requests.post(
            f'{self.base_url}/api/v4-generate',
            json=payload
        )
        response.raise_for_status()
        
        return response.json()['results']
    
    def get_user_profile(self, user_id: str) -> Dict:
        response = requests.get(f'{self.base_url}/api/user-profile/{user_id}')
        response.raise_for_status()
        return response.json()
    
    def submit_feedback(self, user_id: str, username: str, 
                       rating: int, action: str) -> None:
        payload = {
            'user_id': user_id,
            'username': username,
            'rating': rating,
            'action': action
        }
        
        response = requests.post(
            f'{self.base_url}/api/feedback',
            json=payload
        )
        response.raise_for_status()

# 使用示例
client = V4FirstPrinciplesClient('https://api.namer.com')

results = client.generate(
    style='modern',
    themes=['tech', 'humor'],
    complexity=3,
    user_id='user_123'
)

print(f"生成的用户名: {results[0]['username']}")
```

## 🛠️ 错误处理指南

### 1. 重试策略

```javascript
class RetryableClient {
  constructor(baseUrl, maxRetries = 3) {
    this.baseUrl = baseUrl;
    this.maxRetries = maxRetries;
  }
  
  async generateWithRetry(request) {
    let lastError;
    
    for (let attempt = 0; attempt <= this.maxRetries; attempt++) {
      try {
        const response = await fetch(`${this.baseUrl}/api/v4-generate`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(request)
        });
        
        if (response.ok) {
          return await response.json();
        }
        
        // 如果是客户端错误(4xx)，不重试
        if (response.status >= 400 && response.status < 500) {
          throw new Error(`Client error: ${response.statusText}`);
        }
        
        lastError = new Error(`Server error: ${response.statusText}`);
        
      } catch (error) {
        lastError = error;
        
        if (attempt < this.maxRetries) {
          // 指数退避
          const delay = Math.pow(2, attempt) * 1000;
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }
    
    throw lastError;
  }
}
```

### 2. 降级策略

```javascript
class FallbackClient {
  constructor(primaryUrl, fallbackUrl) {
    this.primaryUrl = primaryUrl;
    this.fallbackUrl = fallbackUrl;
  }
  
  async generateWithFallback(request) {
    try {
      // 尝试个性化生成
      return await this.generatePersonalized(request);
    } catch (error) {
      console.warn('个性化生成失败，使用通用生成:', error.message);
      
      // 降级到通用生成
      const fallbackRequest = {
        ...request,
        user_id: undefined,  // 移除用户ID
        strategy: undefined  // 移除指定策略
      };
      
      return await this.generateGeneric(fallbackRequest);
    }
  }
  
  async generatePersonalized(request) {
    const response = await fetch(`${this.primaryUrl}/api/v4-generate`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(request)
    });
    
    if (!response.ok) {
      throw new Error(`Personalized generation failed: ${response.statusText}`);
    }
    
    return await response.json();
  }
  
  async generateGeneric(request) {
    const response = await fetch(`${this.fallbackUrl}/api/v4-generate`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(request)
    });
    
    if (!response.ok) {
      throw new Error(`Generic generation failed: ${response.statusText}`);
    }
    
    return await response.json();
  }
}
```

## 📈 性能优化建议

### 1. 缓存策略

```javascript
class CachedClient {
  constructor(baseUrl, cacheSize = 100) {
    this.baseUrl = baseUrl;
    this.cache = new Map();
    this.cacheSize = cacheSize;
  }
  
  getCacheKey(request) {
    return JSON.stringify(request);
  }
  
  async generate(request) {
    const cacheKey = this.getCacheKey(request);
    
    // 检查缓存
    if (this.cache.has(cacheKey)) {
      console.log('Cache hit');
      return this.cache.get(cacheKey);
    }
    
    // 生成新结果
    const result = await this.generateFromAPI(request);
    
    // 更新缓存
    if (this.cache.size >= this.cacheSize) {
      // 删除最旧的条目
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }
    
    this.cache.set(cacheKey, result);
    return result;
  }
  
  async generateFromAPI(request) {
    const response = await fetch(`${this.baseUrl}/api/v4-generate`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(request)
    });
    
    if (!response.ok) {
      throw new Error(`Generation failed: ${response.statusText}`);
    }
    
    return await response.json();
  }
}
```

### 2. 批量处理

```javascript
class BatchClient {
  constructor(baseUrl, batchSize = 5) {
    this.baseUrl = baseUrl;
    this.batchSize = batchSize;
    this.queue = [];
    this.processing = false;
  }
  
  async generate(request) {
    return new Promise((resolve, reject) => {
      this.queue.push({ request, resolve, reject });
      this.processBatch();
    });
  }
  
  async processBatch() {
    if (this.processing || this.queue.length === 0) {
      return;
    }
    
    this.processing = true;
    
    while (this.queue.length > 0) {
      const batch = this.queue.splice(0, this.batchSize);
      
      try {
        const promises = batch.map(item => 
          this.generateSingle(item.request)
            .then(result => item.resolve(result))
            .catch(error => item.reject(error))
        );
        
        await Promise.all(promises);
        
      } catch (error) {
        batch.forEach(item => item.reject(error));
      }
      
      // 批次间延迟
      if (this.queue.length > 0) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }
    
    this.processing = false;
  }
  
  async generateSingle(request) {
    const response = await fetch(`${this.baseUrl}/api/v4-generate`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(request)
    });
    
    if (!response.ok) {
      throw new Error(`Generation failed: ${response.statusText}`);
    }
    
    return await response.json();
  }
}
```

## 🧪 测试示例

### 单元测试

```javascript
// Jest测试示例
describe('V4FirstPrinciplesClient', () => {
  let client;
  
  beforeEach(() => {
    client = new V4FirstPrinciplesClient('http://localhost:3000');
  });
  
  test('should generate username successfully', async () => {
    const request = {
      language: 'zh',
      style: 'modern',
      themes: ['tech'],
      complexity: 3
    };
    
    const results = await client.generate(request);
    
    expect(results).toHaveLength(1);
    expect(results[0]).toHaveProperty('username');
    expect(results[0]).toHaveProperty('creativity_assessment');
    expect(results[0].creativity_assessment.overall_score).toBeGreaterThan(0);
  });
  
  test('should handle invalid parameters', async () => {
    const request = {
      language: 'invalid',
      style: 'invalid',
      themes: [],
      complexity: 10
    };
    
    await expect(client.generate(request)).rejects.toThrow();
  });
});
```

## 🔍 常见问题解答

### Q: 如何提高生成质量？
A: 
1. 提供准确的用户画像信息
2. 收集用户反馈数据
3. 使用个性化推荐模式
4. 适当调整复杂度参数

### Q: 如何处理生成失败？
A:
1. 实施重试机制
2. 使用降级策略
3. 检查参数有效性
4. 监控错误日志

### Q: 如何优化性能？
A:
1. 使用缓存策略
2. 实施批量处理
3. 减少不必要的API调用
4. 使用CDN加速

### Q: 如何集成用户反馈？
A:
1. 收集用户评分
2. 记录用户行为
3. 定期更新用户画像
4. 分析反馈趋势

---

*指南版本: V4.1-FirstPrinciples*  
*最后更新: 2025-06-14*  
*技术支持: 第一性原理引擎团队*
