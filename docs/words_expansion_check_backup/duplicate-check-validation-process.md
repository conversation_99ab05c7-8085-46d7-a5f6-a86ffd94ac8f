# 词汇去重验证流程和标准

## 📋 **去重验证概览**

**目标**: 确保新提供的词汇不与现有466个词汇重复  
**覆盖范围**: 原有156个 + 第一阶段260个 + 夸张修辞50个  
**检查方法**: 精确匹配 + 语义相似度 + 音近词检查  
**验证标准**: 100%无重复，95%语义差异，90%音韵差异

---

## 🔍 **三层检查机制**

### **第一层: 精确匹配检查**

#### **检查标准**
- **匹配方式**: 字符串完全相同
- **敏感度**: 100% (零容忍)
- **处理方式**: 自动拒绝
- **适用场景**: 防止完全相同的词汇

#### **检查算法**
```python
def exact_match_check(new_word, existing_words):
    """
    精确匹配检查
    """
    if new_word in existing_words:
        return {
            "status": "rejected",
            "reason": "完全重复",
            "conflict_word": new_word,
            "action": "自动拒绝"
        }
    return {"status": "passed"}
```

#### **现有词汇索引**
```json
{
  "total_words": 466,
  "word_index": [
    "原有156个词汇索引",
    "网络流行语120个词汇索引", 
    "文艺诗意80个词汇索引",
    "动物拟人60个词汇索引",
    "夸张修辞50个词汇索引"
  ]
}
```

### **第二层: 语义相似度检查**

#### **检查标准**
- **相似度阈值**: 85% (高度相似即拒绝)
- **分析维度**: 含义、用法、语境
- **处理方式**: 人工审核决定
- **适用场景**: 防止含义高度相似的词汇

#### **语义分析算法**
```python
def semantic_similarity_check(new_word, existing_words):
    """
    语义相似度检查
    """
    for existing_word in existing_words:
        similarity = calculate_semantic_similarity(new_word, existing_word)
        if similarity >= 0.85:
            return {
                "status": "warning",
                "reason": "语义高度相似",
                "similarity_score": similarity,
                "conflict_word": existing_word,
                "action": "需要人工审核"
            }
    return {"status": "passed"}
```

#### **语义相似度评估维度**
1. **含义相似度** (权重40%): 词汇基本含义的相似程度
2. **用法相似度** (权重30%): 使用场景和方式的相似程度
3. **语境相似度** (权重20%): 适用语境的相似程度
4. **情感相似度** (权重10%): 情感倾向的相似程度

#### **高风险相似词汇示例**
```json
{
  "high_risk_pairs": [
    {
      "word1": "史上最强",
      "word2": "历史最强", 
      "similarity": 0.92,
      "reason": "含义几乎相同"
    },
    {
      "word1": "宇宙级",
      "word2": "银河系级",
      "similarity": 0.88,
      "reason": "都表达超大规模"
    },
    {
      "word1": "小猫咪",
      "word2": "小猫",
      "similarity": 0.95,
      "reason": "基本相同，仅有语气差异"
    }
  ]
}
```

### **第三层: 音近词检查**

#### **检查标准**
- **音似度阈值**: 90% (高度音似需审核)
- **分析方法**: 拼音相似度分析
- **处理方式**: 人工审核决定
- **适用场景**: 防止读音相近造成混淆

#### **音近词检查算法**
```python
def phonetic_similarity_check(new_word, existing_words):
    """
    音近词检查
    """
    new_word_pinyin = get_pinyin(new_word)
    
    for existing_word in existing_words:
        existing_pinyin = get_pinyin(existing_word)
        similarity = calculate_pinyin_similarity(new_word_pinyin, existing_pinyin)
        
        if similarity >= 0.90:
            return {
                "status": "warning",
                "reason": "读音高度相似",
                "phonetic_similarity": similarity,
                "conflict_word": existing_word,
                "action": "需要人工审核"
            }
    return {"status": "passed"}
```

#### **音近词风险示例**
```json
{
  "phonetic_risks": [
    {
      "word1": "神级",
      "word2": "深级",
      "pinyin1": "shen ji",
      "pinyin2": "shen ji", 
      "similarity": 1.0,
      "risk": "完全同音"
    },
    {
      "word1": "王者",
      "word2": "往者",
      "pinyin1": "wang zhe",
      "pinyin2": "wang zhe",
      "similarity": 1.0,
      "risk": "完全同音"
    }
  ]
}
```

---

## 📝 **验证流程标准**

### **标准验证流程**

#### **步骤1: 格式预检查**
```
输入: 新词汇JSON数据
检查: 格式完整性、字段规范性
输出: 格式验证结果
时间: <1秒
```

#### **步骤2: 精确匹配检查**
```
输入: 格式验证通过的词汇
检查: 与466个现有词汇精确匹配
输出: 重复检查结果
时间: <1秒
```

#### **步骤3: 语义相似度分析**
```
输入: 精确匹配通过的词汇
检查: 语义相似度≥85%的词汇
输出: 相似度分析报告
时间: 2-5秒
```

#### **步骤4: 音近词检查**
```
输入: 语义检查通过的词汇
检查: 拼音相似度≥90%的词汇
输出: 音近词检查报告
时间: 1-3秒
```

#### **步骤5: 人工审核**
```
输入: 自动检查标记的疑似重复词汇
检查: 专业人员最终判断
输出: 人工审核决定
时间: 5-10分钟
```

#### **步骤6: 最终确认**
```
输入: 通过所有检查的词汇
处理: 添加到词库并更新索引
输出: 词库更新确认
时间: <1分钟
```

### **验证结果分类**

#### **自动通过** ✅
- 精确匹配: 无重复
- 语义相似度: <85%
- 音近词相似度: <90%
- 处理方式: 直接添加到词库

#### **需要审核** ⚠️
- 语义相似度: 85%-95%
- 音近词相似度: 90%-95%
- 处理方式: 人工审核决定

#### **自动拒绝** ❌
- 精确匹配: 完全重复
- 语义相似度: >95%
- 音近词相似度: >95%
- 处理方式: 自动拒绝并说明原因

---

## 🛠️ **外部协助使用指南**

### **提交前自检清单**

#### **基础检查**
- [ ] 词汇格式符合JSON标准
- [ ] 所有必需字段完整填写
- [ ] 词汇长度在2-8字符范围内
- [ ] 无特殊符号和敏感内容

#### **重复性自检**
- [ ] 在现有词汇清单中搜索相同词汇
- [ ] 检查是否有含义相近的词汇
- [ ] 确认无读音相同或相近的词汇
- [ ] 验证词汇的独特性和必要性

#### **质量自检**
- [ ] 词汇含义准确清晰
- [ ] 适合目标用户群体
- [ ] 具有良好的组合潜力
- [ ] 符合文化和时代特征

### **提交标准格式**

#### **单个词汇提交格式**
```json
{
  "submission_info": {
    "contributor": "提交者名称",
    "submission_date": "YYYY-MM-DD",
    "category": "词汇分类",
    "pre_check_completed": true
  },
  "word_data": {
    "word": "新词汇",
    "meaning": "详细含义",
    "popularity": 8.0,
    "age_group": "16-30",
    "sentiment": "positive",
    "usage_context": "使用场景描述",
    // 其他必需字段...
  },
  "duplicate_check_declaration": {
    "self_checked": true,
    "no_exact_match": true,
    "no_high_similarity": true,
    "unique_contribution": true
  }
}
```

#### **批量词汇提交格式**
```json
{
  "batch_submission": {
    "contributor": "提交者名称",
    "submission_date": "YYYY-MM-DD", 
    "total_words": 10,
    "category": "词汇分类",
    "batch_pre_check_completed": true
  },
  "words": [
    // 词汇数组，每个词汇包含完整信息
  ],
  "batch_duplicate_check": {
    "cross_check_completed": true,
    "internal_duplicates": 0,
    "external_duplicates": 0
  }
}
```

---

## 📊 **质量保证措施**

### **自动化工具支持**

#### **重复检查工具**
```bash
# 使用示例
python duplicate_checker.py --input new_words.json --database existing_words.json --output check_result.json
```

#### **批量验证工具**
```bash
# 批量验证
python batch_validator.py --batch batch_submission.json --strict-mode true
```

### **人工审核标准**

#### **审核员要求**
- 熟悉V5词库体系和质量标准
- 具备语言学或相关专业背景
- 了解网络语言和流行文化
- 具有良好的判断力和责任心

#### **审核决策标准**
1. **保留**: 独特性强，质量高，无明显重复
2. **修改**: 轻微重复，可通过调整保留
3. **拒绝**: 明显重复，质量不达标，不适合

### **质量监控机制**

#### **定期审查**
- **频率**: 每月一次
- **内容**: 新增词汇质量回顾
- **目标**: 发现和解决质量问题

#### **用户反馈**
- **渠道**: 用户反馈系统
- **处理**: 及时响应和处理
- **改进**: 基于反馈持续优化

---

## 🎯 **成功标准**

### **量化指标**
- **重复率**: <1% (466个词汇中重复<5个)
- **误检率**: <2% (正确词汇被误判为重复)
- **处理效率**: 平均处理时间<10分钟
- **质量保持**: 新词汇平均质量≥8.0/10

### **质量标准**
- **独特性**: 95%以上词汇具有独特性
- **实用性**: 90%以上词汇适合用户名生成
- **时效性**: 85%以上词汇具有良好时效性
- **文化适应性**: 95%以上词汇文化适应良好

**🔍 通过完善的三层检查机制和标准化流程，确保新词汇的独特性和质量，为V5词库扩展提供可靠的去重保障！**
