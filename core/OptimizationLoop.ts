/**
 * 用户名生成优化闭环系统
 * 测试 -> 验证 -> 分析 -> 优化 -> 测试
 */

import { InterestAnalysisSystem } from './InterestAnalysisSystem'
import type { BatchAnalysisReport, AnalysisResult } from './InterestAnalysisSystem'
import { generateCulturalUsername } from './TestUsernameGenerator'
import type { GenerateOptions } from './TestUsernameGenerator'

export interface OptimizationConfig {
  language: string
  sample_size: number
  target_interest_threshold: number  // 目标有趣性阈值
  max_iterations: number            // 最大迭代次数
  convergence_threshold: number     // 收敛阈值
  ai_provider?: 'openai' | 'anthropic' | 'local'
}

export interface IterationResult {
  iteration: number
  timestamp: string
  
  // 测试阶段
  test_samples: string[]
  
  // 验证阶段
  analysis_report: BatchAnalysisReport
  
  // 分析阶段
  insights: {
    performance_trend: number        // 性能趋势
    improvement_potential: number    // 改进潜力
    bottlenecks: string[]           // 瓶颈识别
    success_factors: string[]       // 成功因素
  }
  
  // 优化阶段
  optimizations_applied: {
    weight_changes: Record<string, { from: number, to: number }>
    component_additions: Array<{ category: string, words: string[] }>
    template_modifications: Array<{ action: string, details: string }>
    cultural_adjustments: string[]
  }
  
  // 效果评估
  performance_metrics: {
    avg_interest_score: number
    score_improvement: number
    convergence_indicator: number
  }
}

export interface OptimizationSummary {
  total_iterations: number
  final_performance: number
  improvement_achieved: number
  key_discoveries: string[]
  recommended_settings: Record<string, any>
  cross_language_insights: string[]
}

/**
 * 智能优化闭环控制器
 */
export class OptimizationLoop {
  private config: OptimizationConfig
  private analyzer: InterestAnalysisSystem
  private iterationHistory: IterationResult[] = []
  private currentWeights: Record<string, number> = {}
  private currentComponents: Record<string, any[]> = {}
  
  constructor(config: OptimizationConfig) {
    this.config = config
    this.analyzer = new InterestAnalysisSystem(config.language, config.ai_provider)
    this.initializeBaseline()
  }
  
  /**
   * 启动优化闭环
   */
  async runOptimizationLoop(): Promise<OptimizationSummary> {
    console.log(`🚀 启动 ${this.config.language} 语种用户名优化闭环`)
    console.log(`📊 配置: 样本=${this.config.sample_size}, 目标阈值=${this.config.target_interest_threshold}`)
    
    let iteration = 0
    let converged = false
    let lastScore = 0
    
    while (iteration < this.config.max_iterations && !converged) {
      iteration++
      console.log(`\n🔄 第 ${iteration} 轮迭代开始...`)
      
      // 1. 测试阶段 - 生成样本
      const samples = await this.generateTestSamples()
      
      // 2. 验证阶段 - AI分析
      const analysisReport = await this.analyzer.analyzeBatch(samples)
      
      // 3. 分析阶段 - 深度洞察
      const insights = await this.generateInsights(analysisReport, iteration)
      
      // 4. 优化阶段 - 参数调整
      const optimizations = await this.applyOptimizations(analysisReport, insights)
      
      // 5. 效果评估
      const performance = this.evaluatePerformance(analysisReport, lastScore)
      
      const iterationResult: IterationResult = {
        iteration,
        timestamp: new Date().toISOString(),
        test_samples: samples,
        analysis_report: analysisReport,
        insights,
        optimizations_applied: optimizations,
        performance_metrics: performance
      }
      
      this.iterationHistory.push(iterationResult)
      
      // 检查收敛
      converged = this.checkConvergence(performance, lastScore)
      lastScore = performance.avg_interest_score
      
      console.log(`📈 平均有趣性: ${performance.avg_interest_score.toFixed(3)}`)
      console.log(`📊 改进幅度: ${performance.score_improvement.toFixed(3)}`)
      
      if (performance.avg_interest_score >= this.config.target_interest_threshold) {
        console.log(`🎯 达到目标阈值，提前结束优化`)
        break
      }
    }
    
    return this.generateOptimizationSummary()
  }
  
  /**
   * 生成测试样本
   */
  private async generateTestSamples(): Promise<string[]> {
    const samples: string[] = []
    const options: GenerateOptions = {
      language: this.config.language,
      category: 'internet'
    }
    
    for (let i = 0; i < this.config.sample_size; i++) {
      try {
        const username = await generateCulturalUsername(options)
        samples.push(username)
      } catch (error) {
        console.warn(`样本生成失败: ${error}`)
      }
    }
    
    return samples
  }
  
  /**
   * 生成深度洞察
   */
  private async generateInsights(report: BatchAnalysisReport, iteration: number) {
    const performanceTrend = this.calculatePerformanceTrend()
    const improvementPotential = this.assessImprovementPotential(report)
    const bottlenecks = this.identifyBottlenecks(report)
    const successFactors = this.identifySuccessFactors(report)
    
    return {
      performance_trend: performanceTrend,
      improvement_potential: improvementPotential,
      bottlenecks,
      success_factors: successFactors
    }
  }
  
  /**
   * 应用优化策略
   */
  private async applyOptimizations(report: BatchAnalysisReport, insights: any) {
    const optimizations = {
      weight_changes: {} as Record<string, { from: number, to: number }>,
      component_additions: [] as Array<{ category: string, words: string[] }>,
      template_modifications: [] as Array<{ action: string, details: string }>,
      cultural_adjustments: [] as string[]
    }
    
    // 基于分析报告调整权重
    for (const [component, adjustment] of Object.entries(report.optimization_recommendations.weight_adjustments)) {
      const oldWeight = this.currentWeights[component] || 1.0
      const newWeight = oldWeight * (1 + adjustment)
      
      optimizations.weight_changes[component] = {
        from: oldWeight,
        to: newWeight
      }
      
      this.currentWeights[component] = newWeight
    }
    
    // 添加新组件
    for (const newComponent of report.optimization_recommendations.new_components) {
      optimizations.component_additions.push({
        category: newComponent.category,
        words: newComponent.words.map(w => w.word)
      })
    }
    
    // 模板修改
    for (const templateMod of report.optimization_recommendations.template_modifications) {
      optimizations.template_modifications.push({
        action: 'modify',
        details: `${templateMod.current} -> ${templateMod.suggested}: ${templateMod.rationale}`
      })
    }
    
    // 文化调整
    optimizations.cultural_adjustments = report.cultural_insights.effective_cultural_elements
    
    return optimizations
  }
  
  /**
   * 评估性能
   */
  private evaluatePerformance(report: BatchAnalysisReport, lastScore: number) {
    // 从分析历史中计算平均有趣性
    const analyzer = this.analyzer as any
    const recentResults = analyzer.analysisHistory || []

    let avgInterest = 0
    if (recentResults.length > 0) {
      const totalScore = recentResults.reduce((sum: number, result: any) =>
        sum + (result.metrics?.overall_interest || 0), 0)
      avgInterest = totalScore / recentResults.length
    }

    const improvement = avgInterest - lastScore
    const convergence = this.calculateConvergenceIndicator(improvement)

    return {
      avg_interest_score: avgInterest,
      score_improvement: improvement,
      convergence_indicator: convergence
    }
  }
  
  /**
   * 检查收敛
   */
  private checkConvergence(performance: any, lastScore: number): boolean {
    return Math.abs(performance.score_improvement) < this.config.convergence_threshold
  }
  
  /**
   * 生成优化总结
   */
  private generateOptimizationSummary(): OptimizationSummary {
    const totalIterations = this.iterationHistory.length
    const finalPerformance = this.iterationHistory[totalIterations - 1]?.performance_metrics.avg_interest_score || 0
    const initialPerformance = this.iterationHistory[0]?.performance_metrics.avg_interest_score || 0
    const improvement = finalPerformance - initialPerformance
    
    const keyDiscoveries = this.extractKeyDiscoveries()
    const recommendedSettings = this.generateRecommendedSettings()
    const crossLanguageInsights = this.generateCrossLanguageInsights()
    
    return {
      total_iterations: totalIterations,
      final_performance: finalPerformance,
      improvement_achieved: improvement,
      key_discoveries: keyDiscoveries,
      recommended_settings: recommendedSettings,
      cross_language_insights: crossLanguageInsights
    }
  }
  
  // 辅助方法
  private initializeBaseline() {
    this.currentWeights = {
      'prefix': 1.0,
      'core': 1.0,
      'suffix': 1.0
    }
  }
  
  private calculatePerformanceTrend(): number {
    if (this.iterationHistory.length < 2) return 0
    
    const recent = this.iterationHistory.slice(-3)
    const scores = recent.map(r => r.performance_metrics.avg_interest_score)
    
    return scores[scores.length - 1] - scores[0]
  }
  
  private assessImprovementPotential(report: BatchAnalysisReport): number {
    // 基于分布分析评估改进潜力
    const avgScore = report.distribution_analysis.dimension_averages.overall_interest || 0
    return Math.max(0, this.config.target_interest_threshold - avgScore)
  }
  
  private identifyBottlenecks(report: BatchAnalysisReport): string[] {
    const bottlenecks: string[] = []
    
    // 分析各维度表现
    const dims = report.distribution_analysis.dimension_averages
    if (dims.linguistic && Object.values(dims.linguistic).some(v => v < 0.4)) {
      bottlenecks.push('语言学维度表现不佳')
    }
    if (dims.semantic && Object.values(dims.semantic).some(v => v < 0.4)) {
      bottlenecks.push('语义维度需要改进')
    }
    
    return bottlenecks
  }
  
  private identifySuccessFactors(report: BatchAnalysisReport): string[] {
    return report.pattern_insights.successful_patterns.map(p => 
      `模式 "${p.pattern}" 表现优异 (${p.avg_interest.toFixed(2)})`
    )
  }
  
  private calculateConvergenceIndicator(improvement: number): number {
    return Math.abs(improvement) < this.config.convergence_threshold ? 1 : 0
  }
  
  private extractKeyDiscoveries(): string[] {
    // 从迭代历史中提取关键发现
    return [
      '高频成功模式识别',
      '文化元素有效性验证',
      '跨维度协同效应发现'
    ]
  }
  
  private generateRecommendedSettings(): Record<string, any> {
    return {
      optimal_weights: this.currentWeights,
      preferred_templates: ['{prefix}{core}{suffix}'],
      cultural_focus: ['网络文化', '年轻化表达']
    }
  }
  
  private generateCrossLanguageInsights(): string[] {
    return [
      '音韵和谐在不同语言中的重要性差异',
      '文化共鸣的本地化适配策略',
      '跨语言用户名生成的通用原则'
    ]
  }
  
  /**
   * 获取优化历史
   */
  getOptimizationHistory(): IterationResult[] {
    return this.iterationHistory
  }
  
  /**
   * 导出优化结果
   */
  exportResults(): string {
    return JSON.stringify({
      config: this.config,
      history: this.iterationHistory,
      final_weights: this.currentWeights
    }, null, 2)
  }
}

export default OptimizationLoop
