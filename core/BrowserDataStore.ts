/**
 * BrowserDataStore —— 浏览器兼容的数据中心
 * 负责在浏览器环境中加载文化数据、趋势数据、敏感词
 * 使用 fetch API 替代 Node.js 文件系统操作
 */

// ---- 类型声明 ----
export interface WordItem {
  word: string
  weight: number
  tags?: string[]
}

export interface PatternTemplate {
  template: string | string[]
  weight: number
}

export interface Pattern {
  name: string
  weight: number
  templates: PatternTemplate[]
  components: Record<string, WordItem[]>
}

export interface CulturalData {
  meta: {
    language: string
    category: string
    version: string
    updated_at: string
    checksum?: string
  }
  patterns: Pattern[]
  alias_tables?: Record<string, any>
}

export interface TrendingData {
  meta: {
    language: string
    updated_at: string
    version: string
  }
  trending_words: Array<{
    word: string
    weight: number
    category: string
    tags?: string[]
  }>
}

/**
 * 浏览器兼容的数据存储类
 */
class BrowserDataStore {
  private cultural: Map<string, Record<string, CulturalData>> = new Map()
  private trending: Map<string, TrendingData> = new Map()
  private sensitive: Map<string, Set<string>> = new Map()
  private initialized: Set<string> = new Set()
  private dataBaseUrl: string = '/data' // 相对于网站根目录的数据路径

  /**
   * 设置数据基础URL
   */
  setDataBaseUrl(url: string): void {
    this.dataBaseUrl = url
  }

  /**
   * 初始化指定语言的数据
   */
  async initLanguage(language: string): Promise<void> {
    if (this.initialized.has(language)) {
      return
    }

    try {
      // 并行加载文化数据、趋势数据和敏感词
      await Promise.all([
        this.loadCulturalData(language),
        this.loadTrendingData(language),
        this.loadSensitiveWords(language)
      ])

      this.initialized.add(language)
      console.log(`✅ Language ${language} initialized successfully`)
    } catch (error) {
      console.error(`❌ Failed to initialize language ${language}:`, error)
      throw error
    }
  }

  /**
   * 加载文化数据
   */
  private async loadCulturalData(language: string): Promise<void> {
    try {
      // 尝试加载预定义的文化类别
      const categories = ['internet', 'traditional', 'pop', 'pos', 'long']
      const cultural: Record<string, CulturalData> = {}

      for (const category of categories) {
        try {
          const url = `${this.dataBaseUrl}/cultural/${language}/${category}.json`
          const response = await fetch(url)
          
          if (response.ok) {
            const data = await response.json()
            cultural[category] = data
          } else {
            console.warn(`⚠️ Cultural data not found: ${url}`)
          }
        } catch (error) {
          console.warn(`⚠️ Failed to load cultural data for ${language}/${category}:`, error)
        }
      }

      // 尝试加载增强版本的数据
      try {
        const enhancedUrl = `${this.dataBaseUrl}/cultural/${language}/enhanced_internet.json`
        const response = await fetch(enhancedUrl)
        
        if (response.ok) {
          const data = await response.json()
          cultural['enhanced_internet'] = data
        }
      } catch (error) {
        console.warn(`⚠️ Enhanced cultural data not available for ${language}`)
      }

      this.cultural.set(language, cultural)
    } catch (error) {
      console.error(`❌ Failed to load cultural data for ${language}:`, error)
      // 设置空的文化数据以避免后续错误
      this.cultural.set(language, {})
    }
  }

  /**
   * 加载趋势数据
   */
  private async loadTrendingData(language: string): Promise<void> {
    try {
      const url = `${this.dataBaseUrl}/trends/current/${language}.json`
      const response = await fetch(url)
      
      if (response.ok) {
        const data = await response.json()
        this.trending.set(language, data)
      } else {
        console.warn(`⚠️ Trending data not found for ${language}`)
        // 设置空的趋势数据
        this.trending.set(language, {
          meta: {
            language,
            updated_at: new Date().toISOString(),
            version: '1.0.0'
          },
          trending_words: []
        })
      }
    } catch (error) {
      console.warn(`⚠️ Failed to load trending data for ${language}:`, error)
      // 设置空的趋势数据
      this.trending.set(language, {
        meta: {
          language,
          updated_at: new Date().toISOString(),
          version: '1.0.0'
        },
        trending_words: []
      })
    }
  }

  /**
   * 加载敏感词
   */
  private async loadSensitiveWords(language: string): Promise<void> {
    try {
      const url = `${this.dataBaseUrl}/sensitive/${language}.txt`
      const response = await fetch(url)
      
      if (response.ok) {
        const text = await response.text()
        const words = text.split('\n')
          .map(line => line.trim())
          .filter(line => line && !line.startsWith('#'))
        
        this.sensitive.set(language, new Set(words))
      } else {
        console.warn(`⚠️ Sensitive words not found for ${language}`)
        this.sensitive.set(language, new Set())
      }
    } catch (error) {
      console.warn(`⚠️ Failed to load sensitive words for ${language}:`, error)
      this.sensitive.set(language, new Set())
    }
  }

  /**
   * 获取文化数据
   */
  getCultural(language: string, category: string): CulturalData {
    const langData = this.cultural.get(language)
    if (!langData || !langData[category]) {
      throw new Error(`Cultural data not found for ${language}/${category}`)
    }
    return langData[category]
  }

  /**
   * 获取趋势数据
   */
  getTrending(language: string): TrendingData | undefined {
    return this.trending.get(language)
  }

  /**
   * 获取可用的文化类别
   */
  getCategories(language: string): string[] {
    const langData = this.cultural.get(language)
    return langData ? Object.keys(langData) : []
  }

  /**
   * 检查是否为敏感词
   */
  isSensitive(language: string, word: string): boolean {
    const sensitiveSet = this.sensitive.get(language)
    if (!sensitiveSet) return false
    
    // 检查完整匹配和部分匹配
    if (sensitiveSet.has(word.toLowerCase())) return true
    
    // 检查是否包含敏感词
    for (const sensitive of sensitiveSet) {
      if (word.toLowerCase().includes(sensitive)) {
        return true
      }
    }
    
    return false
  }

  /**
   * 获取数据统计信息
   */
  getStats(): Record<string, any> {
    const stats: Record<string, any> = {}
    
    for (const [language, cultural] of this.cultural.entries()) {
      stats[language] = {
        categories: Object.keys(cultural).length,
        patterns: Object.values(cultural).reduce((sum, data) => sum + data.patterns.length, 0),
        trending_words: this.trending.get(language)?.trending_words.length || 0,
        sensitive_words: this.sensitive.get(language)?.size || 0
      }
    }
    
    return stats
  }

  /**
   * 清理缓存
   */
  clearCache(): void {
    this.cultural.clear()
    this.trending.clear()
    this.sensitive.clear()
    this.initialized.clear()
  }

  /**
   * 预加载常用语言数据
   */
  async preloadCommonLanguages(): Promise<void> {
    const commonLanguages = ['zh', 'en', 'ja']
    
    await Promise.allSettled(
      commonLanguages.map(lang => this.initLanguage(lang))
    )
  }
}

// 创建全局实例
export const DataStore = new BrowserDataStore()

// 默认导出
export default DataStore
