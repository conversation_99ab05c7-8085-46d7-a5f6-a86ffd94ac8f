import type { 
  SlotLengthConfig, 
  EnhancedGrammarTemplate,
  EnhancedWordItem 
} from '../types/generator'

/**
 * 槽位长度控制器
 * 实现基于"语法要素个数"的精确长度控制
 */
export class SlotLengthController {
  private configs: Map<string, SlotLengthConfig> = new Map()

  constructor() {
    this.initializeConfigs()
  }

  /**
   * 初始化语言配置
   */
  private initializeConfigs(): void {
    // 中文配置 - 基于thinking.md的分析
    this.configs.set('zh', {
      language: 'zh',
      min_slots: 2,
      max_slots: 8,
      default_slots: 3,
      slot_weight_distribution: {
        2: 0.15,  // 简洁型：大神、战神
        3: 0.35,  // 经典型：超神大佬、王者归来
        4: 0.25,  // 丰富型：究极无敌大魔王
        5: 0.15,  // 复杂型：平平无奇摸鱼小天才
        6: 0.08,  // 超长型：想当鲸鱼的向日葵
        7: 0.02,  // 极长型：把星星藏在口袋里
        8: 0.01   // 最长型：有什么坏心思的小猫咪
      }
    })

    // 英文配置
    this.configs.set('en', {
      language: 'en',
      min_slots: 2,
      max_slots: 6,
      default_slots: 3,
      slot_weight_distribution: {
        2: 0.25,  // StarWalker, DreamChaser
        3: 0.40,  // MysticStarWalker, CosmicDreamChaser
        4: 0.25,  // TheMysticStarWalker
        5: 0.08,  // UltimateMysticStarWalker
        6: 0.02   // TheUltimateMysticStarWalker
      }
    })

    // 日文配置
    this.configs.set('ja', {
      language: 'ja',
      min_slots: 2,
      max_slots: 6,
      default_slots: 3,
      slot_weight_distribution: {
        2: 0.20,  // 星歌、月舞
        3: 0.35,  // 美しい星歌
        4: 0.30,  // 空の美しい歌
        5: 0.12,  // 夜空の美しい星歌
        6: 0.03   // 深夜の空の美しい歌
      }
    })
  }

  /**
   * 获取语言配置
   */
  getConfig(language: string): SlotLengthConfig {
    return this.configs.get(language) || this.configs.get('zh')!
  }

  /**
   * 根据用户输入的长度偏好计算目标槽位数
   */
  calculateTargetSlots(language: string, userLengthPreference?: number): number {
    const config = this.getConfig(language)
    
    if (userLengthPreference) {
      // 将用户的长度偏好映射到槽位数
      const normalizedLength = Math.max(config.min_slots, 
        Math.min(config.max_slots, userLengthPreference))
      return Math.round(normalizedLength)
    }

    // 根据权重分布随机选择槽位数
    return this.weightedRandomSlots(config)
  }

  /**
   * 根据权重分布随机选择槽位数
   */
  private weightedRandomSlots(config: SlotLengthConfig): number {
    const random = Math.random()
    let cumulative = 0

    for (const [slots, weight] of Object.entries(config.slot_weight_distribution)) {
      cumulative += weight
      if (random <= cumulative) {
        return parseInt(slots)
      }
    }

    return config.default_slots
  }

  /**
   * 过滤符合槽位数要求的模板
   */
  filterTemplatesBySlots(
    templates: EnhancedGrammarTemplate[], 
    targetSlots: number,
    tolerance: number = 1
  ): EnhancedGrammarTemplate[] {
    return templates.filter(template => {
      const templateSlots = this.calculateTemplateSlots(template)
      return Math.abs(templateSlots - targetSlots) <= tolerance
    })
  }

  /**
   * 计算模板的槽位数
   */
  calculateTemplateSlots(template: EnhancedGrammarTemplate): number {
    // 过滤掉LINK类型的槽位，它们不计入主要槽位数
    return template.pattern.filter(slot => {
      // 检查是否包含 LINK 标识
      return !slot.includes('LINK')
    }).length
  }

  /**
   * 估算模板生成结果的最小长度
   */
  estimateMinLength(
    template: EnhancedGrammarTemplate, 
    components: Record<string, EnhancedWordItem[]>
  ): number {
    let totalLength = 0

    for (const slotPattern of template.pattern) {
      const [slotName] = slotPattern.split('#')
      const slotComponents = components[slotName] || []
      
      if (slotComponents.length > 0) {
        // 找到最短的词汇长度
        const minWordLength = Math.min(...slotComponents.map(item => item.word.length))
        totalLength += minWordLength
      }
    }

    return totalLength
  }

  /**
   * 估算模板生成结果的最大长度
   */
  estimateMaxLength(
    template: EnhancedGrammarTemplate, 
    components: Record<string, EnhancedWordItem[]>
  ): number {
    let totalLength = 0

    for (const slotPattern of template.pattern) {
      const [slotName] = slotPattern.split('#')
      const slotComponents = components[slotName] || []
      
      if (slotComponents.length > 0) {
        // 找到最长的词汇长度
        const maxWordLength = Math.max(...slotComponents.map(item => item.word.length))
        totalLength += maxWordLength
      }
    }

    return totalLength
  }

  /**
   * 根据目标字符长度过滤模板
   */
  filterTemplatesByCharLength(
    templates: EnhancedGrammarTemplate[],
    components: Record<string, EnhancedWordItem[]>,
    minCharLength: number,
    maxCharLength: number
  ): EnhancedGrammarTemplate[] {
    return templates.filter(template => {
      const minLength = this.estimateMinLength(template, components)
      const maxLength = this.estimateMaxLength(template, components)
      
      // 模板的长度范围与目标范围有重叠
      return !(maxLength < minCharLength || minLength > maxCharLength)
    })
  }

  /**
   * 智能长度控制：同时考虑槽位数和字符长度
   */
  smartLengthFilter(
    templates: EnhancedGrammarTemplate[],
    components: Record<string, EnhancedWordItem[]>,
    options: {
      targetSlots?: number
      minCharLength?: number
      maxCharLength?: number
      slotTolerance?: number
      prioritizeSlots?: boolean  // 是否优先考虑槽位数
    }
  ): EnhancedGrammarTemplate[] {
    let filtered = templates

    // 槽位数过滤
    if (options.targetSlots !== undefined) {
      filtered = this.filterTemplatesBySlots(
        filtered, 
        options.targetSlots, 
        options.slotTolerance || 1
      )
    }

    // 字符长度过滤
    if (options.minCharLength !== undefined || options.maxCharLength !== undefined) {
      const minLength = options.minCharLength || 0
      const maxLength = options.maxCharLength || Infinity
      
      filtered = this.filterTemplatesByCharLength(
        filtered,
        components,
        minLength,
        maxLength
      )
    }

    // 如果过滤后没有结果，根据优先级放宽条件
    if (filtered.length === 0) {
      if (options.prioritizeSlots && options.targetSlots !== undefined) {
        // 优先保证槽位数，放宽字符长度限制
        filtered = this.filterTemplatesBySlots(
          templates, 
          options.targetSlots, 
          (options.slotTolerance || 1) + 1
        )
      } else if (options.minCharLength !== undefined || options.maxCharLength !== undefined) {
        // 优先保证字符长度，放宽槽位数限制
        const minLength = options.minCharLength || 0
        const maxLength = options.maxCharLength || Infinity
        filtered = this.filterTemplatesByCharLength(
          templates,
          components,
          minLength,
          maxLength
        )
      }
    }

    return filtered
  }

  /**
   * 获取槽位数分布统计
   */
  getSlotDistributionStats(language: string): Record<number, number> {
    const config = this.getConfig(language)
    return { ...config.slot_weight_distribution }
  }

  /**
   * 更新槽位数权重分布（用于A/B测试和优化）
   */
  updateSlotDistribution(language: string, newDistribution: Record<number, number>): void {
    const config = this.getConfig(language)
    if (config) {
      // 验证权重总和为1
      const totalWeight = Object.values(newDistribution).reduce((sum, weight) => sum + weight, 0)
      if (Math.abs(totalWeight - 1.0) > 0.01) {
        throw new Error('Slot weight distribution must sum to 1.0')
      }
      
      config.slot_weight_distribution = { ...newDistribution }
    }
  }

  /**
   * 分析模板集合的槽位数分布
   */
  analyzeTemplateSlotDistribution(templates: EnhancedGrammarTemplate[]): Record<number, number> {
    const slotCounts: Record<number, number> = {}
    
    templates.forEach(template => {
      const slots = this.calculateTemplateSlots(template)
      slotCounts[slots] = (slotCounts[slots] || 0) + 1
    })

    // 转换为比例
    const total = templates.length
    const distribution: Record<number, number> = {}
    Object.entries(slotCounts).forEach(([slots, count]) => {
      distribution[parseInt(slots)] = count / total
    })

    return distribution
  }

  /**
   * 推荐最佳槽位数（基于用户历史偏好）
   */
  recommendOptimalSlots(
    language: string, 
    userHistory?: Array<{ slots: number; feedback: 'positive' | 'negative' }>
  ): number {
    const config = this.getConfig(language)
    
    if (!userHistory || userHistory.length === 0) {
      return config.default_slots
    }

    // 分析用户历史偏好
    const positiveSlots = userHistory
      .filter(h => h.feedback === 'positive')
      .map(h => h.slots)

    if (positiveSlots.length === 0) {
      return config.default_slots
    }

    // 计算平均偏好槽位数
    const avgSlots = positiveSlots.reduce((sum, slots) => sum + slots, 0) / positiveSlots.length
    
    // 限制在有效范围内
    return Math.max(config.min_slots, Math.min(config.max_slots, Math.round(avgSlots)))
  }
}
