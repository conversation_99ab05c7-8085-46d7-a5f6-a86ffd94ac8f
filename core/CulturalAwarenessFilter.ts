import type { 
  CulturalTag, 
  SemanticTag, 
  Enhanced<PERSON>ordItem,
  CulturalFilterConfig,
  Tone
} from '../types/generator'

/**
 * 文化感知过滤器
 * 实现多层次的文化适配和语义冲突检测
 */
export class CulturalAwarenessFilter {
  private config: CulturalFilterConfig
  private culturalCompatibilityMatrix: Map<string, Map<string, number>> = new Map()
  private semanticConflictRules: Array<{
    conflicting_tags: SemanticTag[]
    severity: 'warning' | 'error'
    message: string
  }> = []

  constructor(config?: Partial<CulturalFilterConfig>) {
    this.config = this.mergeWithDefaultConfig(config || {})
    this.initializeCompatibilityMatrix()
    this.initializeSemanticRules()
  }

  /**
   * 合并默认配置
   */
  private mergeWithDefaultConfig(userConfig: Partial<CulturalFilterConfig>): CulturalFilterConfig {
    const defaultConfig: CulturalFilterConfig = {
      cultural_compatibility_matrix: {},
      semantic_conflict_rules: [],
      temporal_context_rules: {
        'traditional': {
          preferred_eras: ['ancient'],
          avoided_eras: ['future']
        },
        'internet': {
          preferred_eras: ['modern', 'future'],
          avoided_eras: ['ancient']
        },
        'cyberpunk': {
          preferred_eras: ['future'],
          avoided_eras: ['ancient']
        }
      }
    }

    return {
      cultural_compatibility_matrix: { 
        ...defaultConfig.cultural_compatibility_matrix, 
        ...userConfig.cultural_compatibility_matrix 
      },
      semantic_conflict_rules: [
        ...defaultConfig.semantic_conflict_rules,
        ...(userConfig.semantic_conflict_rules || [])
      ],
      temporal_context_rules: {
        ...defaultConfig.temporal_context_rules,
        ...userConfig.temporal_context_rules
      }
    }
  }

  /**
   * 初始化文化兼容性矩阵
   */
  private initializeCompatibilityMatrix(): void {
    // 文化标签之间的兼容性评分 (0-1, 1表示完全兼容)
    const compatibilityData = {
      '传统': {
        '传统': 1.0,
        '网络': 0.3,
        '二次元': 0.2,
        '流行': 0.4,
        '学术': 0.8,
        '搞怪': 0.1
      },
      '网络': {
        '传统': 0.3,
        '网络': 1.0,
        '二次元': 0.8,
        '流行': 0.9,
        '学术': 0.4,
        '搞怪': 0.9
      },
      '二次元': {
        '传统': 0.2,
        '网络': 0.8,
        '二次元': 1.0,
        '流行': 0.7,
        '学术': 0.2,
        '搞怪': 0.8
      },
      '流行': {
        '传统': 0.4,
        '网络': 0.9,
        '二次元': 0.7,
        '流行': 1.0,
        '学术': 0.3,
        '搞怪': 0.8
      },
      '学术': {
        '传统': 0.8,
        '网络': 0.4,
        '二次元': 0.2,
        '流行': 0.3,
        '学术': 1.0,
        '搞怪': 0.1
      },
      '搞怪': {
        '传统': 0.1,
        '网络': 0.9,
        '二次元': 0.8,
        '流行': 0.8,
        '学术': 0.1,
        '搞怪': 1.0
      }
    }

    // 转换为Map结构
    Object.entries(compatibilityData).forEach(([tag1, compatibilities]) => {
      const innerMap = new Map<string, number>()
      Object.entries(compatibilities).forEach(([tag2, score]) => {
        innerMap.set(tag2, score)
      })
      this.culturalCompatibilityMatrix.set(tag1, innerMap)
    })
  }

  /**
   * 初始化语义冲突规则
   */
  private initializeSemanticRules(): void {
    this.semanticConflictRules = [
      {
        conflicting_tags: ['自然', '科技'],
        severity: 'warning',
        message: '自然元素与科技元素可能产生风格冲突'
      },
      {
        conflicting_tags: ['自然', '科技'],
        severity: 'warning',
        message: '传统自然元素与未来科技风格存在时代冲突'
      },
      {
        conflicting_tags: ['神秘', '科技'],
        severity: 'warning',
        message: '神秘元素与科技元素的组合需要谨慎处理'
      },
      {
        conflicting_tags: ['情感', '力量'],
        severity: 'warning',
        message: '情感类与力量类标签组合时注意平衡'
      }
    ]
  }

  /**
   * 检查文化兼容性
   */
  checkCulturalCompatibility(culturalTags: CulturalTag[]): {
    compatible: boolean
    score: number
    conflicts: Array<{ tags: CulturalTag[]; severity: string; message: string }>
  } {
    if (culturalTags.length <= 1) {
      return { compatible: true, score: 1.0, conflicts: [] }
    }

    let totalScore = 0
    let pairCount = 0
    const conflicts: Array<{ tags: CulturalTag[]; severity: string; message: string }> = []

    // 检查所有标签对的兼容性
    for (let i = 0; i < culturalTags.length; i++) {
      for (let j = i + 1; j < culturalTags.length; j++) {
        const tag1 = culturalTags[i]
        const tag2 = culturalTags[j]
        
        const compatibility = this.getCulturalCompatibility(tag1, tag2)
        totalScore += compatibility
        pairCount++

        // 检查是否存在严重冲突
        if (compatibility < 0.3) {
          conflicts.push({
            tags: [tag1, tag2],
            severity: 'error',
            message: `文化标签"${tag1}"与"${tag2}"存在严重冲突`
          })
        } else if (compatibility < 0.5) {
          conflicts.push({
            tags: [tag1, tag2],
            severity: 'warning',
            message: `文化标签"${tag1}"与"${tag2}"兼容性较低`
          })
        }
      }
    }

    const averageScore = pairCount > 0 ? totalScore / pairCount : 1.0
    const compatible = averageScore >= 0.5 && conflicts.filter(c => c.severity === 'error').length === 0

    return {
      compatible,
      score: averageScore,
      conflicts
    }
  }

  /**
   * 获取两个文化标签的兼容性分数
   */
  private getCulturalCompatibility(tag1: CulturalTag, tag2: CulturalTag): number {
    const matrix1 = this.culturalCompatibilityMatrix.get(tag1)
    if (matrix1) {
      return matrix1.get(tag2) || 0.5
    }
    return 0.5 // 默认中等兼容性
  }

  /**
   * 检查语义冲突
   */
  checkSemanticConflicts(semanticTags: SemanticTag[]): {
    hasConflicts: boolean
    conflicts: Array<{ tags: SemanticTag[]; severity: string; message: string }>
  } {
    const conflicts: Array<{ tags: SemanticTag[]; severity: string; message: string }> = []

    for (const rule of this.semanticConflictRules) {
      const matchingTags = semanticTags.filter(tag => rule.conflicting_tags.includes(tag))
      
      if (matchingTags.length >= 2) {
        conflicts.push({
          tags: matchingTags,
          severity: rule.severity,
          message: rule.message
        })
      }
    }

    return {
      hasConflicts: conflicts.some(c => c.severity === 'error'),
      conflicts
    }
  }

  /**
   * 检查时代背景匹配
   */
  checkTemporalContext(
    culturalTags: CulturalTag[], 
    era: 'ancient' | 'modern' | 'future'
  ): {
    compatible: boolean
    score: number
    issues: string[]
  } {
    const issues: string[] = []
    let compatibilityScore = 1.0

    for (const tag of culturalTags) {
      const rules = this.config.temporal_context_rules[tag]
      if (rules) {
        if (rules.avoided_eras.includes(era)) {
          issues.push(`文化标签"${tag}"与时代"${era}"不匹配`)
          compatibilityScore *= 0.5
        } else if (!rules.preferred_eras.includes(era)) {
          compatibilityScore *= 0.8
        }
      }
    }

    return {
      compatible: compatibilityScore >= 0.6,
      score: compatibilityScore,
      issues
    }
  }

  /**
   * 过滤词汇组合
   */
  filterWordCombination(words: EnhancedWordItem[]): {
    filtered: EnhancedWordItem[]
    removed: Array<{ word: EnhancedWordItem; reason: string }>
  } {
    const filtered: EnhancedWordItem[] = []
    const removed: Array<{ word: EnhancedWordItem; reason: string }> = []

    // 提取所有文化和语义标签
    const allCulturalTags = new Set<CulturalTag>()
    const allSemanticTags = new Set<SemanticTag>()
    
    words.forEach(word => {
      word.cultural_tags.forEach(tag => allCulturalTags.add(tag))
      word.semantic_tags.forEach(tag => allSemanticTags.add(tag))
    })

    // 检查整体兼容性
    const culturalCheck = this.checkCulturalCompatibility(Array.from(allCulturalTags))
    const semanticCheck = this.checkSemanticConflicts(Array.from(allSemanticTags))

    // 如果存在严重冲突，移除冲突词汇
    if (!culturalCheck.compatible || semanticCheck.hasConflicts) {
      const conflictingCulturalTags = new Set<CulturalTag>()
      const conflictingSemanticTags = new Set<SemanticTag>()

      // 收集冲突标签
      culturalCheck.conflicts
        .filter(c => c.severity === 'error')
        .forEach(c => c.tags.forEach(tag => conflictingCulturalTags.add(tag)))

      semanticCheck.conflicts
        .filter(c => c.severity === 'error')
        .forEach(c => c.tags.forEach(tag => conflictingSemanticTags.add(tag)))

      // 过滤词汇
      for (const word of words) {
        const hasCulturalConflict = word.cultural_tags.some(tag => 
          conflictingCulturalTags.has(tag)
        )
        const hasSemanticConflict = word.semantic_tags.some(tag => 
          conflictingSemanticTags.has(tag)
        )

        if (hasCulturalConflict) {
          removed.push({ word, reason: '文化标签冲突' })
        } else if (hasSemanticConflict) {
          removed.push({ word, reason: '语义标签冲突' })
        } else {
          filtered.push(word)
        }
      }
    } else {
      // 没有严重冲突，保留所有词汇
      filtered.push(...words)
    }

    return { filtered, removed }
  }

  /**
   * 计算词汇组合的文化一致性分数
   */
  calculateCulturalConsistencyScore(words: EnhancedWordItem[]): number {
    if (words.length <= 1) return 1.0

    const culturalTags = words.flatMap(word => word.cultural_tags)
    const semanticTags = words.flatMap(word => word.semantic_tags)

    // 文化兼容性分数
    const culturalCheck = this.checkCulturalCompatibility(culturalTags)
    
    // 语义一致性分数
    const semanticCheck = this.checkSemanticConflicts(semanticTags)
    const semanticScore = semanticCheck.hasConflicts ? 0.3 : 0.8

    // 语调一致性分数
    const tones = words.map(word => word.tone)
    const uniqueTones = new Set(tones)
    const toneScore = uniqueTones.size <= 2 ? 1.0 : 0.6

    // 综合分数
    return (culturalCheck.score * 0.5 + semanticScore * 0.3 + toneScore * 0.2)
  }

  /**
   * 推荐文化标签组合
   */
  recommendCulturalCombination(
    availableTags: CulturalTag[], 
    targetCount: number = 2
  ): CulturalTag[] {
    if (availableTags.length <= targetCount) {
      return availableTags
    }

    const combinations: Array<{ tags: CulturalTag[]; score: number }> = []

    // 生成所有可能的组合
    this.generateCombinations(availableTags, targetCount).forEach(combination => {
      const check = this.checkCulturalCompatibility(combination)
      combinations.push({ tags: combination, score: check.score })
    })

    // 按分数排序并返回最佳组合
    combinations.sort((a, b) => b.score - a.score)
    return combinations[0]?.tags || availableTags.slice(0, targetCount)
  }

  /**
   * 生成组合
   */
  private generateCombinations<T>(arr: T[], size: number): T[][] {
    if (size === 1) return arr.map(item => [item])
    if (size === arr.length) return [arr]
    if (size > arr.length) return []

    const result: T[][] = []
    
    for (let i = 0; i <= arr.length - size; i++) {
      const head = arr[i]
      const tailCombinations = this.generateCombinations(arr.slice(i + 1), size - 1)
      tailCombinations.forEach(tail => {
        result.push([head, ...tail])
      })
    }

    return result
  }

  /**
   * 更新文化兼容性矩阵（用于学习优化）
   */
  updateCompatibilityMatrix(
    tag1: CulturalTag, 
    tag2: CulturalTag, 
    newScore: number
  ): void {
    // 更新双向兼容性
    let matrix1 = this.culturalCompatibilityMatrix.get(tag1)
    if (!matrix1) {
      matrix1 = new Map()
      this.culturalCompatibilityMatrix.set(tag1, matrix1)
    }
    matrix1.set(tag2, newScore)

    let matrix2 = this.culturalCompatibilityMatrix.get(tag2)
    if (!matrix2) {
      matrix2 = new Map()
      this.culturalCompatibilityMatrix.set(tag2, matrix2)
    }
    matrix2.set(tag1, newScore)
  }

  /**
   * 获取文化过滤统计信息
   */
  getFilterStats(): {
    totalCulturalTags: number
    compatibilityMatrix: Record<string, Record<string, number>>
    conflictRules: number
  } {
    const matrixObj: Record<string, Record<string, number>> = {}
    
    this.culturalCompatibilityMatrix.forEach((innerMap, tag1) => {
      matrixObj[tag1] = {}
      innerMap.forEach((score, tag2) => {
        matrixObj[tag1][tag2] = score
      })
    })

    return {
      totalCulturalTags: this.culturalCompatibilityMatrix.size,
      compatibilityMatrix: matrixObj,
      conflictRules: this.semanticConflictRules.length
    }
  }
}
