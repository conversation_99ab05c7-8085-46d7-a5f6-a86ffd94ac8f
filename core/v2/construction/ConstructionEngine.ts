/**
 * 构式模板引擎
 * 
 * 基于构式语法理论的模板引擎，负责构式的选择、匹配和实例化
 */

import {
  ConstructionTemplate,
  ConstructionSlot,
  ConstructionMatch,
  ConstructionSelectionStrategy,
  ConstructionGenerationParams,
  SemanticFrame,
  SlotConstraint,
  PragmaticFunction
} from '../types/ConstructionTypes'

import {
  SemanticLexicalEntry,
  GenerationContext,
  QualityMetrics
} from '../types/SemanticTypes'

import { SemanticLexicon } from '../lexicon/SemanticLexicon'

/** 构式实例化结果 */
export interface ConstructionInstance {
  construction: ConstructionTemplate
  slot_fillers: { [slot_id: string]: SemanticLexicalEntry }
  generated_text: string
  quality_metrics: QualityMetrics
  constraint_violations: { constraint: SlotConstraint; severity: number }[]
}

/** 构式引擎类 */
export class ConstructionEngine {
  private constructions: Map<string, ConstructionTemplate> = new Map()
  private lexicon: SemanticLexicon
  
  // 缓存
  private matchCache: Map<string, ConstructionMatch[]> = new Map()
  private instanceCache: Map<string, ConstructionInstance[]> = new Map()
  
  constructor(lexicon: SemanticLexicon) {
    this.lexicon = lexicon
  }
  
  // ============ 构式管理 ============
  
  /** 添加构式模板 */
  addConstruction(construction: ConstructionTemplate): void {
    this.constructions.set(construction.id, construction)
    this.clearCache() // 清除缓存
  }
  
  /** 批量添加构式模板 */
  addConstructions(constructions: ConstructionTemplate[]): void {
    constructions.forEach(c => this.constructions.set(c.id, c))
    this.clearCache()
  }
  
  /** 获取构式模板 */
  getConstruction(id: string): ConstructionTemplate | undefined {
    return this.constructions.get(id)
  }
  
  /** 获取所有构式 */
  getAllConstructions(): ConstructionTemplate[] {
    return Array.from(this.constructions.values())
  }
  
  /** 清除缓存 */
  private clearCache(): void {
    this.matchCache.clear()
    this.instanceCache.clear()
  }
  
  // ============ 构式匹配 ============
  
  /** 匹配合适的构式 */
  matchConstructions(
    context: GenerationContext,
    params: ConstructionGenerationParams
  ): ConstructionMatch[] {
    const cacheKey = this.generateCacheKey(context, params)
    
    if (this.matchCache.has(cacheKey)) {
      return this.matchCache.get(cacheKey)!
    }
    
    let candidates = this.getAllConstructions()
    
    // 过滤构式
    candidates = this.filterConstructions(candidates, context, params)
    
    // 计算匹配分数
    const matches = candidates.map(construction => 
      this.calculateConstructionMatch(construction, context, params)
    ).filter(match => match.match_score > 0)
    
    // 排序
    matches.sort((a, b) => b.match_score - a.match_score)
    
    this.matchCache.set(cacheKey, matches)
    return matches
  }
  
  /** 过滤构式 */
  private filterConstructions(
    constructions: ConstructionTemplate[],
    context: GenerationContext,
    params: ConstructionGenerationParams
  ): ConstructionTemplate[] {
    return constructions.filter(construction => {
      // 语言匹配
      if (construction.language !== context.target_language) {
        return false
      }
      
      // 指定构式过滤
      if (params.target_constructions && 
          !params.target_constructions.includes(construction.id)) {
        return false
      }
      
      // 排除构式过滤
      if (params.excluded_constructions && 
          params.excluded_constructions.includes(construction.id)) {
        return false
      }
      
      // 构式类别过滤
      if (params.construction_categories && 
          !params.construction_categories.includes(construction.category)) {
        return false
      }
      
      // 语域兼容性检查
      const userFormality = context.user_preferences.formality_level || 0.5
      const constructionFormality = this.estimateConstructionFormality(construction)
      const formalityDiff = Math.abs(userFormality - constructionFormality)
      if (formalityDiff > 0.5) { // 正式程度差异过大
        return false
      }
      
      return true
    })
  }
  
  /** 计算构式匹配度 */
  private calculateConstructionMatch(
    construction: ConstructionTemplate,
    context: GenerationContext,
    params: ConstructionGenerationParams
  ): ConstructionMatch {
    let matchScore = construction.base_weight
    
    // 文化适应性评分
    const culturalScore = this.calculateCulturalFit(construction, context)
    matchScore *= culturalScore
    
    // 语义适应性评分
    const semanticScore = this.calculateSemanticFit(construction, params)
    matchScore *= semanticScore
    
    // 语用功能匹配
    const pragmaticScore = this.calculatePragmaticFit(construction, context)
    matchScore *= pragmaticScore
    
    // 为每个槽位寻找候选词汇
    const slotAssignments: { [slot_id: string]: string[] } = {}
    const constraintViolations: { constraint: SlotConstraint; severity: number; explanation: string }[] = []
    
    construction.structure.forEach(slot => {
      const candidates = this.findSlotCandidates(slot, context, params)
      slotAssignments[slot.id] = candidates.map(c => c.word)
      
      // 检查约束违反
      if (candidates.length === 0) {
        constraintViolations.push({
          constraint: { type: 'semantic', constraint: 'no_candidates', strength: 1, violation_penalty: 0.5 },
          severity: 1,
          explanation: `No candidates found for slot ${slot.id}`
        })
        matchScore *= 0.5 // 严重惩罚
      }
    })
    
    // 估算质量
    const estimatedQuality = this.estimateConstructionQuality(construction, slotAssignments)
    
    return {
      construction,
      match_score: Math.max(0, Math.min(1, matchScore)),
      slot_assignments: slotAssignments,
      constraint_violations: constraintViolations,
      estimated_quality: estimatedQuality
    }
  }
  
  /** 计算文化适应性 */
  private calculateCulturalFit(construction: ConstructionTemplate, context: GenerationContext): number {
    const culturalModifier = construction.cultural_weight_modifiers[context.target_culture] || 1.0
    
    // 检查文化约束
    const culturalConstraint = construction.cultural_constraints.find(c => c.culture === context.target_culture)
    if (culturalConstraint) {
      // 这里可以添加更复杂的文化维度检查逻辑
      return culturalModifier
    }
    
    return culturalModifier
  }
  
  /** 计算语义适应性 */
  private calculateSemanticFit(construction: ConstructionTemplate, params: ConstructionGenerationParams): number {
    let score = 1.0
    
    // 检查语义域匹配
    if (params.semantic_preferences.domains.length > 0) {
      const constructionDomains = this.extractConstructionSemanticDomains(construction)
      const overlap = params.semantic_preferences.domains.filter(d => constructionDomains.includes(d)).length
      const maxOverlap = Math.max(params.semantic_preferences.domains.length, constructionDomains.length)
      score *= maxOverlap > 0 ? overlap / maxOverlap : 0.5
    }
    
    return score
  }
  
  /** 计算语用适应性 */
  private calculatePragmaticFit(construction: ConstructionTemplate, context: GenerationContext): number {
    // 基于应用域和平台的语用功能匹配
    let score = 1.0
    
    if (context.domain) {
      const domainFunctionMap: { [domain: string]: PragmaticFunction[] } = {
        'gaming': [PragmaticFunction.INTIMIDATION, PragmaticFunction.HUMOR, PragmaticFunction.CREATIVITY],
        'social': [PragmaticFunction.SELF_EXPRESSION, PragmaticFunction.ATTRACTION, PragmaticFunction.HUMOR],
        'professional': [PragmaticFunction.PROFESSIONALISM, PragmaticFunction.IDENTIFICATION]
      }
      
      const expectedFunctions = domainFunctionMap[context.domain] || []
      const matchingFunctions = construction.pragmatic_function.filter(f => expectedFunctions.includes(f))
      
      if (expectedFunctions.length > 0) {
        score *= matchingFunctions.length / expectedFunctions.length
      }
    }
    
    return score
  }
  
  // ============ 槽位填充 ============
  
  /** 为槽位寻找候选词汇 */
  private findSlotCandidates(
    slot: ConstructionSlot,
    context: GenerationContext,
    params: ConstructionGenerationParams
  ): SemanticLexicalEntry[] {
    // 构建语义查询
    const query = {
      language: context.target_language,
      pos: slot.pos_constraints,
      register: slot.register_constraints,
      semantic_domains: slot.semantic_domains,
      conceptual_categories: slot.conceptual_categories,
      emotional_valence_range: params.semantic_preferences.emotional_tone !== undefined ? 
        [params.semantic_preferences.emotional_tone - 0.2, params.semantic_preferences.emotional_tone + 0.2] : undefined,
      syllable_count_range: slot.syllable_constraints ? 
        [slot.syllable_constraints.min_syllables || 1, slot.syllable_constraints.max_syllables || 5] : undefined,
      age_groups: context.user_preferences.age_group ? [context.user_preferences.age_group] : undefined,
      limit: 20 // 获取足够的候选
    }
    
    const candidates = this.lexicon.search(query)
    
    // 应用槽位特定的过滤和排序
    return this.filterAndRankSlotCandidates(candidates, slot, context, params)
  }
  
  /** 过滤和排序槽位候选词汇 */
  private filterAndRankSlotCandidates(
    candidates: SemanticLexicalEntry[],
    slot: ConstructionSlot,
    context: GenerationContext,
    params: ConstructionGenerationParams
  ): SemanticLexicalEntry[] {
    // 应用文化约束
    let filtered = candidates.filter(candidate => {
      if (slot.cultural_constraints) {
        // 检查正式程度
        if (slot.cultural_constraints.formality_range) {
          const [min, max] = slot.cultural_constraints.formality_range
          const candidateFormality = this.estimateWordFormality(candidate)
          if (candidateFormality < min || candidateFormality > max) {
            return false
          }
        }
        
        // 检查禁忌程度
        if (slot.cultural_constraints.taboo_threshold !== undefined) {
          const tabooLevel = this.estimateWordTabooLevel(candidate, context.target_culture)
          if (tabooLevel > slot.cultural_constraints.taboo_threshold) {
            return false
          }
        }
      }
      
      return true
    })
    
    // 排序：综合考虑频率、流行度、语义匹配度等
    filtered.sort((a, b) => {
      const scoreA = this.calculateSlotCandidateScore(a, slot, context, params)
      const scoreB = this.calculateSlotCandidateScore(b, slot, context, params)
      return scoreB - scoreA
    })
    
    return filtered.slice(0, 10) // 返回前10个候选
  }
  
  /** 计算槽位候选词汇评分 */
  private calculateSlotCandidateScore(
    candidate: SemanticLexicalEntry,
    slot: ConstructionSlot,
    context: GenerationContext,
    params: ConstructionGenerationParams
  ): number {
    let score = 0
    
    // 基础质量分数
    score += candidate.confidence * 0.2
    
    // 频率分数
    score += Math.log(candidate.frequency + 1) * 0.15
    
    // 流行度分数
    score += candidate.trend_score * 0.15
    
    // 语义匹配分数
    if (slot.semantic_domains) {
      const domainMatch = slot.semantic_domains.some(domain => 
        candidate.semantic_domains.includes(domain)
      )
      if (domainMatch) score += 0.3
    }
    
    // 情感匹配分数
    if (params.semantic_preferences.emotional_tone !== undefined) {
      const emotionalDistance = Math.abs(candidate.emotional_valence - params.semantic_preferences.emotional_tone)
      score += (1 - emotionalDistance / 2) * 0.2
    }
    
    return score
  }
  
  // ============ 构式实例化 ============
  
  /** 实例化构式 */
  instantiateConstruction(
    match: ConstructionMatch,
    context: GenerationContext,
    params: ConstructionGenerationParams
  ): ConstructionInstance | null {
    const construction = match.construction
    const slotFillers: { [slot_id: string]: SemanticLexicalEntry } = {}
    
    // 为每个槽位选择最佳词汇
    for (const slot of construction.structure) {
      const candidates = match.slot_assignments[slot.id] || []
      if (candidates.length === 0) {
        // 使用回退策略
        const fallbackWord = this.getFallbackWord(slot, context)
        if (!fallbackWord) return null
        slotFillers[slot.id] = fallbackWord
      } else {
        // 选择最佳候选
        const bestCandidate = this.lexicon.getEntry(candidates[0], context.target_language)
        if (!bestCandidate) return null
        slotFillers[slot.id] = bestCandidate
      }
    }
    
    // 生成文本
    const generatedText = this.generateTextFromSlots(construction, slotFillers)
    
    // 计算质量指标
    const qualityMetrics = this.calculateQualityMetrics(construction, slotFillers, generatedText, context)
    
    return {
      construction,
      slot_fillers: slotFillers,
      generated_text: generatedText,
      quality_metrics: qualityMetrics,
      constraint_violations: match.constraint_violations
    }
  }
  
  /** 从槽位生成文本 */
  private generateTextFromSlots(
    construction: ConstructionTemplate,
    slotFillers: { [slot_id: string]: SemanticLexicalEntry }
  ): string {
    // 按位置排序槽位
    const sortedSlots = construction.structure.sort((a, b) => a.position - b.position)
    
    // 组合文本
    const parts: string[] = []
    sortedSlots.forEach(slot => {
      const filler = slotFillers[slot.id]
      if (filler) {
        parts.push(filler.word)
      }
    })
    
    return parts.join('')
  }
  
  /** 计算质量指标 */
  private calculateQualityMetrics(
    construction: ConstructionTemplate,
    slotFillers: { [slot_id: string]: SemanticLexicalEntry },
    generatedText: string,
    context: GenerationContext
  ): QualityMetrics {
    // 语义连贯性
    const semanticCoherence = this.calculateSemanticCoherence(slotFillers)
    
    // 文化适宜性
    const culturalAppropriateness = this.calculateCulturalAppropriateness(slotFillers, context)
    
    // 音韵和谐性
    const phoneticHarmony = this.calculatePhoneticHarmony(slotFillers)
    
    // 记忆性
    const memorability = this.calculateMemorability(generatedText, slotFillers)
    
    // 独特性
    const uniqueness = this.calculateUniqueness(generatedText)
    
    // 综合评分
    const overallScore = (
      semanticCoherence * 0.25 +
      culturalAppropriateness * 0.25 +
      phoneticHarmony * 0.2 +
      memorability * 0.15 +
      uniqueness * 0.15
    )
    
    return {
      semantic_coherence: semanticCoherence,
      cultural_appropriateness: culturalAppropriateness,
      phonetic_harmony: phoneticHarmony,
      memorability: memorability,
      uniqueness: uniqueness,
      overall_score: overallScore
    }
  }
  
  // ============ 辅助方法 ============
  
  private generateCacheKey(context: GenerationContext, params: ConstructionGenerationParams): string {
    return JSON.stringify({ context, params })
  }
  
  private estimateConstructionFormality(construction: ConstructionTemplate): number {
    // 基于语域兼容性估算正式程度
    const formalRegisters = construction.register_compatibility.filter(r => 
      r === 'formal' || r === 'literary'
    ).length
    const totalRegisters = construction.register_compatibility.length
    return totalRegisters > 0 ? formalRegisters / totalRegisters : 0.5
  }
  
  private extractConstructionSemanticDomains(construction: ConstructionTemplate): string[] {
    const domains = new Set<string>()
    construction.structure.forEach(slot => {
      if (slot.semantic_domains) {
        slot.semantic_domains.forEach(domain => domains.add(domain))
      }
    })
    return Array.from(domains)
  }
  
  private estimateWordFormality(word: SemanticLexicalEntry): number {
    // 基于语域估算正式程度
    switch (word.register) {
      case 'formal': return 0.9
      case 'literary': return 0.8
      case 'technical': return 0.7
      case 'informal': return 0.3
      case 'colloquial': return 0.2
      case 'slang': return 0.1
      default: return 0.5
    }
  }
  
  private estimateWordTabooLevel(word: SemanticLexicalEntry, culture: string): number {
    // 基于文化内涵估算禁忌程度
    const culturalConnotation = word.cultural_connotations.find(c => c.culture === culture)
    return culturalConnotation?.taboo_level || 0
  }
  
  private getFallbackWord(slot: ConstructionSlot, context: GenerationContext): SemanticLexicalEntry | null {
    if (slot.default_fillers && slot.default_fillers.length > 0) {
      const randomDefault = slot.default_fillers[Math.floor(Math.random() * slot.default_fillers.length)]
      return this.lexicon.getEntry(randomDefault, context.target_language) || null
    }
    return null
  }
  
  private estimateConstructionQuality(construction: ConstructionTemplate, slotAssignments: { [slot_id: string]: string[] }): number {
    // 基于槽位填充情况估算质量
    const totalSlots = construction.structure.length
    const filledSlots = Object.values(slotAssignments).filter(candidates => candidates.length > 0).length
    return filledSlots / totalSlots
  }
  
  // 质量计算方法的简化实现
  private calculateSemanticCoherence(slotFillers: { [slot_id: string]: SemanticLexicalEntry }): number {
    // 简化实现：基于语义域重叠度
    const words = Object.values(slotFillers)
    if (words.length < 2) return 1
    
    let totalSimilarity = 0
    let comparisons = 0
    
    for (let i = 0; i < words.length; i++) {
      for (let j = i + 1; j < words.length; j++) {
        const similarity = this.lexicon.calculateSimilarity(words[i].word, words[j].word, words[i].language)
        if (similarity) {
          totalSimilarity += similarity.similarity_score
          comparisons++
        }
      }
    }
    
    return comparisons > 0 ? totalSimilarity / comparisons : 0.5
  }
  
  private calculateCulturalAppropriateness(slotFillers: { [slot_id: string]: SemanticLexicalEntry }, context: GenerationContext): number {
    const words = Object.values(slotFillers)
    let totalScore = 0
    
    words.forEach(word => {
      const culturalConnotation = word.cultural_connotations.find(c => c.culture === context.target_culture)
      if (culturalConnotation) {
        totalScore += (1 - culturalConnotation.taboo_level) // 禁忌程度越低，适宜性越高
      } else {
        totalScore += 0.7 // 默认中等适宜性
      }
    })
    
    return words.length > 0 ? totalScore / words.length : 0.5
  }
  
  private calculatePhoneticHarmony(slotFillers: { [slot_id: string]: SemanticLexicalEntry }): number {
    // 简化实现：基于音节数的和谐性
    const words = Object.values(slotFillers)
    if (words.length < 2) return 1
    
    const syllableCounts = words.map(w => w.syllable_count)
    const avgSyllables = syllableCounts.reduce((a, b) => a + b, 0) / syllableCounts.length
    const variance = syllableCounts.reduce((sum, count) => sum + Math.pow(count - avgSyllables, 2), 0) / syllableCounts.length
    
    // 方差越小，和谐性越高
    return Math.max(0, 1 - variance / 4)
  }
  
  private calculateMemorability(generatedText: string, slotFillers: { [slot_id: string]: SemanticLexicalEntry }): number {
    // 简化实现：基于长度和频率
    const length = generatedText.length
    const avgFrequency = Object.values(slotFillers).reduce((sum, word) => sum + word.frequency, 0) / Object.values(slotFillers).length
    
    // 适中的长度和适中的频率有利于记忆
    const lengthScore = length >= 4 && length <= 12 ? 1 : 0.5
    const frequencyScore = avgFrequency >= 0.3 && avgFrequency <= 0.8 ? 1 : 0.7
    
    return (lengthScore + frequencyScore) / 2
  }
  
  private calculateUniqueness(generatedText: string): number {
    // 简化实现：基于字符组合的独特性
    const uniqueChars = new Set(generatedText).size
    const totalChars = generatedText.length
    return totalChars > 0 ? uniqueChars / totalChars : 0
  }

  // ============ 批量生成方法 ============

  /** 批量生成用户名 */
  generateMultiple(
    context: GenerationContext,
    params: ConstructionGenerationParams,
    count: number = 5
  ): ConstructionInstance[] {
    const matches = this.matchConstructions(context, params)
    const results: ConstructionInstance[] = []

    let attempts = 0
    const maxAttempts = params.generation_constraints.max_attempts || count * 3

    while (results.length < count && attempts < maxAttempts) {
      // 选择构式（带随机性以增加多样性）
      const selectedMatch = this.selectConstructionWithVariety(matches, results)
      if (!selectedMatch) break

      const instance = this.instantiateConstruction(selectedMatch, context, params)
      if (instance && this.meetsQualityRequirements(instance, params)) {
        results.push(instance)
      }

      attempts++
    }

    return results.sort((a, b) => b.quality_metrics.overall_score - a.quality_metrics.overall_score)
  }

  /** 选择构式（考虑多样性） */
  private selectConstructionWithVariety(
    matches: ConstructionMatch[],
    existingResults: ConstructionInstance[]
  ): ConstructionMatch | null {
    if (matches.length === 0) return null

    // 过滤已使用的构式以增加多样性
    const usedConstructionIds = new Set(existingResults.map(r => r.construction.id))
    const unusedMatches = matches.filter(m => !usedConstructionIds.has(m.construction.id))

    const candidateMatches = unusedMatches.length > 0 ? unusedMatches : matches

    // 加权随机选择
    const weights = candidateMatches.map(m => m.match_score)
    const totalWeight = weights.reduce((sum, w) => sum + w, 0)

    if (totalWeight === 0) return candidateMatches[0]

    let random = Math.random() * totalWeight
    for (let i = 0; i < candidateMatches.length; i++) {
      random -= weights[i]
      if (random <= 0) {
        return candidateMatches[i]
      }
    }

    return candidateMatches[0]
  }

  /** 检查是否满足质量要求 */
  private meetsQualityRequirements(
    instance: ConstructionInstance,
    params: ConstructionGenerationParams
  ): boolean {
    const metrics = instance.quality_metrics
    const requirements = params.quality_requirements

    return (
      metrics.semantic_coherence >= requirements.min_semantic_coherence &&
      metrics.cultural_appropriateness >= requirements.min_cultural_appropriateness &&
      metrics.phonetic_harmony >= requirements.min_phonetic_harmony &&
      metrics.overall_score >= requirements.min_overall_quality
    )
  }
}
