/**
 * 文化适配器
 * 
 * 基于文化语言学理论的文化适配系统，负责将抽象的风格需求映射到具体的文化表达
 */

import {
  CulturalDimension,
  CulturalConnotation,
  SemanticLexicalEntry,
  GenerationContext,
  GenerationPreference
} from '../types/SemanticTypes'

import {
  ConstructionTemplate,
  PragmaticFunction
} from '../types/ConstructionTypes'

/** 文化风格映射 */
export interface CulturalStyleMapping {
  culture: string
  style: string
  characteristics: {
    preferred_semantic_domains: string[]
    preferred_pos_patterns: string[]
    emotional_tone_range: [number, number]
    formality_preference: number
    creativity_preference: number
    typical_constructions: string[]
  }
  linguistic_features: {
    preferred_syllable_patterns: number[]
    phonetic_preferences: string[]
    morphological_preferences: string[]
  }
  cultural_markers: {
    positive_concepts: string[]
    negative_concepts: string[]
    taboo_concepts: string[]
    generational_markers: { [ageGroup: string]: string[] }
  }
}

/** 文化知识库 */
export interface CulturalKnowledgeBase {
  culture_id: string
  culture_name: string
  language: string
  
  // Hofstede文化维度
  cultural_dimensions: CulturalDimension
  
  // 风格映射
  style_mappings: CulturalStyleMapping[]
  
  // 语用规则
  pragmatic_rules: {
    function: PragmaticFunction
    preferred_expressions: string[]
    avoided_expressions: string[]
    contextual_constraints: string[]
  }[]
  
  // 社会语言学特征
  sociolinguistic_features: {
    age_group_variations: { [ageGroup: string]: string[] }
    gender_variations: { [gender: string]: string[] }
    social_class_variations: { [socialClass: string]: string[] }
    regional_variations: { [region: string]: string[] }
  }
  
  // 时间性特征
  temporal_features: {
    historical_layers: { [period: string]: string[] }
    trend_patterns: { [trend: string]: number } // 流行度变化
    seasonal_preferences: { [season: string]: string[] }
  }
}

/** 文化适配结果 */
export interface CulturalAdaptationResult {
  adapted_preferences: GenerationPreference
  cultural_constraints: {
    required_elements: string[]
    forbidden_elements: string[]
    preferred_constructions: string[]
    style_modifiers: { [style: string]: number }
  }
  explanation: string
  confidence: number
}

/** 文化适配器类 */
export class CulturalAdapter {
  private knowledgeBases: Map<string, CulturalKnowledgeBase> = new Map()
  private styleCache: Map<string, CulturalStyleMapping> = new Map()
  
  constructor() {
    this.initializeDefaultKnowledgeBases()
  }
  
  // ============ 知识库管理 ============
  
  /** 添加文化知识库 */
  addKnowledgeBase(kb: CulturalKnowledgeBase): void {
    this.knowledgeBases.set(kb.culture_id, kb)
    
    // 更新风格缓存
    kb.style_mappings.forEach(mapping => {
      const key = `${kb.culture_id}:${mapping.style}`
      this.styleCache.set(key, mapping)
    })
  }
  
  /** 获取文化知识库 */
  getKnowledgeBase(cultureId: string): CulturalKnowledgeBase | undefined {
    return this.knowledgeBases.get(cultureId)
  }
  
  /** 初始化默认知识库 */
  private initializeDefaultKnowledgeBases(): void {
    // 中文文化知识库
    const chineseKB: CulturalKnowledgeBase = {
      culture_id: 'zh-CN',
      culture_name: '中国文化',
      language: 'zh',
      cultural_dimensions: {
        power_distance: 80,
        individualism: 20,
        masculinity: 66,
        uncertainty_avoidance: 30,
        long_term_orientation: 87,
        indulgence: 24
      },
      style_mappings: [
        {
          culture: 'zh-CN',
          style: 'traditional',
          characteristics: {
            preferred_semantic_domains: ['nature', 'virtue', 'wisdom', 'harmony'],
            preferred_pos_patterns: ['adj+noun', 'noun+noun'],
            emotional_tone_range: [0.2, 0.8],
            formality_preference: 0.7,
            creativity_preference: 0.4,
            typical_constructions: ['classical_compound', 'poetic_phrase']
          },
          linguistic_features: {
            preferred_syllable_patterns: [2, 4],
            phonetic_preferences: ['tonal_harmony', 'rhythmic_balance'],
            morphological_preferences: ['compound', 'reduplication']
          },
          cultural_markers: {
            positive_concepts: ['和谐', '智慧', '仁义', '诚信'],
            negative_concepts: ['冲突', '愚昧', '欺骗'],
            taboo_concepts: ['死亡', '疾病', '不吉利数字'],
            generational_markers: {
              'seniors': ['传统', '经典', '古雅'],
              'middle_aged': ['稳重', '成熟', '内敛'],
              'young_adults': ['时尚', '个性', '创新'],
              'teenagers': ['潮流', '酷炫', '二次元']
            }
          }
        },
        {
          culture: 'zh-CN',
          style: 'modern',
          characteristics: {
            preferred_semantic_domains: ['technology', 'fashion', 'entertainment', 'social'],
            preferred_pos_patterns: ['adj+noun', 'verb+noun', 'noun+suffix'],
            emotional_tone_range: [0.3, 0.9],
            formality_preference: 0.3,
            creativity_preference: 0.8,
            typical_constructions: ['trendy_compound', 'internet_slang']
          },
          linguistic_features: {
            preferred_syllable_patterns: [2, 3, 4],
            phonetic_preferences: ['catchy_rhythm', 'easy_pronunciation'],
            morphological_preferences: ['abbreviation', 'blend', 'borrowing']
          },
          cultural_markers: {
            positive_concepts: ['创新', '时尚', '个性', '自由'],
            negative_concepts: ['老土', '过时', '无聊'],
            taboo_concepts: ['政治敏感', '极端言论'],
            generational_markers: {
              'teenagers': ['yyds', '绝绝子', '破防', '内卷'],
              'young_adults': ['佛系', '躺平', '打工人', '社恐'],
              'middle_aged': ['稳重', '靠谱', '专业'],
              'seniors': ['传统', '经典']
            }
          }
        }
      ],
      pragmatic_rules: [
        {
          function: PragmaticFunction.SELF_EXPRESSION,
          preferred_expressions: ['个性', '独特', '真实'],
          avoided_expressions: ['平庸', '普通'],
          contextual_constraints: ['avoid_overly_boastful']
        },
        {
          function: PragmaticFunction.HUMOR,
          preferred_expressions: ['搞笑', '幽默', '逗比'],
          avoided_expressions: ['严肃', '正经'],
          contextual_constraints: ['context_appropriate']
        }
      ],
      sociolinguistic_features: {
        age_group_variations: {
          'teenagers': ['网络用语', '二次元词汇', '游戏术语'],
          'young_adults': ['职场词汇', '生活化表达', '流行语'],
          'middle_aged': ['正式用语', '传统表达'],
          'seniors': ['古典词汇', '文言色彩']
        },
        gender_variations: {
          'male': ['阳刚', '力量', '竞争'],
          'female': ['优雅', '温柔', '细腻'],
          'neutral': ['平衡', '包容', '多元']
        },
        social_class_variations: {
          'professional': ['专业', '精英', '高端'],
          'student': ['青春', '活力', '学术'],
          'general': ['大众', '亲民', '实用']
        },
        regional_variations: {
          'north': ['豪爽', '直接', '大气'],
          'south': ['精致', '细腻', '温和'],
          'general': ['通用', '标准', '普适']
        }
      },
      temporal_features: {
        historical_layers: {
          'ancient': ['古典', '文言', '典雅'],
          'modern': ['现代', '时尚', '流行'],
          'contemporary': ['当代', '前沿', '创新']
        },
        trend_patterns: {
          'retro': 0.3,
          'futuristic': 0.7,
          'minimalist': 0.6,
          'maximalist': 0.4
        },
        seasonal_preferences: {
          'spring': ['生机', '活力', '希望'],
          'summer': ['热情', '活跃', '阳光'],
          'autumn': ['成熟', '收获', '沉稳'],
          'winter': ['纯净', '宁静', '深沉']
        }
      }
    }
    
    this.addKnowledgeBase(chineseKB)
  }
  
  // ============ 文化适配方法 ============
  
  /** 执行文化适配 */
  adaptToCulture(
    context: GenerationContext,
    basePreferences: GenerationPreference
  ): CulturalAdaptationResult {
    const kb = this.knowledgeBases.get(context.target_culture)
    if (!kb) {
      return this.createFallbackAdaptation(basePreferences)
    }
    
    // 确定目标风格
    const targetStyle = this.determineTargetStyle(context, kb)
    const styleMapping = this.getStyleMapping(context.target_culture, targetStyle)
    
    if (!styleMapping) {
      return this.createFallbackAdaptation(basePreferences)
    }
    
    // 适配偏好
    const adaptedPreferences = this.adaptPreferences(basePreferences, styleMapping, kb)
    
    // 生成文化约束
    const culturalConstraints = this.generateCulturalConstraints(styleMapping, kb, context)
    
    // 生成解释
    const explanation = this.generateExplanation(targetStyle, styleMapping, context)
    
    // 计算置信度
    const confidence = this.calculateAdaptationConfidence(context, kb, styleMapping)
    
    return {
      adapted_preferences: adaptedPreferences,
      cultural_constraints: culturalConstraints,
      explanation: explanation,
      confidence: confidence
    }
  }
  
  /** 确定目标风格 */
  private determineTargetStyle(context: GenerationContext, kb: CulturalKnowledgeBase): string {
    // 基于用户偏好、应用域和平台确定风格
    let style = 'modern' // 默认风格
    
    // 基于正式程度
    const formalityLevel = context.user_preferences.formality_level || 0.5
    if (formalityLevel > 0.7) {
      style = 'traditional'
    } else if (formalityLevel < 0.3) {
      style = 'casual'
    }
    
    // 基于应用域
    if (context.domain) {
      const domainStyleMap: { [domain: string]: string } = {
        'gaming': 'modern',
        'social': 'casual',
        'professional': 'traditional',
        'creative': 'artistic'
      }
      style = domainStyleMap[context.domain] || style
    }
    
    // 基于年龄群体
    if (context.user_preferences.age_group) {
      const ageStyleMap: { [ageGroup: string]: string } = {
        'teenagers': 'trendy',
        'young_adults': 'modern',
        'middle_aged': 'mature',
        'seniors': 'traditional'
      }
      style = ageStyleMap[context.user_preferences.age_group] || style
    }
    
    // 确保风格在知识库中存在
    const availableStyles = kb.style_mappings.map(m => m.style)
    if (!availableStyles.includes(style)) {
      style = availableStyles[0] || 'modern'
    }
    
    return style
  }
  
  /** 获取风格映射 */
  private getStyleMapping(culture: string, style: string): CulturalStyleMapping | undefined {
    const key = `${culture}:${style}`
    return this.styleCache.get(key)
  }
  
  /** 适配偏好 */
  private adaptPreferences(
    basePreferences: GenerationPreference,
    styleMapping: CulturalStyleMapping,
    kb: CulturalKnowledgeBase
  ): GenerationPreference {
    const adapted: GenerationPreference = { ...basePreferences }
    
    // 适配语义域
    if (!adapted.semantic_domains || adapted.semantic_domains.length === 0) {
      adapted.semantic_domains = styleMapping.characteristics.preferred_semantic_domains.slice(0, 3)
    } else {
      // 合并用户偏好和文化偏好
      const combined = [...adapted.semantic_domains, ...styleMapping.characteristics.preferred_semantic_domains]
      adapted.semantic_domains = [...new Set(combined)].slice(0, 5)
    }
    
    // 适配情感色调
    if (adapted.emotional_tone === undefined) {
      const [min, max] = styleMapping.characteristics.emotional_tone_range
      adapted.emotional_tone = (min + max) / 2
    } else {
      // 调整到文化偏好范围内
      const [min, max] = styleMapping.characteristics.emotional_tone_range
      adapted.emotional_tone = Math.max(min, Math.min(max, adapted.emotional_tone))
    }
    
    // 适配正式程度
    if (adapted.formality_level === undefined) {
      adapted.formality_level = styleMapping.characteristics.formality_preference
    } else {
      // 加权平均
      adapted.formality_level = (adapted.formality_level + styleMapping.characteristics.formality_preference) / 2
    }
    
    // 适配创意程度
    if (adapted.creativity_level === undefined) {
      adapted.creativity_level = styleMapping.characteristics.creativity_preference
    } else {
      adapted.creativity_level = (adapted.creativity_level + styleMapping.characteristics.creativity_preference) / 2
    }
    
    // 适配文化适应程度
    adapted.cultural_adaptation = adapted.cultural_adaptation || 0.8
    
    return adapted
  }
  
  /** 生成文化约束 */
  private generateCulturalConstraints(
    styleMapping: CulturalStyleMapping,
    kb: CulturalKnowledgeBase,
    context: GenerationContext
  ): any {
    return {
      required_elements: styleMapping.cultural_markers.positive_concepts.slice(0, 3),
      forbidden_elements: [
        ...styleMapping.cultural_markers.negative_concepts,
        ...styleMapping.cultural_markers.taboo_concepts
      ],
      preferred_constructions: styleMapping.characteristics.typical_constructions,
      style_modifiers: {
        [styleMapping.style]: 1.5,
        'default': 1.0
      }
    }
  }
  
  /** 生成解释 */
  private generateExplanation(
    targetStyle: string,
    styleMapping: CulturalStyleMapping,
    context: GenerationContext
  ): string {
    return `基于${context.target_culture}文化和${targetStyle}风格，调整了语义偏好和表达方式，以更好地符合文化期望和使用习惯。`
  }
  
  /** 计算适配置信度 */
  private calculateAdaptationConfidence(
    context: GenerationContext,
    kb: CulturalKnowledgeBase,
    styleMapping: CulturalStyleMapping
  ): number {
    let confidence = 0.8 // 基础置信度
    
    // 基于知识库完整性
    const kbCompleteness = (
      (kb.style_mappings.length > 0 ? 0.2 : 0) +
      (kb.pragmatic_rules.length > 0 ? 0.2 : 0) +
      (Object.keys(kb.sociolinguistic_features.age_group_variations).length > 0 ? 0.2 : 0) +
      (Object.keys(kb.temporal_features.trend_patterns).length > 0 ? 0.2 : 0) +
      0.2 // 基础分
    )
    
    confidence *= kbCompleteness
    
    // 基于风格匹配度
    const styleMatch = styleMapping ? 1.0 : 0.5
    confidence *= styleMatch
    
    return Math.max(0.3, Math.min(1.0, confidence))
  }
  
  /** 创建回退适配 */
  private createFallbackAdaptation(basePreferences: GenerationPreference): CulturalAdaptationResult {
    return {
      adapted_preferences: basePreferences,
      cultural_constraints: {
        required_elements: [],
        forbidden_elements: [],
        preferred_constructions: [],
        style_modifiers: { 'default': 1.0 }
      },
      explanation: '使用默认文化适配策略',
      confidence: 0.3
    }
  }
  
  // ============ 文化分析方法 ============
  
  /** 分析文化兼容性 */
  analyzeCulturalCompatibility(
    words: SemanticLexicalEntry[],
    targetCulture: string
  ): { compatibility_score: number; issues: string[] } {
    const kb = this.knowledgeBases.get(targetCulture)
    if (!kb) {
      return { compatibility_score: 0.5, issues: ['未知文化'] }
    }
    
    let totalScore = 0
    const issues: string[] = []
    
    words.forEach(word => {
      const culturalConnotation = word.cultural_connotations.find(c => c.culture === targetCulture)
      if (culturalConnotation) {
        totalScore += (1 - culturalConnotation.taboo_level)
        if (culturalConnotation.taboo_level > 0.5) {
          issues.push(`词汇"${word.word}"可能存在文化敏感性`)
        }
      } else {
        totalScore += 0.7 // 默认中等兼容性
        issues.push(`词汇"${word.word}"缺少文化内涵信息`)
      }
    })
    
    const compatibilityScore = words.length > 0 ? totalScore / words.length : 0.5
    
    return {
      compatibility_score: compatibilityScore,
      issues: issues
    }
  }
  
  /** 获取文化建议 */
  getCulturalSuggestions(
    context: GenerationContext,
    currentWords: string[]
  ): string[] {
    const kb = this.knowledgeBases.get(context.target_culture)
    if (!kb) return []
    
    const suggestions: string[] = []
    
    // 基于年龄群体的建议
    if (context.user_preferences.age_group) {
      const ageVariations = kb.sociolinguistic_features.age_group_variations[context.user_preferences.age_group]
      if (ageVariations) {
        suggestions.push(...ageVariations.slice(0, 3))
      }
    }
    
    // 基于应用域的建议
    if (context.domain) {
      const domainMapping = kb.style_mappings.find(m => 
        m.characteristics.preferred_semantic_domains.includes(context.domain!)
      )
      if (domainMapping) {
        suggestions.push(...domainMapping.cultural_markers.positive_concepts.slice(0, 2))
      }
    }
    
    return [...new Set(suggestions)].slice(0, 5)
  }
}
