/**
 * 代码质量检查脚本
 * 
 * 检查V2系统的代码质量、文档完整性和最佳实践
 */

import * as fs from 'fs/promises'
import * as path from 'path'

class QualityChecker {
  private issues: QualityIssue[] = []
  private metrics: QualityMetrics = {
    totalFiles: 0,
    totalLines: 0,
    documentedFunctions: 0,
    totalFunctions: 0,
    typeScriptFiles: 0,
    testFiles: 0,
    codeComplexity: 0,
    duplicateCode: 0
  }
  
  async checkQuality(): Promise<QualityReport> {
    console.log('🔍 开始代码质量检查')
    console.log('=' .repeat(50))
    
    const projectRoot = path.join(__dirname, '..')
    
    // 检查项目结构
    await this.checkProjectStructure(projectRoot)
    
    // 检查代码文件
    await this.checkCodeFiles(projectRoot)
    
    // 检查文档
    await this.checkDocumentation(projectRoot)
    
    // 检查测试覆盖率
    await this.checkTestCoverage(projectRoot)
    
    // 生成报告
    return this.generateReport()
  }
  
  private async checkProjectStructure(projectRoot: string): Promise<void> {
    console.log('\n📁 项目结构检查')
    console.log('-' .repeat(30))
    
    const requiredDirs = [
      'architecture',
      'data/lexical',
      'data/grammar', 
      'data/culture',
      'tests/unit',
      'tests/integration',
      'tests/performance',
      'types'
    ]
    
    for (const dir of requiredDirs) {
      const dirPath = path.join(projectRoot, dir)
      try {
        await fs.access(dirPath)
        console.log(`✅ ${dir}`)
      } catch {
        this.addIssue('structure', `缺少必需目录: ${dir}`, 'high')
        console.log(`❌ ${dir}`)
      }
    }
    
    const requiredFiles = [
      'README.md',
      'PHASE1_TASKS.md',
      'types/index.ts',
      'architecture/DataStructures.ts',
      'architecture/CoreAlgorithms.ts',
      'architecture/DataManager.ts',
      'architecture/GenerationEngine.ts',
      'architecture/PerformanceOptimizer.ts',
      'architecture/MainAPI.ts'
    ]
    
    for (const file of requiredFiles) {
      const filePath = path.join(projectRoot, file)
      try {
        await fs.access(filePath)
        console.log(`✅ ${file}`)
      } catch {
        this.addIssue('structure', `缺少必需文件: ${file}`, 'high')
        console.log(`❌ ${file}`)
      }
    }
  }
  
  private async checkCodeFiles(projectRoot: string): Promise<void> {
    console.log('\n💻 代码文件检查')
    console.log('-' .repeat(30))
    
    const codeFiles = await this.findCodeFiles(projectRoot)
    this.metrics.totalFiles = codeFiles.length
    this.metrics.typeScriptFiles = codeFiles.filter(f => f.endsWith('.ts')).length
    
    for (const file of codeFiles) {
      await this.analyzeCodeFile(file)
    }
    
    console.log(`总文件数: ${this.metrics.totalFiles}`)
    console.log(`TypeScript文件: ${this.metrics.typeScriptFiles}`)
    console.log(`总代码行数: ${this.metrics.totalLines}`)
    console.log(`函数文档率: ${((this.metrics.documentedFunctions / this.metrics.totalFunctions) * 100).toFixed(1)}%`)
  }
  
  private async findCodeFiles(dir: string): Promise<string[]> {
    const files: string[] = []
    
    try {
      const entries = await fs.readdir(dir, { withFileTypes: true })
      
      for (const entry of entries) {
        const fullPath = path.join(dir, entry.name)
        
        if (entry.isDirectory() && !entry.name.startsWith('.') && entry.name !== 'node_modules') {
          const subFiles = await this.findCodeFiles(fullPath)
          files.push(...subFiles)
        } else if (entry.isFile() && (entry.name.endsWith('.ts') || entry.name.endsWith('.js'))) {
          files.push(fullPath)
        }
      }
    } catch (error) {
      // 忽略无法访问的目录
    }
    
    return files
  }
  
  private async analyzeCodeFile(filePath: string): Promise<void> {
    try {
      const content = await fs.readFile(filePath, 'utf-8')
      const lines = content.split('\n')
      
      this.metrics.totalLines += lines.length
      
      // 检查文件头注释
      if (!this.hasFileHeader(content)) {
        this.addIssue('documentation', `文件缺少头部注释: ${path.basename(filePath)}`, 'medium')
      }
      
      // 检查函数文档
      this.checkFunctionDocumentation(content, filePath)
      
      // 检查代码复杂度
      this.checkCodeComplexity(content, filePath)
      
      // 检查TypeScript严格性
      if (filePath.endsWith('.ts')) {
        this.checkTypeScriptStrict(content, filePath)
      }
      
    } catch (error) {
      this.addIssue('file', `无法读取文件: ${filePath}`, 'high')
    }
  }
  
  private hasFileHeader(content: string): boolean {
    const lines = content.split('\n').slice(0, 10)
    return lines.some(line => line.includes('/**') || line.includes('*'))
  }
  
  private checkFunctionDocumentation(content: string, filePath: string): void {
    // 简单的函数检测
    const functionRegex = /(?:function\s+\w+|(?:async\s+)?(?:public\s+|private\s+|protected\s+)?\w+\s*\()/g
    const docRegex = /\/\*\*[\s\S]*?\*\//g
    
    const functions = content.match(functionRegex) || []
    const docs = content.match(docRegex) || []
    
    this.metrics.totalFunctions += functions.length
    this.metrics.documentedFunctions += Math.min(functions.length, docs.length)
    
    if (functions.length > docs.length) {
      const undocumented = functions.length - docs.length
      this.addIssue('documentation', 
        `${path.basename(filePath)}: ${undocumented}个函数缺少文档`, 
        'medium')
    }
  }
  
  private checkCodeComplexity(content: string, filePath: string): void {
    // 简单的复杂度检测
    const complexityIndicators = [
      /if\s*\(/g,
      /else\s*if/g,
      /for\s*\(/g,
      /while\s*\(/g,
      /switch\s*\(/g,
      /catch\s*\(/g,
      /&&/g,
      /\|\|/g
    ]
    
    let complexity = 0
    for (const regex of complexityIndicators) {
      const matches = content.match(regex)
      complexity += matches ? matches.length : 0
    }
    
    this.metrics.codeComplexity += complexity
    
    const lines = content.split('\n').length
    const complexityRatio = complexity / lines
    
    if (complexityRatio > 0.1) {
      this.addIssue('complexity', 
        `${path.basename(filePath)}: 代码复杂度较高 (${complexityRatio.toFixed(2)})`, 
        'medium')
    }
  }
  
  private checkTypeScriptStrict(content: string, filePath: string): void {
    // 检查是否使用了any类型
    const anyUsage = content.match(/:\s*any\b/g)
    if (anyUsage && anyUsage.length > 2) {
      this.addIssue('typescript', 
        `${path.basename(filePath)}: 过多使用any类型 (${anyUsage.length}处)`, 
        'medium')
    }
    
    // 检查是否有未使用的导入
    const imports = content.match(/import\s+.*?from/g) || []
    const unusedImports = imports.filter(imp => {
      const importName = imp.match(/import\s+(?:\{([^}]+)\}|\w+)/)?.[1] || imp.match(/import\s+(\w+)/)?.[1]
      if (importName) {
        const names = importName.includes(',') ? importName.split(',').map(n => n.trim()) : [importName.trim()]
        return names.some(name => !content.includes(name.replace(/\s+as\s+\w+/, '')))
      }
      return false
    })
    
    if (unusedImports.length > 0) {
      this.addIssue('typescript', 
        `${path.basename(filePath)}: 可能存在未使用的导入`, 
        'low')
    }
  }
  
  private async checkDocumentation(projectRoot: string): Promise<void> {
    console.log('\n📚 文档检查')
    console.log('-' .repeat(30))
    
    const docFiles = [
      'README.md',
      'PHASE1_TASKS.md',
      '../../../docs/V2_SYSTEM_DESIGN.md',
      '../../../docs/V2_IMPLEMENTATION_PLAN.md',
      '../../../docs/V2_API_REFERENCE.md'
    ]
    
    for (const docFile of docFiles) {
      const docPath = path.join(projectRoot, docFile)
      try {
        const content = await fs.readFile(docPath, 'utf-8')
        
        if (content.length < 1000) {
          this.addIssue('documentation', `文档内容过少: ${docFile}`, 'medium')
        }
        
        // 检查是否有TODO或FIXME
        const todos = content.match(/TODO|FIXME|XXX/gi)
        if (todos && todos.length > 5) {
          this.addIssue('documentation', `${docFile}: 过多待办事项 (${todos.length}个)`, 'low')
        }
        
        console.log(`✅ ${docFile} (${content.length}字符)`)
      } catch {
        this.addIssue('documentation', `文档文件缺失: ${docFile}`, 'high')
        console.log(`❌ ${docFile}`)
      }
    }
  }
  
  private async checkTestCoverage(projectRoot: string): Promise<void> {
    console.log('\n🧪 测试覆盖率检查')
    console.log('-' .repeat(30))
    
    const testFiles = await this.findTestFiles(projectRoot)
    this.metrics.testFiles = testFiles.length
    
    const coreFiles = [
      'DataStructures.ts',
      'CoreAlgorithms.ts', 
      'DataManager.ts',
      'GenerationEngine.ts',
      'PerformanceOptimizer.ts',
      'MainAPI.ts'
    ]
    
    const testCoverage = new Map<string, boolean>()
    
    for (const coreFile of coreFiles) {
      const testFile = testFiles.find(tf => tf.includes(coreFile.replace('.ts', '.test.ts')))
      testCoverage.set(coreFile, !!testFile)
      
      if (testFile) {
        console.log(`✅ ${coreFile} -> ${path.basename(testFile)}`)
      } else {
        console.log(`❌ ${coreFile} (无测试文件)`)
        this.addIssue('testing', `核心文件缺少测试: ${coreFile}`, 'high')
      }
    }
    
    const coverageRate = (Array.from(testCoverage.values()).filter(Boolean).length / coreFiles.length) * 100
    console.log(`测试覆盖率: ${coverageRate.toFixed(1)}%`)
    
    if (coverageRate < 80) {
      this.addIssue('testing', `测试覆盖率过低: ${coverageRate.toFixed(1)}%`, 'high')
    }
  }
  
  private async findTestFiles(dir: string): Promise<string[]> {
    const files: string[] = []
    
    try {
      const entries = await fs.readdir(dir, { withFileTypes: true })
      
      for (const entry of entries) {
        const fullPath = path.join(dir, entry.name)
        
        if (entry.isDirectory() && entry.name === 'tests') {
          const testFiles = await this.findCodeFiles(fullPath)
          files.push(...testFiles.filter(f => f.includes('.test.')))
        } else if (entry.isDirectory() && !entry.name.startsWith('.')) {
          const subFiles = await this.findTestFiles(fullPath)
          files.push(...subFiles)
        }
      }
    } catch (error) {
      // 忽略错误
    }
    
    return files
  }
  
  private addIssue(category: string, description: string, severity: 'low' | 'medium' | 'high'): void {
    this.issues.push({ category, description, severity })
  }
  
  private generateReport(): QualityReport {
    const report: QualityReport = {
      timestamp: new Date().toISOString(),
      metrics: this.metrics,
      issues: this.issues,
      summary: this.generateSummary()
    }
    
    this.logReport(report)
    return report
  }
  
  private generateSummary(): QualitySummary {
    const highIssues = this.issues.filter(i => i.severity === 'high').length
    const mediumIssues = this.issues.filter(i => i.severity === 'medium').length
    const lowIssues = this.issues.filter(i => i.severity === 'low').length
    
    const documentationRate = this.metrics.totalFunctions > 0 ? 
      (this.metrics.documentedFunctions / this.metrics.totalFunctions) * 100 : 100
    
    const testCoverageRate = this.metrics.totalFiles > 0 ? 
      (this.metrics.testFiles / this.metrics.totalFiles) * 100 : 0
    
    // 计算总体质量分数
    let qualityScore = 100
    qualityScore -= highIssues * 10
    qualityScore -= mediumIssues * 5
    qualityScore -= lowIssues * 1
    qualityScore = Math.max(0, qualityScore)
    
    let grade: 'A' | 'B' | 'C' | 'D' | 'F'
    if (qualityScore >= 90) grade = 'A'
    else if (qualityScore >= 80) grade = 'B'
    else if (qualityScore >= 70) grade = 'C'
    else if (qualityScore >= 60) grade = 'D'
    else grade = 'F'
    
    return {
      qualityScore,
      grade,
      totalIssues: this.issues.length,
      highIssues,
      mediumIssues,
      lowIssues,
      documentationRate,
      testCoverageRate
    }
  }
  
  private logReport(report: QualityReport): void {
    console.log('\n📊 质量检查报告')
    console.log('=' .repeat(50))
    
    console.log(`总体评分: ${report.summary.qualityScore}/100 (${report.summary.grade}级)`)
    console.log(`文档覆盖率: ${report.summary.documentationRate.toFixed(1)}%`)
    console.log(`测试覆盖率: ${report.summary.testCoverageRate.toFixed(1)}%`)
    
    console.log('\n问题统计:')
    console.log(`  🔴 高优先级: ${report.summary.highIssues}个`)
    console.log(`  🟡 中优先级: ${report.summary.mediumIssues}个`)
    console.log(`  🟢 低优先级: ${report.summary.lowIssues}个`)
    
    if (report.issues.length > 0) {
      console.log('\n主要问题:')
      const highPriorityIssues = report.issues.filter(i => i.severity === 'high')
      highPriorityIssues.slice(0, 5).forEach(issue => {
        console.log(`  🔴 ${issue.description}`)
      })
      
      const mediumPriorityIssues = report.issues.filter(i => i.severity === 'medium')
      mediumPriorityIssues.slice(0, 3).forEach(issue => {
        console.log(`  🟡 ${issue.description}`)
      })
    }
    
    console.log('\n代码指标:')
    console.log(`  总文件数: ${report.metrics.totalFiles}`)
    console.log(`  总代码行数: ${report.metrics.totalLines}`)
    console.log(`  TypeScript文件: ${report.metrics.typeScriptFiles}`)
    console.log(`  测试文件: ${report.metrics.testFiles}`)
    console.log(`  函数总数: ${report.metrics.totalFunctions}`)
    console.log(`  已文档化函数: ${report.metrics.documentedFunctions}`)
    
    // 建议
    console.log('\n💡 改进建议:')
    if (report.summary.documentationRate < 80) {
      console.log('  - 增加函数和类的文档注释')
    }
    if (report.summary.testCoverageRate < 80) {
      console.log('  - 增加单元测试覆盖率')
    }
    if (report.summary.highIssues > 0) {
      console.log('  - 优先解决高优先级问题')
    }
    if (report.summary.qualityScore < 80) {
      console.log('  - 进行代码重构以提高质量')
    }
  }
}

// 类型定义
interface QualityIssue {
  category: string
  description: string
  severity: 'low' | 'medium' | 'high'
}

interface QualityMetrics {
  totalFiles: number
  totalLines: number
  documentedFunctions: number
  totalFunctions: number
  typeScriptFiles: number
  testFiles: number
  codeComplexity: number
  duplicateCode: number
}

interface QualitySummary {
  qualityScore: number
  grade: 'A' | 'B' | 'C' | 'D' | 'F'
  totalIssues: number
  highIssues: number
  mediumIssues: number
  lowIssues: number
  documentationRate: number
  testCoverageRate: number
}

interface QualityReport {
  timestamp: string
  metrics: QualityMetrics
  issues: QualityIssue[]
  summary: QualitySummary
}

// 主函数
async function main() {
  const checker = new QualityChecker()
  
  try {
    const report = await checker.checkQuality()
    
    // 保存报告
    const reportPath = path.join(__dirname, '../quality-report.json')
    await fs.writeFile(reportPath, JSON.stringify(report, null, 2))
    console.log(`\n📄 质量报告已保存到: ${reportPath}`)
    
    // 根据质量分数设置退出码
    const exitCode = report.summary.qualityScore >= 80 ? 0 : 1
    process.exit(exitCode)
    
  } catch (error) {
    console.error('❌ 质量检查失败:', error)
    process.exit(1)
  }
}

// 如果直接运行此文件，则执行检查
if (require.main === module) {
  main().catch(console.error)
}

export { QualityChecker, QualityReport, QualityIssue }
