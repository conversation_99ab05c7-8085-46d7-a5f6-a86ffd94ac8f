{"timestamp": "2025-06-13T15:55:25.002Z", "metrics": {"totalFiles": 18, "totalLines": 7815, "documentedFunctions": 115, "totalFunctions": 2868, "typeScriptFiles": 18, "testFiles": 7, "codeComplexity": 403, "duplicateCode": 0}, "issues": [{"category": "documentation", "description": "UsernameGeneratorAPI.ts: 62个函数缺少文档", "severity": "medium"}, {"category": "complexity", "description": "UsernameGeneratorAPI.ts: 代码复杂度较高 (0.11)", "severity": "medium"}, {"category": "typescript", "description": "UsernameGeneratorAPI.ts: 过多使用any类型 (4处)", "severity": "medium"}, {"category": "documentation", "description": "ConstructionEngine.ts: 164个函数缺少文档", "severity": "medium"}, {"category": "documentation", "description": "CulturalAdapter.ts: 59个函数缺少文档", "severity": "medium"}, {"category": "documentation", "description": "SemanticUsernameGenerator.ts: 96个函数缺少文档", "severity": "medium"}, {"category": "typescript", "description": "SemanticUsernameGenerator.ts: 过多使用any类型 (4处)", "severity": "medium"}, {"category": "documentation", "description": "SemanticLexicon.ts: 158个函数缺少文档", "severity": "medium"}, {"category": "complexity", "description": "SemanticLexicon.ts: 代码复杂度较高 (0.11)", "severity": "medium"}, {"category": "documentation", "description": "quality-check.ts: 234个函数缺少文档", "severity": "medium"}, {"category": "complexity", "description": "quality-check.ts: 代码复杂度较高 (0.11)", "severity": "medium"}, {"category": "documentation", "description": "run-tests.ts: 119个函数缺少文档", "severity": "medium"}, {"category": "documentation", "description": "BasicGeneration.test.ts: 231个函数缺少文档", "severity": "medium"}, {"category": "documentation", "description": "Benchmark.test.ts: 238个函数缺少文档", "severity": "medium"}, {"category": "documentation", "description": "simple-test.ts: 153个函数缺少文档", "severity": "medium"}, {"category": "complexity", "description": "simple-test.ts: 代码复杂度较高 (0.14)", "severity": "medium"}, {"category": "documentation", "description": "CoreAlgorithms.test.ts: 217个函数缺少文档", "severity": "medium"}, {"category": "documentation", "description": "DataManager.test.ts: 306个函数缺少文档", "severity": "medium"}, {"category": "documentation", "description": "DataStructures.test.ts: 233个函数缺少文档", "severity": "medium"}, {"category": "documentation", "description": "GenerationEngine.test.ts: 210个函数缺少文档", "severity": "medium"}, {"category": "typescript", "description": "GenerationEngine.test.ts: 过多使用any类型 (7处)", "severity": "medium"}, {"category": "documentation", "description": "MainAPI.test.ts: 245个函数缺少文档", "severity": "medium"}, {"category": "documentation", "description": "index.ts: 28个函数缺少文档", "severity": "medium"}, {"category": "typescript", "description": "index.ts: 过多使用any类型 (5处)", "severity": "medium"}, {"category": "testing", "description": "核心文件缺少测试: UsernameGeneratorAPI.ts", "severity": "high"}, {"category": "testing", "description": "核心文件缺少测试: ConstructionEngine.ts", "severity": "high"}, {"category": "testing", "description": "核心文件缺少测试: CulturalAdapter.ts", "severity": "high"}, {"category": "testing", "description": "核心文件缺少测试: SemanticUsernameGenerator.ts", "severity": "high"}, {"category": "testing", "description": "核心文件缺少测试: SemanticLexicon.ts", "severity": "high"}, {"category": "testing", "description": "测试覆盖率过低: 0.0%", "severity": "high"}], "summary": {"qualityScore": 0, "grade": "F", "totalIssues": 30, "highIssues": 6, "mediumIssues": 24, "lowIssues": 0, "documentationRate": 4.00976290097629, "testCoverageRate": 38.88888888888889}}