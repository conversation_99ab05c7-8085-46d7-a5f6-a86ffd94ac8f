/**
 * 用户名生成器API接口
 * 
 * 提供简洁的API接口，封装复杂的语义生成逻辑
 */

import { SemanticUsernameGenerator } from '../generator/SemanticUsernameGenerator'
import {
  GenerationContext,
  GenerationPreference,
  AgeGroup
} from '../types/SemanticTypes'

/** API请求参数 */
export interface GenerateUsernameRequest {
  // 基础参数
  language?: string
  culture?: string
  count?: number
  
  // 用户偏好
  style?: 'traditional' | 'modern' | 'casual' | 'creative' | 'professional'
  tone?: 'positive' | 'neutral' | 'playful' | 'serious'
  formality?: 'formal' | 'informal' | 'mixed'
  creativity?: 'conservative' | 'moderate' | 'creative' | 'experimental'
  
  // 语义偏好
  themes?: string[] // 如 ['nature', 'technology', 'art']
  avoid_themes?: string[]
  
  // 目标群体
  age_group?: 'children' | 'teenagers' | 'young_adults' | 'middle_aged' | 'seniors'
  gender_preference?: 'male' | 'female' | 'neutral'
  
  // 应用场景
  platform?: 'gaming' | 'social' | 'professional' | 'creative' | 'general'
  domain?: string
  
  // 约束条件
  max_length?: number
  min_length?: number
  avoid_numbers?: boolean
  avoid_special_chars?: boolean
  
  // 高级选项
  cultural_adaptation?: number // 0-1, 文化适应程度
  enable_explanations?: boolean
  return_alternatives?: boolean
  debug_mode?: boolean
}

/** API响应结果 */
export interface GenerateUsernameResponse {
  success: boolean
  data?: {
    usernames: {
      username: string
      quality_score: number
      explanation?: string
      components?: {
        word: string
        meaning: string
        role: string
      }[]
      alternatives?: string[]
    }[]
    generation_info: {
      total_time_ms: number
      attempts_made: number
      success_rate: number
      cultural_adaptation_applied: boolean
    }
    debug_info?: any
  }
  error?: {
    code: string
    message: string
    details?: any
  }
}

/** 用户名生成器API类 */
export class UsernameGeneratorAPI {
  private generator: SemanticUsernameGenerator
  private initialized: boolean = false
  
  constructor() {
    this.generator = new SemanticUsernameGenerator({
      min_quality_threshold: 0.6,
      max_generation_attempts: 10,
      diversity_factor: 0.8,
      enable_caching: true,
      debug_mode: false
    })
  }
  
  /** 初始化API */
  async initialize(): Promise<void> {
    if (this.initialized) return
    
    try {
      await this.generator.initialize()
      
      // 加载默认语言数据
      await this.generator.loadLanguageData('zh')
      await this.generator.loadLanguageData('en')
      
      this.initialized = true
    } catch (error) {
      throw new Error(`Failed to initialize API: ${error}`)
    }
  }
  
  /** 生成用户名 */
  async generateUsernames(request: GenerateUsernameRequest): Promise<GenerateUsernameResponse> {
    try {
      // 确保已初始化
      if (!this.initialized) {
        await this.initialize()
      }
      
      // 验证请求参数
      const validationError = this.validateRequest(request)
      if (validationError) {
        return {
          success: false,
          error: {
            code: 'INVALID_REQUEST',
            message: validationError
          }
        }
      }
      
      // 转换请求参数
      const context = this.buildGenerationContext(request)
      const preferences = this.buildGenerationPreferences(request)
      const count = request.count || 1
      
      // 生成用户名
      const startTime = Date.now()
      const results = count === 1 
        ? [await this.generator.generateUsername(context, preferences)]
        : await this.generator.generateMultipleUsernames(context, preferences, count)
      
      const endTime = Date.now()
      
      // 构建响应
      const response: GenerateUsernameResponse = {
        success: true,
        data: {
          usernames: results.map(result => ({
            username: result.username,
            quality_score: Math.round(result.quality_metrics.overall_score * 100) / 100,
            explanation: request.enable_explanations ? result.explanation : undefined,
            components: request.enable_explanations ? result.components.map(comp => ({
              word: comp.word,
              meaning: this.getWordMeaning(comp),
              role: this.getWordRole(comp)
            })) : undefined,
            alternatives: request.return_alternatives ? result.alternatives : undefined
          })),
          generation_info: {
            total_time_ms: endTime - startTime,
            attempts_made: results.reduce((sum, r) => sum + r.statistics.total_attempts, 0),
            success_rate: results.length / Math.max(1, results.reduce((sum, r) => sum + r.statistics.total_attempts, 0)),
            cultural_adaptation_applied: results.some(r => r.generation_process.cultural_adaptation !== null)
          },
          debug_info: request.debug_mode ? {
            generation_processes: results.map(r => r.generation_process),
            global_statistics: this.generator.getGlobalStatistics()
          } : undefined
        }
      }
      
      return response
      
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'GENERATION_FAILED',
          message: error instanceof Error ? error.message : 'Unknown error occurred',
          details: error
        }
      }
    }
  }
  
  /** 获取生成器统计信息 */
  async getStatistics(): Promise<any> {
    if (!this.initialized) {
      await this.initialize()
    }
    
    return {
      global_statistics: this.generator.getGlobalStatistics(),
      supported_languages: this.generator.getSupportedLanguages(),
      available_constructions: this.generator.getSupportedLanguages().reduce((acc, lang) => {
        acc[lang] = this.generator.getAvailableConstructions(lang).map(c => ({
          id: c.id,
          name: c.name,
          category: c.category,
          description: c.description
        }))
        return acc
      }, {} as any)
    }
  }
  
  /** 清除缓存 */
  async clearCache(): Promise<void> {
    if (this.initialized) {
      this.generator.clearCache()
    }
  }
  
  // ============ 私有辅助方法 ============
  
  /** 验证请求参数 */
  private validateRequest(request: GenerateUsernameRequest): string | null {
    // 基础验证
    if (request.count && (request.count < 1 || request.count > 20)) {
      return 'Count must be between 1 and 20'
    }
    
    if (request.max_length && request.min_length && request.max_length < request.min_length) {
      return 'Max length must be greater than min length'
    }
    
    if (request.cultural_adaptation && (request.cultural_adaptation < 0 || request.cultural_adaptation > 1)) {
      return 'Cultural adaptation must be between 0 and 1'
    }
    
    // 语言支持验证
    const supportedLanguages = ['zh', 'en', 'ja']
    if (request.language && !supportedLanguages.includes(request.language)) {
      return `Unsupported language: ${request.language}. Supported languages: ${supportedLanguages.join(', ')}`
    }
    
    return null
  }
  
  /** 构建生成上下文 */
  private buildGenerationContext(request: GenerateUsernameRequest): GenerationContext {
    const language = request.language || 'zh'
    const culture = request.culture || (language === 'zh' ? 'zh-CN' : `${language}-US`)
    
    return {
      target_language: language,
      target_culture: culture,
      user_preferences: this.buildGenerationPreferences(request),
      domain: request.platform || request.domain,
      platform: request.platform
    }
  }
  
  /** 构建生成偏好 */
  private buildGenerationPreferences(request: GenerateUsernameRequest): GenerationPreference {
    const preferences: GenerationPreference = {}
    
    // 语义域映射
    if (request.themes) {
      preferences.semantic_domains = request.themes
    }
    
    // 情感色调映射
    if (request.tone) {
      const toneMap: { [key: string]: number } = {
        'positive': 0.7,
        'neutral': 0.0,
        'playful': 0.8,
        'serious': -0.2
      }
      preferences.emotional_tone = toneMap[request.tone] || 0.0
    }
    
    // 正式程度映射
    if (request.formality) {
      const formalityMap: { [key: string]: number } = {
        'formal': 0.8,
        'informal': 0.2,
        'mixed': 0.5
      }
      preferences.formality_level = formalityMap[request.formality] || 0.5
    }
    
    // 创意程度映射
    if (request.creativity) {
      const creativityMap: { [key: string]: number } = {
        'conservative': 0.2,
        'moderate': 0.5,
        'creative': 0.8,
        'experimental': 0.9
      }
      preferences.creativity_level = creativityMap[request.creativity] || 0.5
    }
    
    // 年龄群体映射
    if (request.age_group) {
      const ageGroupMap: { [key: string]: AgeGroup } = {
        'children': AgeGroup.CHILDREN,
        'teenagers': AgeGroup.TEENAGERS,
        'young_adults': AgeGroup.YOUNG_ADULTS,
        'middle_aged': AgeGroup.MIDDLE_AGED,
        'seniors': AgeGroup.SENIORS
      }
      preferences.age_group = ageGroupMap[request.age_group]
    }
    
    // 文化适应程度
    if (request.cultural_adaptation !== undefined) {
      preferences.cultural_adaptation = request.cultural_adaptation
    }
    
    // 长度约束
    if (request.max_length || request.min_length) {
      preferences.max_syllables = request.max_length
      preferences.min_syllables = request.min_length
    }
    
    // 禁忌设置
    preferences.avoid_taboos = true
    
    return preferences
  }
  
  /** 获取词汇含义 */
  private getWordMeaning(word: any): string {
    // 简化实现，实际中可以从词典或语义网络获取
    const meaningMap: { [key: string]: string } = {
      '星': '天空中的发光天体，象征希望和梦想',
      '辰': '时光、星宿，代表时间和命运',
      '月': '夜空中的月亮，象征宁静和美好',
      '云': '天空中的云朵，代表自由和变化'
    }
    
    return meaningMap[word.word] || '暂无释义'
  }
  
  /** 获取词汇角色 */
  private getWordRole(word: any): string {
    // 基于词性和语义角色确定
    const roleMap: { [key: string]: string } = {
      'noun': '名词',
      'adjective': '形容词',
      'verb': '动词'
    }
    
    return roleMap[word.pos] || '未知'
  }
}

// ============ 便捷函数 ============

/** 创建API实例的便捷函数 */
export async function createUsernameGeneratorAPI(): Promise<UsernameGeneratorAPI> {
  const api = new UsernameGeneratorAPI()
  await api.initialize()
  return api
}

/** 快速生成用户名的便捷函数 */
export async function quickGenerateUsername(
  options: {
    language?: string
    style?: string
    count?: number
    themes?: string[]
  } = {}
): Promise<string[]> {
  const api = await createUsernameGeneratorAPI()
  
  const request: GenerateUsernameRequest = {
    language: options.language || 'zh',
    style: options.style as any || 'modern',
    count: options.count || 1,
    themes: options.themes,
    enable_explanations: false,
    return_alternatives: false
  }
  
  const response = await api.generateUsernames(request)
  
  if (response.success && response.data) {
    return response.data.usernames.map(u => u.username)
  } else {
    throw new Error(response.error?.message || 'Generation failed')
  }
}

/** 生成带解释的用户名 */
export async function generateUsernameWithExplanation(
  options: GenerateUsernameRequest
): Promise<{ username: string; explanation: string; quality: number }[]> {
  const api = await createUsernameGeneratorAPI()
  
  const request: GenerateUsernameRequest = {
    ...options,
    enable_explanations: true,
    count: options.count || 1
  }
  
  const response = await api.generateUsernames(request)
  
  if (response.success && response.data) {
    return response.data.usernames.map(u => ({
      username: u.username,
      explanation: u.explanation || '无解释',
      quality: u.quality_score
    }))
  } else {
    throw new Error(response.error?.message || 'Generation failed')
  }
}
