/**
 * 简化的中文用户名生成器
 * 
 * 基于V2架构的可工作实现，专注于中文用户名生成
 */

import * as fs from 'fs/promises';
import * as path from 'path';

// 简化的数据结构
interface LexicalWord {
  word: string;
  language: string;
  pos: string;
  domains: string[];
  cultural_scores: { [style: string]: number };
  frequency: number;
  sentiment: number;
  formality: number;
  syllables: number;
  length: number;
}

interface GrammarPattern {
  id: string;
  structure: string[];
  cultural_fitness: { [style: string]: number };
  weight: number;
  examples: string[];
}

interface GenerationOptions {
  style?: 'traditional' | 'modern' | 'elegant' | 'cute';
  themes?: string[];
  count?: number;
  minLength?: number;
  maxLength?: number;
}

interface GenerationResult {
  username: string;
  quality: number;
  components: LexicalWord[];
  pattern: GrammarPattern;
  explanation: string;
}

/**
 * 简化的中文用户名生成器
 */
export class SimpleChineseGenerator {
  private lexicalData: LexicalWord[] = [];
  private grammarPatterns: GrammarPattern[] = [];
  private initialized = false;
  
  /**
   * 初始化生成器
   */
  async initialize(): Promise<void> {
    if (this.initialized) return;
    
    try {
      // 加载词汇数据
      const lexicalPath = path.join(__dirname, '..', 'data', 'lexical', 'zh.json');
      const lexicalContent = await fs.readFile(lexicalPath, 'utf-8');
      const lexicalData = JSON.parse(lexicalContent);
      this.lexicalData = lexicalData.words || [];
      
      // 加载语法模式
      const grammarPath = path.join(__dirname, '..', 'data', 'grammar', 'zh.json');
      const grammarContent = await fs.readFile(grammarPath, 'utf-8');
      const grammarData = JSON.parse(grammarContent);
      this.grammarPatterns = grammarData.patterns || [];
      
      this.initialized = true;
      console.log(`✅ 已加载 ${this.lexicalData.length} 个词汇和 ${this.grammarPatterns.length} 个语法模式`);
    } catch (error) {
      throw new Error(`初始化失败: ${error}`);
    }
  }
  
  /**
   * 生成中文用户名
   */
  async generate(options: GenerationOptions = {}): Promise<GenerationResult[]> {
    await this.initialize();
    
    const {
      style = 'modern',
      themes = [],
      count = 5,
      minLength = 2,
      maxLength = 8
    } = options;
    
    const results: GenerationResult[] = [];
    const maxAttempts = count * 10;
    let attempts = 0;
    
    while (results.length < count && attempts < maxAttempts) {
      attempts++;
      
      try {
        const result = await this.generateSingle(style, themes, minLength, maxLength);
        if (result && !results.some(r => r.username === result.username)) {
          results.push(result);
        }
      } catch (error) {
        // 忽略单次生成失败，继续尝试
      }
    }
    
    // 按质量排序
    results.sort((a, b) => b.quality - a.quality);
    
    return results;
  }
  
  /**
   * 生成单个用户名
   */
  private async generateSingle(
    style: string,
    themes: string[],
    minLength: number,
    maxLength: number
  ): Promise<GenerationResult | null> {
    // 1. 选择语法模式
    const pattern = this.selectPattern(style);
    if (!pattern) {
      throw new Error('无法找到合适的语法模式');
    }
    
    // 2. 为每个位置选择词汇
    const components: LexicalWord[] = [];
    for (let i = 0; i < pattern.structure.length; i++) {
      const pos = pattern.structure[i];
      const word = this.selectWord(pos, style, themes, components);
      if (!word) {
        throw new Error(`无法为位置 ${i} 找到合适的词汇`);
      }
      components.push(word);
    }
    
    // 3. 构造用户名
    const username = components.map(c => c.word).join('');
    
    // 4. 检查长度约束
    if (username.length < minLength || username.length > maxLength) {
      throw new Error('用户名长度不符合要求');
    }
    
    // 5. 计算质量分数
    const quality = this.calculateQuality(username, components, pattern, style);
    
    // 6. 生成解释
    const explanation = this.generateExplanation(components, pattern, style);
    
    return {
      username,
      quality,
      components,
      pattern,
      explanation
    };
  }
  
  /**
   * 选择语法模式
   */
  private selectPattern(style: string): GrammarPattern | null {
    // 过滤适合当前风格的模式
    const suitablePatterns = this.grammarPatterns.filter(pattern => {
      const fitness = pattern.cultural_fitness[style] || 0.5;
      return fitness > 0.4;
    });
    
    if (suitablePatterns.length === 0) {
      return this.grammarPatterns[0] || null;
    }
    
    // 基于文化适应度加权随机选择
    const weights = suitablePatterns.map(p => (p.cultural_fitness[style] || 0.5) * p.weight);
    const selectedIndex = this.weightedRandomSelect(weights);
    
    return suitablePatterns[selectedIndex];
  }
  
  /**
   * 选择词汇 - 改进版
   */
  private selectWord(
    pos: string,
    style: string,
    themes: string[],
    existingComponents: LexicalWord[]
  ): LexicalWord | null {
    // 第一轮过滤：基础条件
    let candidates = this.lexicalData.filter(word => {
      // 词性匹配
      if (word.pos !== pos) return false;

      // 避免重复
      if (existingComponents.some(c => c.word === word.word)) return false;

      return true;
    });

    if (candidates.length === 0) {
      return null;
    }

    // 第二轮过滤：主题匹配
    if (themes.length > 0) {
      const themeMatched = candidates.filter(word =>
        themes.some(theme => word.domains.includes(theme))
      );

      // 如果有主题匹配的词汇，优先使用
      if (themeMatched.length > 0) {
        candidates = themeMatched;
      }
      // 否则保持原候选列表，但会在权重计算中给主题匹配加分
    }

    // 第三轮过滤：风格适应度
    const styleFiltered = candidates.filter(word => {
      const styleScore = word.cultural_scores[style] || 0.5;
      return styleScore > 0.3; // 过滤掉明显不适合的风格
    });

    if (styleFiltered.length > 0) {
      candidates = styleFiltered;
    }

    // 智能权重计算
    const weights = candidates.map(word => this.calculateWordWeight(
      word, style, themes, existingComponents
    ));

    const selectedIndex = this.weightedRandomSelect(weights);
    return candidates[selectedIndex];
  }

  /**
   * 计算词汇权重 - 新增
   */
  private calculateWordWeight(
    word: LexicalWord,
    style: string,
    themes: string[],
    existingComponents: LexicalWord[]
  ): number {
    let weight = 0.1; // 基础权重

    // 1. 文化适应度 (40%)
    const culturalScore = word.cultural_scores[style] || 0.5;
    weight += culturalScore * 0.4;

    // 2. 频率分数 (20%)
    const frequencyScore = word.frequency || 0.5;
    weight += frequencyScore * 0.2;

    // 3. 主题匹配奖励 (15%)
    if (themes.length > 0) {
      const themeMatch = themes.some(theme => word.domains.includes(theme));
      if (themeMatch) {
        weight += 0.15;
      }
    }

    // 4. 语义和谐 (15%)
    if (existingComponents.length > 0) {
      const semanticHarmony = this.calculateSemanticHarmonyWithExisting(word, existingComponents);
      weight += semanticHarmony * 0.15;
    }

    // 5. 情感一致性 (10%)
    if (existingComponents.length > 0) {
      const avgExistingSentiment = existingComponents.reduce((sum, c) => sum + c.sentiment, 0) / existingComponents.length;
      const sentimentDiff = Math.abs(word.sentiment - avgExistingSentiment);
      const sentimentScore = Math.max(0, 1 - sentimentDiff);
      weight += sentimentScore * 0.1;
    }

    // 奖励机制

    // 高质量词汇奖励
    if (word.sentiment > 0.8) {
      weight += 0.05; // 积极情感奖励
    }

    // 平衡性奖励
    if (existingComponents.length > 0) {
      const existingPos = existingComponents.map(c => c.pos);
      if (!existingPos.includes(word.pos)) {
        weight += 0.03; // 词性多样性奖励
      }
    }

    // 现代风格特殊处理
    if (style === 'modern' && word.cultural_scores.modern > 0.8) {
      weight += 0.05;
    }

    // 传统风格特殊处理
    if (style === 'traditional' && word.cultural_scores.traditional > 0.8) {
      weight += 0.05;
    }

    return Math.max(0.01, weight); // 确保最小权重
  }

  /**
   * 计算与现有词汇的语义和谐度 - 新增
   */
  private calculateSemanticHarmonyWithExisting(
    word: LexicalWord,
    existingComponents: LexicalWord[]
  ): number {
    if (existingComponents.length === 0) return 1.0;

    let totalHarmony = 0;

    for (const existing of existingComponents) {
      // 语义域重叠
      const domainOverlap = word.domains.filter(d => existing.domains.includes(d)).length;
      const maxDomains = Math.max(word.domains.length, existing.domains.length);
      const domainScore = domainOverlap / maxDomains;

      // 情感相似度
      const sentimentDiff = Math.abs(word.sentiment - existing.sentiment);
      const sentimentScore = Math.max(0, 1 - sentimentDiff);

      // 正式度相似度
      const formalityDiff = Math.abs(word.formality - existing.formality);
      const formalityScore = Math.max(0, 1 - formalityDiff);

      // 综合和谐度
      const harmony = (domainScore * 0.5 + sentimentScore * 0.3 + formalityScore * 0.2);
      totalHarmony += harmony;
    }

    return totalHarmony / existingComponents.length;
  }
  
  /**
   * 加权随机选择
   */
  private weightedRandomSelect(weights: number[]): number {
    const totalWeight = weights.reduce((sum, weight) => sum + weight, 0);
    if (totalWeight === 0) {
      return Math.floor(Math.random() * weights.length);
    }
    
    let random = Math.random() * totalWeight;
    for (let i = 0; i < weights.length; i++) {
      random -= weights[i];
      if (random <= 0) {
        return i;
      }
    }
    
    return weights.length - 1;
  }
  
  /**
   * 计算质量分数 - 改进版
   */
  private calculateQuality(
    username: string,
    components: LexicalWord[],
    pattern: GrammarPattern,
    style: string
  ): number {
    let totalScore = 0;
    let totalWeight = 0;

    // 1. 文化适应度 (权重: 0.35)
    const culturalWeight = 0.35;
    const culturalScore = this.calculateCulturalFitness(components, pattern, style);
    totalScore += culturalScore * culturalWeight;
    totalWeight += culturalWeight;

    // 2. 语义一致性 (权重: 0.25)
    const semanticWeight = 0.25;
    const semanticScore = this.calculateSemanticCoherence(components);
    totalScore += semanticScore * semanticWeight;
    totalWeight += semanticWeight;

    // 3. 音韵和谐 (权重: 0.20)
    const phoneticWeight = 0.20;
    const phoneticScore = this.calculatePhoneticHarmony(components);
    totalScore += phoneticScore * phoneticWeight;
    totalWeight += phoneticWeight;

    // 4. 记忆性 (权重: 0.12)
    const memorabilityWeight = 0.12;
    const memorabilityScore = this.calculateMemorability(username, components);
    totalScore += memorabilityScore * memorabilityWeight;
    totalWeight += memorabilityWeight;

    // 5. 独特性 (权重: 0.08)
    const uniquenessWeight = 0.08;
    const uniquenessScore = this.calculateUniqueness(username, components);
    totalScore += uniquenessScore * uniquenessWeight;
    totalWeight += uniquenessWeight;

    // 计算基础分数
    const baseScore = totalScore / totalWeight;

    // 应用奖励和惩罚
    let finalScore = baseScore;

    // 长度奖励/惩罚
    if (username.length === 2) {
      finalScore += 0.1; // 2字用户名奖励
    } else if (username.length === 3) {
      finalScore += 0.05; // 3字用户名小奖励
    } else if (username.length > 5) {
      finalScore -= 0.1; // 过长惩罚
    }

    // 频率奖励
    const avgFrequency = components.reduce((sum, c) => sum + c.frequency, 0) / components.length;
    if (avgFrequency > 0.8) {
      finalScore += 0.05; // 高频词奖励
    }

    // 情感奖励
    const avgSentiment = components.reduce((sum, c) => sum + c.sentiment, 0) / components.length;
    if (avgSentiment > 0.8) {
      finalScore += 0.05; // 积极情感奖励
    }

    return Math.max(0, Math.min(1.0, finalScore));
  }

  /**
   * 计算文化适应度 - 改进版
   */
  private calculateCulturalFitness(
    components: LexicalWord[],
    pattern: GrammarPattern,
    style: string
  ): number {
    // 词汇文化适应度
    const wordScores = components.map(c => c.cultural_scores[style] || 0.5);
    const avgWordScore = wordScores.reduce((sum, s) => sum + s, 0) / wordScores.length;

    // 模式文化适应度
    const patternScore = pattern.cultural_fitness[style] || 0.5;

    // 文化一致性（词汇间的文化风格是否一致）
    const culturalVariance = this.calculateVariance(wordScores);
    const consistencyScore = Math.max(0, 1 - culturalVariance * 2);

    // 综合评分
    return (avgWordScore * 0.5 + patternScore * 0.3 + consistencyScore * 0.2);
  }
  
  /**
   * 计算语义一致性
   */
  private calculateSemanticCoherence(components: LexicalWord[]): number {
    if (components.length <= 1) return 1.0;
    
    // 检查语义域重叠
    const allDomains = components.flatMap(c => c.domains);
    const uniqueDomains = new Set(allDomains);
    const overlapRatio = (allDomains.length - uniqueDomains.size) / allDomains.length;
    
    // 检查情感一致性
    const sentiments = components.map(c => c.sentiment);
    const sentimentVariance = this.calculateVariance(sentiments);
    const sentimentScore = Math.max(0, 1 - sentimentVariance);
    
    return (overlapRatio * 0.6 + sentimentScore * 0.4);
  }
  
  /**
   * 计算音韵和谐 - 改进版
   */
  private calculatePhoneticHarmony(components: LexicalWord[]): number {
    if (components.length <= 1) return 1.0;

    let totalScore = 0;
    let totalWeight = 0;

    // 1. 音节数和谐 (权重: 0.4)
    const syllableWeight = 0.4;
    const syllables = components.map(c => c.syllables);
    const syllableScore = this.calculateSyllableHarmony(syllables);
    totalScore += syllableScore * syllableWeight;
    totalWeight += syllableWeight;

    // 2. 长度和谐 (权重: 0.3)
    const lengthWeight = 0.3;
    const lengths = components.map(c => c.length);
    const lengthScore = this.calculateLengthHarmony(lengths);
    totalScore += lengthScore * lengthWeight;
    totalWeight += lengthWeight;

    // 3. 声调和谐 (权重: 0.2) - 简化实现
    const toneWeight = 0.2;
    const toneScore = this.calculateToneHarmony(components);
    totalScore += toneScore * toneWeight;
    totalWeight += toneWeight;

    // 4. 韵律节奏 (权重: 0.1)
    const rhythmWeight = 0.1;
    const rhythmScore = this.calculateRhythmHarmony(components);
    totalScore += rhythmScore * rhythmWeight;
    totalWeight += rhythmWeight;

    return totalScore / totalWeight;
  }

  /**
   * 计算音节和谐度
   */
  private calculateSyllableHarmony(syllables: number[]): number {
    if (syllables.length <= 1) return 1.0;

    // 偏好全单音节或有规律的音节组合
    const allSingle = syllables.every(s => s === 1);
    if (allSingle) return 1.0; // 全单音节最和谐

    // 计算音节变化的平滑度
    const variance = this.calculateVariance(syllables);
    const varianceScore = Math.max(0, 1 - variance);

    // 检查是否有好的音节模式
    const patternScore = this.checkSyllablePattern(syllables);

    return (varianceScore * 0.6 + patternScore * 0.4);
  }

  /**
   * 计算长度和谐度
   */
  private calculateLengthHarmony(lengths: number[]): number {
    if (lengths.length <= 1) return 1.0;

    // 偏好相同长度或递增/递减模式
    const allSame = lengths.every(l => l === lengths[0]);
    if (allSame) return 1.0;

    // 检查是否是递增或递减模式
    const isIncreasing = this.isIncreasingSequence(lengths);
    const isDecreasing = this.isDecreasingSequence(lengths);
    if (isIncreasing || isDecreasing) return 0.9;

    // 计算长度变化的平滑度
    const variance = this.calculateVariance(lengths);
    return Math.max(0, 1 - variance / 2);
  }

  /**
   * 计算声调和谐度 - 简化实现
   */
  private calculateToneHarmony(components: LexicalWord[]): number {
    // 基于词汇的情感和正式度来模拟声调和谐
    const sentiments = components.map(c => c.sentiment);
    const formalities = components.map(c => c.formality);

    const sentimentVariance = this.calculateVariance(sentiments);
    const formalityVariance = this.calculateVariance(formalities);

    const sentimentScore = Math.max(0, 1 - sentimentVariance);
    const formalityScore = Math.max(0, 1 - formalityVariance);

    return (sentimentScore * 0.6 + formalityScore * 0.4);
  }

  /**
   * 计算韵律节奏
   */
  private calculateRhythmHarmony(components: LexicalWord[]): number {
    if (components.length <= 1) return 1.0;

    // 基于词汇的音节和长度创建节奏模式
    const rhythm = components.map(c => c.syllables * c.length);

    // 检查是否有好的节奏模式
    if (rhythm.length === 2) {
      // 二字组合：偏好相同节奏或简单对比
      if (rhythm[0] === rhythm[1]) return 1.0; // 相同节奏
      if (Math.abs(rhythm[0] - rhythm[1]) === 1) return 0.8; // 轻微对比
    }

    if (rhythm.length === 3) {
      // 三字组合：偏好对称或递进
      if (rhythm[0] === rhythm[2]) return 0.9; // 对称
      if (rhythm[0] < rhythm[1] && rhythm[1] > rhythm[2]) return 0.8; // 山形
    }

    // 默认基于变化平滑度
    const variance = this.calculateVariance(rhythm);
    return Math.max(0.3, 1 - variance / 2);
  }

  /**
   * 检查音节模式
   */
  private checkSyllablePattern(syllables: number[]): number {
    if (syllables.length === 2) {
      // 二字组合模式
      if (syllables[0] === 1 && syllables[1] === 1) return 1.0; // 1-1 最佳
      if (syllables[0] === 1 && syllables[1] === 2) return 0.8; // 1-2 不错
      if (syllables[0] === 2 && syllables[1] === 1) return 0.7; // 2-1 可以
    }

    if (syllables.length === 3) {
      // 三字组合模式
      if (syllables.every(s => s === 1)) return 1.0; // 1-1-1 最佳
      if (syllables[0] === 1 && syllables[1] === 1 && syllables[2] === 2) return 0.8; // 1-1-2
    }

    return 0.5; // 默认分数
  }

  /**
   * 检查是否为递增序列
   */
  private isIncreasingSequence(arr: number[]): boolean {
    for (let i = 1; i < arr.length; i++) {
      if (arr[i] <= arr[i - 1]) return false;
    }
    return true;
  }

  /**
   * 检查是否为递减序列
   */
  private isDecreasingSequence(arr: number[]): boolean {
    for (let i = 1; i < arr.length; i++) {
      if (arr[i] >= arr[i - 1]) return false;
    }
    return true;
  }
  
  /**
   * 计算记忆性 - 改进版
   */
  private calculateMemorability(username: string, components: LexicalWord[]): number {
    let score = 0.3; // 基础分

    // 长度评分
    if (username.length === 2) {
      score += 0.4; // 2字最易记忆
    } else if (username.length === 3) {
      score += 0.3; // 3字也不错
    } else if (username.length === 4) {
      score += 0.2; // 4字稍难
    } else if (username.length > 4) {
      score -= 0.1; // 过长难记忆
    }

    // 常用字加分
    const commonChars = ['星', '月', '云', '风', '雨', '花', '美', '好', '新', '亮', '心', '光', '梦'];
    const commonCharCount = username.split('').filter(char => commonChars.includes(char)).length;
    score += commonCharCount * 0.1;

    // 高频词加分
    const avgFrequency = components.reduce((sum, c) => sum + c.frequency, 0) / components.length;
    score += avgFrequency * 0.2;

    // 音节和谐加分
    const syllables = components.map(c => c.syllables);
    if (syllables.every(s => s === 1)) {
      score += 0.1; // 全单音节易读
    }

    // 重复字符惩罚
    const chars = username.split('');
    const uniqueChars = new Set(chars);
    if (chars.length !== uniqueChars.size) {
      score -= 0.1; // 重复字符降低记忆性
    }

    return Math.max(0, Math.min(1.0, score));
  }

  /**
   * 计算独特性 - 改进版
   */
  private calculateUniqueness(username: string, components: LexicalWord[]): number {
    let score = 0.5; // 基础分

    // 字符多样性
    const charCount = username.length;
    const uniqueChars = new Set(username.split('')).size;
    const diversityRatio = uniqueChars / charCount;
    score += diversityRatio * 0.3;

    // 低频词加分
    const avgFrequency = components.reduce((sum, c) => sum + c.frequency, 0) / components.length;
    score += (1 - avgFrequency) * 0.2; // 低频词更独特

    // 词汇组合稀有度
    const domains = components.flatMap(c => c.domains);
    const uniqueDomains = new Set(domains);
    if (uniqueDomains.size > 1) {
      score += 0.2; // 跨域组合更独特
    }

    // 现代词汇加分
    const hasModernWord = components.some(c =>
      c.cultural_scores.modern > 0.8 ||
      ['酷', '萌', '炫'].includes(c.word)
    );
    if (hasModernWord) {
      score += 0.1;
    }

    // 长度独特性
    if (username.length === 1 || username.length > 4) {
      score += 0.1; // 极短或较长的用户名更独特
    }

    return Math.max(0, Math.min(1.0, score));
  }
  
  /**
   * 计算方差
   */
  private calculateVariance(values: number[]): number {
    if (values.length <= 1) return 0;
    
    const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
    const squaredDiffs = values.map(val => Math.pow(val - mean, 2));
    return squaredDiffs.reduce((sum, diff) => sum + diff, 0) / values.length;
  }
  
  /**
   * 生成解释 - 改进版
   */
  private generateExplanation(
    components: LexicalWord[],
    pattern: GrammarPattern,
    style: string
  ): string {
    // 基础组合描述
    const componentDescriptions = components.map(c => {
      const domains = c.domains.join('、');
      return `"${c.word}"(${domains})`;
    }).join(' + ');

    // 文化内涵分析
    const culturalMeaning = this.analyzeCulturalMeaning(components, style);

    // 音韵特色
    const phoneticFeature = this.analyzePhoneticFeature(components);

    // 情感色彩
    const emotionalTone = this.analyzeEmotionalTone(components);

    let explanation = `采用${pattern.name || pattern.id}模式，组合${componentDescriptions}`;

    if (culturalMeaning) {
      explanation += `，${culturalMeaning}`;
    }

    if (phoneticFeature) {
      explanation += `，${phoneticFeature}`;
    }

    if (emotionalTone) {
      explanation += `，${emotionalTone}`;
    }

    explanation += `，体现${this.getStyleDescription(style)}风格特色。`;

    return explanation;
  }

  /**
   * 分析文化内涵
   */
  private analyzeCulturalMeaning(components: LexicalWord[], style: string): string {
    const meanings: string[] = [];

    // 检查自然元素组合
    const natureWords = components.filter(c => c.domains.includes('nature'));
    if (natureWords.length >= 2) {
      const natureElements = natureWords.map(w => w.word);
      if (natureElements.includes('星') && natureElements.includes('月')) {
        meanings.push('寓意星月同辉，光明永恒');
      } else if (natureElements.includes('云') && natureElements.includes('水')) {
        meanings.push('象征行云流水，自然和谐');
      } else if (natureWords.length >= 2) {
        meanings.push('体现天人合一的自然美学');
      }
    }

    // 检查情感表达
    const emotionWords = components.filter(c => c.domains.includes('emotion'));
    if (emotionWords.length > 0) {
      const avgSentiment = emotionWords.reduce((sum, w) => sum + w.sentiment, 0) / emotionWords.length;
      if (avgSentiment > 0.8) {
        meanings.push('传达积极向上的人生态度');
      } else if (avgSentiment > 0.6) {
        meanings.push('表达温和平静的内心世界');
      }
    }

    // 检查文化传承
    const cultureWords = components.filter(c => c.domains.includes('culture'));
    if (cultureWords.length > 0) {
      meanings.push('承载深厚的文化底蕴');
    }

    // 检查现代元素
    const techWords = components.filter(c => c.domains.includes('tech'));
    if (techWords.length > 0 && style === 'modern') {
      meanings.push('融合时代特色与科技美感');
    }

    return meanings.length > 0 ? meanings[0] : '';
  }

  /**
   * 分析音韵特色
   */
  private analyzePhoneticFeature(components: LexicalWord[]): string {
    const features: string[] = [];

    // 检查音节特色
    const syllables = components.map(c => c.syllables);
    if (syllables.every(s => s === 1)) {
      features.push('音节简洁明快');
    } else if (syllables.length === 2 && syllables[0] === 1 && syllables[1] === 2) {
      features.push('音律先简后繁，富有层次');
    }

    // 检查长度特色
    const lengths = components.map(c => c.length);
    if (lengths.every(l => l === 1)) {
      features.push('字形简约');
    }

    // 检查重复字符
    const allChars = components.flatMap(c => c.word.split(''));
    const uniqueChars = new Set(allChars);
    if (allChars.length !== uniqueChars.size) {
      features.push('音韵回环，朗朗上口');
    }

    return features.length > 0 ? features[0] : '';
  }

  /**
   * 分析情感色彩
   */
  private analyzeEmotionalTone(components: LexicalWord[]): string {
    const avgSentiment = components.reduce((sum, c) => sum + c.sentiment, 0) / components.length;
    const avgFormality = components.reduce((sum, c) => sum + c.formality, 0) / components.length;

    if (avgSentiment > 0.8) {
      if (avgFormality > 0.7) {
        return '情感积极而庄重';
      } else {
        return '情感明朗活泼';
      }
    } else if (avgSentiment > 0.6) {
      if (avgFormality > 0.7) {
        return '情感温和雅致';
      } else {
        return '情感平和自然';
      }
    } else if (avgSentiment < 0.4) {
      return '情感深沉内敛';
    }

    return '';
  }

  /**
   * 获取风格描述
   */
  private getStyleDescription(style: string): string {
    const descriptions: { [key: string]: string } = {
      'traditional': '传统典雅',
      'modern': '现代时尚',
      'cute': '可爱甜美',
      'cool': '酷炫个性',
      'elegant': '优雅精致',
      'playful': '活泼俏皮',
      'mysterious': '神秘深邃',
      'powerful': '强劲有力'
    };

    return descriptions[style] || style;
  }
}

/**
 * 便捷函数：快速生成中文用户名
 */
export async function generateChineseUsernames(options: GenerationOptions = {}): Promise<string[]> {
  const generator = new SimpleChineseGenerator();
  const results = await generator.generate(options);
  return results.map(r => r.username);
}

/**
 * 便捷函数：生成带详细信息的中文用户名
 */
export async function generateChineseUsernamesWithDetails(options: GenerationOptions = {}): Promise<GenerationResult[]> {
  const generator = new SimpleChineseGenerator();
  return await generator.generate(options);
}
