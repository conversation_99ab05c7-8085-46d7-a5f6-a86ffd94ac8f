/**
 * 语义词库系统
 * 
 * 基于认知语言学理论的语义词库实现，支持多维语义检索和关系推理
 */

import {
  SemanticLexicalEntry,
  SemanticDomain,
  ConceptualCategory,
  SemanticFeature,
  SemanticRelation,
  Collocation,
  PartOfSpeech,
  Register,
  AgeGroup,
  GenerationPreference
} from '../types/SemanticTypes'

/** 语义检索查询 */
export interface SemanticQuery {
  // 基础过滤
  pos?: PartOfSpeech[]
  register?: Register[]
  language?: string
  
  // 语义过滤
  semantic_domains?: string[]
  conceptual_categories?: string[]
  semantic_features?: { feature: string; value: any; tolerance?: number }[]
  
  // 情感和文化过滤
  emotional_valence_range?: [number, number]
  arousal_level_range?: [number, number]
  cultural_appropriateness?: { culture: string; min_score: number }
  
  // 使用特征过滤
  frequency_range?: [number, number]
  trend_score_range?: [number, number]
  age_groups?: AgeGroup[]
  
  // 音韵过滤
  syllable_count_range?: [number, number]
  phonetic_features?: string[]
  
  // 关系过滤
  related_to?: { word: string; relation_types: string[]; min_strength: number }
  collocates_with?: { word: string; min_strength: number }
  
  // 排序和限制
  sort_by?: 'frequency' | 'trend_score' | 'semantic_similarity' | 'random'
  limit?: number
  offset?: number
}

/** 语义相似度计算结果 */
export interface SemanticSimilarity {
  word1: string
  word2: string
  similarity_score: number
  similarity_components: {
    semantic_domain_overlap: number
    feature_similarity: number
    relation_similarity: number
    cultural_similarity: number
  }
}

/** 语义词库类 */
export class SemanticLexicon {
  private entries: Map<string, SemanticLexicalEntry> = new Map()
  private domainIndex: Map<string, Set<string>> = new Map() // domain -> words
  private categoryIndex: Map<string, Set<string>> = new Map() // category -> words
  private posIndex: Map<PartOfSpeech, Set<string>> = new Map() // pos -> words
  private relationIndex: Map<string, Map<string, SemanticRelation[]>> = new Map() // word -> related_word -> relations
  private collocationIndex: Map<string, Map<string, Collocation>> = new Map() // word -> partner -> collocation
  
  // 语义域和概念类别定义
  private domains: Map<string, SemanticDomain> = new Map()
  private categories: Map<string, ConceptualCategory> = new Map()
  
  constructor() {
    this.initializeIndices()
  }
  
  private initializeIndices(): void {
    // 初始化词性索引
    Object.values(PartOfSpeech).forEach(pos => {
      this.posIndex.set(pos, new Set())
    })
  }
  
  // ============ 数据管理方法 ============
  
  /** 添加词汇条目 */
  addEntry(entry: SemanticLexicalEntry): void {
    const key = `${entry.language}:${entry.word}`
    this.entries.set(key, entry)
    this.updateIndices(entry)
  }
  
  /** 批量添加词汇条目 */
  addEntries(entries: SemanticLexicalEntry[]): void {
    entries.forEach(entry => this.addEntry(entry))
  }
  
  /** 获取词汇条目 */
  getEntry(word: string, language: string): SemanticLexicalEntry | undefined {
    return this.entries.get(`${language}:${word}`)
  }
  
  /** 更新索引 */
  private updateIndices(entry: SemanticLexicalEntry): void {
    const word = entry.word
    
    // 更新语义域索引
    entry.semantic_domains.forEach(domain => {
      if (!this.domainIndex.has(domain)) {
        this.domainIndex.set(domain, new Set())
      }
      this.domainIndex.get(domain)!.add(word)
    })
    
    // 更新概念类别索引
    entry.conceptual_categories.forEach(category => {
      if (!this.categoryIndex.has(category)) {
        this.categoryIndex.set(category, new Set())
      }
      this.categoryIndex.get(category)!.add(word)
    })
    
    // 更新词性索引
    this.posIndex.get(entry.pos)?.add(word)
    
    // 更新关系索引
    if (!this.relationIndex.has(word)) {
      this.relationIndex.set(word, new Map())
    }
    entry.semantic_relations.forEach(relation => {
      const relatedWord = relation.target
      if (!this.relationIndex.get(word)!.has(relatedWord)) {
        this.relationIndex.get(word)!.set(relatedWord, [])
      }
      this.relationIndex.get(word)!.get(relatedWord)!.push(relation)
    })
    
    // 更新搭配索引
    if (!this.collocationIndex.has(word)) {
      this.collocationIndex.set(word, new Map())
    }
    entry.collocations.forEach(collocation => {
      this.collocationIndex.get(word)!.set(collocation.partner, collocation)
    })
  }
  
  // ============ 语义域和类别管理 ============
  
  /** 添加语义域 */
  addDomain(domain: SemanticDomain): void {
    this.domains.set(domain.id, domain)
  }
  
  /** 添加概念类别 */
  addCategory(category: ConceptualCategory): void {
    this.categories.set(category.id, category)
  }
  
  /** 获取语义域层次结构 */
  getDomainHierarchy(domainId: string): string[] {
    const hierarchy: string[] = []
    let currentDomain = this.domains.get(domainId)
    
    while (currentDomain) {
      hierarchy.unshift(currentDomain.id)
      currentDomain = currentDomain.parent ? this.domains.get(currentDomain.parent) : undefined
    }
    
    return hierarchy
  }
  
  // ============ 语义检索方法 ============
  
  /** 语义检索 */
  search(query: SemanticQuery): SemanticLexicalEntry[] {
    let candidates = new Set<string>()
    
    // 如果没有指定语言，默认搜索所有语言
    const targetLanguage = query.language
    
    // 初始候选集
    if (query.semantic_domains && query.semantic_domains.length > 0) {
      const domainCandidates = new Set<string>()
      query.semantic_domains.forEach(domain => {
        const wordsInDomain = this.domainIndex.get(domain)
        if (wordsInDomain) {
          wordsInDomain.forEach(word => domainCandidates.add(word))
        }
      })
      candidates = domainCandidates
    } else if (query.pos && query.pos.length > 0) {
      const posCandidates = new Set<string>()
      query.pos.forEach(pos => {
        const wordsWithPos = this.posIndex.get(pos)
        if (wordsWithPos) {
          wordsWithPos.forEach(word => posCandidates.add(word))
        }
      })
      candidates = posCandidates
    } else {
      // 如果没有指定过滤条件，使用所有词汇
      this.entries.forEach((entry, key) => {
        if (!targetLanguage || key.startsWith(`${targetLanguage}:`)) {
          candidates.add(entry.word)
        }
      })
    }
    
    // 应用过滤条件
    const results: SemanticLexicalEntry[] = []
    
    candidates.forEach(word => {
      const entry = this.getEntry(word, targetLanguage || 'zh') // 默认中文
      if (entry && this.matchesQuery(entry, query)) {
        results.push(entry)
      }
    })
    
    // 排序
    this.sortResults(results, query.sort_by || 'frequency')
    
    // 分页
    const start = query.offset || 0
    const end = query.limit ? start + query.limit : results.length
    
    return results.slice(start, end)
  }
  
  /** 检查条目是否匹配查询 */
  private matchesQuery(entry: SemanticLexicalEntry, query: SemanticQuery): boolean {
    // 词性过滤
    if (query.pos && !query.pos.includes(entry.pos)) {
      return false
    }
    
    // 语域过滤
    if (query.register && !query.register.includes(entry.register)) {
      return false
    }
    
    // 概念类别过滤
    if (query.conceptual_categories) {
      const hasMatchingCategory = query.conceptual_categories.some(cat => 
        entry.conceptual_categories.includes(cat)
      )
      if (!hasMatchingCategory) return false
    }
    
    // 情感价值过滤
    if (query.emotional_valence_range) {
      const [min, max] = query.emotional_valence_range
      if (entry.emotional_valence < min || entry.emotional_valence > max) {
        return false
      }
    }
    
    // 激活程度过滤
    if (query.arousal_level_range) {
      const [min, max] = query.arousal_level_range
      if (entry.arousal_level < min || entry.arousal_level > max) {
        return false
      }
    }
    
    // 频率过滤
    if (query.frequency_range) {
      const [min, max] = query.frequency_range
      if (entry.frequency < min || entry.frequency > max) {
        return false
      }
    }
    
    // 流行度过滤
    if (query.trend_score_range) {
      const [min, max] = query.trend_score_range
      if (entry.trend_score < min || entry.trend_score > max) {
        return false
      }
    }
    
    // 年龄群体过滤
    if (query.age_groups) {
      const hasMatchingAgeGroup = query.age_groups.some(age => 
        entry.age_group_preference.includes(age)
      )
      if (!hasMatchingAgeGroup) return false
    }
    
    // 音节数过滤
    if (query.syllable_count_range) {
      const [min, max] = query.syllable_count_range
      if (entry.syllable_count < min || entry.syllable_count > max) {
        return false
      }
    }
    
    return true
  }
  
  /** 排序结果 */
  private sortResults(results: SemanticLexicalEntry[], sortBy: string): void {
    switch (sortBy) {
      case 'frequency':
        results.sort((a, b) => b.frequency - a.frequency)
        break
      case 'trend_score':
        results.sort((a, b) => b.trend_score - a.trend_score)
        break
      case 'random':
        for (let i = results.length - 1; i > 0; i--) {
          const j = Math.floor(Math.random() * (i + 1));
          [results[i], results[j]] = [results[j], results[i]]
        }
        break
      default:
        // 默认按频率排序
        results.sort((a, b) => b.frequency - a.frequency)
    }
  }
  
  // ============ 语义相似度计算 ============
  
  /** 计算语义相似度 */
  calculateSimilarity(word1: string, word2: string, language: string): SemanticSimilarity | null {
    const entry1 = this.getEntry(word1, language)
    const entry2 = this.getEntry(word2, language)
    
    if (!entry1 || !entry2) return null
    
    // 语义域重叠度
    const domainOverlap = this.calculateDomainOverlap(entry1, entry2)
    
    // 特征相似度
    const featureSimilarity = this.calculateFeatureSimilarity(entry1, entry2)
    
    // 关系相似度
    const relationSimilarity = this.calculateRelationSimilarity(entry1, entry2)
    
    // 文化相似度
    const culturalSimilarity = this.calculateCulturalSimilarity(entry1, entry2)
    
    // 综合相似度 (加权平均)
    const overallSimilarity = (
      domainOverlap * 0.3 +
      featureSimilarity * 0.3 +
      relationSimilarity * 0.2 +
      culturalSimilarity * 0.2
    )
    
    return {
      word1,
      word2,
      similarity_score: overallSimilarity,
      similarity_components: {
        semantic_domain_overlap: domainOverlap,
        feature_similarity: featureSimilarity,
        relation_similarity: relationSimilarity,
        cultural_similarity: culturalSimilarity
      }
    }
  }
  
  /** 计算语义域重叠度 */
  private calculateDomainOverlap(entry1: SemanticLexicalEntry, entry2: SemanticLexicalEntry): number {
    const domains1 = new Set(entry1.semantic_domains)
    const domains2 = new Set(entry2.semantic_domains)
    
    const intersection = new Set([...domains1].filter(d => domains2.has(d)))
    const union = new Set([...domains1, ...domains2])
    
    return union.size > 0 ? intersection.size / union.size : 0
  }
  
  /** 计算特征相似度 */
  private calculateFeatureSimilarity(entry1: SemanticLexicalEntry, entry2: SemanticLexicalEntry): number {
    // 简化实现：基于情感价值和激活程度的相似度
    const valenceDiff = Math.abs(entry1.emotional_valence - entry2.emotional_valence)
    const arousalDiff = Math.abs(entry1.arousal_level - entry2.arousal_level)
    
    const valenceSimilarity = 1 - valenceDiff / 2 // 归一化到 [0, 1]
    const arousalSimilarity = 1 - arousalDiff
    
    return (valenceSimilarity + arousalSimilarity) / 2
  }
  
  /** 计算关系相似度 */
  private calculateRelationSimilarity(entry1: SemanticLexicalEntry, entry2: SemanticLexicalEntry): number {
    // 基于共同关联词的相似度
    const relations1 = new Set(entry1.semantic_relations.map(r => r.target))
    const relations2 = new Set(entry2.semantic_relations.map(r => r.target))
    
    const intersection = new Set([...relations1].filter(r => relations2.has(r)))
    const union = new Set([...relations1, ...relations2])
    
    return union.size > 0 ? intersection.size / union.size : 0
  }
  
  /** 计算文化相似度 */
  private calculateCulturalSimilarity(entry1: SemanticLexicalEntry, entry2: SemanticLexicalEntry): number {
    // 基于语域和年龄群体偏好的相似度
    const registerMatch = entry1.register === entry2.register ? 1 : 0
    
    const ageGroups1 = new Set(entry1.age_group_preference)
    const ageGroups2 = new Set(entry2.age_group_preference)
    const ageIntersection = new Set([...ageGroups1].filter(a => ageGroups2.has(a)))
    const ageUnion = new Set([...ageGroups1, ...ageGroups2])
    const ageOverlap = ageUnion.size > 0 ? ageIntersection.size / ageUnion.size : 0
    
    return (registerMatch + ageOverlap) / 2
  }
  
  // ============ 智能推荐方法 ============
  
  /** 基于偏好推荐词汇 */
  recommendWords(preferences: GenerationPreference, language: string, count: number = 10): SemanticLexicalEntry[] {
    const query: SemanticQuery = {
      language,
      semantic_domains: preferences.semantic_domains,
      emotional_valence_range: preferences.emotional_tone ? 
        [preferences.emotional_tone - 0.3, preferences.emotional_tone + 0.3] : undefined,
      age_groups: preferences.age_group ? [preferences.age_group] : undefined,
      sort_by: 'trend_score',
      limit: count * 2 // 获取更多候选，然后进行智能筛选
    }
    
    const candidates = this.search(query)
    
    // 基于偏好进行智能筛选和排序
    return this.rankByPreferences(candidates, preferences).slice(0, count)
  }
  
  /** 基于偏好对词汇进行排序 */
  private rankByPreferences(words: SemanticLexicalEntry[], preferences: GenerationPreference): SemanticLexicalEntry[] {
    return words.map(word => ({
      word,
      score: this.calculatePreferenceScore(word, preferences)
    }))
    .sort((a, b) => b.score - a.score)
    .map(item => item.word)
  }
  
  /** 计算偏好匹配分数 */
  private calculatePreferenceScore(word: SemanticLexicalEntry, preferences: GenerationPreference): number {
    let score = 0
    
    // 语义域匹配
    if (preferences.semantic_domains) {
      const domainMatch = preferences.semantic_domains.some(domain => 
        word.semantic_domains.includes(domain)
      )
      if (domainMatch) score += 0.3
    }
    
    // 情感色调匹配
    if (preferences.emotional_tone !== undefined) {
      const emotionalDistance = Math.abs(word.emotional_valence - preferences.emotional_tone)
      score += (1 - emotionalDistance / 2) * 0.2
    }
    
    // 正式程度匹配
    if (preferences.formality_level !== undefined) {
      // 简化实现：基于语域的正式程度评估
      const formalityScore = word.register === Register.FORMAL ? 1 : 
                           word.register === Register.INFORMAL ? 0.3 : 0.6
      const formalityDistance = Math.abs(formalityScore - preferences.formality_level)
      score += (1 - formalityDistance) * 0.2
    }
    
    // 创意程度匹配
    if (preferences.creativity_level !== undefined) {
      // 基于流行度的创意程度评估（流行度低的词汇更有创意）
      const creativityScore = 1 - word.trend_score
      const creativityDistance = Math.abs(creativityScore - preferences.creativity_level)
      score += (1 - creativityDistance) * 0.15
    }
    
    // 基础质量分数
    score += word.confidence * 0.15
    
    return score
  }
}
