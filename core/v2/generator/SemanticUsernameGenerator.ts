/**
 * 基于第一性原理的语义用户名生成器
 * 
 * 整合语义词库、构式引擎和文化适配器，实现高质量的多语言用户名生成
 */

import { SemanticLexicon } from '../lexicon/SemanticLexicon'
import { ConstructionEngine, ConstructionInstance } from '../construction/ConstructionEngine'
import { CulturalAdapter } from '../cultural/CulturalAdapter'

import {
  GenerationContext,
  GenerationPreference,
  GenerationResult,
  QualityMetrics,
  SemanticLexicalEntry
} from '../types/SemanticTypes'

import {
  ConstructionGenerationParams,
  ConstructionTemplate
} from '../types/ConstructionTypes'

/** 生成器配置 */
export interface GeneratorConfig {
  // 质量控制
  min_quality_threshold: number
  max_generation_attempts: number
  diversity_factor: number // 0-1, 结果多样性要求
  
  // 性能控制
  enable_caching: boolean
  cache_size_limit: number
  timeout_ms: number
  
  // 调试选项
  debug_mode: boolean
  log_generation_process: boolean
  return_intermediate_results: boolean
}

/** 生成统计信息 */
export interface GenerationStatistics {
  total_attempts: number
  successful_generations: number
  average_quality_score: number
  generation_time_ms: number
  cache_hit_rate: number
  
  // 详细统计
  construction_usage: { [construction_id: string]: number }
  semantic_domain_usage: { [domain: string]: number }
  quality_distribution: { [quality_range: string]: number }
}

/** 扩展的生成结果 */
export interface ExtendedGenerationResult extends GenerationResult {
  generation_process: {
    cultural_adaptation: any
    construction_matches: any[]
    selected_construction: string
    slot_filling_details: any
  }
  statistics: GenerationStatistics
  debug_info?: any
}

/** 语义用户名生成器类 */
export class SemanticUsernameGenerator {
  private lexicon: SemanticLexicon
  private constructionEngine: ConstructionEngine
  private culturalAdapter: CulturalAdapter
  private config: GeneratorConfig
  
  // 缓存
  private resultCache: Map<string, ExtendedGenerationResult[]> = new Map()
  private statisticsCache: Map<string, GenerationStatistics> = new Map()
  
  // 统计信息
  private globalStatistics: GenerationStatistics = {
    total_attempts: 0,
    successful_generations: 0,
    average_quality_score: 0,
    generation_time_ms: 0,
    cache_hit_rate: 0,
    construction_usage: {},
    semantic_domain_usage: {},
    quality_distribution: {}
  }
  
  constructor(config: Partial<GeneratorConfig> = {}) {
    this.config = {
      min_quality_threshold: 0.6,
      max_generation_attempts: 10,
      diversity_factor: 0.7,
      enable_caching: true,
      cache_size_limit: 1000,
      timeout_ms: 5000,
      debug_mode: false,
      log_generation_process: false,
      return_intermediate_results: false,
      ...config
    }
    
    this.lexicon = new SemanticLexicon()
    this.constructionEngine = new ConstructionEngine(this.lexicon)
    this.culturalAdapter = new CulturalAdapter()
  }
  
  // ============ 初始化方法 ============
  
  /** 初始化生成器 */
  async initialize(): Promise<void> {
    // 这里可以加载预训练的数据
    if (this.config.debug_mode) {
      console.log('SemanticUsernameGenerator initialized')
    }
  }
  
  /** 加载语言数据 */
  async loadLanguageData(language: string): Promise<void> {
    // 加载词汇数据
    await this.loadLexicalData(language)
    
    // 加载构式数据
    await this.loadConstructionData(language)
    
    // 加载文化数据
    await this.loadCulturalData(language)
  }
  
  private async loadLexicalData(language: string): Promise<void> {
    // 实际实现中，这里会从数据文件或API加载词汇数据
    // 现在使用示例数据
    const sampleEntries: SemanticLexicalEntry[] = [
      {
        word: '星',
        language: language,
        pos: 'noun' as any,
        semantic_domains: ['nature', 'celestial'],
        conceptual_categories: ['concrete', 'visible'],
        semantic_features: [
          { name: 'brightness', value: true, confidence: 0.9 },
          { name: 'distance', value: 'far', confidence: 0.8 }
        ],
        cultural_connotations: [{
          culture: 'zh-CN',
          positive_associations: ['希望', '梦想', '光明'],
          negative_associations: [],
          taboo_level: 0,
          formality_level: 0.6,
          generational_preference: ['young_adults', 'teenagers'] as any
        }],
        register: 'informal' as any,
        emotional_valence: 0.7,
        arousal_level: 0.6,
        frequency: 0.8,
        trend_score: 0.7,
        age_group_preference: ['young_adults', 'teenagers'] as any,
        phonetic_features: [
          { type: 'consonant', value: 'x' },
          { type: 'vowel', value: 'ing' }
        ],
        syllable_count: 1,
        syllable_structure: [{ nucleus: 'ing' }],
        collocations: [
          { partner: '月', relation: 'coordinate', strength: 0.8, frequency: 0.6 }
        ],
        semantic_relations: [
          { type: 'coordinate', target: '月', strength: 0.8 }
        ],
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        confidence: 0.9,
        source: 'manual'
      }
    ]
    
    this.lexicon.addEntries(sampleEntries)
  }
  
  private async loadConstructionData(language: string): Promise<void> {
    // 加载构式模板数据
    const sampleConstructions: ConstructionTemplate[] = [
      {
        id: 'simple_compound',
        language: language,
        name: '简单复合',
        description: '两个词汇的简单组合',
        category: 'compound',
        structure: [
          {
            id: 'modifier',
            name: '修饰语',
            type: 'semantic' as any,
            required: true,
            position: 0,
            semantic_domains: ['nature', 'emotion', 'quality'],
            pos_constraints: ['adjective', 'noun'] as any
          },
          {
            id: 'head',
            name: '中心词',
            type: 'semantic' as any,
            required: true,
            position: 1,
            semantic_domains: ['entity', 'person', 'concept'],
            pos_constraints: ['noun'] as any
          }
        ],
        semantic_frame: {
          id: 'attribution',
          name: '属性描述',
          description: '为实体添加属性描述',
          core_meaning: '具有某种属性的实体',
          semantic_type: 'entity',
          semantic_roles: [
            { role: 'attribute' as any, slot_id: 'modifier', obligatory: true },
            { role: 'theme' as any, slot_id: 'head', obligatory: true }
          ],
          semantic_constraints: []
        },
        pragmatic_function: ['identification', 'self_expression'] as any,
        global_constraints: {
          max_length: 6,
          min_length: 2,
          semantic_coherence_threshold: 0.6
        },
        base_weight: 1.0,
        priority: 1,
        cultural_weight_modifiers: { 'zh-CN': 1.2 },
        cultural_constraints: [],
        style_weight_modifiers: { 'modern': 1.1, 'traditional': 0.9 },
        register_compatibility: ['informal', 'colloquial'] as any,
        examples: [
          {
            example: '星辰',
            components: { modifier: '星', head: '辰' },
            quality_score: 0.8,
            cultural_context: 'zh-CN'
          }
        ],
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        version: '1.0',
        author: 'system',
        confidence: 0.8
      }
    ]
    
    this.constructionEngine.addConstructions(sampleConstructions)
  }
  
  private async loadCulturalData(language: string): Promise<void> {
    // 文化适配器已经在构造函数中初始化了默认的文化知识库
    // 这里可以加载更多的文化数据
  }
  
  // ============ 主要生成方法 ============
  
  /** 生成单个用户名 */
  async generateUsername(
    context: GenerationContext,
    preferences: GenerationPreference = {}
  ): Promise<ExtendedGenerationResult> {
    const startTime = Date.now()
    
    try {
      // 检查缓存
      if (this.config.enable_caching) {
        const cached = this.getCachedResult(context, preferences)
        if (cached) {
          this.updateCacheHitRate(true)
          return cached
        }
      }
      
      this.updateCacheHitRate(false)
      
      // 文化适配
      const culturalAdaptation = this.culturalAdapter.adaptToCulture(context, preferences)
      const adaptedPreferences = culturalAdaptation.adapted_preferences
      
      // 构建生成参数
      const generationParams: ConstructionGenerationParams = {
        semantic_preferences: {
          domains: adaptedPreferences.semantic_domains || [],
          themes: [],
          emotional_tone: adaptedPreferences.emotional_tone || 0.5
        },
        cultural_preferences: {
          target_culture: context.target_culture,
          adaptation_level: adaptedPreferences.cultural_adaptation || 0.8,
          avoid_cultural_conflicts: true
        },
        quality_requirements: {
          min_semantic_coherence: 0.6,
          min_cultural_appropriateness: 0.7,
          min_phonetic_harmony: 0.5,
          min_overall_quality: this.config.min_quality_threshold
        },
        generation_constraints: {
          max_attempts: this.config.max_generation_attempts,
          timeout_ms: this.config.timeout_ms,
          allow_partial_matches: false,
          fallback_strategy: 'template_based'
        }
      }
      
      // 匹配构式
      const constructionMatches = this.constructionEngine.matchConstructions(context, generationParams)
      
      if (constructionMatches.length === 0) {
        throw new Error('No suitable constructions found')
      }
      
      // 生成实例
      let bestInstance: ConstructionInstance | null = null
      let attempts = 0
      
      while (!bestInstance && attempts < this.config.max_generation_attempts) {
        const selectedMatch = constructionMatches[attempts % constructionMatches.length]
        const instance = this.constructionEngine.instantiateConstruction(selectedMatch, context, generationParams)
        
        if (instance && instance.quality_metrics.overall_score >= this.config.min_quality_threshold) {
          bestInstance = instance
          break
        }
        
        attempts++
      }
      
      if (!bestInstance) {
        throw new Error('Failed to generate username meeting quality requirements')
      }
      
      // 构建结果
      const result: ExtendedGenerationResult = {
        username: bestInstance.generated_text,
        components: Object.values(bestInstance.slot_fillers),
        construction_used: bestInstance.construction.id,
        quality_metrics: bestInstance.quality_metrics,
        explanation: `使用${bestInstance.construction.name}构式生成，${culturalAdaptation.explanation}`,
        alternatives: [], // 可以添加备选方案
        generation_process: {
          cultural_adaptation: culturalAdaptation,
          construction_matches: constructionMatches.map(m => ({
            id: m.construction.id,
            score: m.match_score
          })),
          selected_construction: bestInstance.construction.id,
          slot_filling_details: bestInstance.slot_fillers
        },
        statistics: this.calculateGenerationStatistics(startTime, attempts, bestInstance)
      }
      
      // 更新全局统计
      this.updateGlobalStatistics(result)
      
      // 缓存结果
      if (this.config.enable_caching) {
        this.cacheResult(context, preferences, result)
      }
      
      return result
      
    } catch (error) {
      // 错误处理
      const fallbackResult = this.generateFallbackResult(context, preferences, startTime)
      this.updateGlobalStatistics(fallbackResult)
      return fallbackResult
    }
  }
  
  /** 批量生成用户名 */
  async generateMultipleUsernames(
    context: GenerationContext,
    preferences: GenerationPreference = {},
    count: number = 5
  ): Promise<ExtendedGenerationResult[]> {
    const results: ExtendedGenerationResult[] = []
    const usedUsernames = new Set<string>()
    
    let attempts = 0
    const maxAttempts = count * 3
    
    while (results.length < count && attempts < maxAttempts) {
      try {
        // 为了增加多样性，稍微调整偏好
        const adjustedPreferences = this.adjustPreferencesForDiversity(preferences, attempts)
        const result = await this.generateUsername(context, adjustedPreferences)
        
        // 检查重复
        if (!usedUsernames.has(result.username)) {
          results.push(result)
          usedUsernames.add(result.username)
        }
        
      } catch (error) {
        if (this.config.debug_mode) {
          console.warn('Generation attempt failed:', error)
        }
      }
      
      attempts++
    }
    
    // 按质量排序
    results.sort((a, b) => b.quality_metrics.overall_score - a.quality_metrics.overall_score)
    
    return results
  }
  
  // ============ 辅助方法 ============
  
  /** 调整偏好以增加多样性 */
  private adjustPreferencesForDiversity(
    basePreferences: GenerationPreference,
    attempt: number
  ): GenerationPreference {
    const adjusted = { ...basePreferences }
    
    // 随机调整情感色调
    if (adjusted.emotional_tone !== undefined) {
      const variation = (Math.random() - 0.5) * 0.4 * this.config.diversity_factor
      adjusted.emotional_tone = Math.max(-1, Math.min(1, adjusted.emotional_tone + variation))
    }
    
    // 随机调整创意程度
    if (adjusted.creativity_level !== undefined) {
      const variation = (Math.random() - 0.5) * 0.3 * this.config.diversity_factor
      adjusted.creativity_level = Math.max(0, Math.min(1, adjusted.creativity_level + variation))
    }
    
    return adjusted
  }
  
  /** 计算生成统计信息 */
  private calculateGenerationStatistics(
    startTime: number,
    attempts: number,
    instance: ConstructionInstance
  ): GenerationStatistics {
    const endTime = Date.now()
    
    return {
      total_attempts: attempts,
      successful_generations: 1,
      average_quality_score: instance.quality_metrics.overall_score,
      generation_time_ms: endTime - startTime,
      cache_hit_rate: 0,
      construction_usage: { [instance.construction.id]: 1 },
      semantic_domain_usage: {},
      quality_distribution: {
        [this.getQualityRange(instance.quality_metrics.overall_score)]: 1
      }
    }
  }
  
  /** 获取质量范围 */
  private getQualityRange(score: number): string {
    if (score >= 0.9) return 'excellent'
    if (score >= 0.8) return 'good'
    if (score >= 0.7) return 'fair'
    if (score >= 0.6) return 'acceptable'
    return 'poor'
  }
  
  /** 更新全局统计 */
  private updateGlobalStatistics(result: ExtendedGenerationResult): void {
    this.globalStatistics.total_attempts += result.statistics.total_attempts
    this.globalStatistics.successful_generations += 1
    
    // 更新平均质量分数
    const totalScore = this.globalStatistics.average_quality_score * (this.globalStatistics.successful_generations - 1) + 
                      result.quality_metrics.overall_score
    this.globalStatistics.average_quality_score = totalScore / this.globalStatistics.successful_generations
    
    // 更新构式使用统计
    const constructionId = result.construction_used
    this.globalStatistics.construction_usage[constructionId] = 
      (this.globalStatistics.construction_usage[constructionId] || 0) + 1
  }
  
  /** 生成回退结果 */
  private generateFallbackResult(
    context: GenerationContext,
    preferences: GenerationPreference,
    startTime: number
  ): ExtendedGenerationResult {
    // 简单的回退策略
    const fallbackUsernames = ['用户星辰', '默认用户', 'User123']
    const username = fallbackUsernames[Math.floor(Math.random() * fallbackUsernames.length)]
    
    return {
      username,
      components: [],
      construction_used: 'fallback',
      quality_metrics: {
        semantic_coherence: 0.3,
        cultural_appropriateness: 0.5,
        phonetic_harmony: 0.4,
        memorability: 0.6,
        uniqueness: 0.2,
        overall_score: 0.4
      },
      explanation: '使用回退策略生成',
      generation_process: {
        cultural_adaptation: null,
        construction_matches: [],
        selected_construction: 'fallback',
        slot_filling_details: {}
      },
      statistics: {
        total_attempts: 1,
        successful_generations: 1,
        average_quality_score: 0.4,
        generation_time_ms: Date.now() - startTime,
        cache_hit_rate: 0,
        construction_usage: { 'fallback': 1 },
        semantic_domain_usage: {},
        quality_distribution: { 'poor': 1 }
      }
    }
  }
  
  // ============ 缓存管理 ============
  
  private getCachedResult(
    context: GenerationContext,
    preferences: GenerationPreference
  ): ExtendedGenerationResult | null {
    const key = this.generateCacheKey(context, preferences)
    const cached = this.resultCache.get(key)
    return cached && cached.length > 0 ? cached[0] : null
  }
  
  private cacheResult(
    context: GenerationContext,
    preferences: GenerationPreference,
    result: ExtendedGenerationResult
  ): void {
    const key = this.generateCacheKey(context, preferences)
    
    if (!this.resultCache.has(key)) {
      this.resultCache.set(key, [])
    }
    
    const cached = this.resultCache.get(key)!
    cached.unshift(result)
    
    // 限制缓存大小
    if (cached.length > 5) {
      cached.splice(5)
    }
    
    // 清理过期缓存
    if (this.resultCache.size > this.config.cache_size_limit) {
      const oldestKey = this.resultCache.keys().next().value
      this.resultCache.delete(oldestKey)
    }
  }
  
  private generateCacheKey(context: GenerationContext, preferences: GenerationPreference): string {
    return JSON.stringify({ context, preferences })
  }
  
  private updateCacheHitRate(hit: boolean): void {
    // 简化的缓存命中率计算
    const currentRate = this.globalStatistics.cache_hit_rate
    const newRate = hit ? currentRate + 0.01 : Math.max(0, currentRate - 0.01)
    this.globalStatistics.cache_hit_rate = Math.min(1, newRate)
  }
  
  // ============ 公共接口方法 ============
  
  /** 获取全局统计信息 */
  getGlobalStatistics(): GenerationStatistics {
    return { ...this.globalStatistics }
  }
  
  /** 清除缓存 */
  clearCache(): void {
    this.resultCache.clear()
    this.statisticsCache.clear()
  }
  
  /** 获取支持的语言列表 */
  getSupportedLanguages(): string[] {
    // 实际实现中，这里会返回已加载的语言列表
    return ['zh', 'en', 'ja']
  }
  
  /** 获取可用的构式列表 */
  getAvailableConstructions(language: string): ConstructionTemplate[] {
    return this.constructionEngine.getAllConstructions().filter(c => c.language === language)
  }
}
