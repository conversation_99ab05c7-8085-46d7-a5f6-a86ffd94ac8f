/**
 * 质量改进测试脚本
 * 
 * 测试改进后的中文生成器质量提升效果
 */

const fs = require('fs');
const path = require('path');

// 模拟改进后的生成器
async function importImprovedGenerator() {
  return {
    generateChineseUsernamesWithDetails: async (options = {}) => {
      // 加载扩展后的数据
      const lexicalPath = path.join(__dirname, '..', 'data', 'lexical', 'zh.json');
      const grammarPath = path.join(__dirname, '..', 'data', 'grammar', 'zh.json');
      
      const lexicalData = JSON.parse(fs.readFileSync(lexicalPath, 'utf-8'));
      const grammarData = JSON.parse(fs.readFileSync(grammarPath, 'utf-8'));
      
      const words = lexicalData.words || [];
      const patterns = grammarData.patterns || [];
      
      console.log(`📊 使用扩展词库: ${words.length}个词汇, ${patterns.length}个模式`);
      
      const {
        style = 'modern',
        themes = [],
        count = 5,
        minLength = 2,
        maxLength = 8
      } = options;
      
      const results = [];
      const maxAttempts = count * 15; // 增加尝试次数以获得更高质量
      let attempts = 0;
      
      while (results.length < count && attempts < maxAttempts) {
        attempts++;
        
        try {
          // 选择模式（包括新的动词模式）
          const suitablePatterns = patterns.filter(p => {
            const fitness = p.cultural_fitness[style] || 0.5;
            return fitness > 0.3;
          });
          
          if (suitablePatterns.length === 0) continue;
          
          const pattern = suitablePatterns[Math.floor(Math.random() * suitablePatterns.length)];
          
          // 为每个位置选择词汇（使用改进的选择算法）
          const components = [];
          let failed = false;
          
          for (const pos of pattern.structure) {
            let candidates = words.filter(word => {
              if (word.pos !== pos) return false;
              if (components.some(c => c.word === word.word)) return false;
              
              // 风格适应度过滤
              const styleScore = word.cultural_scores[style] || 0.5;
              if (styleScore < 0.3) return false;
              
              return true;
            });
            
            // 主题过滤
            if (themes.length > 0) {
              const themeMatched = candidates.filter(word => 
                themes.some(theme => word.domains.includes(theme))
              );
              if (themeMatched.length > 0) {
                candidates = themeMatched;
              }
            }
            
            if (candidates.length === 0) {
              failed = true;
              break;
            }
            
            // 改进的权重计算
            const weights = candidates.map(word => {
              let weight = 0.1;
              
              // 文化适应度 (40%)
              const culturalScore = word.cultural_scores[style] || 0.5;
              weight += culturalScore * 0.4;
              
              // 频率分数 (20%)
              weight += word.frequency * 0.2;
              
              // 主题匹配奖励 (15%)
              if (themes.length > 0 && themes.some(theme => word.domains.includes(theme))) {
                weight += 0.15;
              }
              
              // 语义和谐 (15%)
              if (components.length > 0) {
                const domainOverlap = word.domains.filter(d => 
                  components.some(c => c.domains.includes(d))
                ).length;
                const maxDomains = Math.max(word.domains.length, 
                  Math.max(...components.map(c => c.domains.length))
                );
                const harmonyScore = domainOverlap / maxDomains;
                weight += harmonyScore * 0.15;
              }
              
              // 情感一致性 (10%)
              if (components.length > 0) {
                const avgSentiment = components.reduce((sum, c) => sum + c.sentiment, 0) / components.length;
                const sentimentDiff = Math.abs(word.sentiment - avgSentiment);
                const sentimentScore = Math.max(0, 1 - sentimentDiff);
                weight += sentimentScore * 0.1;
              }
              
              // 特殊奖励
              if (word.sentiment > 0.8) weight += 0.05; // 积极情感
              if (style === 'modern' && word.cultural_scores.modern > 0.8) weight += 0.05;
              if (style === 'cute' && word.cultural_scores.cute > 0.8) weight += 0.05;
              
              return Math.max(0.01, weight);
            });
            
            // 加权随机选择
            const totalWeight = weights.reduce((sum, w) => sum + w, 0);
            let random = Math.random() * totalWeight;
            let selectedIndex = 0;
            
            for (let i = 0; i < weights.length; i++) {
              random -= weights[i];
              if (random <= 0) {
                selectedIndex = i;
                break;
              }
            }
            
            components.push(candidates[selectedIndex]);
          }
          
          if (failed) continue;
          
          // 构造用户名
          const username = components.map(c => c.word).join('');
          
          // 检查长度
          if (username.length < minLength || username.length > maxLength) {
            continue;
          }
          
          // 检查重复
          if (results.some(r => r.username === username)) {
            continue;
          }
          
          // 改进的质量计算
          const quality = calculateImprovedQuality(username, components, pattern, style);
          
          // 生成详细解释
          const explanation = generateDetailedExplanation(components, pattern, style);
          
          results.push({
            username,
            quality,
            components,
            pattern,
            explanation
          });
        } catch (error) {
          // 忽略错误，继续尝试
        }
      }
      
      // 按质量排序
      results.sort((a, b) => b.quality - a.quality);
      
      return results;
    }
  };
}

// 改进的质量计算函数
function calculateImprovedQuality(username, components, pattern, style) {
  let totalScore = 0;
  let totalWeight = 0;
  
  // 1. 文化适应度 (35%)
  const culturalWeight = 0.35;
  const culturalScore = calculateCulturalFitness(components, pattern, style);
  totalScore += culturalScore * culturalWeight;
  totalWeight += culturalWeight;
  
  // 2. 语义一致性 (25%)
  const semanticWeight = 0.25;
  const semanticScore = calculateSemanticCoherence(components);
  totalScore += semanticScore * semanticWeight;
  totalWeight += semanticWeight;
  
  // 3. 音韵和谐 (20%)
  const phoneticWeight = 0.20;
  const phoneticScore = calculatePhoneticHarmony(components);
  totalScore += phoneticScore * phoneticWeight;
  totalWeight += phoneticWeight;
  
  // 4. 记忆性 (12%)
  const memorabilityWeight = 0.12;
  const memorabilityScore = calculateMemorability(username, components);
  totalScore += memorabilityScore * memorabilityWeight;
  totalWeight += memorabilityWeight;
  
  // 5. 独特性 (8%)
  const uniquenessWeight = 0.08;
  const uniquenessScore = calculateUniqueness(username, components);
  totalScore += uniquenessScore * uniquenessWeight;
  totalWeight += uniquenessWeight;
  
  let finalScore = totalScore / totalWeight;
  
  // 奖励机制
  if (username.length === 2) finalScore += 0.1;
  if (components.some(c => c.frequency > 0.8)) finalScore += 0.05;
  if (components.reduce((sum, c) => sum + c.sentiment, 0) / components.length > 0.8) finalScore += 0.05;
  
  return Math.max(0, Math.min(1.0, finalScore));
}

function calculateCulturalFitness(components, pattern, style) {
  const wordScores = components.map(c => c.cultural_scores[style] || 0.5);
  const avgWordScore = wordScores.reduce((sum, s) => sum + s, 0) / wordScores.length;
  const patternScore = pattern.cultural_fitness[style] || 0.5;
  const variance = calculateVariance(wordScores);
  const consistencyScore = Math.max(0, 1 - variance * 2);
  
  return (avgWordScore * 0.5 + patternScore * 0.3 + consistencyScore * 0.2);
}

function calculateSemanticCoherence(components) {
  if (components.length <= 1) return 1.0;
  
  const allDomains = components.flatMap(c => c.domains);
  const uniqueDomains = new Set(allDomains);
  const overlapRatio = (allDomains.length - uniqueDomains.size) / allDomains.length;
  
  const sentiments = components.map(c => c.sentiment);
  const sentimentVariance = calculateVariance(sentiments);
  const sentimentScore = Math.max(0, 1 - sentimentVariance);
  
  return (overlapRatio * 0.6 + sentimentScore * 0.4);
}

function calculatePhoneticHarmony(components) {
  if (components.length <= 1) return 1.0;
  
  const syllables = components.map(c => c.syllables);
  const lengths = components.map(c => c.length);
  
  // 全单音节奖励
  if (syllables.every(s => s === 1)) return 1.0;
  
  const syllableVariance = calculateVariance(syllables);
  const lengthVariance = calculateVariance(lengths);
  
  const syllableScore = Math.max(0, 1 - syllableVariance / 2);
  const lengthScore = Math.max(0, 1 - lengthVariance / 2);
  
  return (syllableScore * 0.6 + lengthScore * 0.4);
}

function calculateMemorability(username, components) {
  let score = 0.3;
  
  if (username.length === 2) score += 0.4;
  else if (username.length === 3) score += 0.3;
  else if (username.length === 4) score += 0.2;
  
  const commonChars = ['星', '月', '云', '风', '雨', '花', '美', '好', '新', '亮', '心', '光', '梦'];
  const commonCharCount = username.split('').filter(char => commonChars.includes(char)).length;
  score += commonCharCount * 0.1;
  
  const avgFrequency = components.reduce((sum, c) => sum + c.frequency, 0) / components.length;
  score += avgFrequency * 0.2;
  
  return Math.max(0, Math.min(1.0, score));
}

function calculateUniqueness(username, components) {
  let score = 0.5;
  
  const charCount = username.length;
  const uniqueChars = new Set(username.split('')).size;
  const diversityRatio = uniqueChars / charCount;
  score += diversityRatio * 0.3;
  
  const avgFrequency = components.reduce((sum, c) => sum + c.frequency, 0) / components.length;
  score += (1 - avgFrequency) * 0.2;
  
  const domains = components.flatMap(c => c.domains);
  const uniqueDomains = new Set(domains);
  if (uniqueDomains.size > 1) score += 0.2;
  
  return Math.max(0, Math.min(1.0, score));
}

function calculateVariance(values) {
  if (values.length <= 1) return 0;
  const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
  const squaredDiffs = values.map(val => Math.pow(val - mean, 2));
  return squaredDiffs.reduce((sum, diff) => sum + diff, 0) / values.length;
}

function generateDetailedExplanation(components, pattern, style) {
  const componentDescriptions = components.map(c => {
    const domains = c.domains.join('、');
    return `"${c.word}"(${domains})`;
  }).join(' + ');
  
  let explanation = `采用${pattern.name || pattern.id}模式，组合${componentDescriptions}`;
  
  // 文化内涵分析
  const natureWords = components.filter(c => c.domains.includes('nature'));
  if (natureWords.length >= 2) {
    explanation += '，体现天人合一的自然美学';
  }
  
  const emotionWords = components.filter(c => c.domains.includes('emotion'));
  if (emotionWords.length > 0) {
    const avgSentiment = emotionWords.reduce((sum, w) => sum + w.sentiment, 0) / emotionWords.length;
    if (avgSentiment > 0.8) {
      explanation += '，传达积极向上的人生态度';
    }
  }
  
  const techWords = components.filter(c => c.domains.includes('tech'));
  if (techWords.length > 0 && style === 'modern') {
    explanation += '，融合时代特色与科技美感';
  }
  
  // 音韵特色
  const syllables = components.map(c => c.syllables);
  if (syllables.every(s => s === 1)) {
    explanation += '，音节简洁明快';
  }
  
  const styleDescriptions = {
    'traditional': '传统典雅',
    'modern': '现代时尚',
    'cute': '可爱甜美',
    'cool': '酷炫个性',
    'elegant': '优雅精致'
  };
  
  explanation += `，体现${styleDescriptions[style] || style}风格特色。`;
  
  return explanation;
}

class QualityImprovementTester {
  constructor() {
    this.results = [];
  }
  
  async runAllTests() {
    console.log('🚀 开始质量改进效果测试');
    console.log('='.repeat(50));
    
    const generator = await importImprovedGenerator();
    
    // 测试词汇库扩展效果
    await this.testVocabularyExpansion(generator);
    
    // 测试不同风格的质量提升
    await this.testStyleQualityImprovement(generator);
    
    // 测试新增语义域效果
    await this.testNewSemanticDomains(generator);
    
    // 测试质量评估改进
    await this.testQualityAssessmentImprovement(generator);
    
    // 输出结果
    this.printResults();
  }
  
  async runTest(name, testFn) {
    const startTime = Date.now();
    
    try {
      await testFn();
      this.results.push({
        name,
        passed: true,
        duration: Date.now() - startTime
      });
      console.log(`✅ ${name}`);
    } catch (error) {
      this.results.push({
        name,
        passed: false,
        error: error.message,
        duration: Date.now() - startTime
      });
      console.log(`❌ ${name}: ${error.message}`);
    }
  }
  
  async testVocabularyExpansion(generator) {
    console.log('\n📚 词汇库扩展效果测试');
    console.log('-'.repeat(30));
    
    await this.runTest('科技域词汇生成', async () => {
      const results = await generator.generateChineseUsernamesWithDetails({
        themes: ['tech'],
        count: 3
      });
      
      if (results.length === 0) {
        throw new Error('无法生成科技主题用户名');
      }
      
      const hasTechWords = results.some(r => 
        r.components.some(c => c.domains.includes('tech'))
      );
      
      if (!hasTechWords) {
        throw new Error('生成的用户名中没有科技域词汇');
      }
      
      console.log(`    ✓ 科技主题: ${results.map(r => r.username).join(', ')}`);
    });
    
    await this.runTest('现代风格词汇丰富度', async () => {
      const results = await generator.generateChineseUsernamesWithDetails({
        style: 'modern',
        count: 5
      });
      
      if (results.length === 0) {
        throw new Error('无法生成现代风格用户名');
      }
      
      const modernWords = ['潮', '炫', '酷', '萌', '码', '网'];
      const hasModernWords = results.some(r => 
        r.components.some(c => modernWords.includes(c.word))
      );
      
      if (!hasModernWords) {
        throw new Error('生成的用户名中缺少新增的现代词汇');
      }
      
      console.log(`    ✓ 现代风格: ${results.map(r => r.username).join(', ')}`);
    });
  }
  
  async testStyleQualityImprovement(generator) {
    console.log('\n🎨 风格质量提升测试');
    console.log('-'.repeat(30));
    
    const styles = ['modern', 'cute', 'cool', 'elegant'];
    
    for (const style of styles) {
      await this.runTest(`${style}风格质量提升`, async () => {
        const results = await generator.generateChineseUsernamesWithDetails({
          style,
          count: 3
        });
        
        if (results.length === 0) {
          throw new Error(`无法生成${style}风格用户名`);
        }
        
        const avgQuality = results.reduce((sum, r) => sum + r.quality, 0) / results.length;
        
        if (avgQuality < 0.6) {
          throw new Error(`${style}风格质量偏低: ${avgQuality.toFixed(3)}`);
        }
        
        console.log(`    ✓ ${style}: ${results.map(r => `${r.username}(${r.quality.toFixed(2)})`).join(', ')}`);
      });
    }
  }
  
  async testNewSemanticDomains(generator) {
    console.log('\n🌐 新语义域测试');
    console.log('-'.repeat(30));
    
    const newDomains = [
      { domain: 'tech', name: '科技' },
      { domain: 'fantasy', name: '幻想' },
      { domain: 'social', name: '社交' }
    ];
    
    for (const { domain, name } of newDomains) {
      await this.runTest(`${name}域词汇应用`, async () => {
        const results = await generator.generateChineseUsernamesWithDetails({
          themes: [domain],
          count: 2
        });
        
        if (results.length === 0) {
          throw new Error(`无法生成${name}主题用户名`);
        }
        
        const hasDomainWords = results.some(r => 
          r.components.some(c => c.domains.includes(domain))
        );
        
        if (!hasDomainWords) {
          throw new Error(`生成的用户名中没有${name}域词汇`);
        }
        
        console.log(`    ✓ ${name}域: ${results.map(r => r.username).join(', ')}`);
      });
    }
  }
  
  async testQualityAssessmentImprovement(generator) {
    console.log('\n⭐ 质量评估改进测试');
    console.log('-'.repeat(30));
    
    await this.runTest('高质量用户名生成', async () => {
      const results = await generator.generateChineseUsernamesWithDetails({
        style: 'elegant',
        count: 5
      });
      
      if (results.length === 0) {
        throw new Error('无法生成用户名');
      }
      
      const highQualityCount = results.filter(r => r.quality > 0.7).length;
      
      if (highQualityCount < 3) {
        throw new Error(`高质量用户名数量不足: ${highQualityCount}/5`);
      }
      
      console.log(`    ✓ 高质量用户名 (>0.7): ${highQualityCount}/5`);
      results.forEach(r => {
        console.log(`      ${r.username}: ${r.quality.toFixed(3)}`);
      });
    });
    
    await this.runTest('详细解释生成', async () => {
      const results = await generator.generateChineseUsernamesWithDetails({
        style: 'traditional',
        themes: ['nature'],
        count: 2
      });
      
      if (results.length === 0) {
        throw new Error('无法生成用户名');
      }
      
      const hasDetailedExplanation = results.every(r => 
        r.explanation && r.explanation.length > 20
      );
      
      if (!hasDetailedExplanation) {
        throw new Error('解释不够详细');
      }
      
      console.log(`    ✓ 详细解释示例:`);
      results.forEach(r => {
        console.log(`      ${r.username}: ${r.explanation}`);
      });
    });
  }
  
  printResults() {
    console.log('\n📊 质量改进测试结果汇总');
    console.log('='.repeat(50));
    
    const passed = this.results.filter(r => r.passed).length;
    const failed = this.results.filter(r => !r.passed).length;
    const total = this.results.length;
    
    console.log(`总测试数: ${total}`);
    console.log(`✅ 通过: ${passed}`);
    console.log(`❌ 失败: ${failed}`);
    console.log(`通过率: ${((passed / total) * 100).toFixed(1)}%`);
    
    if (failed > 0) {
      console.log('\n❌ 失败的测试:');
      this.results
        .filter(r => !r.passed)
        .forEach(r => {
          console.log(`  - ${r.name}: ${r.error}`);
        });
    }
    
    if (passed === total) {
      console.log('\n🎉 所有质量改进测试通过！V2系统质量显著提升！');
    } else if (passed / total >= 0.8) {
      console.log('\n👍 大部分质量改进测试通过，系统质量明显提升');
    } else {
      console.log('\n⚠️  部分质量改进测试失败，需要进一步优化');
    }
  }
}

// 主函数
async function main() {
  const tester = new QualityImprovementTester();
  await tester.runAllTests();
}

// 如果直接运行此文件，则执行测试
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { QualityImprovementTester };
