/**
 * 核心算法单元测试
 * 
 * 测试核心算法的正确性、性能和边界情况
 */

import {
  WeightedRandomSelector,
  SemanticSimilarityCalculator,
  PhoneticHarmonyCalculator,
  GreedySearchAlgorithm,
  DynamicProgrammingAlgorithm,
  AlgorithmFactory,
  LexicalEntry,
  GrammarPattern,
  GenerationContext,
  PartOfSpeech,
  SemanticDomain,
  CulturalStyle
} from '../../types'

import { createSampleLexicalEntry, createSampleGrammarPattern } from './DataStructures.test'

describe('CoreAlgorithms', () => {
  
  describe('WeightedRandomSelector', () => {
    test('should create selector with valid weights', () => {
      const weights = [0.1, 0.3, 0.5, 0.1]
      const selector = new WeightedRandomSelector(weights)
      expect(selector).toBeDefined()
    })
    
    test('should handle empty weights', () => {
      const selector = new WeightedRandomSelector([])
      expect(selector.select()).toBe(-1)
    })
    
    test('should handle zero weights', () => {
      const weights = [0, 0, 0, 0]
      const selector = new WeightedRandomSelector(weights)
      expect(selector.select()).toBeGreaterThanOrEqual(0)
    })
    
    test('should select indices within valid range', () => {
      const weights = [0.2, 0.3, 0.4, 0.1]
      const selector = new WeightedRandomSelector(weights)
      
      for (let i = 0; i < 100; i++) {
        const selected = selector.select()
        expect(selected).toBeGreaterThanOrEqual(0)
        expect(selected).toBeLessThan(weights.length)
      }
    })
    
    test('should respect weight distribution approximately', () => {
      const weights = [0.1, 0.9] // 高权重应该被选中更多
      const selector = new WeightedRandomSelector(weights)
      
      const counts = [0, 0]
      const iterations = 10000
      
      for (let i = 0; i < iterations; i++) {
        const selected = selector.select()
        counts[selected]++
      }
      
      const ratio = counts[1] / counts[0]
      expect(ratio).toBeGreaterThan(5) // 0.9/0.1 = 9, 允许一些误差
    })
    
    test('should select multiple unique indices', () => {
      const weights = [0.2, 0.2, 0.2, 0.2, 0.2]
      const selector = new WeightedRandomSelector(weights)
      
      const selected = selector.selectMultiple(3)
      expect(selected).toHaveLength(3)
      expect(new Set(selected).size).toBe(3) // 确保唯一性
    })
    
    test('should handle selectMultiple with count > weights.length', () => {
      const weights = [0.5, 0.5]
      const selector = new WeightedRandomSelector(weights)
      
      const selected = selector.selectMultiple(5)
      expect(selected.length).toBeLessThanOrEqual(weights.length)
    })
  })
  
  describe('SemanticSimilarityCalculator', () => {
    const word1 = createSampleLexicalEntry({
      word: '星',
      domains: [SemanticDomain.NATURE],
      sentiment: 0.7,
      formality: 0.5,
      cultural_scores: {
        [CulturalStyle.TRADITIONAL]: 0.8,
        [CulturalStyle.MODERN]: 0.6,
        [CulturalStyle.CUTE]: 0.7,
        [CulturalStyle.COOL]: 0.5,
        [CulturalStyle.ELEGANT]: 0.9,
        [CulturalStyle.PLAYFUL]: 0.4,
        [CulturalStyle.MYSTERIOUS]: 0.6,
        [CulturalStyle.POWERFUL]: 0.3
      }
    })
    
    const word2 = createSampleLexicalEntry({
      word: '月',
      domains: [SemanticDomain.NATURE],
      sentiment: 0.6,
      formality: 0.6,
      cultural_scores: {
        [CulturalStyle.TRADITIONAL]: 0.9,
        [CulturalStyle.MODERN]: 0.5,
        [CulturalStyle.CUTE]: 0.8,
        [CulturalStyle.COOL]: 0.4,
        [CulturalStyle.ELEGANT]: 0.8,
        [CulturalStyle.PLAYFUL]: 0.3,
        [CulturalStyle.MYSTERIOUS]: 0.7,
        [CulturalStyle.POWERFUL]: 0.2
      }
    })
    
    test('should calculate similarity between similar words', () => {
      const similarity = SemanticSimilarityCalculator.calculate(word1, word2)
      expect(similarity).toBeGreaterThan(0.5) // 相似词汇应该有较高相似度
      expect(similarity).toBeLessThanOrEqual(1)
    })
    
    test('should return 1 for identical words', () => {
      const similarity = SemanticSimilarityCalculator.calculate(word1, word1)
      expect(similarity).toBe(1)
    })
    
    test('should return lower similarity for different domains', () => {
      const word3 = createSampleLexicalEntry({
        domains: [SemanticDomain.TECHNOLOGY],
        sentiment: -0.5,
        formality: 0.9
      })
      
      const similarity = SemanticSimilarityCalculator.calculate(word1, word3)
      expect(similarity).toBeLessThan(0.5)
    })
    
    test('should handle edge cases', () => {
      const emptyWord = createSampleLexicalEntry({
        domains: [],
        cultural_scores: {} as any
      })
      
      const similarity = SemanticSimilarityCalculator.calculate(word1, emptyWord)
      expect(similarity).toBeGreaterThanOrEqual(0)
      expect(similarity).toBeLessThanOrEqual(1)
    })
  })
  
  describe('PhoneticHarmonyCalculator', () => {
    test('should calculate harmony for single word', () => {
      const words = [createSampleLexicalEntry({ syllables: 2 })]
      const harmony = PhoneticHarmonyCalculator.calculate(words)
      expect(harmony).toBe(1.0) // 单个词汇应该完全和谐
    })
    
    test('should calculate harmony for similar syllable counts', () => {
      const words = [
        createSampleLexicalEntry({ syllables: 2, length: 2 }),
        createSampleLexicalEntry({ syllables: 2, length: 2 })
      ]
      const harmony = PhoneticHarmonyCalculator.calculate(words)
      expect(harmony).toBeGreaterThan(0.8) // 相似音节应该和谐
    })
    
    test('should calculate lower harmony for different syllable counts', () => {
      const words = [
        createSampleLexicalEntry({ syllables: 1, length: 1 }),
        createSampleLexicalEntry({ syllables: 4, length: 4 })
      ]
      const harmony = PhoneticHarmonyCalculator.calculate(words)
      expect(harmony).toBeLessThan(0.8) // 差异较大应该不太和谐
    })
    
    test('should handle empty word list', () => {
      const harmony = PhoneticHarmonyCalculator.calculate([])
      expect(harmony).toBe(1.0) // 空列表默认和谐
    })
    
    test('should calculate harmony for multiple words', () => {
      const words = [
        createSampleLexicalEntry({ syllables: 2, length: 2 }),
        createSampleLexicalEntry({ syllables: 2, length: 3 }),
        createSampleLexicalEntry({ syllables: 3, length: 3 })
      ]
      const harmony = PhoneticHarmonyCalculator.calculate(words)
      expect(harmony).toBeGreaterThan(0)
      expect(harmony).toBeLessThanOrEqual(1)
    })
  })
  
  describe('GreedySearchAlgorithm', () => {
    const pattern = createSampleGrammarPattern({
      structure: [PartOfSpeech.ADJECTIVE, PartOfSpeech.NOUN]
    })
    
    const context: GenerationContext = {
      language: 'zh',
      style: CulturalStyle.MODERN,
      preferred_domains: [SemanticDomain.NATURE],
      length_range: [2, 8],
      sentiment_target: 0.5,
      formality_target: 0.5,
      target_age: 'adult',
      avoid_words: new Set(),
      creativity: 0.5,
      count: 1,
      unique: true
    }
    
    test('should find valid word combination', () => {
      const candidatesByPosition = [
        [
          createSampleLexicalEntry({ 
            pos: PartOfSpeech.ADJECTIVE,
            domains: [SemanticDomain.NATURE]
          })
        ],
        [
          createSampleLexicalEntry({ 
            pos: PartOfSpeech.NOUN,
            domains: [SemanticDomain.NATURE]
          })
        ]
      ]
      
      const result = GreedySearchAlgorithm.search(
        pattern,
        candidatesByPosition,
        context,
        100
      )
      
      expect(result).not.toBeNull()
      expect(result).toHaveLength(2)
      expect(result![0].pos).toBe(PartOfSpeech.ADJECTIVE)
      expect(result![1].pos).toBe(PartOfSpeech.NOUN)
    })
    
    test('should return null for empty candidates', () => {
      const result = GreedySearchAlgorithm.search(pattern, [], context, 100)
      expect(result).toBeNull()
    })
    
    test('should return null for insufficient candidates', () => {
      const candidatesByPosition = [
        [createSampleLexicalEntry({ pos: PartOfSpeech.ADJECTIVE })]
        // 缺少第二个位置的候选
      ]
      
      const result = GreedySearchAlgorithm.search(
        pattern,
        candidatesByPosition,
        context,
        100
      )
      
      expect(result).toBeNull()
    })
    
    test('should avoid duplicate words', () => {
      const sameWord = createSampleLexicalEntry({ 
        word: '同一个词',
        pos: PartOfSpeech.ADJECTIVE
      })
      
      const candidatesByPosition = [
        [sameWord],
        [{ ...sameWord, pos: PartOfSpeech.NOUN }] // 同一个词但不同词性
      ]
      
      const result = GreedySearchAlgorithm.search(
        pattern,
        candidatesByPosition,
        context,
        100
      )
      
      expect(result).toBeNull() // 应该避免重复词汇
    })
  })
  
  describe('DynamicProgrammingAlgorithm', () => {
    const pattern = createSampleGrammarPattern({
      structure: [PartOfSpeech.ADJECTIVE, PartOfSpeech.NOUN]
    })
    
    const context: GenerationContext = {
      language: 'zh',
      style: CulturalStyle.MODERN,
      preferred_domains: [SemanticDomain.NATURE],
      length_range: [2, 8],
      sentiment_target: 0.5,
      formality_target: 0.5,
      target_age: 'adult',
      avoid_words: new Set(),
      creativity: 0.5,
      count: 1,
      unique: true
    }
    
    test('should find optimal combination', () => {
      const candidatesByPosition = [
        [
          createSampleLexicalEntry({ 
            pos: PartOfSpeech.ADJECTIVE,
            word: '美',
            cultural_scores: { [CulturalStyle.MODERN]: 0.8 } as any
          }),
          createSampleLexicalEntry({ 
            pos: PartOfSpeech.ADJECTIVE,
            word: '丑',
            cultural_scores: { [CulturalStyle.MODERN]: 0.2 } as any
          })
        ],
        [
          createSampleLexicalEntry({ 
            pos: PartOfSpeech.NOUN,
            word: '星',
            cultural_scores: { [CulturalStyle.MODERN]: 0.9 } as any
          })
        ]
      ]
      
      const result = DynamicProgrammingAlgorithm.findOptimalCombination(
        pattern,
        candidatesByPosition,
        context
      )
      
      expect(result).not.toBeNull()
      expect(result).toHaveLength(2)
      expect(result![0].word).toBe('美') // 应该选择更高分的词汇
    })
    
    test('should handle empty pattern', () => {
      const emptyPattern = createSampleGrammarPattern({ structure: [] })
      const result = DynamicProgrammingAlgorithm.findOptimalCombination(
        emptyPattern,
        [],
        context
      )
      
      expect(result).toEqual([])
    })
    
    test('should return null for no valid combinations', () => {
      const candidatesByPosition = [
        [], // 空的候选列表
        [createSampleLexicalEntry({ pos: PartOfSpeech.NOUN })]
      ]
      
      const result = DynamicProgrammingAlgorithm.findOptimalCombination(
        pattern,
        candidatesByPosition,
        context
      )
      
      expect(result).toBeNull()
    })
  })
  
  describe('AlgorithmFactory', () => {
    test('should create weighted lexical selector', () => {
      const selector = AlgorithmFactory.createLexicalSelector('weighted')
      expect(selector).toBeDefined()
      expect(typeof selector.select).toBe('function')
    })
    
    test('should create greedy lexical selector', () => {
      const selector = AlgorithmFactory.createLexicalSelector('greedy')
      expect(selector).toBeDefined()
      expect(typeof selector.select).toBe('function')
    })
    
    test('should create dp lexical selector', () => {
      const selector = AlgorithmFactory.createLexicalSelector('dp')
      expect(selector).toBeDefined()
      expect(typeof selector.select).toBe('function')
    })
    
    test('should create default selector for unknown type', () => {
      const selector = AlgorithmFactory.createLexicalSelector('unknown' as any)
      expect(selector).toBeDefined()
    })
    
    test('should create quality evaluator', () => {
      const evaluator = AlgorithmFactory.createQualityEvaluator()
      expect(evaluator).toBeDefined()
      expect(typeof evaluator.evaluate).toBe('function')
    })
  })
  
  describe('Performance Tests', () => {
    test('WeightedRandomSelector should be fast for large datasets', () => {
      const largeWeights = Array(10000).fill(0).map(() => Math.random())
      
      const startTime = Date.now()
      const selector = new WeightedRandomSelector(largeWeights)
      
      // 执行多次选择
      for (let i = 0; i < 1000; i++) {
        selector.select()
      }
      
      const endTime = Date.now()
      const duration = endTime - startTime
      
      expect(duration).toBeLessThan(100) // 应该在100ms内完成
    })
    
    test('SemanticSimilarityCalculator should be efficient', () => {
      const word1 = createSampleLexicalEntry()
      const word2 = createSampleLexicalEntry()
      
      const startTime = Date.now()
      
      // 计算1000次相似度
      for (let i = 0; i < 1000; i++) {
        SemanticSimilarityCalculator.calculate(word1, word2)
      }
      
      const endTime = Date.now()
      const duration = endTime - startTime
      
      expect(duration).toBeLessThan(50) // 应该在50ms内完成
    })
  })
  
  describe('Edge Cases', () => {
    test('should handle NaN and Infinity values gracefully', () => {
      const invalidWord = createSampleLexicalEntry({
        sentiment: NaN,
        formality: Infinity,
        frequency: -Infinity
      })
      
      const normalWord = createSampleLexicalEntry()
      
      expect(() => {
        SemanticSimilarityCalculator.calculate(invalidWord, normalWord)
      }).not.toThrow()
    })
    
    test('should handle very large numbers', () => {
      const weights = [Number.MAX_SAFE_INTEGER, 1, 1]
      
      expect(() => {
        new WeightedRandomSelector(weights)
      }).not.toThrow()
    })
    
    test('should handle very small numbers', () => {
      const weights = [Number.MIN_VALUE, Number.MIN_VALUE, Number.MIN_VALUE]
      
      expect(() => {
        new WeightedRandomSelector(weights)
      }).not.toThrow()
    })
  })
})
