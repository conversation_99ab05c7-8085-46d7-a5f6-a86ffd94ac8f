/**
 * 数据结构单元测试
 * 
 * 测试核心数据结构的类型安全性和基础功能
 */

import {
  PartOfSpeech,
  SemanticDomain,
  CulturalStyle,
  LexicalEntry,
  GrammarPattern,
  CultureConfig,
  GenerationContext,
  GenerationResult,
  Utils,
  ValidationError
} from '../../types'

describe('DataStructures', () => {
  
  describe('Enums', () => {
    test('PartOfSpeech should have correct values', () => {
      expect(PartOfSpeech.NOUN).toBe('n')
      expect(PartOfSpeech.VERB).toBe('v')
      expect(PartOfSpeech.ADJECTIVE).toBe('adj')
      expect(PartOfSpeech.ADVERB).toBe('adv')
    })
    
    test('SemanticDomain should have correct values', () => {
      expect(SemanticDomain.NATURE).toBe('nature')
      expect(SemanticDomain.EMOTION).toBe('emotion')
      expect(SemanticDomain.TECHNOLOGY).toBe('tech')
    })
    
    test('CulturalStyle should have correct values', () => {
      expect(CulturalStyle.TRADITIONAL).toBe('traditional')
      expect(CulturalStyle.MODERN).toBe('modern')
      expect(CulturalStyle.CUTE).toBe('cute')
    })
  })
  
  describe('LexicalEntry', () => {
    const sampleEntry: LexicalEntry = {
      word: '星',
      language: 'zh',
      pos: PartOfSpeech.NOUN,
      domains: [SemanticDomain.NATURE],
      cultural_scores: {
        [CulturalStyle.TRADITIONAL]: 0.8,
        [CulturalStyle.MODERN]: 0.6,
        [CulturalStyle.CUTE]: 0.7,
        [CulturalStyle.COOL]: 0.5,
        [CulturalStyle.ELEGANT]: 0.9,
        [CulturalStyle.PLAYFUL]: 0.4,
        [CulturalStyle.MYSTERIOUS]: 0.6,
        [CulturalStyle.POWERFUL]: 0.3
      },
      frequency: 0.8,
      popularity: 0.7,
      sentiment: 0.6,
      formality: 0.5,
      age_groups: ['teen', 'adult'],
      syllables: 1,
      length: 1,
      related_words: ['月', '云'],
      taboo_level: 0,
      created_at: Date.now(),
      weight: 1.0
    }
    
    test('should create valid lexical entry', () => {
      expect(sampleEntry.word).toBe('星')
      expect(sampleEntry.language).toBe('zh')
      expect(sampleEntry.pos).toBe(PartOfSpeech.NOUN)
      expect(sampleEntry.domains).toContain(SemanticDomain.NATURE)
    })
    
    test('should have valid cultural scores', () => {
      expect(sampleEntry.cultural_scores[CulturalStyle.TRADITIONAL]).toBe(0.8)
      expect(sampleEntry.cultural_scores[CulturalStyle.ELEGANT]).toBe(0.9)
    })
    
    test('should have valid numeric properties', () => {
      expect(sampleEntry.frequency).toBeGreaterThanOrEqual(0)
      expect(sampleEntry.frequency).toBeLessThanOrEqual(1)
      expect(sampleEntry.sentiment).toBeGreaterThanOrEqual(-1)
      expect(sampleEntry.sentiment).toBeLessThanOrEqual(1)
      expect(sampleEntry.syllables).toBeGreaterThan(0)
      expect(sampleEntry.length).toBeGreaterThan(0)
    })
  })
  
  describe('GrammarPattern', () => {
    const samplePattern: GrammarPattern = {
      id: 'adj_noun',
      name: '形容词+名词',
      language: 'zh',
      structure: [PartOfSpeech.ADJECTIVE, PartOfSpeech.NOUN],
      semantic_constraints: [
        {
          type: 'domain_match',
          params: { domains: [SemanticDomain.NATURE] },
          strength: 0.8
        }
      ],
      cultural_fitness: {
        [CulturalStyle.TRADITIONAL]: 0.9,
        [CulturalStyle.MODERN]: 0.7,
        [CulturalStyle.CUTE]: 0.6,
        [CulturalStyle.COOL]: 0.5,
        [CulturalStyle.ELEGANT]: 0.8,
        [CulturalStyle.PLAYFUL]: 0.4,
        [CulturalStyle.MYSTERIOUS]: 0.6,
        [CulturalStyle.POWERFUL]: 0.7
      },
      weight: 1.0,
      examples: ['美丽星辰', '清澈月光'],
      min_length: 2,
      max_length: 8,
      complexity: 0.5
    }
    
    test('should create valid grammar pattern', () => {
      expect(samplePattern.id).toBe('adj_noun')
      expect(samplePattern.structure).toHaveLength(2)
      expect(samplePattern.structure[0]).toBe(PartOfSpeech.ADJECTIVE)
      expect(samplePattern.structure[1]).toBe(PartOfSpeech.NOUN)
    })
    
    test('should have valid constraints', () => {
      expect(samplePattern.semantic_constraints).toHaveLength(1)
      expect(samplePattern.semantic_constraints[0].type).toBe('domain_match')
      expect(samplePattern.semantic_constraints[0].strength).toBe(0.8)
    })
    
    test('should have valid cultural fitness scores', () => {
      expect(samplePattern.cultural_fitness[CulturalStyle.TRADITIONAL]).toBe(0.9)
      expect(samplePattern.cultural_fitness[CulturalStyle.ELEGANT]).toBe(0.8)
    })
  })
  
  describe('GenerationContext', () => {
    const sampleContext: GenerationContext = {
      language: 'zh',
      style: CulturalStyle.MODERN,
      preferred_domains: [SemanticDomain.NATURE, SemanticDomain.EMOTION],
      length_range: [2, 8],
      sentiment_target: 0.5,
      formality_target: 0.6,
      target_age: 'adult',
      avoid_words: new Set(['禁用词']),
      creativity: 0.7,
      count: 5,
      unique: true
    }
    
    test('should create valid generation context', () => {
      expect(sampleContext.language).toBe('zh')
      expect(sampleContext.style).toBe(CulturalStyle.MODERN)
      expect(sampleContext.preferred_domains).toContain(SemanticDomain.NATURE)
      expect(sampleContext.length_range).toEqual([2, 8])
    })
    
    test('should have valid numeric ranges', () => {
      expect(sampleContext.sentiment_target).toBeGreaterThanOrEqual(-1)
      expect(sampleContext.sentiment_target).toBeLessThanOrEqual(1)
      expect(sampleContext.formality_target).toBeGreaterThanOrEqual(0)
      expect(sampleContext.formality_target).toBeLessThanOrEqual(1)
      expect(sampleContext.creativity).toBeGreaterThanOrEqual(0)
      expect(sampleContext.creativity).toBeLessThanOrEqual(1)
    })
    
    test('should handle avoid_words set', () => {
      expect(sampleContext.avoid_words.has('禁用词')).toBe(true)
      expect(sampleContext.avoid_words.has('正常词')).toBe(false)
    })
  })
  
  describe('Utils', () => {
    test('isSupportedLanguage should work correctly', () => {
      expect(Utils.isSupportedLanguage('zh')).toBe(true)
      expect(Utils.isSupportedLanguage('en')).toBe(true)
      expect(Utils.isSupportedLanguage('ja')).toBe(true)
      expect(Utils.isSupportedLanguage('fr')).toBe(false)
      expect(Utils.isSupportedLanguage('invalid')).toBe(false)
    })
    
    test('isValidStyle should work correctly', () => {
      expect(Utils.isValidStyle('modern')).toBe(true)
      expect(Utils.isValidStyle('traditional')).toBe(true)
      expect(Utils.isValidStyle('invalid')).toBe(false)
    })
    
    test('isValidTheme should work correctly', () => {
      expect(Utils.isValidTheme('nature')).toBe(true)
      expect(Utils.isValidTheme('emotion')).toBe(true)
      expect(Utils.isValidTheme('invalid')).toBe(false)
    })
    
    test('normalizeLanguage should work correctly', () => {
      expect(Utils.normalizeLanguage('zh')).toBe('zh')
      expect(Utils.normalizeLanguage('zh-CN')).toBe('zh')
      expect(Utils.normalizeLanguage('en-US')).toBe('en')
      expect(Utils.normalizeLanguage('fr')).toBe('zh') // fallback
      expect(Utils.normalizeLanguage('INVALID')).toBe('zh') // fallback
    })
    
    test('validateGenerationParams should validate correctly', () => {
      // Valid params
      expect(Utils.validateGenerationParams({
        language: 'zh',
        count: 5,
        min_length: 2,
        max_length: 8,
        quality_threshold: 0.7
      })).toEqual([])
      
      // Invalid count
      expect(Utils.validateGenerationParams({
        count: 0
      })).toContain('count must be between 1 and 50')
      
      expect(Utils.validateGenerationParams({
        count: 100
      })).toContain('count must be between 1 and 50')
      
      // Invalid length range
      expect(Utils.validateGenerationParams({
        min_length: 10,
        max_length: 5
      })).toContain('min_length cannot be greater than max_length')
      
      // Invalid quality threshold
      expect(Utils.validateGenerationParams({
        quality_threshold: 1.5
      })).toContain('quality_threshold must be between 0 and 1')
      
      expect(Utils.validateGenerationParams({
        quality_threshold: -0.5
      })).toContain('quality_threshold must be between 0 and 1')
      
      // Invalid language
      expect(Utils.validateGenerationParams({
        language: 'invalid'
      })).toContain('unsupported language: invalid')
    })
  })
  
  describe('Error Classes', () => {
    test('ValidationError should work correctly', () => {
      const error = new ValidationError('Test validation error', { field: 'test' })
      
      expect(error.name).toBe('ValidationError')
      expect(error.code).toBe('VALIDATION_ERROR')
      expect(error.message).toBe('Test validation error')
      expect(error.details).toEqual({ field: 'test' })
      expect(error instanceof Error).toBe(true)
    })
  })
  
  describe('Type Guards', () => {
    test('should properly type guard language codes', () => {
      const testLanguage = 'zh'
      if (Utils.isSupportedLanguage(testLanguage)) {
        // TypeScript should know testLanguage is LanguageCode here
        expect(typeof testLanguage).toBe('string')
      }
    })
    
    test('should properly type guard cultural styles', () => {
      const testStyle = 'modern'
      if (Utils.isValidStyle(testStyle)) {
        // TypeScript should know testStyle is CulturalStyle here
        expect(typeof testStyle).toBe('string')
      }
    })
  })
  
  describe('Constants', () => {
    test('should export correct language constants', () => {
      const { Languages } = require('../../types')
      expect(Languages.CHINESE).toBe('zh')
      expect(Languages.ENGLISH).toBe('en')
      expect(Languages.JAPANESE).toBe('ja')
    })
    
    test('should export correct style constants', () => {
      const { Styles } = require('../../types')
      expect(Styles.MODERN).toBe('modern')
      expect(Styles.TRADITIONAL).toBe('traditional')
      expect(Styles.CUTE).toBe('cute')
    })
    
    test('should export correct theme constants', () => {
      const { Themes } = require('../../types')
      expect(Themes.NATURE).toBe('nature')
      expect(Themes.EMOTION).toBe('emotion')
      expect(Themes.TECHNOLOGY).toBe('tech')
    })
  })
})

// 集成测试辅助函数
export function createSampleLexicalEntry(overrides: Partial<LexicalEntry> = {}): LexicalEntry {
  return {
    word: '测试',
    language: 'zh',
    pos: PartOfSpeech.NOUN,
    domains: [SemanticDomain.NATURE],
    cultural_scores: {
      [CulturalStyle.TRADITIONAL]: 0.5,
      [CulturalStyle.MODERN]: 0.5,
      [CulturalStyle.CUTE]: 0.5,
      [CulturalStyle.COOL]: 0.5,
      [CulturalStyle.ELEGANT]: 0.5,
      [CulturalStyle.PLAYFUL]: 0.5,
      [CulturalStyle.MYSTERIOUS]: 0.5,
      [CulturalStyle.POWERFUL]: 0.5
    },
    frequency: 0.5,
    popularity: 0.5,
    sentiment: 0.0,
    formality: 0.5,
    age_groups: ['adult'],
    syllables: 2,
    length: 2,
    related_words: [],
    taboo_level: 0,
    created_at: Date.now(),
    weight: 1.0,
    ...overrides
  }
}

export function createSampleGrammarPattern(overrides: Partial<GrammarPattern> = {}): GrammarPattern {
  return {
    id: 'test_pattern',
    name: '测试模式',
    language: 'zh',
    structure: [PartOfSpeech.NOUN],
    semantic_constraints: [],
    cultural_fitness: {
      [CulturalStyle.TRADITIONAL]: 0.5,
      [CulturalStyle.MODERN]: 0.5,
      [CulturalStyle.CUTE]: 0.5,
      [CulturalStyle.COOL]: 0.5,
      [CulturalStyle.ELEGANT]: 0.5,
      [CulturalStyle.PLAYFUL]: 0.5,
      [CulturalStyle.MYSTERIOUS]: 0.5,
      [CulturalStyle.POWERFUL]: 0.5
    },
    weight: 1.0,
    examples: [],
    min_length: 1,
    max_length: 10,
    complexity: 0.5,
    ...overrides
  }
}
