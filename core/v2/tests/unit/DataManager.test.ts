/**
 * 数据管理系统单元测试
 * 
 * 测试数据访问、索引管理和缓存机制
 */

import * as fs from 'fs/promises'
import * as path from 'path'
import {
  DataManager,
  FileSystemDataAccess,
  IndexManager,
  LexicalEntry,
  GrammarPattern,
  CultureConfig,
  LexicalDataFile,
  GrammarDataFile,
  CultureDataFile,
  SystemConfig,
  PartOfSpeech,
  SemanticDomain,
  CulturalStyle
} from '../../types'

import { createSampleLexicalEntry, createSampleGrammarPattern } from './DataStructures.test'

// 测试数据目录
const TEST_DATA_DIR = path.join(__dirname, '../test_data')

describe('DataManager', () => {
  
  beforeAll(async () => {
    // 创建测试数据目录
    await fs.mkdir(TEST_DATA_DIR, { recursive: true })
    await fs.mkdir(path.join(TEST_DATA_DIR, 'lexical'), { recursive: true })
    await fs.mkdir(path.join(TEST_DATA_DIR, 'grammar'), { recursive: true })
    await fs.mkdir(path.join(TEST_DATA_DIR, 'culture'), { recursive: true })
  })
  
  afterAll(async () => {
    // 清理测试数据
    try {
      await fs.rm(TEST_DATA_DIR, { recursive: true, force: true })
    } catch (error) {
      // 忽略清理错误
    }
  })
  
  describe('FileSystemDataAccess', () => {
    let dataAccess: FileSystemDataAccess
    
    beforeEach(() => {
      dataAccess = new FileSystemDataAccess(TEST_DATA_DIR)
    })
    
    test('should initialize data directories', async () => {
      await dataAccess.initialize()
      
      // 检查目录是否存在
      const lexicalDir = path.join(TEST_DATA_DIR, 'lexical')
      const grammarDir = path.join(TEST_DATA_DIR, 'grammar')
      const cultureDir = path.join(TEST_DATA_DIR, 'culture')
      
      await expect(fs.access(lexicalDir)).resolves.not.toThrow()
      await expect(fs.access(grammarDir)).resolves.not.toThrow()
      await expect(fs.access(cultureDir)).resolves.not.toThrow()
    })
    
    test('should save and load lexical data', async () => {
      await dataAccess.initialize()
      
      const sampleWords: LexicalEntry[] = [
        createSampleLexicalEntry({ word: '星', language: 'zh' }),
        createSampleLexicalEntry({ word: '月', language: 'zh' })
      ]
      
      // 保存数据
      await dataAccess.saveLexicalData('zh', sampleWords)
      
      // 加载数据
      const loadedWords = await dataAccess.loadLexicalData('zh')
      
      expect(loadedWords).toHaveLength(2)
      expect(loadedWords[0].word).toBe('星')
      expect(loadedWords[1].word).toBe('月')
    })
    
    test('should save and load grammar patterns', async () => {
      await dataAccess.initialize()
      
      const samplePatterns: GrammarPattern[] = [
        createSampleGrammarPattern({ 
          id: 'adj_noun',
          structure: [PartOfSpeech.ADJECTIVE, PartOfSpeech.NOUN]
        })
      ]
      
      await dataAccess.saveGrammarPatterns('zh', samplePatterns)
      const loadedPatterns = await dataAccess.loadGrammarPatterns('zh')
      
      expect(loadedPatterns).toHaveLength(1)
      expect(loadedPatterns[0].id).toBe('adj_noun')
      expect(loadedPatterns[0].structure).toEqual([PartOfSpeech.ADJECTIVE, PartOfSpeech.NOUN])
    })
    
    test('should save and load culture config', async () => {
      await dataAccess.initialize()
      
      const sampleConfig: CultureConfig = {
        culture_id: 'zh-CN',
        language: 'zh',
        name: '中国文化',
        style_preferences: {
          [CulturalStyle.TRADITIONAL]: 0.8,
          [CulturalStyle.MODERN]: 0.6,
          [CulturalStyle.CUTE]: 0.5,
          [CulturalStyle.COOL]: 0.4,
          [CulturalStyle.ELEGANT]: 0.9,
          [CulturalStyle.PLAYFUL]: 0.3,
          [CulturalStyle.MYSTERIOUS]: 0.6,
          [CulturalStyle.POWERFUL]: 0.7
        },
        domain_preferences: {
          [SemanticDomain.NATURE]: 0.9,
          [SemanticDomain.EMOTION]: 0.7,
          [SemanticDomain.ACTION]: 0.5,
          [SemanticDomain.QUALITY]: 0.8,
          [SemanticDomain.TIME]: 0.6,
          [SemanticDomain.SPACE]: 0.5,
          [SemanticDomain.TECHNOLOGY]: 0.4,
          [SemanticDomain.CULTURE]: 0.9,
          [SemanticDomain.FANTASY]: 0.3,
          [SemanticDomain.SOCIAL]: 0.6
        },
        length_preference: { min: 2, max: 8, optimal: 4 },
        taboo_words: new Set(['禁用词']),
        trending_words: ['流行词'],
        special_rules: []
      }
      
      await dataAccess.saveCultureConfig(sampleConfig)
      const loadedConfig = await dataAccess.loadCultureConfig('zh-CN')
      
      expect(loadedConfig.culture_id).toBe('zh-CN')
      expect(loadedConfig.name).toBe('中国文化')
      expect(loadedConfig.style_preferences[CulturalStyle.TRADITIONAL]).toBe(0.8)
    })
    
    test('should return empty array for non-existent lexical data', async () => {
      await dataAccess.initialize()
      
      const words = await dataAccess.loadLexicalData('nonexistent')
      expect(words).toEqual([])
    })
    
    test('should return empty array for non-existent grammar patterns', async () => {
      await dataAccess.initialize()
      
      const patterns = await dataAccess.loadGrammarPatterns('nonexistent')
      expect(patterns).toEqual([])
    })
    
    test('should throw error for non-existent culture config', async () => {
      await dataAccess.initialize()
      
      await expect(dataAccess.loadCultureConfig('nonexistent'))
        .rejects.toThrow()
    })
    
    test('should validate data integrity', async () => {
      await dataAccess.initialize()
      
      // 创建无效的数据文件
      const invalidData: LexicalDataFile = {
        meta: {
          language: 'zh',
          version: '1.0.0',
          created_at: new Date().toISOString(),
          word_count: 2, // 错误的计数
          checksum: 'invalid'
        },
        words: [createSampleLexicalEntry()] // 只有1个词但声明有2个
      }
      
      const filePath = path.join(TEST_DATA_DIR, 'lexical', 'invalid.json')
      await fs.writeFile(filePath, JSON.stringify(invalidData))
      
      await expect(dataAccess.loadLexicalData('invalid'))
        .rejects.toThrow('Word count mismatch')
    })
    
    test('should use cache for repeated requests', async () => {
      await dataAccess.initialize()
      
      const sampleWords = [createSampleLexicalEntry({ word: '缓存测试' })]
      await dataAccess.saveLexicalData('cache_test', sampleWords)
      
      // 第一次加载
      const start1 = Date.now()
      const words1 = await dataAccess.loadLexicalData('cache_test')
      const time1 = Date.now() - start1
      
      // 第二次加载（应该使用缓存）
      const start2 = Date.now()
      const words2 = await dataAccess.loadLexicalData('cache_test')
      const time2 = Date.now() - start2
      
      expect(words1).toEqual(words2)
      expect(time2).toBeLessThan(time1) // 缓存应该更快
    })
  })
  
  describe('IndexManager', () => {
    let indexManager: IndexManager
    
    beforeEach(() => {
      indexManager = new IndexManager()
    })
    
    test('should build lexical index correctly', () => {
      const words: LexicalEntry[] = [
        createSampleLexicalEntry({
          word: '星',
          pos: PartOfSpeech.NOUN,
          domains: [SemanticDomain.NATURE],
          length: 1,
          syllables: 1,
          cultural_scores: { [CulturalStyle.MODERN]: 0.8 } as any
        }),
        createSampleLexicalEntry({
          word: '美丽',
          pos: PartOfSpeech.ADJECTIVE,
          domains: [SemanticDomain.QUALITY],
          length: 2,
          syllables: 2,
          cultural_scores: { [CulturalStyle.ELEGANT]: 0.9 } as any
        })
      ]
      
      const index = indexManager.buildLexicalIndex('zh', words)
      
      // 检查按词性索引
      expect(index.by_pos.get(PartOfSpeech.NOUN)).toContain('星')
      expect(index.by_pos.get(PartOfSpeech.ADJECTIVE)).toContain('美丽')
      
      // 检查按语义域索引
      expect(index.by_domain.get(SemanticDomain.NATURE)).toContain('星')
      expect(index.by_domain.get(SemanticDomain.QUALITY)).toContain('美丽')
      
      // 检查按长度索引
      expect(index.by_length.get(1)).toContain('星')
      expect(index.by_length.get(2)).toContain('美丽')
      
      // 检查按音节数索引
      expect(index.by_syllables.get(1)).toContain('星')
      expect(index.by_syllables.get(2)).toContain('美丽')
      
      // 检查搜索索引
      expect(index.search_index.get('星')).toContain('星')
      expect(index.search_index.get('美')).toContain('美丽') // 前缀搜索
    })
    
    test('should handle empty word list', () => {
      const index = indexManager.buildLexicalIndex('zh', [])
      
      expect(index.by_pos.size).toBe(0)
      expect(index.by_domain.size).toBe(0)
      expect(index.by_length.size).toBe(0)
      expect(index.search_index.size).toBe(0)
    })
    
    test('should handle words with multiple domains', () => {
      const words: LexicalEntry[] = [
        createSampleLexicalEntry({
          word: '爱心',
          domains: [SemanticDomain.EMOTION, SemanticDomain.SOCIAL]
        })
      ]
      
      const index = indexManager.buildLexicalIndex('zh', words)
      
      expect(index.by_domain.get(SemanticDomain.EMOTION)).toContain('爱心')
      expect(index.by_domain.get(SemanticDomain.SOCIAL)).toContain('爱心')
    })
    
    test('should get existing index', () => {
      const words = [createSampleLexicalEntry()]
      indexManager.buildLexicalIndex('zh', words)
      
      const retrievedIndex = indexManager.getIndex('zh')
      expect(retrievedIndex).toBeDefined()
      expect(retrievedIndex!.by_pos.size).toBeGreaterThan(0)
    })
    
    test('should return undefined for non-existent index', () => {
      const index = indexManager.getIndex('nonexistent')
      expect(index).toBeUndefined()
    })
  })
  
  describe('DataManager Integration', () => {
    let dataManager: DataManager
    const config: SystemConfig = {
      data_directory: TEST_DATA_DIR,
      cache: {
        max_size: 100,
        default_ttl: 60000,
        cleanup_interval: 30000,
        enable_stats: true
      },
      performance: {
        max_generation_time: 5000,
        max_concurrent_generations: 10,
        enable_profiling: false
      },
      logging: {
        level: 'info',
        enable_file_logging: false,
        log_directory: './logs'
      },
      supported_languages: ['zh', 'en', 'ja'],
      defaults: {
        language: 'zh',
        style: CulturalStyle.MODERN,
        count: 5,
        creativity: 0.5
      }
    }
    
    beforeEach(() => {
      dataManager = new DataManager(config)
    })
    
    test('should initialize successfully', async () => {
      await expect(dataManager.initialize()).resolves.not.toThrow()
    })
    
    test('should load language data and build index', async () => {
      await dataManager.initialize()
      
      // 准备测试数据
      const sampleWords = [
        createSampleLexicalEntry({ word: '测试', language: 'zh' })
      ]
      
      const dataAccess = new FileSystemDataAccess(TEST_DATA_DIR)
      await dataAccess.initialize()
      await dataAccess.saveLexicalData('zh', sampleWords)
      
      // 加载语言数据
      await dataManager.loadLanguageData('zh')
      
      // 检查数据是否加载
      const words = await dataManager.getLexicalData('zh')
      expect(words).toHaveLength(1)
      expect(words[0].word).toBe('测试')
      
      // 检查索引是否构建
      const index = dataManager.getIndex('zh')
      expect(index).toBeDefined()
      expect(index!.search_index.get('测试')).toContain('测试')
    })
    
    test('should search words with criteria', async () => {
      await dataManager.initialize()
      
      const sampleWords = [
        createSampleLexicalEntry({
          word: '美丽',
          pos: PartOfSpeech.ADJECTIVE,
          domains: [SemanticDomain.QUALITY],
          length: 2,
          syllables: 2
        }),
        createSampleLexicalEntry({
          word: '星星',
          pos: PartOfSpeech.NOUN,
          domains: [SemanticDomain.NATURE],
          length: 2,
          syllables: 2
        }),
        createSampleLexicalEntry({
          word: '技术',
          pos: PartOfSpeech.NOUN,
          domains: [SemanticDomain.TECHNOLOGY],
          length: 2,
          syllables: 2
        })
      ]
      
      const dataAccess = new FileSystemDataAccess(TEST_DATA_DIR)
      await dataAccess.initialize()
      await dataAccess.saveLexicalData('zh', sampleWords)
      await dataManager.loadLanguageData('zh')
      
      // 按词性搜索
      const adjectives = await dataManager.searchWords('zh', {
        pos: [PartOfSpeech.ADJECTIVE]
      })
      expect(adjectives).toHaveLength(1)
      expect(adjectives[0].word).toBe('美丽')
      
      // 按语义域搜索
      const natureWords = await dataManager.searchWords('zh', {
        domains: [SemanticDomain.NATURE]
      })
      expect(natureWords).toHaveLength(1)
      expect(natureWords[0].word).toBe('星星')
      
      // 按长度搜索
      const shortWords = await dataManager.searchWords('zh', {
        maxLength: 2
      })
      expect(shortWords).toHaveLength(3) // 所有词都是2个字符
      
      // 组合条件搜索
      const specificWords = await dataManager.searchWords('zh', {
        pos: [PartOfSpeech.NOUN],
        domains: [SemanticDomain.NATURE],
        minLength: 2,
        maxLength: 2
      })
      expect(specificWords).toHaveLength(1)
      expect(specificWords[0].word).toBe('星星')
    })
    
    test('should handle search with no results', async () => {
      await dataManager.initialize()
      await dataManager.loadLanguageData('zh')
      
      const results = await dataManager.searchWords('zh', {
        domains: [SemanticDomain.FANTASY],
        pos: [PartOfSpeech.VERB],
        minLength: 10 // 不太可能的长度
      })
      
      expect(results).toEqual([])
    })
    
    test('should get supported and loaded languages', async () => {
      await dataManager.initialize()
      
      const supported = dataManager.getSupportedLanguages()
      expect(supported).toEqual(['zh', 'en', 'ja'])
      
      const loaded = dataManager.getLoadedLanguages()
      expect(loaded).toEqual([]) // 初始时没有加载任何语言
      
      await dataManager.loadLanguageData('zh')
      const loadedAfter = dataManager.getLoadedLanguages()
      expect(loadedAfter).toContain('zh')
    })
    
    test('should not reload already loaded language', async () => {
      await dataManager.initialize()
      
      // 准备测试数据
      const dataAccess = new FileSystemDataAccess(TEST_DATA_DIR)
      await dataAccess.initialize()
      await dataAccess.saveLexicalData('zh', [createSampleLexicalEntry()])
      
      // 第一次加载
      const start1 = Date.now()
      await dataManager.loadLanguageData('zh')
      const time1 = Date.now() - start1
      
      // 第二次加载（应该跳过）
      const start2 = Date.now()
      await dataManager.loadLanguageData('zh')
      const time2 = Date.now() - start2
      
      expect(time2).toBeLessThan(time1) // 第二次应该更快（跳过加载）
    })
  })
  
  describe('Error Handling', () => {
    test('should handle file system errors gracefully', async () => {
      const invalidDataAccess = new FileSystemDataAccess('/invalid/path/that/does/not/exist')
      
      await expect(invalidDataAccess.initialize()).rejects.toThrow()
    })
    
    test('should handle corrupted data files', async () => {
      const dataAccess = new FileSystemDataAccess(TEST_DATA_DIR)
      await dataAccess.initialize()
      
      // 创建损坏的JSON文件
      const corruptedFile = path.join(TEST_DATA_DIR, 'lexical', 'corrupted.json')
      await fs.writeFile(corruptedFile, 'invalid json content')
      
      await expect(dataAccess.loadLexicalData('corrupted'))
        .rejects.toThrow()
    })
  })
  
  describe('Performance Tests', () => {
    test('should handle large datasets efficiently', async () => {
      const indexManager = new IndexManager()
      
      // 创建大量测试数据
      const largeWordSet: LexicalEntry[] = []
      for (let i = 0; i < 10000; i++) {
        largeWordSet.push(createSampleLexicalEntry({
          word: `word_${i}`,
          pos: i % 2 === 0 ? PartOfSpeech.NOUN : PartOfSpeech.ADJECTIVE,
          domains: [Object.values(SemanticDomain)[i % Object.values(SemanticDomain).length]]
        }))
      }
      
      const startTime = Date.now()
      const index = indexManager.buildLexicalIndex('performance_test', largeWordSet)
      const endTime = Date.now()
      
      expect(endTime - startTime).toBeLessThan(1000) // 应该在1秒内完成
      expect(index.by_pos.size).toBeGreaterThan(0)
      expect(index.search_index.size).toBeGreaterThan(0)
    })
  })
})
