/**
 * 生成引擎单元测试
 * 
 * 测试用户名生成引擎的核心功能
 */

import {
  UsernameGenerationEngine,
  DataManager,
  GenerationContext,
  GenerationResult,
  SystemConfig,
  CulturalStyle,
  SemanticDomain,
  PartOfSpeech
} from '../../types'

import { createSampleLexicalEntry, createSampleGrammarPattern } from './DataStructures.test'

// Mock DataManager for testing
class MockDataManager extends DataManager {
  private mockWords: any[] = []
  private mockPatterns: any[] = []
  
  constructor() {
    super({} as SystemConfig)
  }
  
  setMockData(words: any[], patterns: any[]) {
    this.mockWords = words
    this.mockPatterns = patterns
  }
  
  async loadLanguageData(language: string): Promise<void> {
    // Mock implementation
  }
  
  async getLexicalData(language: string): Promise<any[]> {
    return this.mockWords
  }
  
  async getGrammarPatterns(language: string): Promise<any[]> {
    return this.mockPatterns
  }
  
  async searchWords(language: string, criteria: any): Promise<any[]> {
    return this.mockWords.filter(word => {
      if (criteria.pos && !criteria.pos.includes(word.pos)) return false
      if (criteria.domains && !criteria.domains.some((d: any) => word.domains.includes(d))) return false
      if (criteria.styles && !criteria.styles.some((s: any) => word.cultural_scores[s] > 0.5)) return false
      if (criteria.minLength && word.length < criteria.minLength) return false
      if (criteria.maxLength && word.length > criteria.maxLength) return false
      return true
    })
  }
}

describe('GenerationEngine', () => {
  let engine: UsernameGenerationEngine
  let mockDataManager: MockDataManager
  
  beforeEach(() => {
    mockDataManager = new MockDataManager()
    engine = new UsernameGenerationEngine(mockDataManager)
    
    // 设置测试数据
    const mockWords = [
      createSampleLexicalEntry({
        word: '星',
        pos: PartOfSpeech.NOUN,
        domains: [SemanticDomain.NATURE],
        cultural_scores: { [CulturalStyle.MODERN]: 0.8 } as any,
        sentiment: 0.7,
        formality: 0.6
      }),
      createSampleLexicalEntry({
        word: '美',
        pos: PartOfSpeech.ADJECTIVE,
        domains: [SemanticDomain.QUALITY],
        cultural_scores: { [CulturalStyle.MODERN]: 0.9 } as any,
        sentiment: 0.9,
        formality: 0.6
      }),
      createSampleLexicalEntry({
        word: '月',
        pos: PartOfSpeech.NOUN,
        domains: [SemanticDomain.NATURE],
        cultural_scores: { [CulturalStyle.TRADITIONAL]: 0.9 } as any,
        sentiment: 0.6,
        formality: 0.7
      }),
      createSampleLexicalEntry({
        word: '清',
        pos: PartOfSpeech.ADJECTIVE,
        domains: [SemanticDomain.QUALITY],
        cultural_scores: { [CulturalStyle.ELEGANT]: 0.9 } as any,
        sentiment: 0.8,
        formality: 0.8
      })
    ]
    
    const mockPatterns = [
      createSampleGrammarPattern({
        id: 'single_noun',
        structure: [PartOfSpeech.NOUN],
        cultural_fitness: { [CulturalStyle.MODERN]: 0.7 } as any
      }),
      createSampleGrammarPattern({
        id: 'adj_noun',
        structure: [PartOfSpeech.ADJECTIVE, PartOfSpeech.NOUN],
        cultural_fitness: { [CulturalStyle.MODERN]: 0.8 } as any
      }),
      createSampleGrammarPattern({
        id: 'traditional_pattern',
        structure: [PartOfSpeech.NOUN],
        cultural_fitness: { [CulturalStyle.TRADITIONAL]: 0.9 } as any
      })
    ]
    
    mockDataManager.setMockData(mockWords, mockPatterns)
  })
  
  describe('Basic Generation', () => {
    test('should generate usernames successfully', async () => {
      const context: GenerationContext = {
        language: 'zh',
        style: CulturalStyle.MODERN,
        preferred_domains: [SemanticDomain.NATURE],
        length_range: [1, 4],
        sentiment_target: 0.7,
        formality_target: 0.6,
        target_age: 'adult',
        avoid_words: new Set(),
        creativity: 0.5,
        count: 3,
        unique: true
      }
      
      const results = await engine.generateUsernames(context)
      
      expect(results).toHaveLength(3)
      results.forEach(result => {
        expect(result.username).toBeDefined()
        expect(result.username.length).toBeGreaterThan(0)
        expect(result.components).toBeDefined()
        expect(result.pattern).toBeDefined()
        expect(result.quality_score).toBeGreaterThanOrEqual(0)
        expect(result.quality_score).toBeLessThanOrEqual(1)
        expect(result.generated_at).toBeGreaterThan(0)
      })
    })
    
    test('should respect count parameter', async () => {
      const context: GenerationContext = {
        language: 'zh',
        style: CulturalStyle.MODERN,
        preferred_domains: [],
        length_range: [1, 4],
        sentiment_target: 0.5,
        formality_target: 0.5,
        target_age: 'adult',
        avoid_words: new Set(),
        creativity: 0.5,
        count: 5,
        unique: true
      }
      
      const results = await engine.generateUsernames(context)
      expect(results.length).toBeLessThanOrEqual(5) // 可能少于5个，但不应该多于5个
    })
    
    test('should generate unique usernames when required', async () => {
      const context: GenerationContext = {
        language: 'zh',
        style: CulturalStyle.MODERN,
        preferred_domains: [],
        length_range: [1, 4],
        sentiment_target: 0.5,
        formality_target: 0.5,
        target_age: 'adult',
        avoid_words: new Set(),
        creativity: 0.5,
        count: 3,
        unique: true
      }
      
      const results = await engine.generateUsernames(context)
      const usernames = results.map(r => r.username)
      const uniqueUsernames = new Set(usernames)
      
      expect(uniqueUsernames.size).toBe(usernames.length)
    })
  })
  
  describe('Style Adaptation', () => {
    test('should adapt to different cultural styles', async () => {
      const modernContext: GenerationContext = {
        language: 'zh',
        style: CulturalStyle.MODERN,
        preferred_domains: [],
        length_range: [1, 4],
        sentiment_target: 0.5,
        formality_target: 0.5,
        target_age: 'adult',
        avoid_words: new Set(),
        creativity: 0.5,
        count: 2,
        unique: true
      }
      
      const traditionalContext: GenerationContext = {
        ...modernContext,
        style: CulturalStyle.TRADITIONAL
      }
      
      const modernResults = await engine.generateUsernames(modernContext)
      const traditionalResults = await engine.generateUsernames(traditionalContext)
      
      expect(modernResults).toHaveLength(2)
      expect(traditionalResults).toHaveLength(2)
      
      // 检查是否使用了不同的模式或词汇
      const modernPatterns = modernResults.map(r => r.pattern.id)
      const traditionalPatterns = traditionalResults.map(r => r.pattern.id)
      
      // 至少应该有一些差异（虽然可能偶尔相同）
      expect(modernPatterns.concat(traditionalPatterns).length).toBeGreaterThan(0)
    })
    
    test('should respect domain preferences', async () => {
      const natureContext: GenerationContext = {
        language: 'zh',
        style: CulturalStyle.MODERN,
        preferred_domains: [SemanticDomain.NATURE],
        length_range: [1, 4],
        sentiment_target: 0.5,
        formality_target: 0.5,
        target_age: 'adult',
        avoid_words: new Set(),
        creativity: 0.5,
        count: 2,
        unique: true
      }
      
      const results = await engine.generateUsernames(natureContext)
      
      // 检查生成的用户名是否包含自然相关的词汇
      results.forEach(result => {
        const hasNatureWord = result.components.some(comp => 
          comp.domains.includes(SemanticDomain.NATURE)
        )
        // 由于是偏好而非强制要求，这里只是记录而不强制断言
        if (hasNatureWord) {
          console.log(`包含自然词汇: ${result.username}`)
        }
      })
      
      expect(results.length).toBeGreaterThan(0)
    })
  })
  
  describe('Quality Control', () => {
    test('should calculate quality scores', async () => {
      const context: GenerationContext = {
        language: 'zh',
        style: CulturalStyle.MODERN,
        preferred_domains: [],
        length_range: [1, 4],
        sentiment_target: 0.7,
        formality_target: 0.6,
        target_age: 'adult',
        avoid_words: new Set(),
        creativity: 0.5,
        count: 3,
        unique: true
      }
      
      const results = await engine.generateUsernames(context)
      
      results.forEach(result => {
        expect(result.quality_score).toBeGreaterThanOrEqual(0)
        expect(result.quality_score).toBeLessThanOrEqual(1)
        expect(result.scores).toBeDefined()
        expect(result.scores.semantic_coherence).toBeGreaterThanOrEqual(0)
        expect(result.scores.cultural_fitness).toBeGreaterThanOrEqual(0)
        expect(result.scores.phonetic_harmony).toBeGreaterThanOrEqual(0)
        expect(result.scores.memorability).toBeGreaterThanOrEqual(0)
        expect(result.scores.uniqueness).toBeGreaterThanOrEqual(0)
      })
    })
    
    test('should sort results by quality', async () => {
      const context: GenerationContext = {
        language: 'zh',
        style: CulturalStyle.MODERN,
        preferred_domains: [],
        length_range: [1, 4],
        sentiment_target: 0.5,
        formality_target: 0.5,
        target_age: 'adult',
        avoid_words: new Set(),
        creativity: 0.5,
        count: 3,
        unique: true
      }
      
      const results = await engine.generateUsernames(context)
      
      // 检查是否按质量分数降序排列
      for (let i = 1; i < results.length; i++) {
        expect(results[i].quality_score).toBeLessThanOrEqual(results[i - 1].quality_score)
      }
    })
  })
  
  describe('Constraint Handling', () => {
    test('should respect avoid_words constraint', async () => {
      const context: GenerationContext = {
        language: 'zh',
        style: CulturalStyle.MODERN,
        preferred_domains: [],
        length_range: [1, 4],
        sentiment_target: 0.5,
        formality_target: 0.5,
        target_age: 'adult',
        avoid_words: new Set(['星']), // 避免使用"星"
        creativity: 0.5,
        count: 3,
        unique: true
      }
      
      const results = await engine.generateUsernames(context)
      
      results.forEach(result => {
        expect(result.username).not.toContain('星')
        result.components.forEach(comp => {
          expect(comp.word).not.toBe('星')
        })
      })
    })
    
    test('should respect length constraints', async () => {
      const shortContext: GenerationContext = {
        language: 'zh',
        style: CulturalStyle.MODERN,
        preferred_domains: [],
        length_range: [1, 2],
        sentiment_target: 0.5,
        formality_target: 0.5,
        target_age: 'adult',
        avoid_words: new Set(),
        creativity: 0.5,
        count: 2,
        unique: true
      }
      
      const results = await engine.generateUsernames(shortContext)
      
      results.forEach(result => {
        expect(result.username.length).toBeGreaterThanOrEqual(1)
        expect(result.username.length).toBeLessThanOrEqual(2)
      })
    })
  })
  
  describe('Error Handling', () => {
    test('should handle empty word database gracefully', async () => {
      mockDataManager.setMockData([], [])
      
      const context: GenerationContext = {
        language: 'zh',
        style: CulturalStyle.MODERN,
        preferred_domains: [],
        length_range: [1, 4],
        sentiment_target: 0.5,
        formality_target: 0.5,
        target_age: 'adult',
        avoid_words: new Set(),
        creativity: 0.5,
        count: 3,
        unique: true
      }
      
      const results = await engine.generateUsernames(context)
      
      // 应该返回空数组或回退结果，而不是抛出异常
      expect(Array.isArray(results)).toBe(true)
    })
    
    test('should handle invalid context gracefully', async () => {
      const invalidContext: GenerationContext = {
        language: 'zh',
        style: CulturalStyle.MODERN,
        preferred_domains: [],
        length_range: [10, 5], // 无效范围
        sentiment_target: 2.0, // 超出范围
        formality_target: -1.0, // 超出范围
        target_age: 'adult',
        avoid_words: new Set(),
        creativity: 1.5, // 超出范围
        count: 0, // 无效数量
        unique: true
      }
      
      // 应该能够处理无效输入而不崩溃
      await expect(engine.generateUsernames(invalidContext)).resolves.toBeDefined()
    })
  })
  
  describe('Performance', () => {
    test('should generate usernames within reasonable time', async () => {
      const context: GenerationContext = {
        language: 'zh',
        style: CulturalStyle.MODERN,
        preferred_domains: [],
        length_range: [1, 4],
        sentiment_target: 0.5,
        formality_target: 0.5,
        target_age: 'adult',
        avoid_words: new Set(),
        creativity: 0.5,
        count: 5,
        unique: true
      }
      
      const startTime = Date.now()
      const results = await engine.generateUsernames(context)
      const endTime = Date.now()
      
      const duration = endTime - startTime
      expect(duration).toBeLessThan(1000) // 应该在1秒内完成
      expect(results.length).toBeGreaterThan(0)
    })
    
    test('should handle concurrent generation requests', async () => {
      const context: GenerationContext = {
        language: 'zh',
        style: CulturalStyle.MODERN,
        preferred_domains: [],
        length_range: [1, 4],
        sentiment_target: 0.5,
        formality_target: 0.5,
        target_age: 'adult',
        avoid_words: new Set(),
        creativity: 0.5,
        count: 2,
        unique: true
      }
      
      const promises = []
      for (let i = 0; i < 3; i++) {
        promises.push(engine.generateUsernames(context))
      }
      
      const results = await Promise.all(promises)
      
      results.forEach(result => {
        expect(Array.isArray(result)).toBe(true)
        expect(result.length).toBeGreaterThan(0)
      })
    })
  })
  
  describe('Creativity Levels', () => {
    test('should adapt generation strategy based on creativity level', async () => {
      const lowCreativityContext: GenerationContext = {
        language: 'zh',
        style: CulturalStyle.MODERN,
        preferred_domains: [],
        length_range: [1, 4],
        sentiment_target: 0.5,
        formality_target: 0.5,
        target_age: 'adult',
        avoid_words: new Set(),
        creativity: 0.1, // 低创意
        count: 2,
        unique: true
      }
      
      const highCreativityContext: GenerationContext = {
        ...lowCreativityContext,
        creativity: 0.9 // 高创意
      }
      
      const lowResults = await engine.generateUsernames(lowCreativityContext)
      const highResults = await engine.generateUsernames(highCreativityContext)
      
      expect(lowResults.length).toBeGreaterThan(0)
      expect(highResults.length).toBeGreaterThan(0)
      
      // 记录结果以便观察差异
      console.log('低创意结果:', lowResults.map(r => r.username))
      console.log('高创意结果:', highResults.map(r => r.username))
    })
  })
  
  describe('Generation Metadata', () => {
    test('should include generation metadata', async () => {
      const context: GenerationContext = {
        language: 'zh',
        style: CulturalStyle.MODERN,
        preferred_domains: [],
        length_range: [1, 4],
        sentiment_target: 0.5,
        formality_target: 0.5,
        target_age: 'adult',
        avoid_words: new Set(),
        creativity: 0.5,
        count: 2,
        unique: true
      }
      
      const results = await engine.generateUsernames(context)
      
      results.forEach(result => {
        expect(result.generated_at).toBeGreaterThan(0)
        expect(result.generation_time).toBeGreaterThanOrEqual(0)
        expect(result.pattern).toBeDefined()
        expect(result.pattern.id).toBeDefined()
        expect(result.components).toBeDefined()
        expect(result.components.length).toBeGreaterThan(0)
      })
    })
  })
})
