/**
 * 简单测试脚本
 * 
 * 验证V2系统的基础组件是否正常工作
 */

import * as fs from 'fs'
import * as path from 'path'

// 测试结果接口
interface TestResult {
  name: string
  passed: boolean
  error?: string
  duration: number
}

class SimpleTestRunner {
  private results: TestResult[] = []
  
  async runAllTests(): Promise<void> {
    console.log('🧪 开始V2系统简单测试')
    console.log('=' .repeat(50))
    
    // 测试文件结构
    await this.testFileStructure()
    
    // 测试数据文件
    await this.testDataFiles()
    
    // 测试类型定义
    await this.testTypeDefinitions()
    
    // 测试现有组件
    await this.testExistingComponents()
    
    // 输出结果
    this.printResults()
  }
  
  private async runTest(name: string, testFn: () => Promise<void> | void): Promise<void> {
    const startTime = Date.now()
    
    try {
      await testFn()
      this.results.push({
        name,
        passed: true,
        duration: Date.now() - startTime
      })
      console.log(`✅ ${name}`)
    } catch (error) {
      this.results.push({
        name,
        passed: false,
        error: error instanceof Error ? error.message : String(error),
        duration: Date.now() - startTime
      })
      console.log(`❌ ${name}: ${error}`)
    }
  }
  
  private async testFileStructure(): Promise<void> {
    console.log('\n📁 文件结构测试')
    console.log('-' .repeat(30))
    
    const requiredDirs = [
      'api',
      'construction', 
      'cultural',
      'data',
      'generator',
      'lexicon',
      'tests',
      'types'
    ]
    
    for (const dir of requiredDirs) {
      await this.runTest(`目录存在: ${dir}`, () => {
        const dirPath = path.join(__dirname, '..', dir)
        if (!fs.existsSync(dirPath)) {
          throw new Error(`目录不存在: ${dirPath}`)
        }
      })
    }
    
    const requiredFiles = [
      'types/index.ts',
      'api/UsernameGeneratorAPI.ts',
      'construction/ConstructionEngine.ts',
      'cultural/CulturalAdapter.ts',
      'generator/SemanticUsernameGenerator.ts',
      'lexicon/SemanticLexicon.ts'
    ]
    
    for (const file of requiredFiles) {
      await this.runTest(`文件存在: ${file}`, () => {
        const filePath = path.join(__dirname, '..', file)
        if (!fs.existsSync(filePath)) {
          throw new Error(`文件不存在: ${filePath}`)
        }
      })
    }
  }
  
  private async testDataFiles(): Promise<void> {
    console.log('\n📊 数据文件测试')
    console.log('-' .repeat(30))
    
    const dataFiles = [
      'data/lexical/zh.json',
      'data/grammar/zh.json', 
      'data/culture/zh-CN.json'
    ]
    
    for (const file of dataFiles) {
      await this.runTest(`数据文件: ${file}`, () => {
        const filePath = path.join(__dirname, '..', file)
        if (!fs.existsSync(filePath)) {
          throw new Error(`数据文件不存在: ${filePath}`)
        }
        
        // 验证JSON格式
        const content = fs.readFileSync(filePath, 'utf-8')
        const data = JSON.parse(content)
        
        if (!data || typeof data !== 'object') {
          throw new Error(`无效的JSON数据: ${file}`)
        }
        
        // 验证基本结构
        if (file.includes('lexical') && (!data.words || !Array.isArray(data.words))) {
          throw new Error(`词汇文件缺少words数组: ${file}`)
        }
        
        if (file.includes('grammar') && (!data.patterns || !Array.isArray(data.patterns))) {
          throw new Error(`语法文件缺少patterns数组: ${file}`)
        }
        
        if (file.includes('culture') && !data.config) {
          throw new Error(`文化文件缺少config对象: ${file}`)
        }
      })
    }
  }
  
  private async testTypeDefinitions(): Promise<void> {
    console.log('\n🔧 类型定义测试')
    console.log('-' .repeat(30))
    
    await this.runTest('类型文件语法检查', () => {
      const typesPath = path.join(__dirname, '..', 'types', 'index.ts')
      const content = fs.readFileSync(typesPath, 'utf-8')
      
      // 基本语法检查
      if (!content.includes('export')) {
        throw new Error('类型文件没有导出任何内容')
      }
      
      if (!content.includes('interface') && !content.includes('type') && !content.includes('enum')) {
        throw new Error('类型文件没有定义任何类型')
      }
    })
    
    await this.runTest('枚举定义检查', () => {
      const typesPath = path.join(__dirname, '..', 'types')
      const files = fs.readdirSync(typesPath)
      
      let hasEnums = false
      for (const file of files) {
        if (file.endsWith('.ts')) {
          const content = fs.readFileSync(path.join(typesPath, file), 'utf-8')
          if (content.includes('enum')) {
            hasEnums = true
            break
          }
        }
      }
      
      if (!hasEnums) {
        throw new Error('没有找到枚举定义')
      }
    })
  }
  
  private async testExistingComponents(): Promise<void> {
    console.log('\n🔍 组件测试')
    console.log('-' .repeat(30))
    
    // 测试API组件
    await this.runTest('API组件语法', () => {
      const apiPath = path.join(__dirname, '..', 'api', 'UsernameGeneratorAPI.ts')
      const content = fs.readFileSync(apiPath, 'utf-8')
      
      if (!content.includes('class') && !content.includes('interface')) {
        throw new Error('API文件没有定义类或接口')
      }
    })
    
    // 测试生成器组件
    await this.runTest('生成器组件语法', () => {
      const generatorPath = path.join(__dirname, '..', 'generator', 'SemanticUsernameGenerator.ts')
      const content = fs.readFileSync(generatorPath, 'utf-8')
      
      if (!content.includes('class') && !content.includes('interface')) {
        throw new Error('生成器文件没有定义类或接口')
      }
    })
    
    // 测试词典组件
    await this.runTest('词典组件语法', () => {
      const lexiconPath = path.join(__dirname, '..', 'lexicon', 'SemanticLexicon.ts')
      const content = fs.readFileSync(lexiconPath, 'utf-8')
      
      if (!content.includes('class') && !content.includes('interface')) {
        throw new Error('词典文件没有定义类或接口')
      }
    })
    
    // 测试构造引擎
    await this.runTest('构造引擎语法', () => {
      const constructionPath = path.join(__dirname, '..', 'construction', 'ConstructionEngine.ts')
      const content = fs.readFileSync(constructionPath, 'utf-8')
      
      if (!content.includes('class') && !content.includes('interface')) {
        throw new Error('构造引擎文件没有定义类或接口')
      }
    })
    
    // 测试文化适配器
    await this.runTest('文化适配器语法', () => {
      const culturalPath = path.join(__dirname, '..', 'cultural', 'CulturalAdapter.ts')
      const content = fs.readFileSync(culturalPath, 'utf-8')
      
      if (!content.includes('class') && !content.includes('interface')) {
        throw new Error('文化适配器文件没有定义类或接口')
      }
    })
  }
  
  private printResults(): void {
    console.log('\n📊 测试结果汇总')
    console.log('=' .repeat(50))
    
    const passed = this.results.filter(r => r.passed).length
    const failed = this.results.filter(r => !r.passed).length
    const total = this.results.length
    
    console.log(`总测试数: ${total}`)
    console.log(`✅ 通过: ${passed}`)
    console.log(`❌ 失败: ${failed}`)
    console.log(`通过率: ${((passed / total) * 100).toFixed(1)}%`)
    
    const totalTime = this.results.reduce((sum, r) => sum + r.duration, 0)
    console.log(`总耗时: ${totalTime}ms`)
    
    if (failed > 0) {
      console.log('\n❌ 失败的测试:')
      this.results
        .filter(r => !r.passed)
        .forEach(r => {
          console.log(`  - ${r.name}: ${r.error}`)
        })
    }
    
    if (passed === total) {
      console.log('\n🎉 所有测试通过！V2系统基础组件正常！')
    } else {
      console.log('\n⚠️  部分测试失败，需要检查相关组件')
    }
  }
}

// 主函数
async function main() {
  const runner = new SimpleTestRunner()
  await runner.runAllTests()
}

// 如果直接运行此文件，则执行测试
if (require.main === module) {
  main().catch(console.error)
}

export { SimpleTestRunner }
