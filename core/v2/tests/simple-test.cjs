/**
 * 简单测试脚本 (JavaScript版本)
 * 
 * 验证V2系统的基础组件是否正常工作
 */

const fs = require('fs');
const path = require('path');

class SimpleTestRunner {
  constructor() {
    this.results = [];
  }
  
  async runAllTests() {
    console.log('🧪 开始V2系统简单测试');
    console.log('='.repeat(50));
    
    // 测试文件结构
    await this.testFileStructure();
    
    // 测试数据文件
    await this.testDataFiles();
    
    // 测试类型定义
    await this.testTypeDefinitions();
    
    // 测试现有组件
    await this.testExistingComponents();
    
    // 输出结果
    this.printResults();
  }
  
  async runTest(name, testFn) {
    const startTime = Date.now();
    
    try {
      await testFn();
      this.results.push({
        name,
        passed: true,
        duration: Date.now() - startTime
      });
      console.log(`✅ ${name}`);
    } catch (error) {
      this.results.push({
        name,
        passed: false,
        error: error.message,
        duration: Date.now() - startTime
      });
      console.log(`❌ ${name}: ${error.message}`);
    }
  }
  
  async testFileStructure() {
    console.log('\n📁 文件结构测试');
    console.log('-'.repeat(30));
    
    const requiredDirs = [
      'api',
      'construction', 
      'cultural',
      'data',
      'generator',
      'lexicon',
      'tests',
      'types'
    ];
    
    for (const dir of requiredDirs) {
      await this.runTest(`目录存在: ${dir}`, () => {
        const dirPath = path.join(__dirname, '..', dir);
        if (!fs.existsSync(dirPath)) {
          throw new Error(`目录不存在: ${dirPath}`);
        }
      });
    }
    
    const requiredFiles = [
      'types/index.ts',
      'api/UsernameGeneratorAPI.ts',
      'construction/ConstructionEngine.ts',
      'cultural/CulturalAdapter.ts',
      'generator/SemanticUsernameGenerator.ts',
      'lexicon/SemanticLexicon.ts'
    ];
    
    for (const file of requiredFiles) {
      await this.runTest(`文件存在: ${file}`, () => {
        const filePath = path.join(__dirname, '..', file);
        if (!fs.existsSync(filePath)) {
          throw new Error(`文件不存在: ${filePath}`);
        }
      });
    }
  }
  
  async testDataFiles() {
    console.log('\n📊 数据文件测试');
    console.log('-'.repeat(30));
    
    const dataFiles = [
      'data/lexical/zh.json',
      'data/grammar/zh.json', 
      'data/culture/zh-CN.json'
    ];
    
    for (const file of dataFiles) {
      await this.runTest(`数据文件: ${file}`, () => {
        const filePath = path.join(__dirname, '..', file);
        if (!fs.existsSync(filePath)) {
          throw new Error(`数据文件不存在: ${filePath}`);
        }
        
        // 验证JSON格式
        const content = fs.readFileSync(filePath, 'utf-8');
        const data = JSON.parse(content);
        
        if (!data || typeof data !== 'object') {
          throw new Error(`无效的JSON数据: ${file}`);
        }
        
        // 验证基本结构
        if (file.includes('lexical') && (!data.words || !Array.isArray(data.words))) {
          throw new Error(`词汇文件缺少words数组: ${file}`);
        }
        
        if (file.includes('grammar') && (!data.patterns || !Array.isArray(data.patterns))) {
          throw new Error(`语法文件缺少patterns数组: ${file}`);
        }
        
        if (file.includes('culture') && !data.config) {
          throw new Error(`文化文件缺少config对象: ${file}`);
        }
        
        console.log(`    ✓ ${file} - ${data.words ? data.words.length : data.patterns ? data.patterns.length : 'config'} 项`);
      });
    }
  }
  
  async testTypeDefinitions() {
    console.log('\n🔧 类型定义测试');
    console.log('-'.repeat(30));
    
    await this.runTest('类型文件语法检查', () => {
      const typesPath = path.join(__dirname, '..', 'types', 'index.ts');
      const content = fs.readFileSync(typesPath, 'utf-8');
      
      // 基本语法检查
      if (!content.includes('export')) {
        throw new Error('类型文件没有导出任何内容');
      }
      
      if (!content.includes('interface') && !content.includes('type') && !content.includes('enum')) {
        throw new Error('类型文件没有定义任何类型');
      }
      
      console.log(`    ✓ 类型文件大小: ${Math.round(content.length / 1024)}KB`);
    });
    
    await this.runTest('枚举定义检查', () => {
      const typesPath = path.join(__dirname, '..', 'types');
      const files = fs.readdirSync(typesPath);
      
      let hasEnums = false;
      let enumCount = 0;
      for (const file of files) {
        if (file.endsWith('.ts')) {
          const content = fs.readFileSync(path.join(typesPath, file), 'utf-8');
          const enumMatches = content.match(/enum\s+\w+/g);
          if (enumMatches) {
            hasEnums = true;
            enumCount += enumMatches.length;
          }
        }
      }
      
      if (!hasEnums) {
        throw new Error('没有找到枚举定义');
      }
      
      console.log(`    ✓ 找到 ${enumCount} 个枚举定义`);
    });
  }
  
  async testExistingComponents() {
    console.log('\n🔍 组件测试');
    console.log('-'.repeat(30));
    
    const components = [
      { name: 'API组件', path: 'api/UsernameGeneratorAPI.ts' },
      { name: '生成器组件', path: 'generator/SemanticUsernameGenerator.ts' },
      { name: '词典组件', path: 'lexicon/SemanticLexicon.ts' },
      { name: '构造引擎', path: 'construction/ConstructionEngine.ts' },
      { name: '文化适配器', path: 'cultural/CulturalAdapter.ts' }
    ];
    
    for (const component of components) {
      await this.runTest(`${component.name}语法`, () => {
        const componentPath = path.join(__dirname, '..', component.path);
        const content = fs.readFileSync(componentPath, 'utf-8');
        
        if (!content.includes('class') && !content.includes('interface')) {
          throw new Error(`${component.name}文件没有定义类或接口`);
        }
        
        // 统计类和接口数量
        const classCount = (content.match(/class\s+\w+/g) || []).length;
        const interfaceCount = (content.match(/interface\s+\w+/g) || []).length;
        const functionCount = (content.match(/function\s+\w+/g) || []).length;
        
        console.log(`    ✓ ${component.name} - ${classCount}个类, ${interfaceCount}个接口, ${functionCount}个函数`);
      });
    }
  }
  
  printResults() {
    console.log('\n📊 测试结果汇总');
    console.log('='.repeat(50));
    
    const passed = this.results.filter(r => r.passed).length;
    const failed = this.results.filter(r => !r.passed).length;
    const total = this.results.length;
    
    console.log(`总测试数: ${total}`);
    console.log(`✅ 通过: ${passed}`);
    console.log(`❌ 失败: ${failed}`);
    console.log(`通过率: ${((passed / total) * 100).toFixed(1)}%`);
    
    const totalTime = this.results.reduce((sum, r) => sum + r.duration, 0);
    console.log(`总耗时: ${totalTime}ms`);
    
    if (failed > 0) {
      console.log('\n❌ 失败的测试:');
      this.results
        .filter(r => !r.passed)
        .forEach(r => {
          console.log(`  - ${r.name}: ${r.error}`);
        });
    }
    
    if (passed === total) {
      console.log('\n🎉 所有测试通过！V2系统基础组件正常！');
    } else if (passed / total >= 0.8) {
      console.log('\n👍 大部分测试通过，系统基本正常');
    } else {
      console.log('\n⚠️  部分测试失败，需要检查相关组件');
    }
    
    // 显示系统概览
    console.log('\n📋 系统概览:');
    console.log(`  - 核心组件: ${this.results.filter(r => r.name.includes('组件') && r.passed).length}/5`);
    console.log(`  - 数据文件: ${this.results.filter(r => r.name.includes('数据文件') && r.passed).length}/3`);
    console.log(`  - 文件结构: ${this.results.filter(r => r.name.includes('存在') && r.passed).length}/${this.results.filter(r => r.name.includes('存在')).length}`);
  }
}

// 主函数
async function main() {
  const runner = new SimpleTestRunner();
  await runner.runAllTests();
}

// 如果直接运行此文件，则执行测试
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { SimpleTestRunner };
