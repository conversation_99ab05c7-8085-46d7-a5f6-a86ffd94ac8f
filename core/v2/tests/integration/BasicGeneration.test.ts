/**
 * 基础生成功能集成测试
 * 
 * 测试完整的用户名生成流程
 */

import * as path from 'path'
import {
  createUsernameAPI,
  quickGenerate,
  UsernameGeneratorAPI,
  SystemConfig,
  CulturalStyle,
  SemanticDomain
} from '../../types'

// 使用实际的数据目录
const DATA_DIR = path.join(__dirname, '../../data')

describe('BasicGeneration Integration', () => {
  let api: UsernameGeneratorAPI
  
  const testConfig: Partial<SystemConfig> = {
    data_directory: DATA_DIR,
    supported_languages: ['zh'],
    defaults: {
      language: 'zh',
      style: CulturalStyle.MODERN,
      count: 3,
      creativity: 0.5
    },
    cache: {
      max_size: 100,
      default_ttl: 60000,
      cleanup_interval: 30000,
      enable_stats: true
    },
    performance: {
      max_generation_time: 5000,
      max_concurrent_generations: 5,
      enable_profiling: false
    },
    logging: {
      level: 'info',
      enable_file_logging: false,
      log_directory: './logs'
    }
  }
  
  beforeAll(async () => {
    // 创建API实例并初始化
    api = await createUsernameAPI(testConfig)
  }, 30000) // 30秒超时，因为需要加载数据
  
  describe('Quick Generation', () => {
    test('should generate Chinese usernames quickly', async () => {
      const usernames = await quickGenerate('zh', 3, CulturalStyle.MODERN)
      
      expect(usernames).toHaveLength(3)
      expect(usernames.every(name => typeof name === 'string')).toBe(true)
      expect(usernames.every(name => name.length > 0)).toBe(true)
      
      console.log('快速生成结果:', usernames)
    }, 10000)
    
    test('should generate different styles', async () => {
      const modernNames = await quickGenerate('zh', 2, CulturalStyle.MODERN)
      const traditionalNames = await quickGenerate('zh', 2, CulturalStyle.TRADITIONAL)
      const elegantNames = await quickGenerate('zh', 2, CulturalStyle.ELEGANT)
      
      expect(modernNames).toHaveLength(2)
      expect(traditionalNames).toHaveLength(2)
      expect(elegantNames).toHaveLength(2)
      
      console.log('现代风格:', modernNames)
      console.log('传统风格:', traditionalNames)
      console.log('优雅风格:', elegantNames)
    }, 15000)
  })
  
  describe('Advanced Generation', () => {
    test('should generate with specific themes', async () => {
      const response = await api.generateUsernames({
        language: 'zh',
        style: CulturalStyle.MODERN,
        themes: [SemanticDomain.NATURE],
        count: 3,
        debug: true
      })
      
      expect(response.success).toBe(true)
      expect(response.data).toBeDefined()
      expect(response.data!.usernames).toHaveLength(3)
      
      // 检查生成信息
      expect(response.data!.generation_info).toBeDefined()
      expect(response.data!.generation_info.total_time_ms).toBeGreaterThan(0)
      expect(response.data!.generation_info.strategy_used).toBeDefined()
      
      // 检查用户名质量
      response.data!.usernames.forEach(result => {
        expect(result.username).toBeDefined()
        expect(result.quality_score).toBeGreaterThan(0)
        expect(result.quality_score).toBeLessThanOrEqual(1)
        expect(result.components).toBeDefined()
        expect(result.pattern_used).toBeDefined()
      })
      
      console.log('自然主题生成结果:')
      response.data!.usernames.forEach((result, index) => {
        console.log(`${index + 1}. ${result.username} (质量: ${result.quality_score.toFixed(2)})`)
        console.log(`   模式: ${result.pattern_used}`)
        console.log(`   组成: ${result.components.map(c => c.word).join(' + ')}`)
      })
    }, 10000)
    
    test('should handle different creativity levels', async () => {
      const lowCreativity = await api.generateUsernames({
        language: 'zh',
        creativity: 'low',
        count: 2
      })
      
      const highCreativity = await api.generateUsernames({
        language: 'zh',
        creativity: 'high',
        count: 2
      })
      
      expect(lowCreativity.success).toBe(true)
      expect(highCreativity.success).toBe(true)
      
      console.log('低创意度:', lowCreativity.data?.usernames.map(u => u.username))
      console.log('高创意度:', highCreativity.data?.usernames.map(u => u.username))
    }, 10000)
    
    test('should respect length constraints', async () => {
      const shortNames = await api.generateUsernames({
        language: 'zh',
        min_length: 1,
        max_length: 2,
        count: 3
      })
      
      const longNames = await api.generateUsernames({
        language: 'zh',
        min_length: 4,
        max_length: 6,
        count: 3
      })
      
      expect(shortNames.success).toBe(true)
      expect(longNames.success).toBe(true)
      
      if (shortNames.data) {
        shortNames.data.usernames.forEach(result => {
          expect(result.username.length).toBeGreaterThanOrEqual(1)
          expect(result.username.length).toBeLessThanOrEqual(2)
        })
      }
      
      if (longNames.data) {
        longNames.data.usernames.forEach(result => {
          expect(result.username.length).toBeGreaterThanOrEqual(4)
          expect(result.username.length).toBeLessThanOrEqual(6)
        })
      }
      
      console.log('短用户名:', shortNames.data?.usernames.map(u => u.username))
      console.log('长用户名:', longNames.data?.usernames.map(u => u.username))
    }, 10000)
    
    test('should handle sentiment preferences', async () => {
      const positiveNames = await api.generateUsernames({
        language: 'zh',
        sentiment: 'positive',
        count: 3
      })
      
      const neutralNames = await api.generateUsernames({
        language: 'zh',
        sentiment: 'neutral',
        count: 3
      })
      
      expect(positiveNames.success).toBe(true)
      expect(neutralNames.success).toBe(true)
      
      console.log('积极情感:', positiveNames.data?.usernames.map(u => u.username))
      console.log('中性情感:', neutralNames.data?.usernames.map(u => u.username))
    }, 10000)
  })
  
  describe('Quality Control', () => {
    test('should generate high quality usernames', async () => {
      const response = await api.generateUsernames({
        language: 'zh',
        quality_threshold: 0.7,
        count: 5
      })
      
      expect(response.success).toBe(true)
      
      if (response.data) {
        response.data.usernames.forEach(result => {
          expect(result.quality_score).toBeGreaterThanOrEqual(0.7)
        })
        
        console.log('高质量用户名:')
        response.data.usernames.forEach((result, index) => {
          console.log(`${index + 1}. ${result.username} (${result.quality_score.toFixed(3)})`)
        })
      }
    }, 10000)
    
    test('should provide quality breakdown in debug mode', async () => {
      const response = await api.generateUsernames({
        language: 'zh',
        count: 1,
        debug: true
      })
      
      expect(response.success).toBe(true)
      expect(response.data?.debug_info).toBeDefined()
      
      if (response.data?.debug_info) {
        expect(response.data.debug_info.quality_breakdown).toBeDefined()
        console.log('质量分解:', response.data.debug_info.quality_breakdown)
      }
    }, 10000)
  })
  
  describe('Error Handling', () => {
    test('should handle invalid parameters gracefully', async () => {
      const response = await api.generateUsernames({
        language: 'zh',
        count: 0, // 无效数量
        min_length: 10,
        max_length: 5 // 无效范围
      })
      
      expect(response.success).toBe(false)
      expect(response.error).toBeDefined()
      expect(response.error!.code).toBe('INVALID_REQUEST')
      
      console.log('错误处理:', response.error!.message)
    })
    
    test('should handle unsupported language', async () => {
      const response = await api.generateUsernames({
        language: 'unsupported' as any,
        count: 1
      })
      
      expect(response.success).toBe(false)
      expect(response.error).toBeDefined()
      
      console.log('不支持的语言错误:', response.error!.message)
    })
  })
  
  describe('System Status', () => {
    test('should provide system status', async () => {
      const status = await api.getSystemStatus()
      
      expect(status.status).toBeDefined()
      expect(status.uptime).toBeGreaterThan(0)
      expect(status.performance).toBeDefined()
      expect(status.resources).toBeDefined()
      expect(status.languages).toBeDefined()
      
      expect(status.languages.supported).toContain('zh')
      expect(status.languages.loaded).toContain('zh')
      
      console.log('系统状态:')
      console.log(`  状态: ${status.status}`)
      console.log(`  运行时间: ${Math.round(status.uptime / 1000)}秒`)
      console.log(`  总生成次数: ${status.performance.total_generations}`)
      console.log(`  成功率: ${(status.performance.success_rate * 100).toFixed(1)}%`)
      console.log(`  平均响应时间: ${status.performance.avg_response_time.toFixed(0)}ms`)
      console.log(`  支持语言: ${status.languages.supported.join(', ')}`)
    })
    
    test('should provide performance report', async () => {
      const report = await api.getPerformanceReport()
      
      expect(report).toBeDefined()
      expect(report.summary).toBeDefined()
      
      console.log('性能报告:')
      console.log(`  成功率: ${(report.summary.success_rate * 100).toFixed(1)}%`)
      console.log(`  平均生成时间: ${report.summary.avg_generation_time.toFixed(0)}ms`)
      console.log(`  缓存效率: ${(report.summary.cache_efficiency * 100).toFixed(1)}%`)
      
      if (report.recommendations.length > 0) {
        console.log('  优化建议:')
        report.recommendations.forEach(rec => {
          console.log(`    - ${rec}`)
        })
      }
    })
  })
  
  describe('Performance Tests', () => {
    test('should generate usernames within time limit', async () => {
      const startTime = Date.now()
      
      const response = await api.generateUsernames({
        language: 'zh',
        count: 5
      })
      
      const endTime = Date.now()
      const duration = endTime - startTime
      
      expect(response.success).toBe(true)
      expect(duration).toBeLessThan(5000) // 5秒内完成
      
      console.log(`生成5个用户名耗时: ${duration}ms`)
    })
    
    test('should handle concurrent requests', async () => {
      const promises = []
      
      for (let i = 0; i < 3; i++) {
        promises.push(
          api.generateUsernames({
            language: 'zh',
            count: 2
          })
        )
      }
      
      const results = await Promise.all(promises)
      
      results.forEach(result => {
        expect(result.success).toBe(true)
      })
      
      console.log('并发请求结果:')
      results.forEach((result, index) => {
        if (result.data) {
          console.log(`  请求${index + 1}: ${result.data.usernames.map(u => u.username).join(', ')}`)
        }
      })
    }, 15000)
  })
})
