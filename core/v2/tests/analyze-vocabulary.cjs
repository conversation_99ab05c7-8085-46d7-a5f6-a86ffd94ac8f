/**
 * 词汇分析脚本
 * 
 * 分析当前词汇库的分布和质量，为进一步扩展提供指导
 */

const fs = require('fs');
const path = require('path');

class VocabularyAnalyzer {
  constructor() {
    this.lexicalData = [];
  }
  
  async analyze() {
    console.log('📊 开始词汇库分析');
    console.log('='.repeat(50));
    
    // 加载数据
    await this.loadData();
    
    // 基础统计
    this.analyzeBasicStats();
    
    // 词性分布
    this.analyzePartOfSpeech();
    
    // 语义域分布
    this.analyzeSemanticDomains();
    
    // 文化风格分布
    this.analyzeCulturalStyles();
    
    // 质量分析
    this.analyzeQuality();
    
    // 缺口分析
    this.analyzeGaps();
    
    // 扩展建议
    this.generateRecommendations();
  }
  
  async loadData() {
    try {
      const lexicalPath = path.join(__dirname, '..', 'data', 'lexical', 'zh.json');
      const lexicalContent = fs.readFileSync(lexicalPath, 'utf-8');
      const lexicalData = JSON.parse(lexicalContent);
      this.lexicalData = lexicalData.words || [];
      
      console.log(`✅ 已加载 ${this.lexicalData.length} 个词汇`);
    } catch (error) {
      console.error('❌ 加载数据失败:', error.message);
    }
  }
  
  analyzeBasicStats() {
    console.log('\n📈 基础统计');
    console.log('-'.repeat(30));
    
    const totalWords = this.lexicalData.length;
    const avgLength = this.lexicalData.reduce((sum, w) => sum + w.length, 0) / totalWords;
    const avgFrequency = this.lexicalData.reduce((sum, w) => sum + w.frequency, 0) / totalWords;
    const avgSentiment = this.lexicalData.reduce((sum, w) => sum + w.sentiment, 0) / totalWords;
    
    console.log(`总词汇数: ${totalWords}`);
    console.log(`平均长度: ${avgLength.toFixed(2)} 字符`);
    console.log(`平均频率: ${avgFrequency.toFixed(3)}`);
    console.log(`平均情感: ${avgSentiment.toFixed(3)}`);
  }
  
  analyzePartOfSpeech() {
    console.log('\n🔤 词性分布');
    console.log('-'.repeat(30));
    
    const posStats = {};
    this.lexicalData.forEach(word => {
      posStats[word.pos] = (posStats[word.pos] || 0) + 1;
    });
    
    Object.entries(posStats)
      .sort((a, b) => b[1] - a[1])
      .forEach(([pos, count]) => {
        const percentage = ((count / this.lexicalData.length) * 100).toFixed(1);
        console.log(`${pos}: ${count}个 (${percentage}%)`);
      });
  }
  
  analyzeSemanticDomains() {
    console.log('\n🌐 语义域分布');
    console.log('-'.repeat(30));
    
    const domainStats = {};
    this.lexicalData.forEach(word => {
      word.domains.forEach(domain => {
        domainStats[domain] = (domainStats[domain] || 0) + 1;
      });
    });
    
    Object.entries(domainStats)
      .sort((a, b) => b[1] - a[1])
      .forEach(([domain, count]) => {
        const percentage = ((count / this.lexicalData.length) * 100).toFixed(1);
        console.log(`${domain}: ${count}个 (${percentage}%)`);
      });
  }
  
  analyzeCulturalStyles() {
    console.log('\n🎨 文化风格分析');
    console.log('-'.repeat(30));
    
    const styles = ['traditional', 'modern', 'cute', 'cool', 'elegant', 'playful', 'mysterious', 'powerful'];
    
    styles.forEach(style => {
      const scores = this.lexicalData.map(w => w.cultural_scores[style] || 0);
      const avgScore = scores.reduce((sum, s) => sum + s, 0) / scores.length;
      const highQuality = scores.filter(s => s > 0.8).length;
      
      console.log(`${style}: 平均${avgScore.toFixed(3)}, 高质量${highQuality}个`);
    });
  }
  
  analyzeQuality() {
    console.log('\n⭐ 质量分析');
    console.log('-'.repeat(30));
    
    // 高频词
    const highFreq = this.lexicalData.filter(w => w.frequency > 0.8);
    console.log(`高频词 (>0.8): ${highFreq.length}个`);
    
    // 积极情感词
    const positive = this.lexicalData.filter(w => w.sentiment > 0.8);
    console.log(`积极情感词 (>0.8): ${positive.length}个`);
    
    // 多域词汇
    const multiDomain = this.lexicalData.filter(w => w.domains.length > 1);
    console.log(`多语义域词汇: ${multiDomain.length}个`);
    
    // 全能词汇（在多个风格中都表现良好）
    const versatile = this.lexicalData.filter(w => {
      const styles = ['traditional', 'modern', 'elegant'];
      return styles.every(style => (w.cultural_scores[style] || 0) > 0.6);
    });
    console.log(`全能词汇: ${versatile.length}个`);
  }
  
  analyzeGaps() {
    console.log('\n🔍 缺口分析');
    console.log('-'.repeat(30));
    
    // 词性缺口
    const posStats = {};
    this.lexicalData.forEach(word => {
      posStats[word.pos] = (posStats[word.pos] || 0) + 1;
    });
    
    console.log('词性不平衡:');
    if ((posStats['adj'] || 0) < (posStats['n'] || 0) * 0.6) {
      console.log('  ⚠️  形容词数量不足，建议增加');
    }
    if ((posStats['v'] || 0) < (posStats['n'] || 0) * 0.3) {
      console.log('  ⚠️  动词数量不足，建议增加');
    }
    
    // 语义域缺口
    const domainStats = {};
    this.lexicalData.forEach(word => {
      word.domains.forEach(domain => {
        domainStats[domain] = (domainStats[domain] || 0) + 1;
      });
    });
    
    console.log('语义域不平衡:');
    const expectedDomains = ['nature', 'quality', 'emotion', 'action', 'culture', 'tech', 'social'];
    expectedDomains.forEach(domain => {
      const count = domainStats[domain] || 0;
      if (count < 5) {
        console.log(`  ⚠️  ${domain}域词汇不足 (${count}个)，建议增加`);
      }
    });
    
    // 风格缺口
    console.log('风格适应性缺口:');
    const styles = ['modern', 'cute', 'cool'];
    styles.forEach(style => {
      const highQuality = this.lexicalData.filter(w => (w.cultural_scores[style] || 0) > 0.8).length;
      if (highQuality < 8) {
        console.log(`  ⚠️  ${style}风格高质量词汇不足 (${highQuality}个)，建议增加`);
      }
    });
  }
  
  generateRecommendations() {
    console.log('\n💡 扩展建议');
    console.log('-'.repeat(30));
    
    console.log('优先添加的词汇类型:');
    
    // 基于缺口分析的建议
    console.log('1. 形容词扩展:');
    console.log('   - 现代风格: 潮、炫、帅、酷炫、时尚');
    console.log('   - 可爱风格: 甜、软、呆、乖、萌萌');
    console.log('   - 优雅风格: 淡、素、幽、雅致、清雅');
    
    console.log('2. 动词扩展:');
    console.log('   - 动态词汇: 跃、奔、闪、跳、舞动');
    console.log('   - 情感词汇: 爱、恋、念、想、盼');
    console.log('   - 创造词汇: 创、造、绘、写、编');
    
    console.log('3. 名词扩展:');
    console.log('   - 现代科技: 码、网、云端、数字、像素');
    console.log('   - 文化艺术: 诗、画、曲、韵律、墨');
    console.log('   - 自然元素: 露、霜、雪、冰、晶');
    
    console.log('4. 语义域扩展:');
    console.log('   - tech域: 需要更多科技相关词汇');
    console.log('   - social域: 需要更多社交相关词汇');
    console.log('   - fantasy域: 需要更多幻想相关词汇');
    
    console.log('5. 质量提升建议:');
    console.log('   - 增加更多高频词汇 (frequency > 0.8)');
    console.log('   - 增加更多积极情感词汇 (sentiment > 0.8)');
    console.log('   - 增加更多跨域词汇 (多个语义域)');
    console.log('   - 平衡各风格的高质量词汇分布');
  }
}

// 主函数
async function main() {
  const analyzer = new VocabularyAnalyzer();
  await analyzer.analyze();
}

// 如果直接运行此文件，则执行分析
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { VocabularyAnalyzer };
