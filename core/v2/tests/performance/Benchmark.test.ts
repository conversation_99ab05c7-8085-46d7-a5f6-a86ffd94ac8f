/**
 * 性能基准测试
 * 
 * 测试V2系统的性能指标和基准
 */

import * as path from 'path'
import {
  createUsernameAPI,
  quickGenerate,
  UsernameGeneratorAPI,
  SystemConfig,
  CulturalStyle,
  SemanticDomain,
  WeightedRandomSelector,
  SemanticSimilarityCalculator,
  PhoneticHarmonyCalculator
} from '../../types'

import { createSampleLexicalEntry } from '../unit/DataStructures.test'

// 使用实际的数据目录
const DATA_DIR = path.join(__dirname, '../../data')

describe('Performance Benchmarks', () => {
  let api: UsernameGeneratorAPI
  
  const benchmarkConfig: Partial<SystemConfig> = {
    data_directory: DATA_DIR,
    supported_languages: ['zh'],
    cache: {
      max_size: 1000,
      default_ttl: 300000, // 5分钟
      cleanup_interval: 60000,
      enable_stats: true
    },
    performance: {
      max_generation_time: 5000,
      max_concurrent_generations: 10,
      enable_profiling: true
    }
  }
  
  beforeAll(async () => {
    try {
      api = await createUsernameAPI(benchmarkConfig)
    } catch (error) {
      console.warn('无法初始化API，跳过性能测试:', error)
    }
  }, 30000)
  
  describe('Algorithm Performance', () => {
    test('WeightedRandomSelector performance', () => {
      const weights = Array(10000).fill(0).map(() => Math.random())
      
      const startTime = Date.now()
      const selector = new WeightedRandomSelector(weights)
      const constructionTime = Date.now() - startTime
      
      expect(constructionTime).toBeLessThan(100) // 构造应该在100ms内
      
      const selectionStartTime = Date.now()
      for (let i = 0; i < 1000; i++) {
        selector.select()
      }
      const selectionTime = Date.now() - selectionStartTime
      
      expect(selectionTime).toBeLessThan(50) // 1000次选择应该在50ms内
      
      console.log(`WeightedRandomSelector性能:`)
      console.log(`  构造时间 (10k权重): ${constructionTime}ms`)
      console.log(`  选择时间 (1k次): ${selectionTime}ms`)
      console.log(`  平均每次选择: ${(selectionTime / 1000).toFixed(3)}ms`)
    })
    
    test('SemanticSimilarityCalculator performance', () => {
      const word1 = createSampleLexicalEntry({ word: '星' })
      const word2 = createSampleLexicalEntry({ word: '月' })
      
      const startTime = Date.now()
      for (let i = 0; i < 1000; i++) {
        SemanticSimilarityCalculator.calculate(word1, word2)
      }
      const endTime = Date.now()
      
      const duration = endTime - startTime
      expect(duration).toBeLessThan(100) // 1000次计算应该在100ms内
      
      console.log(`SemanticSimilarityCalculator性能:`)
      console.log(`  1000次计算耗时: ${duration}ms`)
      console.log(`  平均每次计算: ${(duration / 1000).toFixed(3)}ms`)
    })
    
    test('PhoneticHarmonyCalculator performance', () => {
      const words = Array(10).fill(0).map((_, i) => 
        createSampleLexicalEntry({ 
          word: `词${i}`,
          syllables: Math.floor(Math.random() * 3) + 1
        })
      )
      
      const startTime = Date.now()
      for (let i = 0; i < 1000; i++) {
        PhoneticHarmonyCalculator.calculate(words)
      }
      const endTime = Date.now()
      
      const duration = endTime - startTime
      expect(duration).toBeLessThan(200) // 1000次计算应该在200ms内
      
      console.log(`PhoneticHarmonyCalculator性能:`)
      console.log(`  1000次计算耗时 (10词): ${duration}ms`)
      console.log(`  平均每次计算: ${(duration / 1000).toFixed(3)}ms`)
    })
  })
  
  describe('Generation Performance', () => {
    test('single generation performance', async () => {
      if (!api) {
        console.log('跳过生成性能测试 - API未初始化')
        return
      }
      
      const startTime = Date.now()
      
      const response = await api.generateUsernames({
        language: 'zh',
        style: CulturalStyle.MODERN,
        count: 5
      })
      
      const endTime = Date.now()
      const duration = endTime - startTime
      
      expect(duration).toBeLessThan(1000) // 单次生成应该在1秒内
      
      if (response.success && response.data) {
        console.log(`单次生成性能:`)
        console.log(`  生成5个用户名耗时: ${duration}ms`)
        console.log(`  平均每个用户名: ${(duration / 5).toFixed(1)}ms`)
        console.log(`  API报告耗时: ${response.data.generation_info.total_time_ms}ms`)
        console.log(`  缓存命中: ${response.data.generation_info.cache_hit ? '是' : '否'}`)
      }
    }, 10000)
    
    test('batch generation performance', async () => {
      if (!api) {
        console.log('跳过批量生成性能测试 - API未初始化')
        return
      }
      
      const batchSize = 5
      const iterations = 10
      
      const startTime = Date.now()
      
      const promises = []
      for (let i = 0; i < iterations; i++) {
        promises.push(
          api.generateUsernames({
            language: 'zh',
            style: CulturalStyle.MODERN,
            count: batchSize
          })
        )
      }
      
      const results = await Promise.all(promises)
      const endTime = Date.now()
      
      const totalDuration = endTime - startTime
      const successCount = results.filter(r => r.success).length
      const totalGenerated = results.reduce((sum, r) => 
        sum + (r.data?.usernames.length || 0), 0
      )
      
      expect(totalDuration).toBeLessThan(10000) // 批量生成应该在10秒内
      expect(successCount).toBeGreaterThan(iterations * 0.8) // 至少80%成功
      
      console.log(`批量生成性能:`)
      console.log(`  ${iterations}个并发请求，每个${batchSize}个用户名`)
      console.log(`  总耗时: ${totalDuration}ms`)
      console.log(`  成功率: ${(successCount / iterations * 100).toFixed(1)}%`)
      console.log(`  总生成数量: ${totalGenerated}`)
      console.log(`  平均每个用户名: ${(totalDuration / totalGenerated).toFixed(1)}ms`)
      console.log(`  吞吐量: ${(totalGenerated / (totalDuration / 1000)).toFixed(1)} 用户名/秒`)
    }, 30000)
    
    test('concurrent generation stress test', async () => {
      if (!api) {
        console.log('跳过并发压力测试 - API未初始化')
        return
      }
      
      const concurrency = 20
      const requestsPerThread = 3
      
      const startTime = Date.now()
      
      const promises = []
      for (let i = 0; i < concurrency; i++) {
        for (let j = 0; j < requestsPerThread; j++) {
          promises.push(
            api.generateUsernames({
              language: 'zh',
              count: 2,
              style: i % 2 === 0 ? CulturalStyle.MODERN : CulturalStyle.TRADITIONAL
            })
          )
        }
      }
      
      const results = await Promise.allSettled(promises)
      const endTime = Date.now()
      
      const totalDuration = endTime - startTime
      const successCount = results.filter(r => r.status === 'fulfilled').length
      const failureCount = results.filter(r => r.status === 'rejected').length
      
      console.log(`并发压力测试:`)
      console.log(`  ${concurrency}个并发线程，每个${requestsPerThread}个请求`)
      console.log(`  总请求数: ${promises.length}`)
      console.log(`  总耗时: ${totalDuration}ms`)
      console.log(`  成功: ${successCount}`)
      console.log(`  失败: ${failureCount}`)
      console.log(`  成功率: ${(successCount / promises.length * 100).toFixed(1)}%`)
      console.log(`  平均响应时间: ${(totalDuration / promises.length).toFixed(1)}ms`)
      
      // 在高并发下，允许一些失败，但成功率应该不低于70%
      expect(successCount / promises.length).toBeGreaterThan(0.7)
    }, 60000)
  })
  
  describe('Memory Performance', () => {
    test('memory usage during generation', async () => {
      if (!api) {
        console.log('跳过内存测试 - API未初始化')
        return
      }
      
      const initialMemory = process.memoryUsage()
      
      // 执行多次生成以观察内存使用
      for (let i = 0; i < 50; i++) {
        await api.generateUsernames({
          language: 'zh',
          count: 3
        })
      }
      
      const finalMemory = process.memoryUsage()
      
      const heapGrowth = finalMemory.heapUsed - initialMemory.heapUsed
      const heapGrowthMB = heapGrowth / 1024 / 1024
      
      console.log(`内存使用测试:`)
      console.log(`  初始堆内存: ${(initialMemory.heapUsed / 1024 / 1024).toFixed(1)}MB`)
      console.log(`  最终堆内存: ${(finalMemory.heapUsed / 1024 / 1024).toFixed(1)}MB`)
      console.log(`  内存增长: ${heapGrowthMB.toFixed(1)}MB`)
      console.log(`  外部内存: ${(finalMemory.external / 1024 / 1024).toFixed(1)}MB`)
      
      // 内存增长应该控制在合理范围内
      expect(heapGrowthMB).toBeLessThan(50) // 不应该增长超过50MB
    }, 30000)
    
    test('garbage collection efficiency', async () => {
      if (!api || !global.gc) {
        console.log('跳过GC测试 - API未初始化或GC不可用')
        return
      }
      
      // 强制垃圾回收
      global.gc()
      const beforeGC = process.memoryUsage()
      
      // 创建一些临时对象
      const tempData = []
      for (let i = 0; i < 10000; i++) {
        tempData.push({
          id: i,
          data: Array(100).fill(Math.random())
        })
      }
      
      const afterAllocation = process.memoryUsage()
      
      // 清除引用
      tempData.length = 0
      
      // 强制垃圾回收
      global.gc()
      const afterGC = process.memoryUsage()
      
      const allocatedMB = (afterAllocation.heapUsed - beforeGC.heapUsed) / 1024 / 1024
      const freedMB = (afterAllocation.heapUsed - afterGC.heapUsed) / 1024 / 1024
      const gcEfficiency = freedMB / allocatedMB
      
      console.log(`垃圾回收效率测试:`)
      console.log(`  分配内存: ${allocatedMB.toFixed(1)}MB`)
      console.log(`  回收内存: ${freedMB.toFixed(1)}MB`)
      console.log(`  回收效率: ${(gcEfficiency * 100).toFixed(1)}%`)
      
      expect(gcEfficiency).toBeGreaterThan(0.8) // 至少80%的内存应该被回收
    })
  })
  
  describe('Cache Performance', () => {
    test('cache hit rate improvement', async () => {
      if (!api) {
        console.log('跳过缓存测试 - API未初始化')
        return
      }
      
      const testRequest = {
        language: 'zh' as const,
        style: CulturalStyle.MODERN,
        count: 3
      }
      
      // 第一次请求（缓存未命中）
      const firstResponse = await api.generateUsernames(testRequest)
      const firstTime = firstResponse.data?.generation_info.total_time_ms || 0
      
      // 第二次相同请求（应该缓存命中）
      const secondResponse = await api.generateUsernames(testRequest)
      const secondTime = secondResponse.data?.generation_info.total_time_ms || 0
      
      // 第三次相同请求（应该缓存命中）
      const thirdResponse = await api.generateUsernames(testRequest)
      const thirdTime = thirdResponse.data?.generation_info.total_time_ms || 0
      
      console.log(`缓存性能测试:`)
      console.log(`  第一次请求: ${firstTime}ms (缓存未命中)`)
      console.log(`  第二次请求: ${secondTime}ms`)
      console.log(`  第三次请求: ${thirdTime}ms`)
      
      if (secondTime > 0 && thirdTime > 0) {
        const improvement = ((firstTime - secondTime) / firstTime) * 100
        console.log(`  性能提升: ${improvement.toFixed(1)}%`)
        
        // 缓存应该带来明显的性能提升
        expect(secondTime).toBeLessThan(firstTime * 0.8)
        expect(thirdTime).toBeLessThan(firstTime * 0.8)
      }
    }, 15000)
  })
  
  describe('Quick Generate Performance', () => {
    test('quickGenerate function performance', async () => {
      const iterations = 10
      const startTime = Date.now()
      
      const promises = []
      for (let i = 0; i < iterations; i++) {
        promises.push(quickGenerate('zh', 3, CulturalStyle.MODERN))
      }
      
      try {
        const results = await Promise.all(promises)
        const endTime = Date.now()
        
        const totalDuration = endTime - startTime
        const successCount = results.filter(r => Array.isArray(r) && r.length > 0).length
        
        console.log(`quickGenerate性能测试:`)
        console.log(`  ${iterations}次调用耗时: ${totalDuration}ms`)
        console.log(`  平均每次调用: ${(totalDuration / iterations).toFixed(1)}ms`)
        console.log(`  成功率: ${(successCount / iterations * 100).toFixed(1)}%`)
        
        expect(totalDuration).toBeLessThan(10000) // 10次调用应该在10秒内
        expect(successCount).toBeGreaterThan(iterations * 0.8) // 至少80%成功
      } catch (error) {
        console.log('quickGenerate测试失败:', error)
        // 在没有实际数据的情况下，这是预期的
      }
    }, 30000)
  })
  
  describe('System Resource Usage', () => {
    test('system status performance', async () => {
      if (!api) {
        console.log('跳过系统状态测试 - API未初始化')
        return
      }
      
      const startTime = Date.now()
      const status = await api.getSystemStatus()
      const endTime = Date.now()
      
      const duration = endTime - startTime
      
      console.log(`系统状态查询性能:`)
      console.log(`  查询耗时: ${duration}ms`)
      console.log(`  系统状态: ${status.status}`)
      console.log(`  运行时间: ${Math.round(status.uptime / 1000)}秒`)
      console.log(`  内存使用: ${(status.resources.memory_usage.heap_used / 1024 / 1024).toFixed(1)}MB`)
      
      expect(duration).toBeLessThan(100) // 状态查询应该很快
    })
  })
})

// 性能测试辅助函数
function measureExecutionTime<T>(fn: () => T): { result: T; duration: number } {
  const startTime = Date.now()
  const result = fn()
  const endTime = Date.now()
  return { result, duration: endTime - startTime }
}

async function measureAsyncExecutionTime<T>(fn: () => Promise<T>): Promise<{ result: T; duration: number }> {
  const startTime = Date.now()
  const result = await fn()
  const endTime = Date.now()
  return { result, duration: endTime - startTime }
}
