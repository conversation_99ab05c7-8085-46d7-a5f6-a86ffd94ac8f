/**
 * 功能测试脚本
 * 
 * 测试V2系统的实际功能运行
 */

const fs = require('fs');
const path = require('path');

class FunctionalTester {
  constructor() {
    this.results = [];
  }
  
  async runAllTests() {
    console.log('🚀 开始V2系统功能测试');
    console.log('='.repeat(50));
    
    // 测试数据加载
    await this.testDataLoading();
    
    // 测试基础组件
    await this.testBasicComponents();
    
    // 测试数据处理
    await this.testDataProcessing();
    
    // 输出结果
    this.printResults();
  }
  
  async runTest(name, testFn) {
    const startTime = Date.now();
    
    try {
      await testFn();
      this.results.push({
        name,
        passed: true,
        duration: Date.now() - startTime
      });
      console.log(`✅ ${name}`);
    } catch (error) {
      this.results.push({
        name,
        passed: false,
        error: error.message,
        duration: Date.now() - startTime
      });
      console.log(`❌ ${name}: ${error.message}`);
    }
  }
  
  async testDataLoading() {
    console.log('\n📊 数据加载测试');
    console.log('-'.repeat(30));
    
    await this.runTest('加载中文词汇数据', async () => {
      const lexicalPath = path.join(__dirname, '..', 'data', 'lexical', 'zh.json');
      const data = JSON.parse(await fs.promises.readFile(lexicalPath, 'utf-8'));
      
      if (!data.words || !Array.isArray(data.words)) {
        throw new Error('词汇数据格式错误');
      }
      
      if (data.words.length === 0) {
        throw new Error('词汇数据为空');
      }
      
      // 验证词汇结构
      const firstWord = data.words[0];
      const requiredFields = ['word', 'language', 'pos', 'domains', 'cultural_scores'];
      for (const field of requiredFields) {
        if (!(field in firstWord)) {
          throw new Error(`词汇缺少必需字段: ${field}`);
        }
      }
      
      console.log(`    ✓ 加载了 ${data.words.length} 个词汇`);
    });
    
    await this.runTest('加载语法模式数据', async () => {
      const grammarPath = path.join(__dirname, '..', 'data', 'grammar', 'zh.json');
      const data = JSON.parse(await fs.promises.readFile(grammarPath, 'utf-8'));
      
      if (!data.patterns || !Array.isArray(data.patterns)) {
        throw new Error('语法数据格式错误');
      }
      
      if (data.patterns.length === 0) {
        throw new Error('语法数据为空');
      }
      
      // 验证模式结构
      const firstPattern = data.patterns[0];
      const requiredFields = ['id', 'structure', 'cultural_fitness'];
      for (const field of requiredFields) {
        if (!(field in firstPattern)) {
          throw new Error(`语法模式缺少必需字段: ${field}`);
        }
      }
      
      console.log(`    ✓ 加载了 ${data.patterns.length} 个语法模式`);
    });
    
    await this.runTest('加载文化配置数据', async () => {
      const culturePath = path.join(__dirname, '..', 'data', 'culture', 'zh-CN.json');
      const data = JSON.parse(await fs.promises.readFile(culturePath, 'utf-8'));
      
      if (!data.config) {
        throw new Error('文化配置格式错误');
      }
      
      const config = data.config;
      const requiredFields = ['culture_id', 'language', 'style_preferences', 'domain_preferences'];
      for (const field of requiredFields) {
        if (!(field in config)) {
          throw new Error(`文化配置缺少必需字段: ${field}`);
        }
      }
      
      console.log(`    ✓ 加载了文化配置: ${config.culture_id}`);
    });
  }
  
  async testBasicComponents() {
    console.log('\n🔧 基础组件测试');
    console.log('-'.repeat(30));
    
    await this.runTest('词汇过滤功能', async () => {
      const lexicalPath = path.join(__dirname, '..', 'data', 'lexical', 'zh.json');
      const data = JSON.parse(await fs.promises.readFile(lexicalPath, 'utf-8'));
      
      // 测试按词性过滤
      const nouns = data.words.filter(word => word.pos === 'n');
      const adjectives = data.words.filter(word => word.pos === 'adj');
      
      if (nouns.length === 0 && adjectives.length === 0) {
        throw new Error('没有找到名词或形容词');
      }
      
      console.log(`    ✓ 名词: ${nouns.length}个, 形容词: ${adjectives.length}个`);
    });
    
    await this.runTest('语义域分类', async () => {
      const lexicalPath = path.join(__dirname, '..', 'data', 'lexical', 'zh.json');
      const data = JSON.parse(await fs.promises.readFile(lexicalPath, 'utf-8'));
      
      // 统计语义域
      const domainCounts = {};
      data.words.forEach(word => {
        word.domains.forEach(domain => {
          domainCounts[domain] = (domainCounts[domain] || 0) + 1;
        });
      });
      
      if (Object.keys(domainCounts).length === 0) {
        throw new Error('没有找到语义域分类');
      }
      
      console.log(`    ✓ 语义域分布:`, Object.entries(domainCounts).map(([k, v]) => `${k}:${v}`).join(', '));
    });
    
    await this.runTest('文化风格评分', async () => {
      const lexicalPath = path.join(__dirname, '..', 'data', 'lexical', 'zh.json');
      const data = JSON.parse(await fs.promises.readFile(lexicalPath, 'utf-8'));
      
      // 检查文化评分
      const styleStats = {};
      data.words.forEach(word => {
        Object.entries(word.cultural_scores).forEach(([style, score]) => {
          if (!styleStats[style]) styleStats[style] = { count: 0, total: 0 };
          styleStats[style].count++;
          styleStats[style].total += score;
        });
      });
      
      if (Object.keys(styleStats).length === 0) {
        throw new Error('没有找到文化风格评分');
      }
      
      const avgScores = Object.entries(styleStats).map(([style, stats]) => 
        `${style}:${(stats.total / stats.count).toFixed(2)}`
      );
      
      console.log(`    ✓ 平均文化评分:`, avgScores.join(', '));
    });
  }
  
  async testDataProcessing() {
    console.log('\n⚙️  数据处理测试');
    console.log('-'.repeat(30));
    
    await this.runTest('词汇组合生成', async () => {
      const lexicalPath = path.join(__dirname, '..', 'data', 'lexical', 'zh.json');
      const grammarPath = path.join(__dirname, '..', 'data', 'grammar', 'zh.json');
      
      const lexicalData = JSON.parse(await fs.promises.readFile(lexicalPath, 'utf-8'));
      const grammarData = JSON.parse(await fs.promises.readFile(grammarPath, 'utf-8'));
      
      // 简单的组合生成测试
      const adjectives = lexicalData.words.filter(w => w.pos === 'adj');
      const nouns = lexicalData.words.filter(w => w.pos === 'n');
      
      if (adjectives.length === 0 || nouns.length === 0) {
        throw new Error('缺少形容词或名词用于组合');
      }
      
      // 生成一些组合
      const combinations = [];
      for (let i = 0; i < Math.min(5, adjectives.length); i++) {
        for (let j = 0; j < Math.min(3, nouns.length); j++) {
          combinations.push(adjectives[i].word + nouns[j].word);
        }
      }
      
      if (combinations.length === 0) {
        throw new Error('无法生成词汇组合');
      }
      
      console.log(`    ✓ 生成了 ${combinations.length} 个组合示例:`, combinations.slice(0, 3).join(', '));
    });
    
    await this.runTest('语法模式匹配', async () => {
      const grammarPath = path.join(__dirname, '..', 'data', 'grammar', 'zh.json');
      const grammarData = JSON.parse(await fs.promises.readFile(grammarPath, 'utf-8'));
      
      // 测试不同长度的模式
      const patternLengths = {};
      grammarData.patterns.forEach(pattern => {
        const length = pattern.structure.length;
        patternLengths[length] = (patternLengths[length] || 0) + 1;
      });
      
      if (Object.keys(patternLengths).length === 0) {
        throw new Error('没有找到有效的语法模式');
      }
      
      console.log(`    ✓ 模式长度分布:`, Object.entries(patternLengths).map(([k, v]) => `${k}词:${v}个`).join(', '));
    });
    
    await this.runTest('质量评估计算', async () => {
      const lexicalPath = path.join(__dirname, '..', 'data', 'lexical', 'zh.json');
      const lexicalData = JSON.parse(await fs.promises.readFile(lexicalPath, 'utf-8'));
      
      // 简单的质量评估测试
      const qualityMetrics = {
        avgSentiment: 0,
        avgFormality: 0,
        avgFrequency: 0,
        count: 0
      };
      
      lexicalData.words.forEach(word => {
        if (typeof word.sentiment === 'number') {
          qualityMetrics.avgSentiment += word.sentiment;
          qualityMetrics.count++;
        }
        if (typeof word.formality === 'number') {
          qualityMetrics.avgFormality += word.formality;
        }
        if (typeof word.frequency === 'number') {
          qualityMetrics.avgFrequency += word.frequency;
        }
      });
      
      if (qualityMetrics.count === 0) {
        throw new Error('无法计算质量指标');
      }
      
      qualityMetrics.avgSentiment /= qualityMetrics.count;
      qualityMetrics.avgFormality /= qualityMetrics.count;
      qualityMetrics.avgFrequency /= qualityMetrics.count;
      
      console.log(`    ✓ 平均情感: ${qualityMetrics.avgSentiment.toFixed(2)}, 正式度: ${qualityMetrics.avgFormality.toFixed(2)}, 频率: ${qualityMetrics.avgFrequency.toFixed(2)}`);
    });
    
    await this.runTest('缓存模拟测试', async () => {
      // 模拟简单的缓存操作
      const cache = new Map();
      
      // 模拟缓存存储
      cache.set('test_key_1', { data: 'test_value_1', timestamp: Date.now() });
      cache.set('test_key_2', { data: 'test_value_2', timestamp: Date.now() });
      
      // 模拟缓存读取
      const value1 = cache.get('test_key_1');
      const value2 = cache.get('test_key_2');
      const value3 = cache.get('non_existent_key');
      
      if (!value1 || !value2 || value3) {
        throw new Error('缓存操作异常');
      }
      
      console.log(`    ✓ 缓存操作正常，存储了 ${cache.size} 个条目`);
    });
  }
  
  printResults() {
    console.log('\n📊 功能测试结果汇总');
    console.log('='.repeat(50));
    
    const passed = this.results.filter(r => r.passed).length;
    const failed = this.results.filter(r => !r.passed).length;
    const total = this.results.length;
    
    console.log(`总测试数: ${total}`);
    console.log(`✅ 通过: ${passed}`);
    console.log(`❌ 失败: ${failed}`);
    console.log(`通过率: ${((passed / total) * 100).toFixed(1)}%`);
    
    const totalTime = this.results.reduce((sum, r) => sum + r.duration, 0);
    console.log(`总耗时: ${totalTime}ms`);
    
    if (failed > 0) {
      console.log('\n❌ 失败的测试:');
      this.results
        .filter(r => !r.passed)
        .forEach(r => {
          console.log(`  - ${r.name}: ${r.error}`);
        });
    }
    
    if (passed === total) {
      console.log('\n🎉 所有功能测试通过！V2系统功能正常！');
    } else if (passed / total >= 0.8) {
      console.log('\n👍 大部分功能测试通过，系统基本可用');
    } else {
      console.log('\n⚠️  多个功能测试失败，需要检查系统实现');
    }
    
    // 显示功能概览
    console.log('\n📋 功能概览:');
    console.log(`  - 数据加载: ${this.results.filter(r => r.name.includes('加载') && r.passed).length}/3`);
    console.log(`  - 基础组件: ${this.results.filter(r => r.name.includes('功能') || r.name.includes('分类') || r.name.includes('评分')).filter(r => r.passed).length}/3`);
    console.log(`  - 数据处理: ${this.results.filter(r => r.name.includes('生成') || r.name.includes('匹配') || r.name.includes('计算') || r.name.includes('缓存')).filter(r => r.passed).length}/4`);
  }
}

// 主函数
async function main() {
  const tester = new FunctionalTester();
  await tester.runAllTests();
}

// 如果直接运行此文件，则执行测试
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { FunctionalTester };
