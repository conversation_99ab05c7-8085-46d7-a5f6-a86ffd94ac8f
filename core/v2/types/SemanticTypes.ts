/**
 * 基于第一性原理的语义类型定义
 * 
 * 这个文件定义了系统的核心语义类型，基于认知语言学和语义学理论
 */

// ============ 基础枚举类型 ============

/** 词性标注 */
export enum PartOfSpeech {
  NOUN = 'noun',
  VERB = 'verb', 
  ADJECTIVE = 'adjective',
  ADVERB = 'adverb',
  PRONOUN = 'pronoun',
  PREPOSITION = 'preposition',
  CONJUNCTION = 'conjunction',
  INTERJECTION = 'interjection',
  PARTICLE = 'particle',
  CLASSIFIER = 'classifier', // 中文量词
  HONORIFIC = 'honorific'    // 敬语标记
}

/** 语域类型 */
export enum Register {
  FORMAL = 'formal',
  INFORMAL = 'informal', 
  COLLOQUIAL = 'colloquial',
  SLANG = 'slang',
  TECHNICAL = 'technical',
  LITERARY = 'literary',
  ARCHAIC = 'archaic'
}

/** 年龄群体 */
export enum AgeGroup {
  CHILDREN = 'children',    // 儿童 (0-12)
  TEENAGERS = 'teenagers',  // 青少年 (13-19)
  YOUNG_ADULTS = 'young_adults', // 青年 (20-35)
  MIDDLE_AGED = 'middle_aged',   // 中年 (36-55)
  SENIORS = 'seniors'       // 老年 (55+)
}

/** 文字系统类型 */
export enum ScriptType {
  LATIN = 'latin',
  CHINESE = 'chinese',
  JAPANESE = 'japanese',
  ARABIC = 'arabic',
  CYRILLIC = 'cyrillic',
  DEVANAGARI = 'devanagari'
}

/** 形态类型 */
export enum MorphologicalType {
  ISOLATING = 'isolating',     // 孤立语 (如中文)
  AGGLUTINATIVE = 'agglutinative', // 黏着语 (如日语)
  FUSIONAL = 'fusional',       // 屈折语 (如英语)
  POLYSYNTHETIC = 'polysynthetic'  // 多综合语
}

/** 语序类型 */
export enum WordOrder {
  SVO = 'svo', // 主谓宾 (如英语)
  SOV = 'sov', // 主宾谓 (如日语)
  VSO = 'vso', // 谓主宾
  VOS = 'vos', // 谓宾主
  OSV = 'osv', // 宾主谓
  OVS = 'ovs'  // 宾谓主
}

// ============ 语义特征类型 ============

/** 语义特征 */
export interface SemanticFeature {
  name: string
  value: boolean | number | string
  confidence: number // 0-1, 特征的确信度
}

/** 语义域 */
export interface SemanticDomain {
  id: string
  name: string
  parent?: string // 父域ID，支持层次结构
  description: string
  cultural_specificity: number // 0-1, 文化特异性程度
}

/** 概念类别 */
export interface ConceptualCategory {
  id: string
  name: string
  features: SemanticFeature[]
  prototypical_members: string[] // 原型成员
}

// ============ 文化相关类型 ============

/** 文化内涵 */
export interface CulturalConnotation {
  culture: string // 文化代码，如 'zh-CN', 'en-US'
  positive_associations: string[] // 正面联想
  negative_associations: string[] // 负面联想
  taboo_level: number // 0-1, 禁忌程度
  formality_level: number // 0-1, 正式程度
  generational_preference: AgeGroup[] // 年龄偏好
}

/** 文化维度 (基于Hofstede文化维度理论) */
export interface CulturalDimension {
  power_distance: number      // 权力距离 0-100
  individualism: number       // 个人主义 0-100
  masculinity: number         // 男性化 0-100
  uncertainty_avoidance: number // 不确定性规避 0-100
  long_term_orientation: number // 长期导向 0-100
  indulgence: number          // 放纵 0-100
}

// ============ 音韵相关类型 ============

/** 音韵特征 */
export interface PhoneticFeature {
  type: 'consonant' | 'vowel' | 'tone' | 'stress'
  value: string
  position?: number // 在词中的位置
}

/** 音节结构 */
export interface SyllableStructure {
  onset?: string    // 声母
  nucleus: string   // 韵核
  coda?: string     // 韵尾
  tone?: number     // 声调 (适用于声调语言)
}

// ============ 搭配和关系类型 ============

/** 词汇搭配 */
export interface Collocation {
  partner: string   // 搭配词
  relation: 'modifier' | 'head' | 'complement' | 'coordinate'
  strength: number  // 搭配强度 0-1
  frequency: number // 共现频率
}

/** 语义关系 */
export interface SemanticRelation {
  type: 'synonym' | 'antonym' | 'hypernym' | 'hyponym' | 'meronym' | 'holonym'
  target: string    // 目标词
  strength: number  // 关系强度 0-1
}

// ============ 核心词汇条目类型 ============

/** 语义词汇条目 */
export interface SemanticLexicalEntry {
  // 基础信息
  word: string
  language: string
  pos: PartOfSpeech
  variants?: string[] // 变体形式
  
  // 语义信息
  semantic_domains: string[]        // 语义域ID列表
  conceptual_categories: string[]   // 概念类别ID列表
  semantic_features: SemanticFeature[]
  
  // 文化信息
  cultural_connotations: CulturalConnotation[]
  register: Register
  emotional_valence: number       // -1.0 (负面) 到 **** (正面)
  arousal_level: number          // 0-1, 激活程度
  
  // 使用信息
  frequency: number               // 使用频率 (对数值)
  trend_score: number            // 流行度评分 0-1
  age_group_preference: AgeGroup[]
  gender_preference?: 'male' | 'female' | 'neutral'
  
  // 音韵信息
  phonetic_features: PhoneticFeature[]
  syllable_count: number
  syllable_structure: SyllableStructure[]
  stress_pattern?: string
  
  // 组合信息
  collocations: Collocation[]
  semantic_relations: SemanticRelation[]
  
  // 元数据
  created_at: string
  updated_at: string
  confidence: number             // 条目质量置信度 0-1
  source: string                 // 数据来源
}

// ============ 生成相关类型 ============

/** 生成偏好 */
export interface GenerationPreference {
  semantic_domains?: string[]     // 偏好的语义域
  emotional_tone?: number        // 期望的情感色调 -1 到 1
  formality_level?: number       // 正式程度 0-1
  creativity_level?: number      // 创意程度 0-1
  cultural_adaptation?: number   // 文化适应程度 0-1
  age_group?: AgeGroup
  avoid_taboos?: boolean
  max_syllables?: number
  min_syllables?: number
}

/** 生成上下文 */
export interface GenerationContext {
  target_language: string
  target_culture: string
  user_preferences: GenerationPreference
  domain?: string               // 应用域，如 'gaming', 'social', 'professional'
  platform?: string            // 平台，如 'twitter', 'instagram', 'linkedin'
}

// ============ 质量评估类型 ============

/** 质量指标 */
export interface QualityMetrics {
  semantic_coherence: number     // 语义连贯性 0-1
  cultural_appropriateness: number // 文化适宜性 0-1
  phonetic_harmony: number       // 音韵和谐性 0-1
  memorability: number           // 记忆性 0-1
  uniqueness: number             // 独特性 0-1
  overall_score: number          // 综合评分 0-1
}

/** 生成结果 */
export interface GenerationResult {
  username: string
  components: SemanticLexicalEntry[] // 组成词汇
  construction_used: string          // 使用的构式ID
  quality_metrics: QualityMetrics
  explanation?: string               // 生成解释
  alternatives?: string[]            // 备选方案
}
