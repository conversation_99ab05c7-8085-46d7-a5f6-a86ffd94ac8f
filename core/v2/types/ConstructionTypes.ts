/**
 * 构式语法相关类型定义
 * 
 * 基于构式语法理论，将用户名视为特定的语言构式
 */

import { 
  PartOfSpeech, 
  Register, 
  SemanticFeature, 
  PhoneticFeature,
  CulturalDimension 
} from './SemanticTypes'

// ============ 构式槽位类型 ============

/** 槽位类型 */
export enum SlotType {
  SEMANTIC = 'semantic',     // 语义槽位
  FUNCTIONAL = 'functional', // 功能槽位 (如连接词)
  PHONETIC = 'phonetic',     // 音韵槽位
  CULTURAL = 'cultural'      // 文化槽位
}

/** 槽位约束 */
export interface SlotConstraint {
  type: 'semantic' | 'syntactic' | 'phonetic' | 'cultural'
  constraint: string
  strength: number // 约束强度 0-1
  violation_penalty: number // 违反惩罚
}

/** 构式槽位 */
export interface ConstructionSlot {
  id: string
  name: string
  type: SlotType
  required: boolean
  position: number // 在构式中的位置
  
  // 语义约束
  semantic_domains?: string[]
  conceptual_categories?: string[]
  semantic_features?: SemanticFeature[]
  
  // 句法约束
  pos_constraints?: PartOfSpeech[]
  register_constraints?: Register[]
  
  // 音韵约束
  phonetic_constraints?: PhoneticFeature[]
  syllable_constraints?: {
    min_syllables?: number
    max_syllables?: number
    preferred_syllables?: number
  }
  
  // 文化约束
  cultural_constraints?: {
    formality_range?: [number, number] // [min, max]
    taboo_threshold?: number
    age_appropriateness?: string[]
  }
  
  // 搭配约束
  collocation_constraints?: {
    required_partners?: string[]
    forbidden_partners?: string[]
    preferred_relations?: string[]
  }
  
  // 默认值和备选
  default_fillers?: string[]
  fallback_strategy?: 'random' | 'frequent' | 'semantic_similar'
}

// ============ 语义框架类型 ============

/** 语义角色 */
export enum SemanticRole {
  AGENT = 'agent',           // 施事
  PATIENT = 'patient',       // 受事
  THEME = 'theme',           // 主题
  EXPERIENCER = 'experiencer', // 体验者
  INSTRUMENT = 'instrument',  // 工具
  LOCATION = 'location',     // 位置
  TIME = 'time',             // 时间
  MANNER = 'manner',         // 方式
  PURPOSE = 'purpose',       // 目的
  ATTRIBUTE = 'attribute',   // 属性
  MODIFIER = 'modifier'      // 修饰语
}

/** 语义框架 */
export interface SemanticFrame {
  id: string
  name: string
  description: string
  
  // 核心语义
  core_meaning: string
  semantic_type: 'entity' | 'event' | 'state' | 'property' | 'relation'
  
  // 语义角色
  semantic_roles: {
    role: SemanticRole
    slot_id: string
    obligatory: boolean
  }[]
  
  // 语义约束
  semantic_constraints: {
    constraint_type: 'compatibility' | 'selectional' | 'thematic'
    description: string
    involved_slots: string[]
    validation_rule: string // 可以是正则表达式或逻辑表达式
  }[]
  
  // 推理规则
  inference_rules?: {
    condition: string
    conclusion: string
    confidence: number
  }[]
}

// ============ 语用功能类型 ============

/** 语用功能 */
export enum PragmaticFunction {
  IDENTIFICATION = 'identification',   // 身份标识
  SELF_EXPRESSION = 'self_expression', // 自我表达
  SOCIAL_SIGNALING = 'social_signaling', // 社交信号
  HUMOR = 'humor',                     // 幽默
  INTIMIDATION = 'intimidation',       // 威慑
  ATTRACTION = 'attraction',           // 吸引
  PROFESSIONALISM = 'professionalism', // 专业性
  CREATIVITY = 'creativity',           // 创意性
  ANONYMITY = 'anonymity'              // 匿名性
}

/** 语用上下文 */
export interface PragmaticContext {
  communicative_goal: PragmaticFunction[]
  social_relationship: 'formal' | 'informal' | 'intimate' | 'professional'
  power_dynamics: 'equal' | 'superior' | 'subordinate'
  cultural_context: string
  situational_context: string
}

// ============ 构式模板类型 ============

/** 构式示例 */
export interface ConstructionExample {
  example: string
  components: { [slot_id: string]: string }
  quality_score: number
  cultural_context: string
  explanation?: string
}

/** 构式统计信息 */
export interface ConstructionStatistics {
  usage_frequency: number
  success_rate: number // 生成成功率
  user_satisfaction: number // 用户满意度
  cultural_acceptance: { [culture: string]: number }
  age_group_preference: { [age_group: string]: number }
}

/** 构式模板 */
export interface ConstructionTemplate {
  // 基础信息
  id: string
  language: string
  name: string
  description: string
  category: string // 构式类别，如 'compound', 'phrase', 'blend'
  
  // 结构信息
  structure: ConstructionSlot[]
  semantic_frame: SemanticFrame
  pragmatic_function: PragmaticFunction[]
  
  // 约束信息
  global_constraints: {
    max_length?: number
    min_length?: number
    phonetic_harmony_required?: boolean
    semantic_coherence_threshold?: number
    cultural_appropriateness_threshold?: number
  }
  
  // 权重和优先级
  base_weight: number
  priority: number // 优先级，用于冲突解决
  
  // 文化适应
  cultural_weight_modifiers: { [culture: string]: number }
  cultural_constraints: {
    culture: string
    dimension_requirements?: Partial<CulturalDimension>
    forbidden_combinations?: string[][]
    required_elements?: string[]
  }[]
  
  // 风格适应
  style_weight_modifiers: { [style: string]: number }
  register_compatibility: Register[]
  
  // 示例和统计
  examples: ConstructionExample[]
  statistics?: ConstructionStatistics
  
  // 元数据
  created_at: string
  updated_at: string
  version: string
  author: string
  confidence: number // 模板质量置信度
}

// ============ 构式关系类型 ============

/** 构式关系类型 */
export enum ConstructionRelationType {
  INHERITANCE = 'inheritance',     // 继承关系
  COMPOSITION = 'composition',     // 组合关系
  ALTERNATION = 'alternation',     // 变体关系
  SPECIALIZATION = 'specialization', // 特化关系
  GENERALIZATION = 'generalization', // 泛化关系
  COMPETITION = 'competition'      // 竞争关系
}

/** 构式关系 */
export interface ConstructionRelation {
  source_construction: string
  target_construction: string
  relation_type: ConstructionRelationType
  strength: number // 关系强度 0-1
  description?: string
}

// ============ 构式网络类型 ============

/** 构式网络 */
export interface ConstructionNetwork {
  language: string
  constructions: ConstructionTemplate[]
  relations: ConstructionRelation[]
  
  // 网络统计
  network_metrics: {
    density: number // 网络密度
    centrality_scores: { [construction_id: string]: number }
    clustering_coefficient: number
    average_path_length: number
  }
  
  // 动态信息
  activation_patterns: { [construction_id: string]: number }
  usage_trends: { [construction_id: string]: number[] }
}

// ============ 构式匹配和选择类型 ============

/** 构式匹配结果 */
export interface ConstructionMatch {
  construction: ConstructionTemplate
  match_score: number // 匹配分数 0-1
  slot_assignments: { [slot_id: string]: string[] } // 候选词汇
  constraint_violations: {
    constraint: SlotConstraint
    severity: number
    explanation: string
  }[]
  estimated_quality: number
}

/** 构式选择策略 */
export interface ConstructionSelectionStrategy {
  strategy_name: string
  weight_factors: {
    semantic_fit: number
    cultural_appropriateness: number
    phonetic_harmony: number
    novelty: number
    frequency: number
  }
  selection_algorithm: 'weighted_random' | 'top_k' | 'tournament' | 'genetic'
  parameters: { [key: string]: any }
}

// ============ 构式生成控制类型 ============

/** 构式生成参数 */
export interface ConstructionGenerationParams {
  target_constructions?: string[] // 指定构式ID
  excluded_constructions?: string[] // 排除构式ID
  construction_categories?: string[] // 构式类别过滤
  
  semantic_preferences: {
    domains: string[]
    themes: string[]
    emotional_tone: number
  }
  
  cultural_preferences: {
    target_culture: string
    adaptation_level: number // 0-1, 文化适应程度
    avoid_cultural_conflicts: boolean
  }
  
  quality_requirements: {
    min_semantic_coherence: number
    min_cultural_appropriateness: number
    min_phonetic_harmony: number
    min_overall_quality: number
  }
  
  generation_constraints: {
    max_attempts: number
    timeout_ms: number
    allow_partial_matches: boolean
    fallback_strategy: 'simple' | 'random' | 'template_based'
  }
}
