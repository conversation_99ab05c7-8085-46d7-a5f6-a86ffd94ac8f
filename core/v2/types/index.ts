/**
 * V2系统类型定义导出
 * 
 * 统一导出所有类型定义，便于外部使用
 */

// 从架构模块导出核心类型
export {
  // 基础枚举类型
  PartOfSpeech,
  SemanticDomain,
  CulturalStyle,
  LanguageCode,
  
  // 数据结构类型
  LexicalEntry,
  GrammarPattern,
  CultureConfig,
  GenerationContext,
  GenerationResult,
  LexicalIndex,
  
  // 约束和规则类型
  SemanticConstraint,
  CulturalRule,
  
  // 缓存相关类型
  CacheEntry,
  CacheConfig,
  
  // 性能监控类型
  PerformanceMetrics,
  
  // 系统配置类型
  SystemConfig,
  
  // 联合类型
  WordId,
  PatternId,
  CultureId,
  DataStructure
} from '../architecture/DataStructures'

export {
  // 算法接口
  LexicalSelector,
  PatternMatcher,
  QualityEvaluator,
  
  // 算法实现类
  WeightedRandomSelector,
  SemanticSimilarityCalculator,
  PhoneticHarmonyCalculator,
  GreedySearchAlgorithm,
  DynamicProgrammingAlgorithm,
  AlgorithmFactory
} from '../architecture/CoreAlgorithms'

export {
  // 数据访问接口
  DataAccessLayer,
  
  // 数据文件格式
  LexicalDataFile,
  GrammarDataFile,
  CultureDataFile,
  
  // 数据管理类
  FileSystemDataAccess,
  IndexManager,
  DataManager
} from '../architecture/DataManager'

export {
  // 生成策略接口
  GenerationStrategy,
  
  // 生成引擎类
  UsernameGenerationEngine,
  
  // 生成实例类型
  ConstructionInstance
} from '../architecture/GenerationEngine'

export {
  // 性能监控类
  PerformanceMonitor,
  AutoOptimizer,
  ResourceManager,
  PerformanceProfiler,
  
  // 性能相关接口
  PerformanceReport,
  OptimizationAction,
  OptimizationResult,
  ResourceStatus
} from '../architecture/PerformanceOptimizer'

export {
  // API请求和响应类型
  GenerateUsernameRequest,
  GenerateUsernameResponse,
  UsernameResult,
  ComponentInfo,
  GenerationInfo,
  DebugInfo,
  ProfileInfo,
  SystemStatusResponse,
  
  // 主API类
  UsernameGeneratorAPI,
  
  // 便捷函数
  createUsernameAPI,
  quickGenerate
} from '../architecture/MainAPI'

// 重新导出常用类型的别名
export type {
  // 生成相关
  GenerationContext as Context,
  GenerationResult as Result,
  GenerateUsernameRequest as Request,
  GenerateUsernameResponse as Response,
  
  // 数据相关
  LexicalEntry as Word,
  GrammarPattern as Pattern,
  CultureConfig as Culture,
  
  // 配置相关
  SystemConfig as Config,
  PerformanceMetrics as Metrics
}

// 导出常用枚举值
export const Languages = {
  CHINESE: 'zh' as LanguageCode,
  ENGLISH: 'en' as LanguageCode,
  JAPANESE: 'ja' as LanguageCode
} as const

export const Styles = {
  TRADITIONAL: CulturalStyle.TRADITIONAL,
  MODERN: CulturalStyle.MODERN,
  CUTE: CulturalStyle.CUTE,
  COOL: CulturalStyle.COOL,
  ELEGANT: CulturalStyle.ELEGANT,
  PLAYFUL: CulturalStyle.PLAYFUL,
  MYSTERIOUS: CulturalStyle.MYSTERIOUS,
  POWERFUL: CulturalStyle.POWERFUL
} as const

export const Themes = {
  NATURE: SemanticDomain.NATURE,
  EMOTION: SemanticDomain.EMOTION,
  ACTION: SemanticDomain.ACTION,
  QUALITY: SemanticDomain.QUALITY,
  TIME: SemanticDomain.TIME,
  SPACE: SemanticDomain.SPACE,
  TECHNOLOGY: SemanticDomain.TECHNOLOGY,
  CULTURE: SemanticDomain.CULTURE,
  FANTASY: SemanticDomain.FANTASY,
  SOCIAL: SemanticDomain.SOCIAL
} as const

// 导出版本信息
export const VERSION = '2.0.0'
export const BUILD_DATE = new Date().toISOString()

// 导出默认配置
export const DEFAULT_CONFIG: Partial<SystemConfig> = {
  supported_languages: ['zh', 'en', 'ja'],
  defaults: {
    language: 'zh',
    style: CulturalStyle.MODERN,
    count: 5,
    creativity: 0.5
  },
  cache: {
    max_size: 1000,
    default_ttl: 10 * 60 * 1000, // 10分钟
    cleanup_interval: 5 * 60 * 1000, // 5分钟
    enable_stats: true
  },
  performance: {
    max_generation_time: 5000,
    max_concurrent_generations: 10,
    enable_profiling: false
  },
  logging: {
    level: 'info',
    enable_file_logging: false,
    log_directory: './logs'
  }
}

// 导出工具函数
export const Utils = {
  /**
   * 检查语言是否支持
   */
  isSupportedLanguage(language: string): language is LanguageCode {
    return DEFAULT_CONFIG.supported_languages?.includes(language) || false
  },
  
  /**
   * 检查风格是否有效
   */
  isValidStyle(style: string): style is CulturalStyle {
    return Object.values(CulturalStyle).includes(style as CulturalStyle)
  },
  
  /**
   * 检查主题是否有效
   */
  isValidTheme(theme: string): theme is SemanticDomain {
    return Object.values(SemanticDomain).includes(theme as SemanticDomain)
  },
  
  /**
   * 标准化语言代码
   */
  normalizeLanguage(language: string): LanguageCode {
    const normalized = language.toLowerCase().split('-')[0]
    return DEFAULT_CONFIG.supported_languages?.includes(normalized) 
      ? normalized as LanguageCode 
      : 'zh'
  },
  
  /**
   * 验证生成参数
   */
  validateGenerationParams(params: Partial<GenerateUsernameRequest>): string[] {
    const errors: string[] = []
    
    if (params.count && (params.count < 1 || params.count > 50)) {
      errors.push('count must be between 1 and 50')
    }
    
    if (params.min_length && params.max_length && params.min_length > params.max_length) {
      errors.push('min_length cannot be greater than max_length')
    }
    
    if (params.quality_threshold && (params.quality_threshold < 0 || params.quality_threshold > 1)) {
      errors.push('quality_threshold must be between 0 and 1')
    }
    
    if (params.language && !Utils.isSupportedLanguage(params.language)) {
      errors.push(`unsupported language: ${params.language}`)
    }
    
    return errors
  }
}

// 导出错误类
export class V2SystemError extends Error {
  constructor(
    message: string,
    public code: string,
    public details?: any
  ) {
    super(message)
    this.name = 'V2SystemError'
  }
}

export class ValidationError extends V2SystemError {
  constructor(message: string, details?: any) {
    super(message, 'VALIDATION_ERROR', details)
    this.name = 'ValidationError'
  }
}

export class GenerationError extends V2SystemError {
  constructor(message: string, details?: any) {
    super(message, 'GENERATION_ERROR', details)
    this.name = 'GenerationError'
  }
}

export class DataError extends V2SystemError {
  constructor(message: string, details?: any) {
    super(message, 'DATA_ERROR', details)
    this.name = 'DataError'
  }
}

export class PerformanceError extends V2SystemError {
  constructor(message: string, details?: any) {
    super(message, 'PERFORMANCE_ERROR', details)
    this.name = 'PerformanceError'
  }
}
