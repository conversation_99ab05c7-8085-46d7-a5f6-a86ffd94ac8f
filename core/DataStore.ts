/**
 * DataStore —— 全局数据中心。
 * 负责加载文化数据、趋势数据、敏感词，支持热重载。
 * 无任何第三方依赖；所有数据在进程内常驻，确保生成性能。
 */
import type { promises as FsPromises } from 'fs'
import path from 'node:path'
import type * as Path from 'path'

// ---- 类型声明 ----
export interface WordItem {
  word: string
  weight: number
  tags?: string[]
}

export interface PatternTemplate {
  template: string
  weight: number
}

export interface PatternComponents {
  [key: string]: WordItem[]
}

export interface Pattern {
  name: string
  weight: number
  templates: PatternTemplate[]
  components: PatternComponents
}

export interface CulturalData {
  meta: {
    language: string
    category: string
    version: string
    updated_at: string
    checksum?: string
  }
  patterns: Pattern[]
  alias_tables?: Record<string, AliasTable>
}

export interface TrendingWord {
  word: string
  weight: number
  tags: string[]
  category: string
}

export interface TrendingData {
  meta: {
    language: string
    period: string
    version: string
    updated_at: string
    checksum?: string
  }
  trending_words: TrendingWord[]
  trending_phrases: {
    phrase: string
    weight: number
    tags: string[]
    components: string[]
  }[]
  cultural_references: {
    reference: string
    weight: number
    tags: string[]
    category: string
  }[]
}

// ---- 权重采样：Alias Method ----
export interface AliasTable {
  prob: number[]
  alias: number[]
}

function buildAliasTable(weights: number[]): AliasTable {
  const n = weights.length
  const prob = new Array<number>(n).fill(0)
  const alias = new Array<number>(n).fill(0)
  const total = weights.reduce((s, w) => s + w, 0)
  const scaled = weights.map((w) => (w * n) / total)

  const small: number[] = []
  const large: number[] = []
  scaled.forEach((p, i) => (p < 1 ? small.push(i) : large.push(i)))

  while (small.length && large.length) {
    const l = small.pop() as number
    const g = large.pop() as number
    prob[l] = scaled[l]
    alias[l] = g
    scaled[g] = scaled[g] + scaled[l] - 1
    if (scaled[g] < 1) small.push(g)
    else large.push(g)
  }

  // 剩余的直接设为1
  ;[...small, ...large].forEach((i) => (prob[i] = 1))
  return { prob, alias }
}

function sampleAlias(table: AliasTable): number {
  const n = table.prob.length
  const i = Math.floor(Math.random() * n)
  // eslint-disable-next-line unicorn/prefer-ternary
  if (Math.random() < table.prob[i]) return i
  return table.alias[i]
}

// ---- 敏感词 Trie ----
class TrieNode {
  children: Map<string, TrieNode> = new Map()
  end = false
}

class SensitiveTrie {
  private root = new TrieNode()

  constructor(words: Iterable<string>) {
    for (const w of words) this.insert(w)
  }

  private insert(word: string) {
    let node = this.root
    for (const ch of word) {
      if (!node.children.has(ch)) node.children.set(ch, new TrieNode())
      node = node.children.get(ch)!
    }
    node.end = true
  }

  /**
   * 检测字符串是否包含敏感词，O(textLen)
   */
  contains(text: string): boolean {
    for (let i = 0; i < text.length; i++) {
      let node = this.root
      let j = i
      while (j < text.length && node.children.has(text[j])) {
        node = node.children.get(text[j])!
        if (node.end) return true
        j++
      }
    }
    return false
  }
}

// ---- DataStore ----
interface LoadedLangData {
  cultural: Record<string, CulturalData> // keyed by category
  trending?: TrendingData
  sensitiveTrie: SensitiveTrie
}

export class DataStore {
  private static store: Map<string, LoadedLangData> = new Map()

  /** root directory of built data volume (default to project /data) */
  private static dataRoot: string | null = null // 仅 SSR 使用

  /**
   * 冷启动加载：一次把指定语言全量数据读入内存
   */
  static async initLanguage(language: string) {
    if (this.store.has(language)) return // already loaded

    const isBrowser = typeof window !== 'undefined'

    // vite 预打包静态 JSON / TXT 以供浏览器端使用
    const culturalModules: Record<string, any> = /* #__PURE__ */
      import.meta.glob('../data/cultural/**/*.json', { eager: true })
    const trendingModules: Record<string, any> = /* #__PURE__ */
      import.meta.glob('../data/trends/*.json', { eager: true })
    const sensitiveModules: Record<string, string> = /* #__PURE__ */
      import.meta.glob('../data/sensitive/*.txt', { eager: true, as: 'raw' })

    if (isBrowser) {
      // ---- Browser branch: 使用预打包模块 ----
      const cultural: Record<string, CulturalData> = {}
      for (const [p, mod] of Object.entries(culturalModules)) {
        if (!p.includes(`/cultural/${language}/`)) continue
        const match = p.match(/\/([^/]+)\.json$/)
        if (!match) continue
        const category = match[1]
        const data: CulturalData = (mod as any).default || mod
        if (!data.alias_tables) {
          data.alias_tables = {}
          for (const ptn of data.patterns) {
            for (const [comp, items] of Object.entries(ptn.components)) {
              if (!data.alias_tables[comp]) {
                const weights = items.map((i) => i.weight)
                data.alias_tables[comp] = buildAliasTable(weights)
              }
            }
          }
        }
        cultural[category] = data
      }

      const trendingEntry = Object.entries(trendingModules).find(([p]) => p.endsWith(`/${language}.json`))
      const trending = trendingEntry ? ((trendingEntry[1] as any).default || trendingEntry[1]) : undefined

      const sensitiveRaw = Object.entries(sensitiveModules).find(([p]) => p.endsWith(`/${language}.txt`))?.[1] || ''
      const sensitiveSet = new Set<string>(
        sensitiveRaw
          .split(/\r?\n/)
          .map((l) => l.trim())
          .filter(Boolean)
      )
      const sensitiveTrie = new SensitiveTrie(sensitiveSet)

      this.store.set(language, { cultural, trending, sensitiveTrie })
      return
    }

    // ---- Node branch ----
    if (!this.dataRoot) {
      const path: typeof Path = await import('node:path')
      this.dataRoot = path.join(path.dirname(new URL(import.meta.url).pathname), '..', 'data')
    }

    const path: typeof Path = await import('node:path')
    const fs: typeof FsPromises = await import('node:fs/promises')

    const langDir = path.join(this.dataRoot!, 'cultural', language)
    const categories = await fs.readdir(langDir).catch(() => [])

    const cultural: Record<string, CulturalData> = {}
    for (const file of categories) {
      if (!file.endsWith('.json')) continue
      const jsonStr = await fs.readFile(path.join(langDir, file), 'utf8')
      const data = JSON.parse(jsonStr) as CulturalData
      // 若无 alias_tables 则预生成
      if (!data.alias_tables) {
        data.alias_tables = {}
        // 遍历所有 pattern 的组件合并
        for (const ptn of data.patterns) {
          for (const [comp, items] of Object.entries(ptn.components)) {
            if (!data.alias_tables[comp]) {
              const weights = items.map((i) => i.weight)
              data.alias_tables[comp] = buildAliasTable(weights)
            }
          }
        }
      }
      cultural[path.basename(file, '.json')] = data
    }

    // trending
    let trending: TrendingData | undefined
    try {
      const trendingPath = path.join(this.dataRoot!, 'trends', `${language}.json`)
      const trendingStr = await fs.readFile(trendingPath, 'utf8')
      trending = JSON.parse(trendingStr)
    } catch {
      // ignore
    }

    // sensitive trie
    const sensitivePath = path.join(this.dataRoot!, 'sensitive', `${language}.txt`)
    const sensitiveTxt = await fs.readFile(sensitivePath, 'utf8').catch(() => '')
    const sensitiveSet = new Set<string>(
      sensitiveTxt
        .split(/\r?\n/)
        .map((l) => l.trim())
        .filter(Boolean)
    )
    const sensitiveTrie = new SensitiveTrie(sensitiveSet)

    this.store.set(language, { cultural, trending, sensitiveTrie })
  }

  static getCultural(language: string, category: string = 'internet'): CulturalData {
    const langData = this.store.get(language)
    if (!langData) throw new Error(`Language not loaded: ${language}`)
    const data = langData.cultural[category]
    if (!data) throw new Error(`No cultural category ${category} for ${language}`)
    return data
  }

  static getCategories(language: string): string[] {
    const langData = this.store.get(language)
    if (!langData) throw new Error(`Language not loaded: ${language}`)
    return Object.keys(langData.cultural)
  }

  static getTrending(language: string): TrendingData | undefined {
    return this.store.get(language)?.trending
  }

  static isSensitive(language: string, text: string): boolean {
    const langData = this.store.get(language)
    if (!langData) return false // fallback allow
    return langData.sensitiveTrie.contains(text)
  }

  /**
   * 热重载：动态 import 指定 npm 包并替换内存数据
   */
  static async reloadFromPackage(pkg: string) {
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    const manifest = await import(/* @vite-ignore */ `${pkg}/manifest.json`).then((m) => m.default || m)
    if (!manifest?.meta?.language) throw new Error('Invalid data package')
    const language = manifest.meta.language
    // naive: just reset store entry and call init from pkg path
    this.store.delete(language)
    // assume pkg root exposes data folder structure identical
    this.dataRoot = path.dirname(require.resolve(`${pkg}/manifest.json`))
    await this.initLanguage(language)
  }
}

// ---- 导出工具 ----
export function pickByAlias<T>(items: T[], table: AliasTable): T {
  return items[sampleAlias(table)]
}

export function randomInt(min: number, max: number): number {
  return Math.floor(Math.random() * (max - min + 1)) + min
}
