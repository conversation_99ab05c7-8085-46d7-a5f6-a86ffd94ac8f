/**
 * MockDataStore —— 测试专用的数据存储
 * 提供内置的测试数据，避免文件系统依赖
 */

import type { WordItem, CulturalData, TrendingData } from './BrowserDataStore'

/**
 * 测试专用的数据存储类
 */
class MockDataStore {
  private cultural: Map<string, Record<string, CulturalData>> = new Map()
  private trending: Map<string, TrendingData> = new Map()
  private sensitive: Map<string, Set<string>> = new Map()
  private initialized: Set<string> = new Set()

  constructor() {
    this.initializeMockData()
  }

  /**
   * 初始化模拟数据
   */
  private initializeMockData(): void {
    // 中文测试数据
    const zhCulturalData: CulturalData = {
      meta: {
        language: 'zh',
        category: 'internet',
        version: '1.0.0',
        updated_at: '2024-12-19'
      },
      patterns: [
        {
          name: '测试模式',
          weight: 1.0,
          templates: [
            {
              template: '{prefix}{core}{suffix}',
              weight: 1.0
            },
            {
              template: '{core}{suffix}',
              weight: 0.8
            },
            {
              template: '{prefix}{core}',
              weight: 0.6
            },
            {
              template: '{core}',
              weight: 0.4
            },
            {
              template: '{prefix}{suffix}',
              weight: 0.3
            }
          ],
          components: {
            prefix: [
              { word: '超', weight: 1.0 },
              { word: '究极', weight: 0.8 },
              { word: '无敌', weight: 0.9 },
              { word: '神级', weight: 0.7 },
              { word: '顶级', weight: 0.6 },
              { word: '王者', weight: 0.8 },
              { word: '霸道', weight: 0.7 },
              { word: '绝世', weight: 0.6 },
              { word: '传说', weight: 0.5 },
              { word: '史诗', weight: 0.4 }
            ],
            core: [
              { word: '大神', weight: 1.0 },
              { word: '高手', weight: 0.9 },
              { word: '玩家', weight: 0.8 },
              { word: '战士', weight: 0.7 },
              { word: '法师', weight: 0.6 },
              { word: '刺客', weight: 0.5 },
              { word: '射手', weight: 0.4 },
              { word: '坦克', weight: 0.3 },
              { word: '辅助', weight: 0.2 },
              { word: '英雄', weight: 0.8 },
              { word: '勇者', weight: 0.7 },
              { word: '骑士', weight: 0.6 },
              { word: '剑客', weight: 0.5 },
              { word: '武者', weight: 0.4 },
              { word: '忍者', weight: 0.3 }
            ],
            suffix: [
              { word: '王', weight: 1.0 },
              { word: '君', weight: 0.8 },
              { word: '者', weight: 0.7 },
              { word: '师', weight: 0.6 },
              { word: '神', weight: 0.9 },
              { word: '圣', weight: 0.5 },
              { word: '尊', weight: 0.4 },
              { word: '帝', weight: 0.3 },
              { word: '皇', weight: 0.2 },
              { word: '侠', weight: 0.6 },
              { word: '仙', weight: 0.5 },
              { word: '魔', weight: 0.4 },
              { word: '龙', weight: 0.7 },
              { word: '凤', weight: 0.6 },
              { word: '虎', weight: 0.5 }
            ]
          }
        }
      ]
    }

    // 设置中文数据
    this.cultural.set('zh', {
      'internet': zhCulturalData,
      'traditional': zhCulturalData, // 复用相同数据
      'pop': zhCulturalData,
      'pos': zhCulturalData,
      'long': zhCulturalData
    })

    // 英文测试数据
    const enCulturalData: CulturalData = {
      meta: {
        language: 'en',
        category: 'internet',
        version: '1.0.0',
        updated_at: '2024-12-19'
      },
      patterns: [
        {
          name: 'TestPattern',
          weight: 1.0,
          templates: [
            {
              template: '{prefix}{core}{suffix}',
              weight: 1.0
            }
          ],
          components: {
            prefix: [
              { word: 'Super', weight: 1.0 },
              { word: 'Ultra', weight: 0.8 }
            ],
            core: [
              { word: 'Player', weight: 1.0 },
              { word: 'Gamer', weight: 0.9 }
            ],
            suffix: [
              { word: 'Pro', weight: 1.0 },
              { word: 'Master', weight: 0.8 }
            ]
          }
        }
      ]
    }

    this.cultural.set('en', {
      'internet': enCulturalData
    })

    // 趋势数据
    this.trending.set('zh', {
      meta: {
        language: 'zh',
        updated_at: '2024-12-19',
        version: '1.0.0'
      },
      trending_words: [
        { word: 'yyds', weight: 1.2, category: 'suffix' },
        { word: '绝绝子', weight: 1.1, category: 'suffix' }
      ]
    })

    this.trending.set('en', {
      meta: {
        language: 'en',
        updated_at: '2024-12-19',
        version: '1.0.0'
      },
      trending_words: [
        { word: 'Epic', weight: 1.2, category: 'prefix' },
        { word: 'Legend', weight: 1.1, category: 'suffix' }
      ]
    })

    // 敏感词
    this.sensitive.set('zh', new Set(['测试敏感词', '禁用词']))
    this.sensitive.set('en', new Set(['badword', 'forbidden']))

    // 标记为已初始化
    this.initialized.add('zh')
    this.initialized.add('en')
  }

  /**
   * 初始化指定语言的数据
   */
  async initLanguage(language: string): Promise<void> {
    // Mock 数据已经预加载，直接返回
    if (!this.initialized.has(language)) {
      this.initialized.add(language)
    }
  }

  /**
   * 获取文化数据
   */
  getCultural(language: string, category: string): CulturalData {
    const langData = this.cultural.get(language)
    if (!langData || !langData[category]) {
      // 如果没有找到，返回默认的中文数据
      const defaultData = this.cultural.get('zh')
      if (defaultData && defaultData['internet']) {
        return defaultData['internet']
      }
      throw new Error(`Cultural data not found for ${language}/${category}`)
    }
    return langData[category]
  }

  /**
   * 获取趋势数据
   */
  getTrending(language: string): TrendingData | undefined {
    return this.trending.get(language)
  }

  /**
   * 获取可用的文化类别
   */
  getCategories(language: string): string[] {
    const langData = this.cultural.get(language)
    return langData ? Object.keys(langData) : ['internet']
  }

  /**
   * 检查是否为敏感词
   */
  isSensitive(language: string, word: string): boolean {
    const sensitiveSet = this.sensitive.get(language)
    if (!sensitiveSet) return false
    
    return sensitiveSet.has(word.toLowerCase())
  }

  /**
   * 获取数据统计信息
   */
  getStats(): Record<string, any> {
    const stats: Record<string, any> = {}
    
    for (const [language, cultural] of this.cultural.entries()) {
      stats[language] = {
        categories: Object.keys(cultural).length,
        patterns: Object.values(cultural).reduce((sum, data) => sum + data.patterns.length, 0),
        trending_words: this.trending.get(language)?.trending_words.length || 0,
        sensitive_words: this.sensitive.get(language)?.size || 0
      }
    }
    
    return stats
  }

  /**
   * 清理缓存
   */
  clearCache(): void {
    // Mock 数据不需要清理
  }
}

// 创建全局实例
export const MockDataStoreInstance = new MockDataStore()

// 默认导出
export default MockDataStoreInstance
