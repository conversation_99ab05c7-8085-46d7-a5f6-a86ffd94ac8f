/**
 * 核心数据结构定义
 * 
 * 基于语言学理论设计的高效数据结构，支持快速检索和组合
 */

// ============ 基础类型定义 ============

/** 语言代码 */
export type LanguageCode = 'zh' | 'en' | 'ja' | string

/** 词性标注 */
export enum PartOfSpeech {
  NOUN = 'n',           // 名词
  VERB = 'v',           // 动词
  ADJECTIVE = 'adj',    // 形容词
  ADVERB = 'adv',       // 副词
  PARTICLE = 'p',       // 助词/粒子
  PREFIX = 'pref',      // 前缀
  SUFFIX = 'suf',       // 后缀
  NUMERAL = 'num',      // 数词
  INTERJECTION = 'int'  // 感叹词
}

/** 语义域枚举 */
export enum SemanticDomain {
  NATURE = 'nature',         // 自然
  EMOTION = 'emotion',       // 情感
  ACTION = 'action',         // 动作
  QUALITY = 'quality',       // 品质
  TIME = 'time',            // 时间
  SPACE = 'space',          // 空间
  TECHNOLOGY = 'tech',      // 科技
  CULTURE = 'culture',      // 文化
  FANTASY = 'fantasy',      // 幻想
  SOCIAL = 'social'         // 社交
}

/** 文化风格 */
export enum CulturalStyle {
  TRADITIONAL = 'traditional',   // 传统
  MODERN = 'modern',            // 现代
  CUTE = 'cute',                // 可爱
  COOL = 'cool',                // 酷炫
  ELEGANT = 'elegant',          // 优雅
  PLAYFUL = 'playful',          // 俏皮
  MYSTERIOUS = 'mysterious',    // 神秘
  POWERFUL = 'powerful'         // 强力
}

// ============ 词汇相关数据结构 ============

/** 词汇条目 */
export interface LexicalEntry {
  /** 词汇本身 */
  word: string
  
  /** 语言代码 */
  language: LanguageCode
  
  /** 词性 */
  pos: PartOfSpeech
  
  /** 语义域 */
  domains: SemanticDomain[]
  
  /** 文化适用性评分 (0-1) */
  cultural_scores: Record<CulturalStyle, number>
  
  /** 使用频率 (0-1) */
  frequency: number
  
  /** 流行度评分 (0-1) */
  popularity: number
  
  /** 情感色彩 (-1到1, 负面到正面) */
  sentiment: number
  
  /** 正式程度 (0-1, 非正式到正式) */
  formality: number
  
  /** 年龄适用性 */
  age_groups: ('child' | 'teen' | 'adult' | 'senior')[]
  
  /** 音节数 */
  syllables: number
  
  /** 字符长度 */
  length: number
  
  /** 相关词汇ID列表 */
  related_words: string[]
  
  /** 禁忌等级 (0-1) */
  taboo_level: number
  
  /** 创建时间 */
  created_at: number
  
  /** 权重 (用于随机选择) */
  weight: number
}

/** 词汇索引结构 */
export interface LexicalIndex {
  /** 按语义域索引 */
  by_domain: Map<SemanticDomain, Set<string>>
  
  /** 按词性索引 */
  by_pos: Map<PartOfSpeech, Set<string>>
  
  /** 按文化风格索引 */
  by_style: Map<CulturalStyle, Set<string>>
  
  /** 按长度索引 */
  by_length: Map<number, Set<string>>
  
  /** 按音节数索引 */
  by_syllables: Map<number, Set<string>>
  
  /** 全文搜索索引 */
  search_index: Map<string, Set<string>>
}

// ============ 语法模式数据结构 ============

/** 语法模式 */
export interface GrammarPattern {
  /** 模式ID */
  id: string
  
  /** 模式名称 */
  name: string
  
  /** 适用语言 */
  language: LanguageCode
  
  /** 模式结构 (词性序列) */
  structure: PartOfSpeech[]
  
  /** 语义约束 */
  semantic_constraints: SemanticConstraint[]
  
  /** 文化适用性 */
  cultural_fitness: Record<CulturalStyle, number>
  
  /** 生成权重 */
  weight: number
  
  /** 示例 */
  examples: string[]
  
  /** 最小/最大长度 */
  min_length: number
  max_length: number
  
  /** 复杂度评分 */
  complexity: number
}

/** 语义约束 */
export interface SemanticConstraint {
  /** 约束类型 */
  type: 'domain_match' | 'sentiment_harmony' | 'formality_match' | 'cultural_fit'
  
  /** 约束参数 */
  params: Record<string, any>
  
  /** 约束强度 (0-1) */
  strength: number
}

// ============ 文化适配数据结构 ============

/** 文化配置 */
export interface CultureConfig {
  /** 文化ID */
  culture_id: string
  
  /** 语言代码 */
  language: LanguageCode
  
  /** 文化名称 */
  name: string
  
  /** 风格偏好权重 */
  style_preferences: Record<CulturalStyle, number>
  
  /** 语义域偏好 */
  domain_preferences: Record<SemanticDomain, number>
  
  /** 长度偏好 */
  length_preference: {
    min: number
    max: number
    optimal: number
  }
  
  /** 禁忌词列表 */
  taboo_words: Set<string>
  
  /** 流行词汇 */
  trending_words: string[]
  
  /** 特殊规则 */
  special_rules: CulturalRule[]
}

/** 文化规则 */
export interface CulturalRule {
  /** 规则ID */
  id: string
  
  /** 规则类型 */
  type: 'combination' | 'avoidance' | 'preference' | 'transformation'
  
  /** 规则描述 */
  description: string
  
  /** 条件 */
  condition: (context: GenerationContext) => boolean
  
  /** 动作 */
  action: (candidates: LexicalEntry[]) => LexicalEntry[]
  
  /** 优先级 */
  priority: number
}

// ============ 生成上下文数据结构 ============

/** 生成上下文 */
export interface GenerationContext {
  /** 目标语言 */
  language: LanguageCode
  
  /** 文化风格 */
  style: CulturalStyle
  
  /** 语义域偏好 */
  preferred_domains: SemanticDomain[]
  
  /** 目标长度范围 */
  length_range: [number, number]
  
  /** 情感倾向 */
  sentiment_target: number
  
  /** 正式程度 */
  formality_target: number
  
  /** 年龄群体 */
  target_age: 'child' | 'teen' | 'adult' | 'senior'
  
  /** 避免的词汇 */
  avoid_words: Set<string>
  
  /** 创意程度 (0-1) */
  creativity: number
  
  /** 生成数量 */
  count: number
  
  /** 去重要求 */
  unique: boolean
}

/** 生成结果 */
export interface GenerationResult {
  /** 生成的用户名 */
  username: string
  
  /** 组成词汇 */
  components: LexicalEntry[]
  
  /** 使用的语法模式 */
  pattern: GrammarPattern
  
  /** 质量评分 */
  quality_score: number
  
  /** 各维度评分 */
  scores: {
    semantic_coherence: number    // 语义连贯性
    cultural_fitness: number      // 文化适配度
    phonetic_harmony: number      // 音韵和谐度
    memorability: number          // 记忆性
    uniqueness: number           // 独特性
  }
  
  /** 生成时间戳 */
  generated_at: number
  
  /** 生成耗时 (毫秒) */
  generation_time: number
}

// ============ 缓存数据结构 ============

/** 缓存条目 */
export interface CacheEntry<T> {
  /** 缓存的数据 */
  data: T
  
  /** 创建时间 */
  created_at: number
  
  /** 访问次数 */
  access_count: number
  
  /** 最后访问时间 */
  last_accessed: number
  
  /** 过期时间 */
  expires_at: number
  
  /** 缓存键 */
  key: string
}

/** LRU缓存配置 */
export interface CacheConfig {
  /** 最大条目数 */
  max_size: number
  
  /** 默认TTL (毫秒) */
  default_ttl: number
  
  /** 清理间隔 (毫秒) */
  cleanup_interval: number
  
  /** 启用统计 */
  enable_stats: boolean
}

// ============ 性能监控数据结构 ============

/** 性能指标 */
export interface PerformanceMetrics {
  /** 总生成次数 */
  total_generations: number
  
  /** 成功生成次数 */
  successful_generations: number
  
  /** 平均生成时间 */
  avg_generation_time: number
  
  /** 缓存命中率 */
  cache_hit_rate: number
  
  /** 内存使用情况 */
  memory_usage: {
    heap_used: number
    heap_total: number
    external: number
  }
  
  /** 各语言生成统计 */
  language_stats: Record<LanguageCode, {
    count: number
    avg_time: number
    avg_quality: number
  }>
  
  /** 错误统计 */
  error_stats: Record<string, number>
}

// ============ 配置数据结构 ============

/** 系统配置 */
export interface SystemConfig {
  /** 数据目录 */
  data_directory: string
  
  /** 缓存配置 */
  cache: CacheConfig
  
  /** 性能配置 */
  performance: {
    max_generation_time: number
    max_concurrent_generations: number
    enable_profiling: boolean
  }
  
  /** 日志配置 */
  logging: {
    level: 'debug' | 'info' | 'warn' | 'error'
    enable_file_logging: boolean
    log_directory: string
  }
  
  /** 支持的语言 */
  supported_languages: LanguageCode[]
  
  /** 默认设置 */
  defaults: {
    language: LanguageCode
    style: CulturalStyle
    count: number
    creativity: number
  }
}

// ============ 导出类型联合 ============

export type WordId = string
export type PatternId = string
export type CultureId = string

/** 所有数据结构的联合类型 */
export type DataStructure = 
  | LexicalEntry 
  | GrammarPattern 
  | CultureConfig 
  | GenerationContext 
  | GenerationResult
  | CacheEntry<any>
  | PerformanceMetrics
  | SystemConfig
