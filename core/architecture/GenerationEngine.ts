/**
 * 核心生成引擎
 * 
 * 整合所有组件的主要生成引擎，实现高效的用户名生成
 */

import {
  LexicalEntry,
  GrammarPattern,
  GenerationContext,
  GenerationResult,
  CultureConfig,
  LanguageCode,
  SemanticDomain,
  CulturalStyle,
  PartOfSpeech
} from './DataStructures'

import {
  WeightedRandomSelector,
  GreedySearchAlgorithm,
  DynamicProgrammingAlgorithm,
  AlgorithmFactory,
  QualityEvaluator
} from './CoreAlgorithms'

import { DataManager } from './DataManager'

// ============ 生成策略接口 ============

export interface GenerationStrategy {
  generate(
    context: GenerationContext,
    dataManager: DataManager
  ): Promise<GenerationResult[]>
}

// ============ 缓存管理器 ============

class GenerationCache {
  private cache: Map<string, GenerationResult[]> = new Map()
  private accessTimes: Map<string, number> = new Map()
  private readonly maxSize: number = 1000
  private readonly ttl: number = 10 * 60 * 1000 // 10分钟

  /** 获取缓存 */
  get(key: string): GenerationResult[] | null {
    const now = Date.now()
    const accessTime = this.accessTimes.get(key)
    
    if (accessTime && (now - accessTime) < this.ttl && this.cache.has(key)) {
      this.accessTimes.set(key, now)
      return this.cache.get(key)!
    }
    
    // 过期或不存在，清理
    this.cache.delete(key)
    this.accessTimes.delete(key)
    return null
  }

  /** 设置缓存 */
  set(key: string, value: GenerationResult[]): void {
    const now = Date.now()
    
    // 如果缓存已满，清理最旧的条目
    if (this.cache.size >= this.maxSize) {
      this.evictOldest()
    }
    
    this.cache.set(key, value)
    this.accessTimes.set(key, now)
  }

  /** 清理最旧的条目 */
  private evictOldest(): void {
    let oldestKey: string | null = null
    let oldestTime = Date.now()
    
    for (const [key, time] of this.accessTimes) {
      if (time < oldestTime) {
        oldestTime = time
        oldestKey = key
      }
    }
    
    if (oldestKey) {
      this.cache.delete(oldestKey)
      this.accessTimes.delete(oldestKey)
    }
  }

  /** 生成缓存键 */
  static generateKey(context: GenerationContext): string {
    return JSON.stringify({
      language: context.language,
      style: context.style,
      domains: context.preferred_domains.sort(),
      length_range: context.length_range,
      sentiment: context.sentiment_target,
      formality: context.formality_target,
      age: context.target_age,
      creativity: context.creativity
    })
  }
}

// ============ 主生成引擎 ============

export class UsernameGenerationEngine {
  private dataManager: DataManager
  private cache: GenerationCache
  private qualityEvaluator: QualityEvaluator
  private strategies: Map<string, GenerationStrategy>

  constructor(dataManager: DataManager) {
    this.dataManager = dataManager
    this.cache = new GenerationCache()
    this.qualityEvaluator = AlgorithmFactory.createQualityEvaluator()
    this.strategies = new Map()
    
    // 注册生成策略
    this.registerStrategies()
  }

  /** 注册生成策略 */
  private registerStrategies(): void {
    this.strategies.set('balanced', new BalancedGenerationStrategy())
    this.strategies.set('creative', new CreativeGenerationStrategy())
    this.strategies.set('traditional', new TraditionalGenerationStrategy())
    this.strategies.set('fast', new FastGenerationStrategy())
  }

  /** 生成用户名 */
  async generateUsernames(context: GenerationContext): Promise<GenerationResult[]> {
    const startTime = Date.now()
    
    try {
      // 检查缓存
      const cacheKey = GenerationCache.generateKey(context)
      const cached = this.cache.get(cacheKey)
      if (cached) {
        return cached.slice(0, context.count)
      }

      // 确保语言数据已加载
      await this.dataManager.loadLanguageData(context.language)

      // 选择生成策略
      const strategy = this.selectStrategy(context)
      
      // 执行生成
      const results = await strategy.generate(context, this.dataManager)
      
      // 质量评估和排序
      const evaluatedResults = await this.evaluateAndSort(results, context)
      
      // 缓存结果
      this.cache.set(cacheKey, evaluatedResults)
      
      // 记录性能指标
      const generationTime = Date.now() - startTime
      this.recordPerformanceMetrics(context, evaluatedResults, generationTime)
      
      return evaluatedResults.slice(0, context.count)
      
    } catch (error) {
      console.error('Generation failed:', error)
      return this.generateFallbackResults(context)
    }
  }

  /** 选择生成策略 */
  private selectStrategy(context: GenerationContext): GenerationStrategy {
    // 根据上下文选择最适合的策略
    if (context.creativity > 0.8) {
      return this.strategies.get('creative')!
    } else if (context.style === CulturalStyle.TRADITIONAL) {
      return this.strategies.get('traditional')!
    } else if (context.count > 10) {
      return this.strategies.get('fast')!
    } else {
      return this.strategies.get('balanced')!
    }
  }

  /** 质量评估和排序 */
  private async evaluateAndSort(
    results: GenerationResult[],
    context: GenerationContext
  ): Promise<GenerationResult[]> {
    // 为每个结果计算质量分数
    for (const result of results) {
      result.quality_score = this.qualityEvaluator.evaluate(
        result.username,
        result.components,
        result.pattern,
        context
      )
    }

    // 按质量分数排序
    results.sort((a, b) => b.quality_score - a.quality_score)
    
    return results
  }

  /** 生成回退结果 */
  private generateFallbackResults(context: GenerationContext): GenerationResult[] {
    const fallbackUsernames = this.getFallbackUsernames(context.language)
    
    return fallbackUsernames.slice(0, context.count).map(username => ({
      username,
      components: [],
      pattern: this.createFallbackPattern(context.language),
      quality_score: 0.3,
      scores: {
        semantic_coherence: 0.3,
        cultural_fitness: 0.3,
        phonetic_harmony: 0.3,
        memorability: 0.4,
        uniqueness: 0.2
      },
      generated_at: Date.now(),
      generation_time: 0
    }))
  }

  /** 获取回退用户名 */
  private getFallbackUsernames(language: LanguageCode): string[] {
    const fallbacks: Record<LanguageCode, string[]> = {
      'zh': ['用户星辰', '默认用户', '临时账号', '访客用户', '新用户'],
      'en': ['DefaultUser', 'GuestUser', 'TempUser', 'NewUser', 'RandomUser'],
      'ja': ['ユーザー', 'ゲスト', 'デフォルト', '新規ユーザー', 'テンプ']
    }
    
    return fallbacks[language] || fallbacks['en']
  }

  /** 创建回退模式 */
  private createFallbackPattern(language: LanguageCode): GrammarPattern {
    return {
      id: 'fallback',
      name: 'Fallback Pattern',
      language,
      structure: [PartOfSpeech.NOUN],
      semantic_constraints: [],
      cultural_fitness: {} as Record<CulturalStyle, number>,
      weight: 0.1,
      examples: [],
      min_length: 2,
      max_length: 10,
      complexity: 0.1
    }
  }

  /** 记录性能指标 */
  private recordPerformanceMetrics(
    context: GenerationContext,
    results: GenerationResult[],
    generationTime: number
  ): void {
    // 这里可以记录到日志或监控系统
    console.log(`Generated ${results.length} usernames for ${context.language} in ${generationTime}ms`)
  }
}

// ============ 具体生成策略实现 ============

/** 平衡生成策略 */
class BalancedGenerationStrategy implements GenerationStrategy {
  async generate(
    context: GenerationContext,
    dataManager: DataManager
  ): Promise<GenerationResult[]> {
    const patterns = await dataManager.getGrammarPatterns(context.language)
    const words = await dataManager.getLexicalData(context.language)
    
    // 过滤适合的模式
    const suitablePatterns = this.filterPatterns(patterns, context)
    
    const results: GenerationResult[] = []
    const maxAttempts = context.count * 3
    let attempts = 0
    
    while (results.length < context.count && attempts < maxAttempts) {
      attempts++
      
      // 选择模式
      const pattern = this.selectPattern(suitablePatterns, context)
      if (!pattern) continue
      
      // 为每个位置选择词汇
      const selectedWords = await this.selectWordsForPattern(pattern, words, context, dataManager)
      if (!selectedWords || selectedWords.length !== pattern.structure.length) continue
      
      // 生成用户名
      const username = selectedWords.map(w => w.word).join('')
      
      // 检查重复和质量
      if (!this.isDuplicate(username, results) && this.meetsMinimumQuality(selectedWords, context)) {
        results.push({
          username,
          components: selectedWords,
          pattern,
          quality_score: 0, // 将在后续评估中计算
          scores: {
            semantic_coherence: 0,
            cultural_fitness: 0,
            phonetic_harmony: 0,
            memorability: 0,
            uniqueness: 0
          },
          generated_at: Date.now(),
          generation_time: 0
        })
      }
    }
    
    return results
  }

  /** 过滤适合的模式 */
  private filterPatterns(patterns: GrammarPattern[], context: GenerationContext): GrammarPattern[] {
    return patterns.filter(pattern => {
      // 长度检查
      if (pattern.min_length > context.length_range[1] || pattern.max_length < context.length_range[0]) {
        return false
      }
      
      // 文化适配度检查
      const culturalScore = pattern.cultural_fitness[context.style] || 0
      return culturalScore > 0.3
    })
  }

  /** 选择模式 */
  private selectPattern(patterns: GrammarPattern[], context: GenerationContext): GrammarPattern | null {
    if (patterns.length === 0) return null
    
    // 计算权重
    const weights = patterns.map(p => {
      const culturalScore = p.cultural_fitness[context.style] || 0
      const complexityScore = context.creativity * p.complexity + (1 - context.creativity) * (1 - p.complexity)
      return p.weight * culturalScore * complexityScore
    })
    
    const selector = new WeightedRandomSelector(weights)
    const index = selector.select()
    
    return index >= 0 ? patterns[index] : null
  }

  /** 为模式选择词汇 */
  private async selectWordsForPattern(
    pattern: GrammarPattern,
    allWords: LexicalEntry[],
    context: GenerationContext,
    dataManager: DataManager
  ): Promise<LexicalEntry[] | null> {
    const selectedWords: LexicalEntry[] = []
    const usedWords = new Set<string>()
    
    for (let i = 0; i < pattern.structure.length; i++) {
      const requiredPos = pattern.structure[i]
      
      // 搜索符合条件的词汇
      const candidates = await dataManager.searchWords(context.language, {
        pos: [requiredPos],
        domains: context.preferred_domains.length > 0 ? context.preferred_domains : undefined,
        styles: [context.style],
        minLength: 1,
        maxLength: 6
      })
      
      // 过滤已使用的词汇
      const availableCandidates = candidates.filter(w => 
        !usedWords.has(w.word) && 
        !context.avoid_words.has(w.word) &&
        this.isWordSuitableForContext(w, context)
      )
      
      if (availableCandidates.length === 0) {
        return null // 无法找到合适的词汇
      }
      
      // 使用贪心算法选择最佳词汇
      const selected = this.selectBestWord(availableCandidates, selectedWords, context)
      selectedWords.push(selected)
      usedWords.add(selected.word)
    }
    
    return selectedWords
  }

  /** 检查词汇是否适合上下文 */
  private isWordSuitableForContext(word: LexicalEntry, context: GenerationContext): boolean {
    // 情感倾向检查
    const sentimentDiff = Math.abs(word.sentiment - context.sentiment_target)
    if (sentimentDiff > 1.0) return false
    
    // 正式程度检查
    const formalityDiff = Math.abs(word.formality - context.formality_target)
    if (formalityDiff > 0.7) return false
    
    // 年龄适用性检查
    if (!word.age_groups.includes(context.target_age)) return false
    
    // 禁忌等级检查
    if (word.taboo_level > 0.3) return false
    
    return true
  }

  /** 选择最佳词汇 */
  private selectBestWord(
    candidates: LexicalEntry[],
    existingWords: LexicalEntry[],
    context: GenerationContext
  ): LexicalEntry {
    // 计算每个候选词的评分
    const scoredCandidates = candidates.map(candidate => ({
      word: candidate,
      score: this.calculateWordScore(candidate, existingWords, context)
    }))
    
    // 排序并选择最高分的词汇
    scoredCandidates.sort((a, b) => b.score - a.score)
    
    // 添加一些随机性以增加多样性
    const topCandidates = scoredCandidates.slice(0, Math.min(3, scoredCandidates.length))
    const randomIndex = Math.floor(Math.random() * topCandidates.length)
    
    return topCandidates[randomIndex].word
  }

  /** 计算词汇评分 */
  private calculateWordScore(
    word: LexicalEntry,
    existingWords: LexicalEntry[],
    context: GenerationContext
  ): number {
    let score = 0
    
    // 文化适配度
    score += (word.cultural_scores[context.style] || 0) * 0.3
    
    // 频率和流行度
    score += word.frequency * 0.2
    score += word.popularity * 0.1
    
    // 情感和正式程度匹配
    const sentimentMatch = 1 - Math.abs(word.sentiment - context.sentiment_target) / 2
    const formalityMatch = 1 - Math.abs(word.formality - context.formality_target)
    score += sentimentMatch * 0.2
    score += formalityMatch * 0.2
    
    return score
  }

  /** 检查是否重复 */
  private isDuplicate(username: string, existingResults: GenerationResult[]): boolean {
    return existingResults.some(result => result.username === username)
  }

  /** 检查是否满足最低质量要求 */
  private meetsMinimumQuality(words: LexicalEntry[], context: GenerationContext): boolean {
    // 简单的质量检查
    const avgCulturalScore = words.reduce((sum, w) => sum + (w.cultural_scores[context.style] || 0), 0) / words.length
    return avgCulturalScore > 0.3
  }
}

/** 创意生成策略 */
class CreativeGenerationStrategy extends BalancedGenerationStrategy {
  // 重写选择模式方法，偏好复杂度更高的模式
  protected selectPattern(patterns: GrammarPattern[], context: GenerationContext): GrammarPattern | null {
    const creativePatterns = patterns.filter(p => p.complexity > 0.5)
    return super.selectPattern(creativePatterns.length > 0 ? creativePatterns : patterns, context)
  }
}

/** 传统生成策略 */
class TraditionalGenerationStrategy extends BalancedGenerationStrategy {
  // 重写过滤方法，偏好传统风格
  protected filterPatterns(patterns: GrammarPattern[], context: GenerationContext): GrammarPattern[] {
    return patterns.filter(pattern => {
      const traditionalScore = pattern.cultural_fitness[CulturalStyle.TRADITIONAL] || 0
      return traditionalScore > 0.5
    })
  }
}

/** 快速生成策略 */
class FastGenerationStrategy extends BalancedGenerationStrategy {
  // 简化选择逻辑以提高速度
  protected async selectWordsForPattern(
    pattern: GrammarPattern,
    allWords: LexicalEntry[],
    context: GenerationContext,
    dataManager: DataManager
  ): Promise<LexicalEntry[] | null> {
    // 使用更简单的选择逻辑
    const selectedWords: LexicalEntry[] = []
    
    for (const requiredPos of pattern.structure) {
      const candidates = allWords.filter(w => 
        w.pos === requiredPos && 
        !context.avoid_words.has(w.word)
      )
      
      if (candidates.length === 0) return null
      
      // 简单随机选择
      const randomIndex = Math.floor(Math.random() * candidates.length)
      selectedWords.push(candidates[randomIndex])
    }
    
    return selectedWords
  }
}
