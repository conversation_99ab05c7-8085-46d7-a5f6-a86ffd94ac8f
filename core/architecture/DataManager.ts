/**
 * 数据管理系统
 * 
 * 高效的本地数据存储和检索系统，支持多语言数据的快速访问
 */

import * as fs from 'fs/promises'
import * as path from 'path'
import {
  LexicalEntry,
  GrammarPattern,
  CultureConfig,
  LexicalIndex,
  SystemConfig,
  LanguageCode,
  SemanticDomain,
  PartOfSpeech,
  CulturalStyle
} from './DataStructures'

// ============ 数据存储格式定义 ============

/** 词汇数据文件格式 */
export interface LexicalDataFile {
  meta: {
    language: LanguageCode
    version: string
    created_at: string
    word_count: number
    checksum: string
  }
  words: LexicalEntry[]
}

/** 语法模式数据文件格式 */
export interface GrammarDataFile {
  meta: {
    language: LanguageCode
    version: string
    created_at: string
    pattern_count: number
  }
  patterns: GrammarPattern[]
}

/** 文化配置数据文件格式 */
export interface CultureDataFile {
  meta: {
    culture_id: string
    version: string
    created_at: string
  }
  config: CultureConfig
}

// ============ 数据访问层接口 ============

export interface DataAccessLayer {
  /** 加载词汇数据 */
  loadLexicalData(language: LanguageCode): Promise<LexicalEntry[]>
  
  /** 加载语法模式 */
  loadGrammarPatterns(language: LanguageCode): Promise<GrammarPattern[]>
  
  /** 加载文化配置 */
  loadCultureConfig(cultureId: string): Promise<CultureConfig>
  
  /** 保存词汇数据 */
  saveLexicalData(language: LanguageCode, data: LexicalEntry[]): Promise<void>
  
  /** 保存语法模式 */
  saveGrammarPatterns(language: LanguageCode, patterns: GrammarPattern[]): Promise<void>
  
  /** 保存文化配置 */
  saveCultureConfig(config: CultureConfig): Promise<void>
}

// ============ 文件系统数据访问实现 ============

export class FileSystemDataAccess implements DataAccessLayer {
  private dataDirectory: string
  private cache: Map<string, any> = new Map()
  private cacheExpiry: Map<string, number> = new Map()
  private readonly CACHE_TTL = 5 * 60 * 1000 // 5分钟缓存

  constructor(dataDirectory: string) {
    this.dataDirectory = dataDirectory
  }

  /** 初始化数据目录 */
  async initialize(): Promise<void> {
    await this.ensureDirectoryExists(this.dataDirectory)
    await this.ensureDirectoryExists(path.join(this.dataDirectory, 'lexical'))
    await this.ensureDirectoryExists(path.join(this.dataDirectory, 'grammar'))
    await this.ensureDirectoryExists(path.join(this.dataDirectory, 'culture'))
    await this.ensureDirectoryExists(path.join(this.dataDirectory, 'cache'))
  }

  /** 加载词汇数据 */
  async loadLexicalData(language: LanguageCode): Promise<LexicalEntry[]> {
    const cacheKey = `lexical:${language}`
    
    // 检查缓存
    if (this.isCacheValid(cacheKey)) {
      return this.cache.get(cacheKey)
    }

    const filePath = path.join(this.dataDirectory, 'lexical', `${language}.json`)
    
    try {
      const content = await fs.readFile(filePath, 'utf-8')
      const data: LexicalDataFile = JSON.parse(content)
      
      // 验证数据完整性
      this.validateLexicalData(data)
      
      // 缓存数据
      this.cache.set(cacheKey, data.words)
      this.cacheExpiry.set(cacheKey, Date.now() + this.CACHE_TTL)
      
      return data.words
    } catch (error) {
      if ((error as any).code === 'ENOENT') {
        // 文件不存在，返回空数组
        return []
      }
      throw new Error(`Failed to load lexical data for ${language}: ${error}`)
    }
  }

  /** 加载语法模式 */
  async loadGrammarPatterns(language: LanguageCode): Promise<GrammarPattern[]> {
    const cacheKey = `grammar:${language}`
    
    if (this.isCacheValid(cacheKey)) {
      return this.cache.get(cacheKey)
    }

    const filePath = path.join(this.dataDirectory, 'grammar', `${language}.json`)
    
    try {
      const content = await fs.readFile(filePath, 'utf-8')
      const data: GrammarDataFile = JSON.parse(content)
      
      this.cache.set(cacheKey, data.patterns)
      this.cacheExpiry.set(cacheKey, Date.now() + this.CACHE_TTL)
      
      return data.patterns
    } catch (error) {
      if ((error as any).code === 'ENOENT') {
        return []
      }
      throw new Error(`Failed to load grammar patterns for ${language}: ${error}`)
    }
  }

  /** 加载文化配置 */
  async loadCultureConfig(cultureId: string): Promise<CultureConfig> {
    const cacheKey = `culture:${cultureId}`
    
    if (this.isCacheValid(cacheKey)) {
      return this.cache.get(cacheKey)
    }

    const filePath = path.join(this.dataDirectory, 'culture', `${cultureId}.json`)
    
    try {
      const content = await fs.readFile(filePath, 'utf-8')
      const data: CultureDataFile = JSON.parse(content)
      
      this.cache.set(cacheKey, data.config)
      this.cacheExpiry.set(cacheKey, Date.now() + this.CACHE_TTL)
      
      return data.config
    } catch (error) {
      throw new Error(`Failed to load culture config for ${cultureId}: ${error}`)
    }
  }

  /** 保存词汇数据 */
  async saveLexicalData(language: LanguageCode, words: LexicalEntry[]): Promise<void> {
    const data: LexicalDataFile = {
      meta: {
        language,
        version: '1.0.0',
        created_at: new Date().toISOString(),
        word_count: words.length,
        checksum: this.calculateChecksum(words)
      },
      words
    }

    const filePath = path.join(this.dataDirectory, 'lexical', `${language}.json`)
    await fs.writeFile(filePath, JSON.stringify(data, null, 2), 'utf-8')
    
    // 清除缓存
    this.cache.delete(`lexical:${language}`)
    this.cacheExpiry.delete(`lexical:${language}`)
  }

  /** 保存语法模式 */
  async saveGrammarPatterns(language: LanguageCode, patterns: GrammarPattern[]): Promise<void> {
    const data: GrammarDataFile = {
      meta: {
        language,
        version: '1.0.0',
        created_at: new Date().toISOString(),
        pattern_count: patterns.length
      },
      patterns
    }

    const filePath = path.join(this.dataDirectory, 'grammar', `${language}.json`)
    await fs.writeFile(filePath, JSON.stringify(data, null, 2), 'utf-8')
    
    this.cache.delete(`grammar:${language}`)
    this.cacheExpiry.delete(`grammar:${language}`)
  }

  /** 保存文化配置 */
  async saveCultureConfig(config: CultureConfig): Promise<void> {
    const data: CultureDataFile = {
      meta: {
        culture_id: config.culture_id,
        version: '1.0.0',
        created_at: new Date().toISOString()
      },
      config
    }

    const filePath = path.join(this.dataDirectory, 'culture', `${config.culture_id}.json`)
    await fs.writeFile(filePath, JSON.stringify(data, null, 2), 'utf-8')
    
    this.cache.delete(`culture:${config.culture_id}`)
    this.cacheExpiry.delete(`culture:${config.culture_id}`)
  }

  /** 检查缓存是否有效 */
  private isCacheValid(key: string): boolean {
    const expiry = this.cacheExpiry.get(key)
    return expiry !== undefined && Date.now() < expiry && this.cache.has(key)
  }

  /** 确保目录存在 */
  private async ensureDirectoryExists(dirPath: string): Promise<void> {
    try {
      await fs.access(dirPath)
    } catch {
      await fs.mkdir(dirPath, { recursive: true })
    }
  }

  /** 验证词汇数据 */
  private validateLexicalData(data: LexicalDataFile): void {
    if (!data.meta || !data.words) {
      throw new Error('Invalid lexical data format')
    }
    
    if (data.words.length !== data.meta.word_count) {
      throw new Error('Word count mismatch in lexical data')
    }
  }

  /** 计算校验和 */
  private calculateChecksum(data: any): string {
    // 简化的校验和实现
    const str = JSON.stringify(data)
    let hash = 0
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // 转换为32位整数
    }
    return hash.toString(16)
  }
}

// ============ 索引管理器 ============

export class IndexManager {
  private indices: Map<LanguageCode, LexicalIndex> = new Map()

  /** 构建词汇索引 */
  buildLexicalIndex(language: LanguageCode, words: LexicalEntry[]): LexicalIndex {
    const index: LexicalIndex = {
      by_domain: new Map(),
      by_pos: new Map(),
      by_style: new Map(),
      by_length: new Map(),
      by_syllables: new Map(),
      search_index: new Map()
    }

    for (const word of words) {
      const wordId = word.word

      // 按语义域索引
      for (const domain of word.domains) {
        if (!index.by_domain.has(domain)) {
          index.by_domain.set(domain, new Set())
        }
        index.by_domain.get(domain)!.add(wordId)
      }

      // 按词性索引
      if (!index.by_pos.has(word.pos)) {
        index.by_pos.set(word.pos, new Set())
      }
      index.by_pos.get(word.pos)!.add(wordId)

      // 按文化风格索引
      for (const [style, score] of Object.entries(word.cultural_scores)) {
        if (score > 0.5) { // 只索引高分的风格
          const styleEnum = style as CulturalStyle
          if (!index.by_style.has(styleEnum)) {
            index.by_style.set(styleEnum, new Set())
          }
          index.by_style.get(styleEnum)!.add(wordId)
        }
      }

      // 按长度索引
      if (!index.by_length.has(word.length)) {
        index.by_length.set(word.length, new Set())
      }
      index.by_length.get(word.length)!.add(wordId)

      // 按音节数索引
      if (!index.by_syllables.has(word.syllables)) {
        index.by_syllables.set(word.syllables, new Set())
      }
      index.by_syllables.get(word.syllables)!.add(wordId)

      // 全文搜索索引
      this.addToSearchIndex(index.search_index, word.word, wordId)
    }

    this.indices.set(language, index)
    return index
  }

  /** 获取索引 */
  getIndex(language: LanguageCode): LexicalIndex | undefined {
    return this.indices.get(language)
  }

  /** 添加到搜索索引 */
  private addToSearchIndex(
    searchIndex: Map<string, Set<string>>,
    word: string,
    wordId: string
  ): void {
    // 添加完整词汇
    if (!searchIndex.has(word)) {
      searchIndex.set(word, new Set())
    }
    searchIndex.get(word)!.add(wordId)

    // 添加前缀（用于前缀搜索）
    for (let i = 1; i <= word.length; i++) {
      const prefix = word.substring(0, i)
      if (!searchIndex.has(prefix)) {
        searchIndex.set(prefix, new Set())
      }
      searchIndex.get(prefix)!.add(wordId)
    }
  }
}

// ============ 数据管理器主类 ============

export class DataManager {
  private dataAccess: DataAccessLayer
  private indexManager: IndexManager
  private config: SystemConfig
  private loadedLanguages: Set<LanguageCode> = new Set()

  constructor(config: SystemConfig) {
    this.config = config
    this.dataAccess = new FileSystemDataAccess(config.data_directory)
    this.indexManager = new IndexManager()
  }

  /** 初始化数据管理器 */
  async initialize(): Promise<void> {
    if (this.dataAccess instanceof FileSystemDataAccess) {
      await this.dataAccess.initialize()
    }
  }

  /** 加载语言数据 */
  async loadLanguageData(language: LanguageCode): Promise<void> {
    if (this.loadedLanguages.has(language)) {
      return // 已加载
    }

    try {
      // 并行加载词汇和语法数据
      const [words, patterns] = await Promise.all([
        this.dataAccess.loadLexicalData(language),
        this.dataAccess.loadGrammarPatterns(language)
      ])

      // 构建索引
      this.indexManager.buildLexicalIndex(language, words)

      this.loadedLanguages.add(language)
      
      console.log(`Loaded ${words.length} words and ${patterns.length} patterns for ${language}`)
    } catch (error) {
      throw new Error(`Failed to load language data for ${language}: ${error}`)
    }
  }

  /** 获取词汇数据 */
  async getLexicalData(language: LanguageCode): Promise<LexicalEntry[]> {
    if (!this.loadedLanguages.has(language)) {
      await this.loadLanguageData(language)
    }
    return this.dataAccess.loadLexicalData(language)
  }

  /** 获取语法模式 */
  async getGrammarPatterns(language: LanguageCode): Promise<GrammarPattern[]> {
    if (!this.loadedLanguages.has(language)) {
      await this.loadLanguageData(language)
    }
    return this.dataAccess.loadGrammarPatterns(language)
  }

  /** 获取文化配置 */
  async getCultureConfig(cultureId: string): Promise<CultureConfig> {
    return this.dataAccess.loadCultureConfig(cultureId)
  }

  /** 获取索引 */
  getIndex(language: LanguageCode): LexicalIndex | undefined {
    return this.indexManager.getIndex(language)
  }

  /** 搜索词汇 */
  async searchWords(
    language: LanguageCode,
    criteria: {
      domains?: SemanticDomain[]
      pos?: PartOfSpeech[]
      styles?: CulturalStyle[]
      minLength?: number
      maxLength?: number
      minSyllables?: number
      maxSyllables?: number
      query?: string
    }
  ): Promise<LexicalEntry[]> {
    const words = await this.getLexicalData(language)
    const index = this.getIndex(language)
    
    if (!index) {
      return words.filter(word => this.matchesCriteria(word, criteria))
    }

    // 使用索引进行快速搜索
    let candidateIds = new Set<string>()
    let firstFilter = true

    // 按语义域过滤
    if (criteria.domains && criteria.domains.length > 0) {
      const domainIds = new Set<string>()
      for (const domain of criteria.domains) {
        const ids = index.by_domain.get(domain)
        if (ids) {
          ids.forEach(id => domainIds.add(id))
        }
      }
      candidateIds = domainIds
      firstFilter = false
    }

    // 按词性过滤
    if (criteria.pos && criteria.pos.length > 0) {
      const posIds = new Set<string>()
      for (const pos of criteria.pos) {
        const ids = index.by_pos.get(pos)
        if (ids) {
          ids.forEach(id => posIds.add(id))
        }
      }
      
      if (firstFilter) {
        candidateIds = posIds
        firstFilter = false
      } else {
        candidateIds = new Set([...candidateIds].filter(id => posIds.has(id)))
      }
    }

    // 按文化风格过滤
    if (criteria.styles && criteria.styles.length > 0) {
      const styleIds = new Set<string>()
      for (const style of criteria.styles) {
        const ids = index.by_style.get(style)
        if (ids) {
          ids.forEach(id => styleIds.add(id))
        }
      }
      
      if (firstFilter) {
        candidateIds = styleIds
        firstFilter = false
      } else {
        candidateIds = new Set([...candidateIds].filter(id => styleIds.has(id)))
      }
    }

    // 如果没有使用索引过滤，使用所有词汇
    if (firstFilter) {
      candidateIds = new Set(words.map(w => w.word))
    }

    // 获取候选词汇并应用其他过滤条件
    const candidates = words.filter(word => candidateIds.has(word.word))
    return candidates.filter(word => this.matchesCriteria(word, criteria))
  }

  /** 检查词汇是否匹配条件 */
  private matchesCriteria(
    word: LexicalEntry,
    criteria: {
      minLength?: number
      maxLength?: number
      minSyllables?: number
      maxSyllables?: number
      query?: string
    }
  ): boolean {
    if (criteria.minLength !== undefined && word.length < criteria.minLength) {
      return false
    }
    
    if (criteria.maxLength !== undefined && word.length > criteria.maxLength) {
      return false
    }
    
    if (criteria.minSyllables !== undefined && word.syllables < criteria.minSyllables) {
      return false
    }
    
    if (criteria.maxSyllables !== undefined && word.syllables > criteria.maxSyllables) {
      return false
    }
    
    if (criteria.query && !word.word.includes(criteria.query)) {
      return false
    }
    
    return true
  }

  /** 获取已加载的语言列表 */
  getLoadedLanguages(): LanguageCode[] {
    return Array.from(this.loadedLanguages)
  }

  /** 获取支持的语言列表 */
  getSupportedLanguages(): LanguageCode[] {
    return this.config.supported_languages
  }
}
