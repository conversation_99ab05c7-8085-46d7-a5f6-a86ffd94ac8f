/**
 * 性能优化和监控系统
 * 
 * 提供全面的性能监控、优化建议和自动调优功能
 */

import {
  PerformanceMetrics,
  GenerationResult,
  GenerationContext,
  LanguageCode
} from './DataStructures'

// ============ 性能监控器 ============

export class PerformanceMonitor {
  private metrics: PerformanceMetrics
  private startTimes: Map<string, number> = new Map()
  private operationCounts: Map<string, number> = new Map()
  private errorCounts: Map<string, number> = new Map()

  constructor() {
    this.metrics = {
      total_generations: 0,
      successful_generations: 0,
      avg_generation_time: 0,
      cache_hit_rate: 0,
      memory_usage: {
        heap_used: 0,
        heap_total: 0,
        external: 0
      },
      language_stats: {},
      error_stats: {}
    }
  }

  /** 开始计时 */
  startTimer(operationId: string): void {
    this.startTimes.set(operationId, Date.now())
  }

  /** 结束计时 */
  endTimer(operationId: string): number {
    const startTime = this.startTimes.get(operationId)
    if (!startTime) return 0
    
    const duration = Date.now() - startTime
    this.startTimes.delete(operationId)
    
    // 更新操作计数
    const currentCount = this.operationCounts.get(operationId) || 0
    this.operationCounts.set(operationId, currentCount + 1)
    
    return duration
  }

  /** 记录生成结果 */
  recordGeneration(
    context: GenerationContext,
    results: GenerationResult[],
    duration: number,
    success: boolean
  ): void {
    this.metrics.total_generations++
    
    if (success) {
      this.metrics.successful_generations++
      
      // 更新平均生成时间
      const totalTime = this.metrics.avg_generation_time * (this.metrics.successful_generations - 1) + duration
      this.metrics.avg_generation_time = totalTime / this.metrics.successful_generations
      
      // 更新语言统计
      if (!this.metrics.language_stats[context.language]) {
        this.metrics.language_stats[context.language] = {
          count: 0,
          avg_time: 0,
          avg_quality: 0
        }
      }
      
      const langStats = this.metrics.language_stats[context.language]
      langStats.count++
      
      // 更新平均时间
      const langTotalTime = langStats.avg_time * (langStats.count - 1) + duration
      langStats.avg_time = langTotalTime / langStats.count
      
      // 更新平均质量
      if (results.length > 0) {
        const avgQuality = results.reduce((sum, r) => sum + r.quality_score, 0) / results.length
        const langTotalQuality = langStats.avg_quality * (langStats.count - 1) + avgQuality
        langStats.avg_quality = langTotalQuality / langStats.count
      }
    }
  }

  /** 记录错误 */
  recordError(errorType: string): void {
    const currentCount = this.errorCounts.get(errorType) || 0
    this.errorCounts.set(errorType, currentCount + 1)
    this.metrics.error_stats[errorType] = currentCount + 1
  }

  /** 更新缓存命中率 */
  updateCacheHitRate(hit: boolean): void {
    const totalRequests = this.metrics.total_generations
    if (totalRequests === 0) return
    
    const currentHits = this.metrics.cache_hit_rate * (totalRequests - 1)
    const newHits = hit ? currentHits + 1 : currentHits
    this.metrics.cache_hit_rate = newHits / totalRequests
  }

  /** 更新内存使用情况 */
  updateMemoryUsage(): void {
    const memUsage = process.memoryUsage()
    this.metrics.memory_usage = {
      heap_used: memUsage.heapUsed,
      heap_total: memUsage.heapTotal,
      external: memUsage.external
    }
  }

  /** 获取性能指标 */
  getMetrics(): PerformanceMetrics {
    this.updateMemoryUsage()
    return { ...this.metrics }
  }

  /** 获取性能报告 */
  getPerformanceReport(): PerformanceReport {
    const metrics = this.getMetrics()
    
    return {
      summary: {
        success_rate: metrics.total_generations > 0 ? 
          metrics.successful_generations / metrics.total_generations : 0,
        avg_generation_time: metrics.avg_generation_time,
        cache_efficiency: metrics.cache_hit_rate,
        memory_efficiency: this.calculateMemoryEfficiency(metrics.memory_usage)
      },
      language_performance: metrics.language_stats,
      bottlenecks: this.identifyBottlenecks(),
      recommendations: this.generateRecommendations()
    }
  }

  /** 计算内存效率 */
  private calculateMemoryEfficiency(memUsage: any): number {
    const heapUtilization = memUsage.heap_used / memUsage.heap_total
    return Math.max(0, 1 - heapUtilization) // 使用率越低，效率越高
  }

  /** 识别性能瓶颈 */
  private identifyBottlenecks(): string[] {
    const bottlenecks: string[] = []
    const metrics = this.metrics
    
    // 检查生成时间
    if (metrics.avg_generation_time > 1000) {
      bottlenecks.push('Generation time is too high (>1s)')
    }
    
    // 检查成功率
    const successRate = metrics.total_generations > 0 ? 
      metrics.successful_generations / metrics.total_generations : 0
    if (successRate < 0.9) {
      bottlenecks.push('Generation success rate is low (<90%)')
    }
    
    // 检查缓存命中率
    if (metrics.cache_hit_rate < 0.3) {
      bottlenecks.push('Cache hit rate is low (<30%)')
    }
    
    // 检查内存使用
    const memUsage = metrics.memory_usage
    if (memUsage.heap_used / memUsage.heap_total > 0.8) {
      bottlenecks.push('Memory usage is high (>80%)')
    }
    
    return bottlenecks
  }

  /** 生成优化建议 */
  private generateRecommendations(): string[] {
    const recommendations: string[] = []
    const bottlenecks = this.identifyBottlenecks()
    
    if (bottlenecks.includes('Generation time is too high (>1s)')) {
      recommendations.push('Consider using faster generation strategies')
      recommendations.push('Optimize word selection algorithms')
      recommendations.push('Increase cache size and TTL')
    }
    
    if (bottlenecks.includes('Generation success rate is low (<90%)')) {
      recommendations.push('Review and expand word databases')
      recommendations.push('Adjust generation constraints')
      recommendations.push('Improve fallback mechanisms')
    }
    
    if (bottlenecks.includes('Cache hit rate is low (<30%)')) {
      recommendations.push('Increase cache size')
      recommendations.push('Optimize cache key generation')
      recommendations.push('Implement cache warming strategies')
    }
    
    if (bottlenecks.includes('Memory usage is high (>80%)')) {
      recommendations.push('Implement memory cleanup routines')
      recommendations.push('Reduce cache size')
      recommendations.push('Optimize data structures')
    }
    
    return recommendations
  }
}

// ============ 性能报告接口 ============

export interface PerformanceReport {
  summary: {
    success_rate: number
    avg_generation_time: number
    cache_efficiency: number
    memory_efficiency: number
  }
  language_performance: Record<LanguageCode, {
    count: number
    avg_time: number
    avg_quality: number
  }>
  bottlenecks: string[]
  recommendations: string[]
}

// ============ 自动优化器 ============

export class AutoOptimizer {
  private monitor: PerformanceMonitor
  private optimizationHistory: OptimizationAction[] = []
  private lastOptimization: number = 0
  private readonly optimizationInterval = 5 * 60 * 1000 // 5分钟

  constructor(monitor: PerformanceMonitor) {
    this.monitor = monitor
  }

  /** 执行自动优化 */
  async performAutoOptimization(): Promise<OptimizationResult> {
    const now = Date.now()
    
    // 检查是否需要优化
    if (now - this.lastOptimization < this.optimizationInterval) {
      return { applied: false, reason: 'Too soon since last optimization' }
    }
    
    const report = this.monitor.getPerformanceReport()
    const actions = this.planOptimizationActions(report)
    
    if (actions.length === 0) {
      return { applied: false, reason: 'No optimization needed' }
    }
    
    // 执行优化动作
    const appliedActions: OptimizationAction[] = []
    for (const action of actions) {
      try {
        await this.executeOptimizationAction(action)
        appliedActions.push(action)
      } catch (error) {
        console.error(`Failed to execute optimization action ${action.type}:`, error)
      }
    }
    
    this.lastOptimization = now
    this.optimizationHistory.push(...appliedActions)
    
    return {
      applied: true,
      actions: appliedActions,
      reason: `Applied ${appliedActions.length} optimization actions`
    }
  }

  /** 规划优化动作 */
  private planOptimizationActions(report: PerformanceReport): OptimizationAction[] {
    const actions: OptimizationAction[] = []
    
    // 基于瓶颈生成优化动作
    for (const bottleneck of report.bottlenecks) {
      if (bottleneck.includes('Generation time is too high')) {
        actions.push({
          type: 'increase_cache_size',
          priority: 'high',
          description: 'Increase cache size to improve performance',
          parameters: { new_size: 2000 }
        })
      }
      
      if (bottleneck.includes('Cache hit rate is low')) {
        actions.push({
          type: 'optimize_cache_strategy',
          priority: 'medium',
          description: 'Optimize cache strategy for better hit rate',
          parameters: { strategy: 'lru_with_frequency' }
        })
      }
      
      if (bottleneck.includes('Memory usage is high')) {
        actions.push({
          type: 'cleanup_memory',
          priority: 'high',
          description: 'Perform memory cleanup to reduce usage',
          parameters: {}
        })
      }
    }
    
    // 按优先级排序
    actions.sort((a, b) => {
      const priorityOrder = { 'high': 3, 'medium': 2, 'low': 1 }
      return priorityOrder[b.priority] - priorityOrder[a.priority]
    })
    
    return actions
  }

  /** 执行优化动作 */
  private async executeOptimizationAction(action: OptimizationAction): Promise<void> {
    switch (action.type) {
      case 'increase_cache_size':
        // 实际实现中会调用缓存管理器的方法
        console.log(`Increasing cache size to ${action.parameters.new_size}`)
        break
        
      case 'optimize_cache_strategy':
        console.log(`Optimizing cache strategy to ${action.parameters.strategy}`)
        break
        
      case 'cleanup_memory':
        // 执行垃圾回收
        if (global.gc) {
          global.gc()
        }
        console.log('Performed memory cleanup')
        break
        
      default:
        console.warn(`Unknown optimization action type: ${action.type}`)
    }
  }

  /** 获取优化历史 */
  getOptimizationHistory(): OptimizationAction[] {
    return [...this.optimizationHistory]
  }
}

// ============ 优化相关接口 ============

export interface OptimizationAction {
  type: string
  priority: 'high' | 'medium' | 'low'
  description: string
  parameters: Record<string, any>
}

export interface OptimizationResult {
  applied: boolean
  reason: string
  actions?: OptimizationAction[]
}

// ============ 资源管理器 ============

export class ResourceManager {
  private memoryThreshold: number = 0.8 // 80%内存使用率阈值
  private cpuThreshold: number = 0.9 // 90%CPU使用率阈值
  private activeOperations: Set<string> = new Set()
  private maxConcurrentOperations: number = 10

  /** 检查资源可用性 */
  checkResourceAvailability(): ResourceStatus {
    const memUsage = process.memoryUsage()
    const memUtilization = memUsage.heapUsed / memUsage.heapTotal
    
    return {
      memory_available: memUtilization < this.memoryThreshold,
      cpu_available: true, // 简化实现，实际中需要检查CPU使用率
      concurrent_operations: this.activeOperations.size,
      can_start_operation: this.activeOperations.size < this.maxConcurrentOperations
    }
  }

  /** 请求资源 */
  requestResource(operationId: string): boolean {
    const status = this.checkResourceAvailability()
    
    if (!status.can_start_operation || !status.memory_available) {
      return false
    }
    
    this.activeOperations.add(operationId)
    return true
  }

  /** 释放资源 */
  releaseResource(operationId: string): void {
    this.activeOperations.delete(operationId)
  }

  /** 强制清理资源 */
  forceCleanup(): void {
    this.activeOperations.clear()
    
    // 执行垃圾回收
    if (global.gc) {
      global.gc()
    }
  }
}

export interface ResourceStatus {
  memory_available: boolean
  cpu_available: boolean
  concurrent_operations: number
  can_start_operation: boolean
}

// ============ 性能分析器 ============

export class PerformanceProfiler {
  private profiles: Map<string, ProfileData> = new Map()

  /** 开始性能分析 */
  startProfiling(profileId: string): void {
    this.profiles.set(profileId, {
      start_time: Date.now(),
      operations: [],
      memory_snapshots: []
    })
  }

  /** 记录操作 */
  recordOperation(profileId: string, operation: string, duration: number): void {
    const profile = this.profiles.get(profileId)
    if (!profile) return
    
    profile.operations.push({
      operation,
      duration,
      timestamp: Date.now()
    })
  }

  /** 记录内存快照 */
  recordMemorySnapshot(profileId: string): void {
    const profile = this.profiles.get(profileId)
    if (!profile) return
    
    const memUsage = process.memoryUsage()
    profile.memory_snapshots.push({
      timestamp: Date.now(),
      heap_used: memUsage.heapUsed,
      heap_total: memUsage.heapTotal,
      external: memUsage.external
    })
  }

  /** 结束性能分析 */
  endProfiling(profileId: string): ProfileResult | null {
    const profile = this.profiles.get(profileId)
    if (!profile) return null
    
    const endTime = Date.now()
    const totalDuration = endTime - profile.start_time
    
    // 分析操作性能
    const operationStats = this.analyzeOperations(profile.operations)
    
    // 分析内存使用
    const memoryStats = this.analyzeMemoryUsage(profile.memory_snapshots)
    
    this.profiles.delete(profileId)
    
    return {
      total_duration: totalDuration,
      operation_stats: operationStats,
      memory_stats: memoryStats,
      recommendations: this.generateProfileRecommendations(operationStats, memoryStats)
    }
  }

  /** 分析操作性能 */
  private analyzeOperations(operations: OperationRecord[]): OperationStats {
    const stats: OperationStats = {}
    
    for (const op of operations) {
      if (!stats[op.operation]) {
        stats[op.operation] = {
          count: 0,
          total_duration: 0,
          avg_duration: 0,
          max_duration: 0,
          min_duration: Infinity
        }
      }
      
      const stat = stats[op.operation]
      stat.count++
      stat.total_duration += op.duration
      stat.max_duration = Math.max(stat.max_duration, op.duration)
      stat.min_duration = Math.min(stat.min_duration, op.duration)
      stat.avg_duration = stat.total_duration / stat.count
    }
    
    return stats
  }

  /** 分析内存使用 */
  private analyzeMemoryUsage(snapshots: MemorySnapshot[]): MemoryStats {
    if (snapshots.length === 0) {
      return { peak_usage: 0, avg_usage: 0, growth_rate: 0 }
    }
    
    const peakUsage = Math.max(...snapshots.map(s => s.heap_used))
    const avgUsage = snapshots.reduce((sum, s) => sum + s.heap_used, 0) / snapshots.length
    
    // 计算增长率
    let growthRate = 0
    if (snapshots.length > 1) {
      const first = snapshots[0]
      const last = snapshots[snapshots.length - 1]
      const timeDiff = last.timestamp - first.timestamp
      const memDiff = last.heap_used - first.heap_used
      growthRate = timeDiff > 0 ? memDiff / timeDiff : 0
    }
    
    return {
      peak_usage: peakUsage,
      avg_usage: avgUsage,
      growth_rate: growthRate
    }
  }

  /** 生成性能分析建议 */
  private generateProfileRecommendations(
    operationStats: OperationStats,
    memoryStats: MemoryStats
  ): string[] {
    const recommendations: string[] = []
    
    // 分析慢操作
    for (const [operation, stats] of Object.entries(operationStats)) {
      if (stats.avg_duration > 100) {
        recommendations.push(`Optimize ${operation} operation (avg: ${stats.avg_duration}ms)`)
      }
    }
    
    // 分析内存使用
    if (memoryStats.growth_rate > 1000) { // 每毫秒增长超过1KB
      recommendations.push('Memory usage is growing rapidly, check for memory leaks')
    }
    
    if (memoryStats.peak_usage > 100 * 1024 * 1024) { // 超过100MB
      recommendations.push('Peak memory usage is high, consider optimization')
    }
    
    return recommendations
  }
}

// ============ 性能分析相关接口 ============

interface ProfileData {
  start_time: number
  operations: OperationRecord[]
  memory_snapshots: MemorySnapshot[]
}

interface OperationRecord {
  operation: string
  duration: number
  timestamp: number
}

interface MemorySnapshot {
  timestamp: number
  heap_used: number
  heap_total: number
  external: number
}

interface OperationStats {
  [operation: string]: {
    count: number
    total_duration: number
    avg_duration: number
    max_duration: number
    min_duration: number
  }
}

interface MemoryStats {
  peak_usage: number
  avg_usage: number
  growth_rate: number
}

interface ProfileResult {
  total_duration: number
  operation_stats: OperationStats
  memory_stats: MemoryStats
  recommendations: string[]
}
