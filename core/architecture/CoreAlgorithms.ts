/**
 * 核心算法设计
 * 
 * 高效的用户名生成算法，基于概率模型和启发式搜索
 */

import {
  LexicalEntry,
  GrammarPattern,
  GenerationContext,
  GenerationResult,
  SemanticDomain,
  CulturalStyle,
  PartOfSpeech
} from './DataStructures'

// ============ 算法接口定义 ============

/** 词汇选择算法接口 */
export interface LexicalSelector {
  select(
    candidates: LexicalEntry[],
    context: GenerationContext,
    position: number
  ): LexicalEntry[]
}

/** 模式匹配算法接口 */
export interface PatternMatcher {
  match(
    patterns: GrammarPattern[],
    context: GenerationContext
  ): GrammarPattern[]
}

/** 质量评估算法接口 */
export interface QualityEvaluator {
  evaluate(
    username: string,
    components: LexicalEntry[],
    pattern: GrammarPattern,
    context: GenerationContext
  ): number
}

// ============ 核心算法实现 ============

/**
 * 加权随机选择算法
 * 基于Alias Method实现O(1)时间复杂度的加权随机选择
 */
export class WeightedRandomSelector {
  private alias: number[] = []
  private prob: number[] = []
  private weights: number[] = []
  
  constructor(weights: number[]) {
    this.weights = [...weights]
    this.buildAliasTable()
  }
  
  /** 构建Alias表 */
  private buildAliasTable(): void {
    const n = this.weights.length
    if (n === 0) return
    
    const totalWeight = this.weights.reduce((sum, w) => sum + w, 0)
    if (totalWeight === 0) return
    
    // 归一化权重
    const normalizedWeights = this.weights.map(w => w * n / totalWeight)
    
    this.alias = new Array(n)
    this.prob = new Array(n)
    
    const small: number[] = []
    const large: number[] = []
    
    // 分类权重
    for (let i = 0; i < n; i++) {
      if (normalizedWeights[i] < 1.0) {
        small.push(i)
      } else {
        large.push(i)
      }
    }
    
    // 构建表
    while (small.length > 0 && large.length > 0) {
      const l = small.pop()!
      const g = large.pop()!
      
      this.prob[l] = normalizedWeights[l]
      this.alias[l] = g
      
      normalizedWeights[g] = normalizedWeights[g] + normalizedWeights[l] - 1.0
      
      if (normalizedWeights[g] < 1.0) {
        small.push(g)
      } else {
        large.push(g)
      }
    }
    
    // 处理剩余项
    while (large.length > 0) {
      const g = large.pop()!
      this.prob[g] = 1.0
    }
    
    while (small.length > 0) {
      const l = small.pop()!
      this.prob[l] = 1.0
    }
  }
  
  /** 选择一个索引 */
  select(): number {
    if (this.weights.length === 0) return -1
    
    const n = this.weights.length
    const i = Math.floor(Math.random() * n)
    const r = Math.random()
    
    return r < this.prob[i] ? i : this.alias[i]
  }
  
  /** 选择多个不重复的索引 */
  selectMultiple(count: number): number[] {
    const selected = new Set<number>()
    const result: number[] = []
    
    while (result.length < count && selected.size < this.weights.length) {
      const index = this.select()
      if (index >= 0 && !selected.has(index)) {
        selected.add(index)
        result.push(index)
      }
    }
    
    return result
  }
}

/**
 * 语义相似度计算算法
 * 基于向量空间模型计算词汇间的语义相似度
 */
export class SemanticSimilarityCalculator {
  /** 计算两个词汇的语义相似度 */
  static calculate(word1: LexicalEntry, word2: LexicalEntry): number {
    let similarity = 0
    let factors = 0
    
    // 语义域重叠度
    const domainOverlap = this.calculateDomainOverlap(word1.domains, word2.domains)
    similarity += domainOverlap * 0.4
    factors += 0.4
    
    // 情感色彩相似度
    const sentimentSimilarity = 1 - Math.abs(word1.sentiment - word2.sentiment) / 2
    similarity += sentimentSimilarity * 0.2
    factors += 0.2
    
    // 正式程度相似度
    const formalitySimilarity = 1 - Math.abs(word1.formality - word2.formality)
    similarity += formalitySimilarity * 0.2
    factors += 0.2
    
    // 文化风格相似度
    const culturalSimilarity = this.calculateCulturalSimilarity(
      word1.cultural_scores,
      word2.cultural_scores
    )
    similarity += culturalSimilarity * 0.2
    factors += 0.2
    
    return factors > 0 ? similarity / factors : 0
  }
  
  /** 计算语义域重叠度 */
  private static calculateDomainOverlap(
    domains1: SemanticDomain[],
    domains2: SemanticDomain[]
  ): number {
    const set1 = new Set(domains1)
    const set2 = new Set(domains2)
    const intersection = new Set([...set1].filter(x => set2.has(x)))
    const union = new Set([...set1, ...set2])
    
    return union.size > 0 ? intersection.size / union.size : 0
  }
  
  /** 计算文化风格相似度 */
  private static calculateCulturalSimilarity(
    scores1: Record<CulturalStyle, number>,
    scores2: Record<CulturalStyle, number>
  ): number {
    const styles = Object.values(CulturalStyle)
    let totalDiff = 0
    
    for (const style of styles) {
      const score1 = scores1[style] || 0
      const score2 = scores2[style] || 0
      totalDiff += Math.abs(score1 - score2)
    }
    
    return 1 - (totalDiff / styles.length)
  }
}

/**
 * 音韵和谐度计算算法
 * 基于音韵学理论计算词汇组合的音韵和谐度
 */
export class PhoneticHarmonyCalculator {
  /** 计算音韵和谐度 */
  static calculate(words: LexicalEntry[]): number {
    if (words.length < 2) return 1.0
    
    let harmony = 0
    let factors = 0
    
    // 音节数和谐度
    const syllableHarmony = this.calculateSyllableHarmony(words)
    harmony += syllableHarmony * 0.4
    factors += 0.4
    
    // 长度和谐度
    const lengthHarmony = this.calculateLengthHarmony(words)
    harmony += lengthHarmony * 0.3
    factors += 0.3
    
    // 节奏和谐度
    const rhythmHarmony = this.calculateRhythmHarmony(words)
    harmony += rhythmHarmony * 0.3
    factors += 0.3
    
    return factors > 0 ? harmony / factors : 0
  }
  
  /** 计算音节数和谐度 */
  private static calculateSyllableHarmony(words: LexicalEntry[]): number {
    const syllables = words.map(w => w.syllables)
    const avg = syllables.reduce((sum, s) => sum + s, 0) / syllables.length
    const variance = syllables.reduce((sum, s) => sum + Math.pow(s - avg, 2), 0) / syllables.length
    
    // 方差越小，和谐度越高
    return Math.max(0, 1 - variance / 4)
  }
  
  /** 计算长度和谐度 */
  private static calculateLengthHarmony(words: LexicalEntry[]): number {
    const lengths = words.map(w => w.length)
    const avg = lengths.reduce((sum, l) => sum + l, 0) / lengths.length
    const variance = lengths.reduce((sum, l) => sum + Math.pow(l - avg, 2), 0) / lengths.length
    
    return Math.max(0, 1 - variance / 9)
  }
  
  /** 计算节奏和谐度 */
  private static calculateRhythmHarmony(words: LexicalEntry[]): number {
    // 简化实现：基于音节数的节奏模式
    const pattern = words.map(w => w.syllables % 2) // 奇偶模式
    const alternating = pattern.every((p, i) => i === 0 || p !== pattern[i - 1])
    
    return alternating ? 1.0 : 0.7
  }
}

/**
 * 贪心搜索算法
 * 用于在大量候选词汇中快速找到最优组合
 */
export class GreedySearchAlgorithm {
  /** 贪心搜索最优词汇组合 */
  static search(
    pattern: GrammarPattern,
    candidatesByPosition: LexicalEntry[][],
    context: GenerationContext,
    maxIterations: number = 1000
  ): LexicalEntry[] | null {
    if (candidatesByPosition.length === 0) return null
    
    const result: LexicalEntry[] = []
    const usedWords = new Set<string>()
    
    for (let pos = 0; pos < pattern.structure.length; pos++) {
      const candidates = candidatesByPosition[pos] || []
      if (candidates.length === 0) return null
      
      // 过滤已使用的词汇
      const availableCandidates = candidates.filter(c => !usedWords.has(c.word))
      if (availableCandidates.length === 0) return null
      
      // 计算每个候选词的评分
      const scoredCandidates = availableCandidates.map(candidate => ({
        word: candidate,
        score: this.calculateCandidateScore(candidate, result, context, pos)
      }))
      
      // 选择最高分的候选词
      scoredCandidates.sort((a, b) => b.score - a.score)
      const selected = scoredCandidates[0].word
      
      result.push(selected)
      usedWords.add(selected.word)
    }
    
    return result
  }
  
  /** 计算候选词评分 */
  private static calculateCandidateScore(
    candidate: LexicalEntry,
    currentResult: LexicalEntry[],
    context: GenerationContext,
    position: number
  ): number {
    let score = 0
    
    // 基础适配度评分
    score += this.calculateBasicFitness(candidate, context) * 0.4
    
    // 与已选词汇的协调性
    if (currentResult.length > 0) {
      score += this.calculateCoherence(candidate, currentResult) * 0.3
    }
    
    // 位置适配度
    score += this.calculatePositionalFitness(candidate, position) * 0.2
    
    // 多样性奖励
    score += this.calculateDiversityBonus(candidate, currentResult) * 0.1
    
    return score
  }
  
  /** 计算基础适配度 */
  private static calculateBasicFitness(
    candidate: LexicalEntry,
    context: GenerationContext
  ): number {
    let fitness = 0
    
    // 文化风格匹配
    const culturalScore = candidate.cultural_scores[context.style] || 0
    fitness += culturalScore * 0.3
    
    // 语义域匹配
    const domainMatch = context.preferred_domains.some(d => candidate.domains.includes(d))
    fitness += domainMatch ? 0.3 : 0
    
    // 情感倾向匹配
    const sentimentDiff = Math.abs(candidate.sentiment - context.sentiment_target)
    fitness += (1 - sentimentDiff / 2) * 0.2
    
    // 正式程度匹配
    const formalityDiff = Math.abs(candidate.formality - context.formality_target)
    fitness += (1 - formalityDiff) * 0.2
    
    return fitness
  }
  
  /** 计算协调性 */
  private static calculateCoherence(
    candidate: LexicalEntry,
    existing: LexicalEntry[]
  ): number {
    if (existing.length === 0) return 1.0
    
    let totalSimilarity = 0
    for (const word of existing) {
      totalSimilarity += SemanticSimilarityCalculator.calculate(candidate, word)
    }
    
    return totalSimilarity / existing.length
  }
  
  /** 计算位置适配度 */
  private static calculatePositionalFitness(
    candidate: LexicalEntry,
    position: number
  ): number {
    // 简化实现：不同位置偏好不同特征
    if (position === 0) {
      // 首位偏好较短、较有力的词汇
      return candidate.length <= 2 ? 1.0 : 0.7
    } else {
      // 后续位置偏好较长、较具体的词汇
      return candidate.length >= 2 ? 1.0 : 0.7
    }
  }
  
  /** 计算多样性奖励 */
  private static calculateDiversityBonus(
    candidate: LexicalEntry,
    existing: LexicalEntry[]
  ): number {
    // 奖励与已选词汇不同语义域的候选词
    const existingDomains = new Set(existing.flatMap(w => w.domains))
    const newDomains = candidate.domains.filter(d => !existingDomains.has(d))
    
    return newDomains.length > 0 ? 1.0 : 0.5
  }
}

/**
 * 动态规划算法
 * 用于复杂约束下的最优解搜索
 */
export class DynamicProgrammingAlgorithm {
  /** 使用动态规划寻找最优词汇组合 */
  static findOptimalCombination(
    pattern: GrammarPattern,
    candidatesByPosition: LexicalEntry[][],
    context: GenerationContext
  ): LexicalEntry[] | null {
    const n = pattern.structure.length
    if (n === 0) return []
    
    // dp[i][j] 表示在位置i选择第j个候选词的最优解
    const dp: Map<string, { score: number; prev: string | null; word: LexicalEntry }> = new Map()
    
    // 初始化第一个位置
    const firstCandidates = candidatesByPosition[0] || []
    for (let j = 0; j < firstCandidates.length; j++) {
      const candidate = firstCandidates[j]
      const score = GreedySearchAlgorithm['calculateBasicFitness'](candidate, context)
      const key = `0-${j}`
      dp.set(key, { score, prev: null, word: candidate })
    }
    
    // 动态规划填表
    for (let i = 1; i < n; i++) {
      const candidates = candidatesByPosition[i] || []
      
      for (let j = 0; j < candidates.length; j++) {
        const candidate = candidates[j]
        let bestScore = -Infinity
        let bestPrev: string | null = null
        
        // 寻找最优前驱
        for (let k = 0; k < (candidatesByPosition[i - 1] || []).length; k++) {
          const prevKey = `${i - 1}-${k}`
          const prevState = dp.get(prevKey)
          
          if (prevState) {
            const transitionScore = this.calculateTransitionScore(
              prevState.word,
              candidate,
              context
            )
            const totalScore = prevState.score + transitionScore
            
            if (totalScore > bestScore) {
              bestScore = totalScore
              bestPrev = prevKey
            }
          }
        }
        
        if (bestPrev !== null) {
          const key = `${i}-${j}`
          dp.set(key, { score: bestScore, prev: bestPrev, word: candidate })
        }
      }
    }
    
    // 回溯找到最优解
    let bestFinalScore = -Infinity
    let bestFinalKey: string | null = null
    
    const lastCandidates = candidatesByPosition[n - 1] || []
    for (let j = 0; j < lastCandidates.length; j++) {
      const key = `${n - 1}-${j}`
      const state = dp.get(key)
      
      if (state && state.score > bestFinalScore) {
        bestFinalScore = state.score
        bestFinalKey = key
      }
    }
    
    if (bestFinalKey === null) return null
    
    // 回溯构建结果
    const result: LexicalEntry[] = []
    let currentKey: string | null = bestFinalKey
    
    while (currentKey !== null) {
      const state = dp.get(currentKey)!
      result.unshift(state.word)
      currentKey = state.prev
    }
    
    return result
  }
  
  /** 计算状态转移评分 */
  private static calculateTransitionScore(
    prevWord: LexicalEntry,
    currentWord: LexicalEntry,
    context: GenerationContext
  ): number {
    // 语义相似度
    const semanticSimilarity = SemanticSimilarityCalculator.calculate(prevWord, currentWord)
    
    // 音韵和谐度
    const phoneticHarmony = PhoneticHarmonyCalculator.calculate([prevWord, currentWord])
    
    // 基础适配度
    const basicFitness = GreedySearchAlgorithm['calculateBasicFitness'](currentWord, context)
    
    return semanticSimilarity * 0.3 + phoneticHarmony * 0.3 + basicFitness * 0.4
  }
}

// ============ 算法工厂 ============

/** 算法工厂类 */
export class AlgorithmFactory {
  /** 创建词汇选择器 */
  static createLexicalSelector(type: 'weighted' | 'greedy' | 'dp'): LexicalSelector {
    switch (type) {
      case 'weighted':
        return new WeightedLexicalSelector()
      case 'greedy':
        return new GreedyLexicalSelector()
      case 'dp':
        return new DPLexicalSelector()
      default:
        return new WeightedLexicalSelector()
    }
  }
  
  /** 创建质量评估器 */
  static createQualityEvaluator(): QualityEvaluator {
    return new ComprehensiveQualityEvaluator()
  }
}

// ============ 具体实现类 ============

class WeightedLexicalSelector implements LexicalSelector {
  select(candidates: LexicalEntry[], context: GenerationContext, position: number): LexicalEntry[] {
    const weights = candidates.map(c => c.weight * c.cultural_scores[context.style])
    const selector = new WeightedRandomSelector(weights)
    const indices = selector.selectMultiple(Math.min(5, candidates.length))
    return indices.map(i => candidates[i])
  }
}

class GreedyLexicalSelector implements LexicalSelector {
  select(candidates: LexicalEntry[], context: GenerationContext, position: number): LexicalEntry[] {
    return candidates
      .map(c => ({
        word: c,
        score: GreedySearchAlgorithm['calculateBasicFitness'](c, context)
      }))
      .sort((a, b) => b.score - a.score)
      .slice(0, 5)
      .map(item => item.word)
  }
}

class DPLexicalSelector implements LexicalSelector {
  select(candidates: LexicalEntry[], context: GenerationContext, position: number): LexicalEntry[] {
    // DP选择器的简化实现
    return new GreedyLexicalSelector().select(candidates, context, position)
  }
}

class ComprehensiveQualityEvaluator implements QualityEvaluator {
  evaluate(
    username: string,
    components: LexicalEntry[],
    pattern: GrammarPattern,
    context: GenerationContext
  ): number {
    let score = 0
    
    // 语义连贯性 (30%)
    const semanticCoherence = this.calculateSemanticCoherence(components)
    score += semanticCoherence * 0.3
    
    // 文化适配度 (25%)
    const culturalFitness = this.calculateCulturalFitness(components, context)
    score += culturalFitness * 0.25
    
    // 音韵和谐度 (20%)
    const phoneticHarmony = PhoneticHarmonyCalculator.calculate(components)
    score += phoneticHarmony * 0.2
    
    // 记忆性 (15%)
    const memorability = this.calculateMemorability(username, components)
    score += memorability * 0.15
    
    // 独特性 (10%)
    const uniqueness = this.calculateUniqueness(username)
    score += uniqueness * 0.1
    
    return Math.max(0, Math.min(1, score))
  }
  
  private calculateSemanticCoherence(components: LexicalEntry[]): number {
    if (components.length < 2) return 1.0
    
    let totalSimilarity = 0
    let pairs = 0
    
    for (let i = 0; i < components.length; i++) {
      for (let j = i + 1; j < components.length; j++) {
        totalSimilarity += SemanticSimilarityCalculator.calculate(components[i], components[j])
        pairs++
      }
    }
    
    return pairs > 0 ? totalSimilarity / pairs : 0
  }
  
  private calculateCulturalFitness(components: LexicalEntry[], context: GenerationContext): number {
    return components.reduce((sum, c) => sum + (c.cultural_scores[context.style] || 0), 0) / components.length
  }
  
  private calculateMemorability(username: string, components: LexicalEntry[]): number {
    // 基于长度、音节数和熟悉度的记忆性评估
    const length = username.length
    const syllables = components.reduce((sum, c) => sum + c.syllables, 0)
    const familiarity = components.reduce((sum, c) => sum + c.frequency, 0) / components.length
    
    const lengthScore = length >= 4 && length <= 8 ? 1.0 : 0.7
    const syllableScore = syllables >= 2 && syllables <= 4 ? 1.0 : 0.8
    const familiarityScore = familiarity
    
    return (lengthScore + syllableScore + familiarityScore) / 3
  }
  
  private calculateUniqueness(username: string): number {
    // 基于字符多样性的独特性评估
    const uniqueChars = new Set(username).size
    const totalChars = username.length
    return totalChars > 0 ? uniqueChars / totalChars : 0
  }
}
