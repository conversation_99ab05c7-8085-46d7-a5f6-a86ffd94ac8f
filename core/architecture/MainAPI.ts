/**
 * 主要API接口
 * 
 * 提供统一的、易于使用的API接口，封装所有底层复杂性
 */

import {
  GenerationContext,
  GenerationResult,
  SystemConfig,
  LanguageCode,
  CulturalStyle,
  SemanticDomain,
  PartOfSpeech
} from './DataStructures'

import { DataManager } from './DataManager'
import { UsernameGenerationEngine } from './GenerationEngine'
import { PerformanceMonitor, AutoOptimizer, ResourceManager } from './PerformanceOptimizer'

// ============ API请求和响应接口 ============

export interface GenerateUsernameRequest {
  // 基础参数
  language?: LanguageCode
  count?: number
  
  // 风格和偏好
  style?: CulturalStyle | string
  themes?: (SemanticDomain | string)[]
  avoid_themes?: (SemanticDomain | string)[]
  
  // 情感和语调
  sentiment?: 'positive' | 'neutral' | 'negative' | number // -1到1
  formality?: 'formal' | 'informal' | 'mixed' | number // 0到1
  creativity?: 'low' | 'medium' | 'high' | number // 0到1
  
  // 目标用户
  target_age?: 'child' | 'teen' | 'adult' | 'senior'
  
  // 约束条件
  min_length?: number
  max_length?: number
  avoid_words?: string[]
  require_unique?: boolean
  
  // 高级选项
  generation_strategy?: 'balanced' | 'creative' | 'traditional' | 'fast'
  quality_threshold?: number // 0到1
  enable_cache?: boolean
  
  // 调试和分析
  debug?: boolean
  profile?: boolean
}

export interface GenerateUsernameResponse {
  success: boolean
  data?: {
    usernames: UsernameResult[]
    generation_info: GenerationInfo
    debug_info?: DebugInfo
    profile_info?: ProfileInfo
  }
  error?: {
    code: string
    message: string
    details?: any
  }
}

export interface UsernameResult {
  username: string
  quality_score: number
  components: ComponentInfo[]
  pattern_used: string
  cultural_fitness: number
  explanation?: string
}

export interface ComponentInfo {
  word: string
  pos: string
  meaning?: string
  domains: string[]
  cultural_score: number
}

export interface GenerationInfo {
  total_time_ms: number
  cache_hit: boolean
  strategy_used: string
  attempts_made: number
  language_loaded: boolean
}

export interface DebugInfo {
  context: GenerationContext
  patterns_considered: number
  words_searched: number
  quality_breakdown: Record<string, number>
}

export interface ProfileInfo {
  operation_times: Record<string, number>
  memory_usage: any
  bottlenecks: string[]
}

// ============ 主API类 ============

export class UsernameGeneratorAPI {
  private dataManager: DataManager
  private generationEngine: UsernameGenerationEngine
  private performanceMonitor: PerformanceMonitor
  private autoOptimizer: AutoOptimizer
  private resourceManager: ResourceManager
  private config: SystemConfig
  private initialized: boolean = false

  constructor(config: Partial<SystemConfig> = {}) {
    // 设置默认配置
    this.config = {
      data_directory: './data',
      cache: {
        max_size: 1000,
        default_ttl: 10 * 60 * 1000,
        cleanup_interval: 5 * 60 * 1000,
        enable_stats: true
      },
      performance: {
        max_generation_time: 5000,
        max_concurrent_generations: 10,
        enable_profiling: false
      },
      logging: {
        level: 'info',
        enable_file_logging: false,
        log_directory: './logs'
      },
      supported_languages: ['zh', 'en', 'ja'],
      defaults: {
        language: 'zh',
        style: CulturalStyle.MODERN,
        count: 5,
        creativity: 0.5
      },
      ...config
    }

    // 初始化组件
    this.dataManager = new DataManager(this.config)
    this.generationEngine = new UsernameGenerationEngine(this.dataManager)
    this.performanceMonitor = new PerformanceMonitor()
    this.autoOptimizer = new AutoOptimizer(this.performanceMonitor)
    this.resourceManager = new ResourceManager()
  }

  /** 初始化API */
  async initialize(): Promise<void> {
    if (this.initialized) return

    try {
      // 初始化数据管理器
      await this.dataManager.initialize()

      // 预加载默认语言数据
      for (const language of this.config.supported_languages) {
        try {
          await this.dataManager.loadLanguageData(language)
        } catch (error) {
          console.warn(`Failed to preload language ${language}:`, error)
        }
      }

      this.initialized = true
      console.log('UsernameGeneratorAPI initialized successfully')
    } catch (error) {
      throw new Error(`Failed to initialize API: ${error}`)
    }
  }

  /** 生成用户名 */
  async generateUsernames(request: GenerateUsernameRequest): Promise<GenerateUsernameResponse> {
    const operationId = `gen_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    
    try {
      // 检查初始化状态
      if (!this.initialized) {
        await this.initialize()
      }

      // 检查资源可用性
      if (!this.resourceManager.requestResource(operationId)) {
        return {
          success: false,
          error: {
            code: 'RESOURCE_UNAVAILABLE',
            message: 'System is currently overloaded, please try again later'
          }
        }
      }

      // 验证请求
      const validationError = this.validateRequest(request)
      if (validationError) {
        return {
          success: false,
          error: {
            code: 'INVALID_REQUEST',
            message: validationError
          }
        }
      }

      // 开始性能监控
      this.performanceMonitor.startTimer(operationId)
      
      // 构建生成上下文
      const context = await this.buildGenerationContext(request)
      
      // 执行生成
      const startTime = Date.now()
      const results = await this.generationEngine.generateUsernames(context)
      const endTime = Date.now()
      
      // 结束性能监控
      const duration = this.performanceMonitor.endTimer(operationId)
      this.performanceMonitor.recordGeneration(context, results, duration, true)
      
      // 构建响应
      const response = this.buildSuccessResponse(request, results, {
        total_time_ms: endTime - startTime,
        cache_hit: false, // 实际实现中需要从生成引擎获取
        strategy_used: this.selectStrategy(request),
        attempts_made: 1, // 实际实现中需要从生成引擎获取
        language_loaded: true
      })

      // 执行自动优化
      if (this.config.performance.enable_profiling) {
        await this.autoOptimizer.performAutoOptimization()
      }

      return response

    } catch (error) {
      // 记录错误
      this.performanceMonitor.recordError(error instanceof Error ? error.message : 'unknown_error')
      this.performanceMonitor.endTimer(operationId)
      
      return {
        success: false,
        error: {
          code: 'GENERATION_FAILED',
          message: error instanceof Error ? error.message : 'Unknown error occurred',
          details: this.config.logging.level === 'debug' ? error : undefined
        }
      }
    } finally {
      // 释放资源
      this.resourceManager.releaseResource(operationId)
    }
  }

  /** 获取系统状态 */
  async getSystemStatus(): Promise<SystemStatusResponse> {
    const metrics = this.performanceMonitor.getMetrics()
    const report = this.performanceMonitor.getPerformanceReport()
    const resourceStatus = this.resourceManager.checkResourceAvailability()

    return {
      status: 'healthy', // 实际实现中需要基于指标判断
      uptime: process.uptime() * 1000,
      performance: {
        total_generations: metrics.total_generations,
        success_rate: metrics.total_generations > 0 ? 
          metrics.successful_generations / metrics.total_generations : 0,
        avg_response_time: metrics.avg_generation_time,
        cache_hit_rate: metrics.cache_hit_rate
      },
      resources: {
        memory_usage: metrics.memory_usage,
        active_operations: resourceStatus.concurrent_operations,
        can_accept_requests: resourceStatus.can_start_operation
      },
      languages: {
        supported: this.config.supported_languages,
        loaded: this.dataManager.getLoadedLanguages()
      },
      recommendations: report.recommendations
    }
  }

  /** 获取性能报告 */
  async getPerformanceReport(): Promise<any> {
    return this.performanceMonitor.getPerformanceReport()
  }

  /** 清除缓存 */
  async clearCache(): Promise<void> {
    // 实际实现中需要调用各个组件的缓存清理方法
    console.log('Cache cleared')
  }

  /** 重新加载语言数据 */
  async reloadLanguageData(language: LanguageCode): Promise<void> {
    await this.dataManager.loadLanguageData(language)
  }

  // ============ 私有辅助方法 ============

  /** 验证请求 */
  private validateRequest(request: GenerateUsernameRequest): string | null {
    // 基础验证
    if (request.count && (request.count < 1 || request.count > 50)) {
      return 'Count must be between 1 and 50'
    }

    if (request.min_length && request.max_length && request.min_length > request.max_length) {
      return 'min_length cannot be greater than max_length'
    }

    if (request.quality_threshold && (request.quality_threshold < 0 || request.quality_threshold > 1)) {
      return 'quality_threshold must be between 0 and 1'
    }

    // 语言支持验证
    if (request.language && !this.config.supported_languages.includes(request.language)) {
      return `Unsupported language: ${request.language}`
    }

    return null
  }

  /** 构建生成上下文 */
  private async buildGenerationContext(request: GenerateUsernameRequest): Promise<GenerationContext> {
    const language = request.language || this.config.defaults.language
    const style = this.parseStyle(request.style) || this.config.defaults.style
    
    return {
      language,
      style,
      preferred_domains: this.parseThemes(request.themes),
      length_range: [
        request.min_length || 2,
        request.max_length || 12
      ],
      sentiment_target: this.parseSentiment(request.sentiment),
      formality_target: this.parseFormality(request.formality),
      target_age: request.target_age || 'adult',
      avoid_words: new Set(request.avoid_words || []),
      creativity: this.parseCreativity(request.creativity),
      count: request.count || this.config.defaults.count,
      unique: request.require_unique !== false
    }
  }

  /** 解析风格参数 */
  private parseStyle(style?: CulturalStyle | string): CulturalStyle {
    if (!style) return CulturalStyle.MODERN
    
    if (Object.values(CulturalStyle).includes(style as CulturalStyle)) {
      return style as CulturalStyle
    }
    
    // 字符串映射
    const styleMap: Record<string, CulturalStyle> = {
      'traditional': CulturalStyle.TRADITIONAL,
      'modern': CulturalStyle.MODERN,
      'cute': CulturalStyle.CUTE,
      'cool': CulturalStyle.COOL,
      'elegant': CulturalStyle.ELEGANT,
      'playful': CulturalStyle.PLAYFUL,
      'mysterious': CulturalStyle.MYSTERIOUS,
      'powerful': CulturalStyle.POWERFUL
    }
    
    return styleMap[style] || CulturalStyle.MODERN
  }

  /** 解析主题参数 */
  private parseThemes(themes?: (SemanticDomain | string)[]): SemanticDomain[] {
    if (!themes) return []
    
    return themes.map(theme => {
      if (Object.values(SemanticDomain).includes(theme as SemanticDomain)) {
        return theme as SemanticDomain
      }
      
      // 字符串映射
      const themeMap: Record<string, SemanticDomain> = {
        'nature': SemanticDomain.NATURE,
        'emotion': SemanticDomain.EMOTION,
        'action': SemanticDomain.ACTION,
        'quality': SemanticDomain.QUALITY,
        'time': SemanticDomain.TIME,
        'space': SemanticDomain.SPACE,
        'technology': SemanticDomain.TECHNOLOGY,
        'tech': SemanticDomain.TECHNOLOGY,
        'culture': SemanticDomain.CULTURE,
        'fantasy': SemanticDomain.FANTASY,
        'social': SemanticDomain.SOCIAL
      }
      
      return themeMap[theme] || SemanticDomain.NATURE
    }).filter(Boolean)
  }

  /** 解析情感参数 */
  private parseSentiment(sentiment?: 'positive' | 'neutral' | 'negative' | number): number {
    if (typeof sentiment === 'number') {
      return Math.max(-1, Math.min(1, sentiment))
    }
    
    const sentimentMap: Record<string, number> = {
      'positive': 0.7,
      'neutral': 0.0,
      'negative': -0.3
    }
    
    return sentimentMap[sentiment || 'neutral'] || 0.0
  }

  /** 解析正式程度参数 */
  private parseFormality(formality?: 'formal' | 'informal' | 'mixed' | number): number {
    if (typeof formality === 'number') {
      return Math.max(0, Math.min(1, formality))
    }
    
    const formalityMap: Record<string, number> = {
      'formal': 0.8,
      'informal': 0.2,
      'mixed': 0.5
    }
    
    return formalityMap[formality || 'mixed'] || 0.5
  }

  /** 解析创意程度参数 */
  private parseCreativity(creativity?: 'low' | 'medium' | 'high' | number): number {
    if (typeof creativity === 'number') {
      return Math.max(0, Math.min(1, creativity))
    }
    
    const creativityMap: Record<string, number> = {
      'low': 0.2,
      'medium': 0.5,
      'high': 0.8
    }
    
    return creativityMap[creativity || 'medium'] || 0.5
  }

  /** 选择生成策略 */
  private selectStrategy(request: GenerateUsernameRequest): string {
    if (request.generation_strategy) {
      return request.generation_strategy
    }
    
    // 基于请求参数自动选择策略
    const creativity = this.parseCreativity(request.creativity)
    if (creativity > 0.7) return 'creative'
    if (request.count && request.count > 10) return 'fast'
    if (request.style === 'traditional') return 'traditional'
    
    return 'balanced'
  }

  /** 构建成功响应 */
  private buildSuccessResponse(
    request: GenerateUsernameRequest,
    results: GenerationResult[],
    generationInfo: GenerationInfo
  ): GenerateUsernameResponse {
    return {
      success: true,
      data: {
        usernames: results.map(result => ({
          username: result.username,
          quality_score: Math.round(result.quality_score * 100) / 100,
          components: result.components.map(comp => ({
            word: comp.word,
            pos: comp.pos,
            meaning: this.getWordMeaning(comp.word),
            domains: comp.domains,
            cultural_score: comp.cultural_scores[this.parseStyle(request.style)] || 0
          })),
          pattern_used: result.pattern.name,
          cultural_fitness: result.scores.cultural_fitness,
          explanation: request.debug ? this.generateExplanation(result) : undefined
        })),
        generation_info: generationInfo,
        debug_info: request.debug ? this.buildDebugInfo(request, results) : undefined
      }
    }
  }

  /** 获取词汇含义 */
  private getWordMeaning(word: string): string {
    // 简化实现，实际中应该从词典获取
    return `Meaning of ${word}`
  }

  /** 生成解释 */
  private generateExplanation(result: GenerationResult): string {
    return `Generated using ${result.pattern.name} pattern with ${result.components.length} components`
  }

  /** 构建调试信息 */
  private buildDebugInfo(request: GenerateUsernameRequest, results: GenerationResult[]): DebugInfo {
    return {
      context: {} as GenerationContext, // 实际实现中需要传入真实的上下文
      patterns_considered: 10, // 示例数据
      words_searched: 100,
      quality_breakdown: {
        semantic_coherence: 0.8,
        cultural_fitness: 0.7,
        phonetic_harmony: 0.6,
        memorability: 0.9,
        uniqueness: 0.5
      }
    }
  }
}

// ============ 系统状态接口 ============

export interface SystemStatusResponse {
  status: 'healthy' | 'degraded' | 'unhealthy'
  uptime: number
  performance: {
    total_generations: number
    success_rate: number
    avg_response_time: number
    cache_hit_rate: number
  }
  resources: {
    memory_usage: any
    active_operations: number
    can_accept_requests: boolean
  }
  languages: {
    supported: LanguageCode[]
    loaded: LanguageCode[]
  }
  recommendations: string[]
}

// ============ 便捷函数 ============

/** 创建API实例 */
export async function createUsernameAPI(config?: Partial<SystemConfig>): Promise<UsernameGeneratorAPI> {
  const api = new UsernameGeneratorAPI(config)
  await api.initialize()
  return api
}

/** 快速生成用户名 */
export async function quickGenerate(
  language: LanguageCode = 'zh',
  count: number = 5,
  style: CulturalStyle = CulturalStyle.MODERN
): Promise<string[]> {
  const api = await createUsernameAPI()
  const response = await api.generateUsernames({
    language,
    count,
    style
  })
  
  if (response.success && response.data) {
    return response.data.usernames.map(u => u.username)
  } else {
    throw new Error(response.error?.message || 'Generation failed')
  }
}
