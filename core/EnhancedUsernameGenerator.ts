import type { 
  EnhancedGenerateOptions, 
  EnhancedGenerationResult, 
  EnhancedCulturalData,
  EnhancedWordItem,
  EnhancedGrammarTemplate,
  SemanticTag,
  CulturalTag,
  POSTag,
  Tone,
  GenerationMetadata,
  SemanticConstraints
} from '../types/generator'

/**
 * 增强的用户名生成器
 * 基于语义标签、文化感知和槽位控制的新一代生成系统
 */
export class EnhancedUsernameGenerator {
  private culturalData: Map<string, EnhancedCulturalData> = new Map()
  private semanticAssociations: Map<string, string[]> = new Map()
  private weightLearningData: Map<string, any> = new Map()

  /**
   * 主要生成方法
   */
  async generateUsername(options: EnhancedGenerateOptions = {}): Promise<EnhancedGenerationResult> {
    const startTime = Date.now()
    const {
      language = 'zh',
      category = 'internet',
      slot_count = 3,
      cultural_preference = [],
      semantic_preference = [],
      tone_preference = ['正面', '搞怪'],
      rarity_preference = 'mixed',
      maxRetry = 3,
      enable_semantic_chain = false
    } = options

    // 加载文化数据
    await this.loadCulturalData(language, category)
    const culturalData = this.culturalData.get(`${language}-${category}`)
    if (!culturalData) {
      throw new Error(`Cultural data not found for ${language}-${category}`)
    }

    let bestResult: EnhancedGenerationResult | null = null
    let retryCount = 0

    for (let attempt = 0; attempt < maxRetry; attempt++) {
      try {
        // 1. 选择合适的模式和模板
        const { pattern, template } = this.selectTemplate(
          culturalData, 
          slot_count, 
          cultural_preference, 
          semantic_preference
        )

        // 2. 填充槽位
        const components = this.fillSlots(
          template,
          pattern.components,
          cultural_preference,
          semantic_preference,
          tone_preference,
          rarity_preference,
          enable_semantic_chain
        )

        // 3. 生成用户名
        const username = this.assembleUsername(template.pattern, components)

        // 4. 后处理和验证
        const processedUsername = this.postProcess(username, slot_count)
        
        if (this.validateUsername(processedUsername, language)) {
          const metadata: GenerationMetadata = {
            template_used: template.name,
            components_selected: components,
            cultural_tags: this.extractCulturalTags(components, pattern.components),
            semantic_tags: this.extractSemanticTags(components, pattern.components),
            generation_time_ms: Date.now() - startTime,
            retry_count: attempt,
            slot_count: template.pattern.length,
            quality_score: this.calculateQualityScore(processedUsername, components, pattern.components)
          }

          const result: EnhancedGenerationResult = {
            username: processedUsername,
            metadata,
            explanation: this.generateExplanation(template, components)
          }

          // 如果是第一次成功或质量更高，更新最佳结果
          if (!bestResult || (metadata.quality_score || 0) > (bestResult.metadata.quality_score || 0)) {
            bestResult = result
          }

          // 如果质量足够高，直接返回
          if ((metadata.quality_score || 0) > 0.8) {
            return result
          }
        }
        
        retryCount++
      } catch (error) {
        console.warn(`Generation attempt ${attempt + 1} failed:`, error)
        retryCount++
      }
    }

    // 返回最佳结果或fallback
    return bestResult || this.generateFallback(language, slot_count)
  }

  /**
   * 选择合适的模板
   */
  private selectTemplate(
    culturalData: EnhancedCulturalData,
    targetSlots: number,
    culturalPreference: CulturalTag[],
    semanticPreference: SemanticTag[]
  ) {
    // 过滤符合槽位数要求的模板
    const candidatePatterns = culturalData.patterns.filter(pattern => {
      return pattern.templates.some(template => 
        template.min_slots <= targetSlots && template.max_slots >= targetSlots
      )
    })

    if (candidatePatterns.length === 0) {
      throw new Error(`No templates found for slot count: ${targetSlots}`)
    }

    // 根据文化和语义偏好计算权重
    const weightedPatterns = candidatePatterns.map(pattern => {
      let weight = pattern.weight

      // 文化偏好加权
      if (culturalPreference.length > 0) {
        const culturalMatch = pattern.cultural_context.filter(tag => 
          culturalPreference.includes(tag)
        ).length
        weight *= (1 + culturalMatch * 0.3)
      }

      // 语义偏好加权
      if (semanticPreference.length > 0) {
        const semanticMatch = pattern.semantic_theme.filter(tag => 
          semanticPreference.includes(tag)
        ).length
        weight *= (1 + semanticMatch * 0.2)
      }

      return { pattern, weight }
    })

    // 权重采样选择模式
    const selectedPattern = this.weightedSample(weightedPatterns)

    // 从选中模式中选择合适的模板
    const candidateTemplates = selectedPattern.templates.filter(template =>
      template.min_slots <= targetSlots && template.max_slots >= targetSlots
    )

    const selectedTemplate = this.weightedSample(
      candidateTemplates.map(template => ({ pattern: template, weight: template.weight }))
    )

    return {
      pattern: selectedPattern,
      template: selectedTemplate
    }
  }

  /**
   * 填充槽位
   */
  private fillSlots(
    template: EnhancedGrammarTemplate,
    components: Record<string, EnhancedWordItem[]>,
    culturalPreference: CulturalTag[],
    semanticPreference: SemanticTag[],
    tonePreference: Tone[],
    rarityPreference: string,
    enableSemanticChain: boolean
  ): Record<string, string> {
    const result: Record<string, string> = {}
    const usedWords = new Set<string>()
    let lastSelectedWord = ''

    for (let i = 0; i < template.pattern.length; i++) {
      const slotPattern = template.pattern[i]
      const [slotName, posTag] = slotPattern.split('#')
      
      // 获取槽位约束
      const constraints = template.semantic_constraints[slotName] || {}
      
      // 过滤候选词汇
      let candidates = this.filterCandidates(
        components[slotName] || [],
        constraints,
        culturalPreference,
        semanticPreference,
        tonePreference,
        rarityPreference,
        usedWords
      )

      // 语义链增强
      if (enableSemanticChain && lastSelectedWord && candidates.length > 1) {
        candidates = this.applySemanticChain(candidates, lastSelectedWord)
      }

      if (candidates.length === 0) {
        throw new Error(`No candidates found for slot: ${slotName}`)
      }

      // 权重采样选择词汇
      const selected = this.weightedSample(
        candidates.map(item => ({ pattern: item, weight: item.weight }))
      )

      result[slotName] = selected.word
      usedWords.add(selected.word)
      lastSelectedWord = selected.word
    }

    return result
  }

  /**
   * 过滤候选词汇
   */
  private filterCandidates(
    items: EnhancedWordItem[],
    constraints: any,
    culturalPreference: CulturalTag[],
    semanticPreference: SemanticTag[],
    tonePreference: Tone[],
    rarityPreference: string,
    usedWords: Set<string>
  ): EnhancedWordItem[] {
    return items.filter(item => {
      // 避免重复
      if (usedWords.has(item.word)) return false

      // 语义标签约束
      if (constraints.semantic_tags && 
          !item.semantic_tags.some(tag => constraints.semantic_tags.includes(tag))) {
        return false
      }

      // 文化标签约束
      if (constraints.cultural_tags && 
          !item.cultural_tags.some(tag => constraints.cultural_tags.includes(tag))) {
        return false
      }

      // 词性标签约束
      if (constraints.pos_tags && 
          !item.pos_tags.some(tag => constraints.pos_tags.includes(tag))) {
        return false
      }

      // 语调约束
      if (constraints.tone && !constraints.tone.includes(item.tone)) {
        return false
      }

      // 稀有度约束
      if (constraints.min_rarity) {
        const rarityOrder = ['common', 'uncommon', 'rare', 'trending']
        const minIndex = rarityOrder.indexOf(constraints.min_rarity)
        const itemIndex = rarityOrder.indexOf(item.rarity || 'common')
        if (itemIndex < minIndex) return false
      }

      // 排除词汇
      if (constraints.exclude_words && constraints.exclude_words.includes(item.word)) {
        return false
      }

      return true
    })
  }

  /**
   * 权重采样
   */
  private weightedSample<T>(items: Array<{ pattern: T; weight: number }>): T {
    const totalWeight = items.reduce((sum, item) => sum + item.weight, 0)
    let random = Math.random() * totalWeight

    for (const item of items) {
      random -= item.weight
      if (random <= 0) {
        return item.pattern
      }
    }

    return items[0].pattern
  }

  /**
   * 组装用户名
   */
  private assembleUsername(pattern: string[], components: Record<string, string>): string {
    return pattern.map(slotPattern => {
      const [slotName] = slotPattern.split('#')
      return components[slotName] || ''
    }).join('')
  }

  /**
   * 后处理
   */
  private postProcess(username: string, targetSlots: number): string {
    // 基本清理
    let processed = username
      .replace(/([\u4e00-\u9fa5]{2,4})\1+/gu, '$1')  // 去重复词块
      .replace(/([\u4e00-\u9fa5])\1{1,}/gu, '$1')    // 去重复单字
      .replace(/[0-9]+/g, '')                         // 移除数字串
      .replace(/[\p{Emoji_Presentation}\uFE0F]/gu, '') // 移除emoji

    return processed
  }

  /**
   * 验证用户名
   */
  private validateUsername(username: string, language: string): boolean {
    // 基本验证
    if (username.length < 2) return false
    if ((username.match(/[\u4e00-\u9fa5]/g) || []).length < 2) return false
    if (/\d/.test(username)) return false
    
    // TODO: 敏感词检测
    
    return true
  }

  /**
   * 计算质量分数
   */
  private calculateQualityScore(
    username: string, 
    components: Record<string, string>,
    componentData: Record<string, EnhancedWordItem[]>
  ): number {
    let score = 0.5 // 基础分

    // 长度适中加分
    if (username.length >= 4 && username.length <= 8) {
      score += 0.2
    }

    // 语义丰富度加分
    const uniqueSemanticTags = new Set<string>()
    Object.entries(components).forEach(([slotName, word]) => {
      const items = componentData[slotName] || []
      const item = items.find(i => i.word === word)
      if (item) {
        item.semantic_tags.forEach(tag => uniqueSemanticTags.add(tag))
      }
    })
    score += Math.min(uniqueSemanticTags.size * 0.1, 0.3)

    return Math.min(score, 1.0)
  }

  /**
   * 生成说明
   */
  private generateExplanation(template: EnhancedGrammarTemplate, components: Record<string, string>): string {
    const componentList = Object.entries(components)
      .map(([slot, word]) => `${slot}: ${word}`)
      .join(', ')
    
    return `使用模板"${template.name}"生成，组件: ${componentList}`
  }

  /**
   * 应用语义链
   */
  private applySemanticChain(candidates: EnhancedWordItem[], lastWord: string): EnhancedWordItem[] {
    const associations = this.semanticAssociations.get(lastWord) || []
    if (associations.length === 0) return candidates

    // 提升语义相关词汇的权重
    return candidates.map(item => {
      if (associations.includes(item.word)) {
        return { ...item, weight: item.weight * 1.5 }
      }
      return item
    })
  }

  /**
   * 提取文化标签
   */
  private extractCulturalTags(components: Record<string, string>, componentData: Record<string, EnhancedWordItem[]>): CulturalTag[] {
    const tags = new Set<CulturalTag>()
    Object.entries(components).forEach(([slotName, word]) => {
      const items = componentData[slotName] || []
      const item = items.find(i => i.word === word)
      if (item) {
        item.cultural_tags.forEach(tag => tags.add(tag))
      }
    })
    return Array.from(tags)
  }

  /**
   * 提取语义标签
   */
  private extractSemanticTags(components: Record<string, string>, componentData: Record<string, EnhancedWordItem[]>): SemanticTag[] {
    const tags = new Set<SemanticTag>()
    Object.entries(components).forEach(([slotName, word]) => {
      const items = componentData[slotName] || []
      const item = items.find(i => i.word === word)
      if (item) {
        item.semantic_tags.forEach(tag => tags.add(tag))
      }
    })
    return Array.from(tags)
  }

  /**
   * 生成fallback结果
   */
  private generateFallback(language: string, slotCount: number): EnhancedGenerationResult {
    const fallbackWords = ['星', '云', '风', '雨', '梦', '影', '夜', '龙', '虎', '猫']
    const username = '用户' + fallbackWords[Math.floor(Math.random() * fallbackWords.length)] + 
                    fallbackWords[Math.floor(Math.random() * fallbackWords.length)]

    return {
      username,
      metadata: {
        template_used: 'fallback',
        components_selected: {},
        cultural_tags: [],
        semantic_tags: [],
        generation_time_ms: 0,
        retry_count: 3,
        slot_count: 2,
        quality_score: 0.3
      },
      explanation: 'Fallback generation due to repeated failures'
    }
  }

  /**
   * 加载文化数据
   */
  private async loadCulturalData(language: string, category: string): Promise<void> {
    const key = `${language}-${category}`
    if (this.culturalData.has(key)) return

    try {
      // 这里应该从实际的数据源加载
      // 暂时使用示例数据
      const data = await import(`../data/cultural/${language}/enhanced_${category}.json`)
      this.culturalData.set(key, data.default || data)
      
      // 加载语义联想数据
      if (data.semantic_associations) {
        Object.entries(data.semantic_associations).forEach(([word, associations]) => {
          this.semanticAssociations.set(word, associations as string[])
        })
      }
    } catch (error) {
      console.error(`Failed to load cultural data for ${key}:`, error)
      throw error
    }
  }
}
