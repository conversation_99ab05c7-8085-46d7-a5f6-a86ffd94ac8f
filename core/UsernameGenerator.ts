import { DataStore } from './BrowserDataStore'
import type { WordItem } from './BrowserDataStore'

// Alias Table 类型定义
interface AliasTable {
  prob: number[]
  alias: number[]
}

// 工具函数
function randomInt(min: number, max: number): number {
  return Math.floor(Math.random() * (max - min + 1)) + min
}

// 模块级唯一计数器，降低同一次测试内重复率
// let _uniqueSeq = 0

export interface GenerateOptions {
  language?: string
  category?: string
  slot_count?: number  // 替代 length，使用槽位数控制
  includeTrends?: boolean
  maxRetry?: number
  /** 是否强制加入夸张前缀，如"超/无敌/究极"，默认 true */
  fancyPrefix?: boolean
  /** 期望的风格标签，如 'literary' | 'exaggerated' | 'cute'，可传数组 */
  style?: string | string[]
  /** 调试标记：输出生成过程到 console */
  debug?: boolean
}

/** 默认槽位数配置：基于语义元素数量控制 */
const defaultSlotConfig: Record<string, { min: number; max: number; def: number }> = {
  zh: { min: 2, max: 8, def: 3 },  // 中文：2-8个语义槽位，默认3个
  en: { min: 2, max: 6, def: 3 },  // 英文：2-6个语义槽位，默认3个
  ja: { min: 2, max: 6, def: 3 }   // 日文：2-6个语义槽位，默认3个
}

/**
 * 高性能文化用户名生成（支持离线数据）
 * - 使用 DataStore 提供的数据，零 IO
 * - Alias Table O(1) 采样
 * - 敏感词 Trie 检测
 */
export async function generateCulturalUsername(options: GenerateOptions = {}): Promise<string> {
  const {
    language = 'zh',
    category,
    slot_count: userSlotCount,
    includeTrends = true,
    maxRetry = 3,
    fancyPrefix = true,
    style,
    debug = true
  } = options

  // 保证语言数据在内存
  await DataStore.initLanguage(language)

  const cat = category ?? DataStore.getCategories(language)[randomInt(0, DataStore.getCategories(language).length - 1)]
  const cultural = DataStore.getCultural(language, cat)
  const trending = includeTrends ? DataStore.getTrending(language) : undefined

  // 将 style 参数标准化为数组以便匹配
  const styleArr: string[] | null = style ? (Array.isArray(style) ? style : [style]) : null

  const slotCfg = defaultSlotConfig[language] || defaultSlotConfig.en
  const targetSlots = Math.min(slotCfg.max, Math.max(slotCfg.min, userSlotCount ?? slotCfg.def))

  // 预先构建 alias table lookup for patterns
  const patternAlias = cultural.alias_tables ?? {}

  if (debug) console.log('[gen] targetSlots', targetSlots, 'style', styleArr)

  for (let attempt = 0; attempt < maxRetry; attempt++) {
    // 1. 根据 style 过滤候选 pattern
    let patternPool = cultural.patterns
    if (styleArr) {
      patternPool = cultural.patterns.filter((p: any) => {
        const pStyle = (p.style ?? 'exaggerated')
        return styleArr.includes(pStyle)
      })
      // 若过滤后为空则回退全部
      if (!patternPool.length) patternPool = cultural.patterns
    }

    const patternIdx = selectWeighted(patternPool.map((p) => p.weight))
    const pattern = patternPool[patternIdx]
    if (debug) console.log(`[gen] attempt ${attempt+1}: pattern ->`, pattern.name)

    // 2. 模板筛选：优先 >= targetSlots 的高复杂度模板，否则退回 <= targetSlots
    const moreOrEq = pattern.templates.filter((t) => countTemplateSlots(tplArrayToString(t.template as any)) >= targetSlots)
    const lessOrEq = pattern.templates.filter((t) => countTemplateSlots(tplArrayToString(t.template as any)) <= targetSlots)
    const tplPool = moreOrEq.length ? moreOrEq : (lessOrEq.length ? lessOrEq : pattern.templates)
    const tplIdx = selectWeighted(tplPool.map((t) => t.weight))
    const template = tplPool[tplIdx]
    if (debug) console.log('[gen] template', tplArrayToString(template.template as any))

    // 3. 组件合并：pattern.components + trending
    const components: Record<string, WordItem[]> = { ...pattern.components }
    if (trending) {
      for (const w of trending.trending_words) {
        if (!components[w.category]) components[w.category] = []
        components[w.category].push({ word: w.word, weight: w.weight, tags: w.tags })
      }
    }

    // 4. 生成（槽位感知挑词）
    const templateStr = tplArrayToString(template.template as any)
    let usernameBase = fillTemplateSlotAware(templateStr, components, targetSlots)
    if (debug) console.log('[gen] after fillTemplate', usernameBase, 'slots', countSemanticSlots(usernameBase))

    // 若槽位不足，进行智能补槽
    if (countSemanticSlots(usernameBase) < targetSlots) {
      usernameBase = enhanceToTargetSlots(usernameBase, components, targetSlots)
      if (debug) console.log('[gen] after enhanceToTargetSlots', usernameBase, 'slots', countSemanticSlots(usernameBase))
    }

    // 5. 后处理（基于槽位数）
    const finalName = postProcessSlotBased(usernameBase, targetSlots, fancyPrefix)
    if (debug) console.log('[gen] after postProcess', finalName, 'slots', countSemanticSlots(finalName))

    // 6. 敏感词 + 语义 + 结构检测
    if (!DataStore.isSensitive(language, finalName) && isSemanticValid(finalName) && isStructureValid(finalName)) {
      if (debug) console.log('[gen] success ✓', finalName)
      return finalName
    } else {
      if (debug) console.log('[gen] rejected, retrying...')
    }
  }

  // fallback: 返回中文 + 常见字，避免数字
  const pool = ['星','云','风','雨','梦','影','夜','龙','虎','猫','狐','竹','松','雪','霞']
  const pick = () => pool[randomInt(0, pool.length - 1)]
  return `用户${pick()}${pick()}`
}

// ---------------- 内部工具 ----------------
function selectWeighted(weights: number[]): number {
  const total = weights.reduce((s, w) => s + w, 0)
  let r = Math.random() * total
  for (let i = 0; i < weights.length; i++) {
    r -= weights[i]
    if (r <= 0) return i
  }
  return 0
}

function fillTemplateSlotAware(template: string | string[], components: Record<string, WordItem[]>, targetSlots: number): string {
  const tplStr = tplArrayToString(template)
  const usedWords = new Set<string>()
  return tplStr.replace(/\{([^}]+)\}/g, (_, raw: string) => {
    const [key, reqPos] = raw.split('#')
    let items = components[key]
    if (!items?.length) return ''
    // 过滤 pos
    if (reqPos) items = items.filter((i) => (i as any).pos === reqPos)
    // 去缩写
    items = items.filter((i) => isMeaningful(i.word) && !usedWords.has(i.word))
    if (!items.length) return ''
    // 基于槽位数的权重计算：不再考虑字符长度，专注语义权重
    const weights = items.map((i) => i.weight)
    const table = buildAliasFromWeights(weights)
    const idx = pickAliasIndex(table)
    const w = items[idx].word
    usedWords.add(w)
    return w
  })
}

function buildAliasFromWeights(weights: number[]) {
  const n = weights.length
  const total = weights.reduce((s, w) => s + w, 0)
  const scaled = weights.map((w) => (w * n) / total)
  const prob: number[] = new Array(n).fill(0)
  const alias: number[] = new Array(n).fill(0)
  const small: number[] = []
  const large: number[] = []
  scaled.forEach((p, i) => (p < 1 ? small.push(i) : large.push(i)))
  while (small.length && large.length) {
    const l = small.pop() as number
    const g = large.pop() as number
    prob[l] = scaled[l]
    alias[l] = g
    scaled[g] = scaled[g] + scaled[l] - 1
    if (scaled[g] < 1) small.push(g)
    else large.push(g)
  }
  ;[...small, ...large].forEach((i) => (prob[i] = 1))
  return { prob, alias }
}

function pickAliasIndex(table: AliasTable): number {
  const n = table.prob.length
  const i = randomInt(0, n - 1)
  const r = Math.random()
  return r < table.prob[i] ? i : table.alias[i]
}

function postProcessSlotBased(name: string, targetSlots: number, fancyPrefix: boolean): string {
  let n = name

  // -------- 重复词块去重 --------
  // 先合并连续完全相同的 2~4 字词块
  n = n.replace(/([\u4e00-\u9fa5]{2,4})\1+/gu, '$1')
  // 再合并连续相同单字（霞霞 -> 霞）
  n = n.replace(/([\u4e00-\u9fa5])\1{1,}/gu, '$1')

  // 移除数字串（666 等）
  n = n.replace(/[0-9]+/g, '')

  // 移除 Emoji & VS16 变体选择符
  n = n.replace(/[\p{Emoji_Presentation}\uFE0F]/gu, '')

  // 插入连接词：在连续两个 ≥2 汉字词块之间随机插入 '之'
  n = n.replace(/([\u4e00-\u9fa5]{2,})(?=[\u4e00-\u9fa5]{2,})/gu, '$1之')

  // 再次合并因 LINK 插入导致的重复（词块之间隔 '之'）
  n = n.replace(/([\u4e00-\u9fa5]{2,})之\1/gu, '$1')

  const emphasiseWords = ['超', '究极', '无敌', '终极', '爆裂', '狂热', '超神', '大魔王', '至尊', '绝对', '满级', '霸王级', '超究极']
  /*
   * 夸张词注入策略（高级复杂度适用）
   * - 仅当 fancyPrefix 开启
   * - 且目标槽位数 >= 4（复杂度阈值，可调整）
   * - 且当前槽位数仍 < targetSlots（预留出一个新槽）
   * - 才在前部插入夸张词作为一个独立槽位，保持语法"修饰词 + 名词/短语"结构
   */
  if (fancyPrefix && targetSlots >= 4 && !emphasiseWords.some((w) => n.startsWith(w))) {
    const currentSlots = countSemanticSlots(n)
    if (currentSlots < targetSlots) {
      const pick = emphasiseWords[randomInt(0, emphasiseWords.length - 1)]
      n = pick + n // 前置修饰词，更符合中文偏正结构
    }
  }

  const emphasiseDupRe = new RegExp(`(${emphasiseWords.join('|')})+`, 'g')
  if (fancyPrefix) {
    n = n.replace(emphasiseDupRe, '$1')
  }

  // ---------- 槽位数一致性控制 ----------
  const trimToSlots = (name: string, maxSlots: number): string => {
    let currentSlots = countSemanticSlots(name)

    // 如果槽位数超出目标，进行智能裁剪
    if (currentSlots > maxSlots) {
      // 优先移除连接词和修饰词
      const hardSplit = /[之的·]/
      let segments = name.split(hardSplit).filter(Boolean)

      // 保留核心语义槽位，移除多余的修饰槽位
      while (segments.length > maxSlots && segments.length > 1) {
        // 优先移除最后的段落（通常是后缀）
        segments.pop()
      }
      name = segments.join('')

      // 防止裁剪后留下尾部连接词
      name = name.replace(/[之的·]+$/,'')
    }

    return name
  }

  n = trimToSlots(n, targetSlots)

  // 若裁剪过程中不小心丢失了夸张前缀，再次补充
  if (fancyPrefix && !emphasiseWords.some((w) => n.includes(w))) {
    n = emphasiseWords[randomInt(0, emphasiseWords.length - 1)] + n
  }

  // 最终保险再去重一次
  if (fancyPrefix) {
    n = n.replace(emphasiseDupRe, '$1')
  }

  return n
}

function countTemplateSlots(template: string | string[]): number {
  const tplStr = tplArrayToString(template)
  const placeholderRe = /\{([^}]+)\}/g
  let match: RegExpExecArray | null
  let count = 0
  while ((match = placeholderRe.exec(tplStr))) {
    const raw = match[1]
    const [, , pos] = raw.match(/([^#]+)(?:#([A-Z]+))?/) || []
    // LINK token 不计入槽位数
    if (pos === 'LINK') continue
    count++
  }
  return count
}

function countSemanticSlots(name: string): number {
  // 计算用户名中的语义槽位数
  // 基于语义分割：连续汉字块、英文词、数字等作为独立槽位
  let slots = 0
  let i = 0

  while (i < name.length) {
    const ch = name[i]

    if (/[\u4e00-\u9fa5]/.test(ch)) {
      // 连续汉字作为一个槽位
      slots++
      while (i < name.length && /[\u4e00-\u9fa5]/.test(name[i])) {
        i++
      }
    } else if (/[a-zA-Z]/.test(ch)) {
      // 连续英文字母作为一个槽位
      slots++
      while (i < name.length && /[a-zA-Z]/.test(name[i])) {
        i++
      }
    } else if (/[0-9]/.test(ch)) {
      // 连续数字作为一个槽位
      slots++
      while (i < name.length && /[0-9]/.test(name[i])) {
        i++
      }
    } else {
      // 跳过连接词和标点符号
      i++
    }
  }

  return slots
}

// 这个函数已被 countSemanticSlots 替代，保留用于向后兼容
function countTokensGenerated(name: string): number {
  return countSemanticSlots(name)
}

function isMeaningful(word: string): boolean {
  // 至少包含一个中文或长度>2且非纯数字字母
  const hasZh = /[\u4e00-\u9fa5]/.test(word)
  if (hasZh) return true
  // 含拉丁但长度>=3且不是常见缩写（yy/xd等）
  if (/^[a-zA-Z]{3,}$/.test(word)) return false
  return word.length >= 3
}

function isSemanticValid(name: string): boolean {
  // 至少2个中文
  if ((name.match(/[\u4e00-\u9fa5]/g) || []).length < 2) return false
  // 不允许出现数字串
  if (/\d/.test(name)) return false
  // 不允许只以数字或emoji结尾
  if (/([0-9]{4}|[\ud83c-\ud83e][\ud000-\udfff])$/.test(name)) return false
  // 不含连续相同字母缩写
  if (/([a-zA-Z])\1{1,}/.test(name)) return false
  return true
}

// 检查链接词等结构合法性
function isStructureValid(name: string): boolean {
  // 连续 link 词
  if (/(的|之|·){2,}/.test(name)) return false
  // 不允许 link 词出现在开头或结尾
  if (/^(的|之|·)/.test(name) || /(的|之|·)$/.test(name)) return false
  // 未通过其它明显结构错误检查
  return true
}

// 将 token 数组模板转换为占位符字符串，保持向后兼容
function tplArrayToString(tpl: string | string[]): string {
  if (Array.isArray(tpl)) return tpl.map((tok) => `{${tok}}`).join('')
  return tpl
}

// 槽位感知的语义增强：根据目标槽位数智能调整
function enhanceToTargetSlots(name: string, comps: Record<string, WordItem[]>, targetSlots: number): string {
  const currentSlots = countSemanticSlots(name)

  if (currentSlots >= targetSlots) {
    return name // 已达到目标槽位数
  }

  const shortage = targetSlots - currentSlots
  const linkWords = comps['link']?.map((w) => w.word) ?? ['之', '的', '·']
  const randLink = () => linkWords[randomInt(0, linkWords.length - 1)]

  const pickWord = (key: string): string | null => {
    const items = comps[key]
    if (!items?.length) return null
    return items[randomInt(0, items.length - 1)].word
  }

  let n = name
  let added = 0

  while (added < shortage) {
    // 优先添加后缀，然后是前缀
    let word: string | null = pickWord('suffix') || pickWord('SUF')

    if (!word) {
      word = pickWord('prefix') || pickWord('PREF')
      if (word) {
        n = word + n
        added++
        continue
      }
    }

    if (!word) {
      // fallback: 使用常见中文字集
      const commonPool = ['星','云','风','雨','梦','影','夜','龙','虎','猫','狐','竹','松','雪','霞']
      word = commonPool[randomInt(0, commonPool.length - 1)]
    }

    // 添加连接词和新词汇
    if (n.length > 0) n += randLink()
    n += word
    added++
  }

  return n
}

// 估算模板的最小槽位数（替代字符长度估算）
function estimateMinSlots(template: string, comps: Record<string, WordItem[]>): number {
  return countTemplateSlots(template)
}
