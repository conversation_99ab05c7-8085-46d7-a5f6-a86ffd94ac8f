/**
 * 测试专用的用户名生成器
 * 使用 MockDataStore 避免文件系统依赖
 */

import { MockDataStoreInstance as DataStore } from './MockDataStore'
import type { WordItem } from './BrowserDataStore'

// Alias Table 类型定义
interface AliasTable {
  prob: number[]
  alias: number[]
}

// 工具函数
function randomInt(min: number, max: number): number {
  return Math.floor(Math.random() * (max - min + 1)) + min
}

export interface GenerateOptions {
  language?: string
  category?: string
  slot_count?: number
  includeTrends?: boolean
  maxRetry?: number
  style?: string
}

/** 默认槽位数配置：基于语义元素数量控制 */
const defaultSlotConfig: Record<string, { min: number; max: number; def: number }> = {
  zh: { min: 2, max: 8, def: 3 },  // 中文：2-8个语义槽位，默认3个
  en: { min: 2, max: 6, def: 3 },  // 英文：2-6个语义槽位，默认3个  
  ja: { min: 2, max: 6, def: 3 }   // 日文：2-6个语义槽位，默认3个
}

export async function generateCulturalUsername(options: GenerateOptions = {}): Promise<string> {
  const {
    language = 'zh',
    category,
    slot_count: userSlotCount,
    includeTrends = true,
    maxRetry = 3,
    style
  } = options

  // 保证语言数据在内存
  await DataStore.initLanguage(language)

  const cat = category ?? DataStore.getCategories(language)[randomInt(0, DataStore.getCategories(language).length - 1)]
  const cultural = DataStore.getCultural(language, cat)
  const trending = includeTrends ? DataStore.getTrending(language) : undefined

  const slotCfg = defaultSlotConfig[language] || defaultSlotConfig.en
  const targetSlots = Math.min(slotCfg.max, Math.max(slotCfg.min, userSlotCount ?? slotCfg.def))

  for (let attempt = 0; attempt < maxRetry; attempt++) {
    // 1. 选择模式
    const patternIdx = selectWeighted(cultural.patterns.map((p) => p.weight))
    const pattern = cultural.patterns[patternIdx]

    // 2. 根据目标槽位数过滤模板（slot-aware）
    const candidateTpls = pattern.templates.filter((t) => {
      const tplStr = tplArrayToString(t.template as any)
      const templateSlots = countTemplateSlots(tplStr)
      return templateSlots <= targetSlots
    })
    const tplPool = candidateTpls.length ? candidateTpls : pattern.templates
    const tplIdx = selectWeighted(tplPool.map((t) => t.weight))
    const template = tplPool[tplIdx]

    // 3. 组件合并：pattern.components + trending
    const components: Record<string, WordItem[]> = { ...pattern.components }
    if (trending) {
      for (const w of trending.trending_words) {
        if (!components[w.category]) components[w.category] = []
        components[w.category].push({ word: w.word, weight: w.weight, tags: w.tags })
      }
    }

    // 4. 生成（槽位感知挑词）
    const templateStr = tplArrayToString(template.template as any)
    const usernameBase = fillTemplateSlotAware(templateStr, components, targetSlots)
    
    // 5. 后处理（基于槽位数）
    const finalName = postProcessSlotBased(usernameBase, targetSlots)

    // 6. 验证
    if (isSemanticValid(finalName) && isStructureValid(finalName)) {
      return finalName
    }
  }

  // fallback: 返回中文 + 常见字，避免数字
  const pool = ['星','云','风','雨','梦','影','夜','龙','虎','猫','狐','竹','松','雪','霞']
  const pick = () => pool[randomInt(0, pool.length - 1)]
  return `用户${pick()}${pick()}`
}

function fillTemplateSlotAware(template: string | string[], components: Record<string, WordItem[]>, targetSlots: number): string {
  const tplStr = tplArrayToString(template)
  const usedWords = new Set<string>()
  return tplStr.replace(/\{([^}]+)\}/g, (_, raw: string) => {
    const [key, reqPos] = raw.split('#')
    let items = components[key]
    if (!items?.length) return ''
    // 过滤 pos
    if (reqPos) items = items.filter((i) => (i as any).pos === reqPos)
    // 去缩写
    items = items.filter((i) => isMeaningful(i.word) && !usedWords.has(i.word))
    if (!items.length) return ''
    // 基于槽位数的权重计算：不再考虑字符长度，专注语义权重
    const weights = items.map((i) => i.weight)
    const table = buildAliasFromWeights(weights)
    const idx = pickAliasIndex(table)
    const w = items[idx].word
    usedWords.add(w)
    return w
  })
}

function postProcessSlotBased(name: string, targetSlots: number): string {
  let n = name

  // -------- 重复词块去重 --------
  // 先合并连续完全相同的 2~4 字词块
  n = n.replace(/([\u4e00-\u9fa5]{2,4})\1+/gu, '$1')
  // 再合并连续相同单字（霞霞 -> 霞）
  n = n.replace(/([\u4e00-\u9fa5])\1{1,}/gu, '$1')

  // 移除数字串（666 等）
  n = n.replace(/[0-9]+/g, '')

  // 移除 Emoji & VS16 变体选择符
  n = n.replace(/[\p{Emoji_Presentation}\uFE0F]/gu, '')

  return n
}

function countTemplateSlots(template: string | string[]): number {
  const tplStr = tplArrayToString(template)
  const placeholderRe = /\{([^}]+)\}/g
  let match: RegExpExecArray | null
  let count = 0
  while ((match = placeholderRe.exec(tplStr))) {
    const raw = match[1]
    const [, , pos] = raw.match(/([^#]+)(?:#([A-Z]+))?/) || []
    // LINK token 不计入槽位数
    if (pos === 'LINK') continue
    count++
  }
  return count
}

function countSemanticSlots(name: string): number {
  // 计算用户名中的语义槽位数
  // 基于语义分割：连续汉字块、英文词、数字等作为独立槽位
  let slots = 0
  let i = 0
  
  while (i < name.length) {
    const ch = name[i]
    
    if (/[\u4e00-\u9fa5]/.test(ch)) {
      // 连续汉字作为一个槽位
      slots++
      while (i < name.length && /[\u4e00-\u9fa5]/.test(name[i])) {
        i++
      }
    } else if (/[a-zA-Z]/.test(ch)) {
      // 连续英文字母作为一个槽位
      slots++
      while (i < name.length && /[a-zA-Z]/.test(name[i])) {
        i++
      }
    } else if (/[0-9]/.test(ch)) {
      // 连续数字作为一个槽位
      slots++
      while (i < name.length && /[0-9]/.test(name[i])) {
        i++
      }
    } else {
      // 跳过连接词和标点符号
      i++
    }
  }
  
  return slots
}

// 辅助函数
function tplArrayToString(template: string | string[]): string {
  return Array.isArray(template) ? template.join('') : template
}

function selectWeighted(weights: number[]): number {
  const total = weights.reduce((s, w) => s + w, 0)
  let r = Math.random() * total
  for (let i = 0; i < weights.length; i++) {
    r -= weights[i]
    if (r <= 0) return i
  }
  return 0
}

function isMeaningful(word: string): boolean {
  return word.length >= 1 && !/^\d+$/.test(word)
}

function isSemanticValid(name: string): boolean {
  if (name.length < 2) return false
  return true
}

function isStructureValid(name: string): boolean {
  return true
}

function buildAliasFromWeights(weights: number[]): AliasTable {
  const n = weights.length
  const prob = new Array(n)
  const alias = new Array(n)
  
  const sum = weights.reduce((a, b) => a + b, 0)
  for (let i = 0; i < n; i++) {
    prob[i] = weights[i] * n / sum
  }
  
  const small: number[] = []
  const large: number[] = []
  
  for (let i = 0; i < n; i++) {
    if (prob[i] < 1.0) {
      small.push(i)
    } else {
      large.push(i)
    }
  }
  
  while (small.length > 0 && large.length > 0) {
    const l = small.pop()!
    const g = large.pop()!
    
    alias[l] = g
    prob[g] = prob[g] + prob[l] - 1.0
    
    if (prob[g] < 1.0) {
      small.push(g)
    } else {
      large.push(g)
    }
  }
  
  while (large.length > 0) {
    prob[large.pop()!] = 1.0
  }
  
  while (small.length > 0) {
    prob[small.pop()!] = 1.0
  }
  
  return { prob, alias }
}

function pickAliasIndex(table: AliasTable): number {
  const n = table.prob.length
  const i = Math.floor(Math.random() * n)
  return Math.random() < table.prob[i] ? i : table.alias[i]
}

// 导出计数函数供测试使用
export { countSemanticSlots }
