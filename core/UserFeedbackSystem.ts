import type { 
  UserFeedback, 
  WeightLearningData,
  CulturalTag,
  SemanticTag,
  EnhancedWordItem
} from '../types/generator'

/**
 * 用户反馈系统
 * 收集用户行为数据并动态调整生成权重
 */
export class UserFeedbackSystem {
  private feedbackStorage: Map<string, UserFeedback[]> = new Map()
  private weightLearningData: Map<string, WeightLearningData> = new Map()
  private sessionData: Map<string, any> = new Map()

  constructor() {
    this.initializeStorage()
  }

  /**
   * 初始化存储
   */
  private initializeStorage(): void {
    // 在实际应用中，这里应该从持久化存储加载数据
    // 例如：localStorage、IndexedDB、或服务器数据库
  }

  /**
   * 记录用户反馈
   */
  recordFeedback(feedback: UserFeedback): void {
    const { username, action, session_id } = feedback

    // 存储反馈记录
    const existingFeedback = this.feedbackStorage.get(username) || []
    existingFeedback.push(feedback)
    this.feedbackStorage.set(username, existingFeedback)

    // 更新权重学习数据
    this.updateWeightLearning(feedback)

    // 更新会话数据
    this.updateSessionData(session_id, feedback)

    // 持久化存储
    this.persistFeedback(feedback)
  }

  /**
   * 更新权重学习数据
   */
  private updateWeightLearning(feedback: UserFeedback): void {
    const { username, action, cultural_context, semantic_context } = feedback

    // 解析用户名中的词汇（这里简化处理）
    const words = this.extractWordsFromUsername(username)

    words.forEach(word => {
      let learningData = this.weightLearningData.get(word)
      
      if (!learningData) {
        learningData = {
          word,
          positive_feedback: 0,
          negative_feedback: 0,
          total_exposures: 0,
          last_updated: Date.now(),
          cultural_context: {},
          semantic_context: {}
        }
      }

      // 更新反馈计数
      if (this.isPositiveFeedback(action)) {
        learningData.positive_feedback++
      } else if (this.isNegativeFeedback(action)) {
        learningData.negative_feedback++
      }

      learningData.total_exposures++
      learningData.last_updated = Date.now()

      // 更新文化背景数据
      if (cultural_context) {
        cultural_context.forEach(tag => {
          const current = learningData!.cultural_context[tag] || 0
          learningData!.cultural_context[tag] = current + (this.isPositiveFeedback(action) ? 1 : -0.5)
        })
      }

      // 更新语义背景数据
      if (semantic_context) {
        semantic_context.forEach(tag => {
          const current = learningData!.semantic_context[tag] || 0
          learningData!.semantic_context[tag] = current + (this.isPositiveFeedback(action) ? 1 : -0.5)
        })
      }

      this.weightLearningData.set(word, learningData)
    })
  }

  /**
   * 判断是否为正面反馈
   */
  private isPositiveFeedback(action: string): boolean {
    return ['copy', 'like', 'share'].includes(action)
  }

  /**
   * 判断是否为负面反馈
   */
  private isNegativeFeedback(action: string): boolean {
    return ['regenerate', 'dislike'].includes(action)
  }

  /**
   * 从用户名中提取词汇（简化实现）
   */
  private extractWordsFromUsername(username: string): string[] {
    // 这里应该使用更复杂的分词算法
    // 暂时使用简单的中文分词
    const words: string[] = []
    
    // 匹配2-4个连续汉字的词汇
    const matches = username.match(/[\u4e00-\u9fa5]{2,4}/g)
    if (matches) {
      words.push(...matches)
    }

    // 匹配英文词汇
    const englishMatches = username.match(/[a-zA-Z]{2,}/g)
    if (englishMatches) {
      words.push(...englishMatches)
    }

    return words
  }

  /**
   * 更新会话数据
   */
  private updateSessionData(sessionId: string, feedback: UserFeedback): void {
    let sessionData = this.sessionData.get(sessionId)
    
    if (!sessionData) {
      sessionData = {
        session_id: sessionId,
        start_time: Date.now(),
        feedback_count: 0,
        positive_count: 0,
        negative_count: 0,
        generated_usernames: [],
        cultural_preferences: new Map<string, number>(),
        semantic_preferences: new Map<string, number>()
      }
    }

    sessionData.feedback_count++
    sessionData.generated_usernames.push(feedback.username)

    if (this.isPositiveFeedback(feedback.action)) {
      sessionData.positive_count++
    } else if (this.isNegativeFeedback(feedback.action)) {
      sessionData.negative_count++
    }

    // 更新偏好数据
    if (feedback.cultural_context) {
      feedback.cultural_context.forEach(tag => {
        const current = sessionData.cultural_preferences.get(tag) || 0
        sessionData.cultural_preferences.set(tag, current + 1)
      })
    }

    if (feedback.semantic_context) {
      feedback.semantic_context.forEach(tag => {
        const current = sessionData.semantic_preferences.get(tag) || 0
        sessionData.semantic_preferences.set(tag, current + 1)
      })
    }

    this.sessionData.set(sessionId, sessionData)
  }

  /**
   * 获取词汇的动态权重
   */
  getDynamicWeight(word: string, baseWeight: number = 1.0): number {
    const learningData = this.weightLearningData.get(word)

    if (!learningData || learningData.total_exposures < 3) {
      return baseWeight // 数据不足时使用基础权重
    }

    const { positive_feedback, negative_feedback, total_exposures } = learningData

    // 计算正面反馈率
    const positiveRate = positive_feedback / total_exposures

    // 计算权重调整因子 (0.5 - 2.0)
    const adjustmentFactor = 0.5 + (positiveRate * 1.5)

    return baseWeight * adjustmentFactor
  }

  /**
   * 获取文化背景下的词汇权重
   */
  getCulturalWeight(
    word: string, 
    culturalContext: CulturalTag[], 
    baseWeight: number = 1.0
  ): number {
    const learningData = this.weightLearningData.get(word)
    
    if (!learningData) {
      return baseWeight
    }

    let culturalBoost = 1.0
    
    culturalContext.forEach(tag => {
      const contextScore = learningData.cultural_context[tag] || 0
      if (contextScore > 0) {
        culturalBoost *= (1 + contextScore * 0.1)
      } else if (contextScore < 0) {
        culturalBoost *= (1 + contextScore * 0.05)
      }
    })

    return baseWeight * culturalBoost
  }

  /**
   * 获取语义背景下的词汇权重
   */
  getSemanticWeight(
    word: string, 
    semanticContext: SemanticTag[], 
    baseWeight: number = 1.0
  ): number {
    const learningData = this.weightLearningData.get(word)
    
    if (!learningData) {
      return baseWeight
    }

    let semanticBoost = 1.0
    
    semanticContext.forEach(tag => {
      const contextScore = learningData.semantic_context[tag] || 0
      if (contextScore > 0) {
        semanticBoost *= (1 + contextScore * 0.1)
      } else if (contextScore < 0) {
        semanticBoost *= (1 + contextScore * 0.05)
      }
    })

    return baseWeight * semanticBoost
  }

  /**
   * 获取用户会话偏好
   */
  getSessionPreferences(sessionId: string): {
    cultural_preferences: CulturalTag[]
    semantic_preferences: SemanticTag[]
    positive_rate: number
  } {
    const sessionData = this.sessionData.get(sessionId)
    
    if (!sessionData) {
      return {
        cultural_preferences: [],
        semantic_preferences: [],
        positive_rate: 0.5
      }
    }

    // 提取偏好最高的标签
    type EntryType = [string, number];
    const culturalEntries = Array.from(sessionData.cultural_preferences.entries()) as EntryType[];
    culturalEntries.sort((a, b) => b[1] - a[1]);
    const culturalPreferences = culturalEntries
      .slice(0, 3)
      .map(entry => entry[0] as CulturalTag);

    const semanticEntries = Array.from(sessionData.semantic_preferences.entries()) as EntryType[];
    semanticEntries.sort((a, b) => b[1] - a[1]);
    const semanticPreferences = semanticEntries
      .slice(0, 3)
      .map(entry => entry[0] as SemanticTag);

    const positiveRate = sessionData.feedback_count > 0 
      ? sessionData.positive_count / sessionData.feedback_count 
      : 0.5

    return {
      cultural_preferences: culturalPreferences,
      semantic_preferences: semanticPreferences,
      positive_rate: positiveRate
    }
  }

  /**
   * 获取全局统计数据
   */
  getGlobalStats(): {
    total_feedback: number
    total_words_learned: number
    top_positive_words: Array<{ word: string; score: number }>
    top_negative_words: Array<{ word: string; score: number }>
    cultural_tag_performance: Record<string, number>
    semantic_tag_performance: Record<string, number>
  } {
    const totalFeedback = Array.from(this.feedbackStorage.values())
      .reduce((sum, feedbacks) => sum + feedbacks.length, 0)

    const wordsWithScores = Array.from(this.weightLearningData.values())
      .filter(data => data.total_exposures >= 5)
      .map(data => ({
        word: data.word,
        score: data.positive_feedback / data.total_exposures
      }))

    const topPositive = wordsWithScores
      .sort((a, b) => b.score - a.score)
      .slice(0, 10)

    const topNegative = wordsWithScores
      .sort((a, b) => a.score - b.score)
      .slice(0, 10)

    // 计算文化标签表现
    const culturalPerformance: Record<string, number> = {}
    const semanticPerformance: Record<string, number> = {}

    this.weightLearningData.forEach(data => {
      Object.entries(data.cultural_context).forEach(([tag, score]) => {
        culturalPerformance[tag] = (culturalPerformance[tag] || 0) + score
      })

      Object.entries(data.semantic_context).forEach(([tag, score]) => {
        semanticPerformance[tag] = (semanticPerformance[tag] || 0) + score
      })
    })

    return {
      total_feedback: totalFeedback,
      total_words_learned: this.weightLearningData.size,
      top_positive_words: topPositive,
      top_negative_words: topNegative,
      cultural_tag_performance: culturalPerformance,
      semantic_tag_performance: semanticPerformance
    }
  }

  /**
   * 导出学习数据（用于备份和分析）
   */
  exportLearningData(): {
    feedback_data: Record<string, UserFeedback[]>
    weight_learning_data: Record<string, WeightLearningData>
    export_timestamp: number
  } {
    const feedbackData: Record<string, UserFeedback[]> = {}
    this.feedbackStorage.forEach((feedbacks, username) => {
      feedbackData[username] = feedbacks
    })

    const weightData: Record<string, WeightLearningData> = {}
    this.weightLearningData.forEach((data, word) => {
      weightData[word] = data
    })

    return {
      feedback_data: feedbackData,
      weight_learning_data: weightData,
      export_timestamp: Date.now()
    }
  }

  /**
   * 导入学习数据
   */
  importLearningData(data: {
    feedback_data: Record<string, UserFeedback[]>
    weight_learning_data: Record<string, WeightLearningData>
  }): void {
    // 导入反馈数据
    Object.entries(data.feedback_data).forEach(([username, feedbacks]) => {
      this.feedbackStorage.set(username, feedbacks)
    })

    // 导入权重学习数据
    Object.entries(data.weight_learning_data).forEach(([word, learningData]) => {
      this.weightLearningData.set(word, learningData)
    })
  }

  /**
   * 清理过期数据
   */
  cleanupExpiredData(maxAge: number = 30 * 24 * 60 * 60 * 1000): void {
    const cutoffTime = Date.now() - maxAge

    // 清理过期的权重学习数据
    this.weightLearningData.forEach((data, word) => {
      if (data.last_updated < cutoffTime) {
        this.weightLearningData.delete(word)
      }
    })

    // 清理过期的反馈数据
    this.feedbackStorage.forEach((feedbacks, username) => {
      const recentFeedbacks = feedbacks.filter(feedback => feedback.timestamp > cutoffTime)
      if (recentFeedbacks.length === 0) {
        this.feedbackStorage.delete(username)
      } else {
        this.feedbackStorage.set(username, recentFeedbacks)
      }
    })
  }

  /**
   * 持久化反馈数据
   */
  private persistFeedback(feedback: UserFeedback): void {
    // 在实际应用中，这里应该将数据保存到持久化存储
    // 例如：localStorage、IndexedDB、或发送到服务器
    try {
      // 检查是否在浏览器环境中
      if (typeof window !== 'undefined' && window.localStorage) {
        const key = `feedback_${feedback.username}_${feedback.timestamp}`
        localStorage.setItem(key, JSON.stringify(feedback))
      }
      // 在测试环境中，我们跳过持久化
    } catch (error) {
      console.warn('Failed to persist feedback:', error)
    }
  }
}
